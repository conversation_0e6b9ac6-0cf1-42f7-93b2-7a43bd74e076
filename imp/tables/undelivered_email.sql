select id,
       date,
       server,
       case  when upper(country) = 'UA' then 1
			 when upper(country) = 'DE' then 2
			 when upper(country) = 'UK' then 3
			 when upper(country) = 'FR' then 4
			 when upper(country) = 'CA' then 5
			 when upper(country) = 'US' then 6
			 when upper(country) = 'ID' then 7
			 when upper(country) = 'RU' then 8
			 when upper(country) = 'PL' then 9
			 when upper(country) = 'HU' then 10
			 when upper(country) = 'RO' then 11
			 when upper(country) = 'ES' then 12
			 when upper(country) = 'AT' then 13
			 when upper(country) = 'BE' then 14
			 when upper(country) = 'BR' then 15
			 when upper(country) = 'CH' then 16
			 when upper(country) = 'CZ' then 17
			 when upper(country) = 'IN' then 18
			 when upper(country) = 'IT' then 19
			 when upper(country) = 'NL' then 20
			 when upper(country) = 'TR' then 21
			 when upper(country) = 'BY' then 22
			 when upper(country) = 'CL' then 23
			 when upper(country) = 'CO' then 24
			 when upper(country) = 'GR' then 25
			 when upper(country) = 'SK' then 26
			 when upper(country) = 'TH' then 27
			 when upper(country) = 'TW' then 28
			 when upper(country) = 'VE' then 29
			 when upper(country) = 'BG' then 30
			 when upper(country) = 'HR' then 31
			 when upper(country) = 'KZ' then 32
			 when upper(country) = 'NO' then 33
			 when upper(country) = 'RS' then 34
			 when upper(country) = 'SE' then 35
			 when upper(country) = 'NZ' then 36
			 when upper(country) = 'NG' then 37
			 when upper(country) = 'AR' then 38
			 when upper(country) = 'MX' then 39
			 when upper(country) = 'PE' then 40
			 when upper(country) = 'CN' then 41
			 when upper(country) = 'HK' then 42
			 when upper(country) = 'KR' then 43
			 when upper(country) = 'PH' then 44
			 when upper(country) = 'PK' then 45
			 when upper(country) = 'JP' then 46
			 when upper(country) = 'CU' then 47
			 when upper(country) = 'PR' then 48
			 when upper(country) = 'SV' then 49
			 when upper(country) = 'CR' then 50
			 when upper(country) = 'AU' then 51
			 when upper(country) = 'DO' then 52
			 when upper(country) = 'UY' then 53
			 when upper(country) = 'EC' then 54
			 when upper(country) = 'SG' then 55
			 when upper(country) = 'AZ' then 56
			 when upper(country) = 'FI' then 57
			 when upper(country) = 'BA' then 58
			 when upper(country) = 'PT' then 59
			 when upper(country) = 'DK' then 60
			 when upper(country) = 'IE' then 61
			 when upper(country) = 'MY' then 62
			 when upper(country) = 'ZA' then 63
			 when upper(country) = 'AE' then 64
			 when upper(country) = 'QA' then 65
			 when upper(country) = 'SA' then 66
			 when upper(country) = 'KW' then 67
			 when upper(country) = 'BH' then 68
			 when upper(country) = 'EG' then 69
			 when upper(country) = 'MA' then 70
			 when upper(country) = 'UZ' then 71
	   end as country_id,
       upper(country) as country_code,
       email_box,
       email_domain,
       undelivered_reason
from email_bounced
where date = current_date - 1;