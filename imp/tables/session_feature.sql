declare @dt_begin int = :to_sqlcode_date_or_datediff_start,
        @dt_end int = :to_sqlcode_date_or_datediff_end;

select id,
       type as "type",
       source as "source",
       date_diff,
       "date" as "date",
       id_account,
       id_session,
       "version" as "version",
       flags,
       feature_data
from dbo.session_feature with (nolock)
where date_diff between @dt_begin and @dt_end;
