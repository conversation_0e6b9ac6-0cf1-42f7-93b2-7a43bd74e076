select country_id = 1,
	   si.date as impression_datediff,
       si.id_session,
       si.id_search,
       count(distinct si.id) as impression_cnt,
       count(distinct case when si.job_destination = 2 then si.id end) as away_job_cnt,
       count(distinct case when j.id_project = -1 then si.id end) as dte_job_cnt,
       count(distinct case when si.visual_flags & 1 = 1 then si.id end) as new_job_cnt,
       count(distinct case when si.visual_flags & 4 = 4 then si.id end) as few_applies_job_cnt,
       count(distinct case when si.visual_flags & 64 = 64 then si.id end) as premium_job_cnt,
       count(distinct case when si.visual_flags & 8 = 8 then si.id end) as address_cnt,
       count(distinct case when si.visual_flags & 8 = 8 and si.job_destination = 3 then si.id end) as exact_address_dte_job_cnt,
       count(distinct case when j.remote_type = 1 then si.id end) as remote_job_cnt,
       count(distinct case when isnull(j.job_type1, '') <> '' or isnull(j.job_type2, '') <> '' then si.id end) as set_jobtype_job_cnt,
       count(distinct case when j.job_type1 = 1 or j.job_type2 = 1 then si.id end) as full_time_job_cnt,
       count(distinct case when j.job_type1 = 2 or j.job_type2 = 2 then si.id end) as temporary_employment_job_cnt,
       count(distinct case when j.job_type1 = 3 or j.job_type2 = 3 then si.id end) as part_time_job_cnt,
       count(distinct case when j.job_type1 = 4 or j.job_type2 = 4 then si.id end) as internship_job_cnt
from session_impression si with (nolock)
join session_impression_on_screen sis with (nolock) on si.id = sis.id_impression and si.date = sis.date_diff
join job_region jr with(nolock) on jr.uid = si.uid_job
join job j with (nolock) on jr.id_job = j.id
where si.date = @dt_start - 1
and si.id_search is not null
group by si.date, si.id_search, si.id_session
