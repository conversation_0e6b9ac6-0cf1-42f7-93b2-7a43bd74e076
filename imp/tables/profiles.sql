select 1 as country,
	   id,
       email,
       phone,
       date_diff,
       is_submitted,
       data,
       date_created,
       date_updated,
       date_submitted,
       date_first_json_update,
       date_last_activity,
       is_deleted,
       date_deleted,
       date_json_first_updated,
       id_public
from dbo.profiles
where date_updated >= current_date - 7
 or date_last_activity >= current_date - 7
 or date_created >= current_date - 7
 or date_submitted >= (current_date - '1900-01-01') - 7
 or date_deleted >= (current_date - '1900-01-01') - 7;