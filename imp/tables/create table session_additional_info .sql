declare @dt_begin int = ${dt_begin},
		@dt_end int = ${dt_end},
		@country_id int = ${country_id}


SELECT @country_id as country_id,
	   session.date_diff,
       first_visit_date_diff,
       session.id                           as id_session,
       session.id_traf_source,
       session.id_current_traf_source,
       session.session_create_page_type,
       ip_cc,
       session.flags,
       cookie_label,
       session_account.id_account,
       count(distinct session_search.id)    as searche_cnt,
       session_away.away_cnt,
       session_away.revenue_usd,
       count(distinct session_jdp.id)       as jdp_cnt,
       count(distinct session_alertview.id) as alertview_cnt,
       count(distinct session_apply.id)     as apply_cnt
FROM dbo.session with (nolock)
         left join dbo.session_search with (nolock)
                   on session.date_diff = session_search.date_diff
                       and session.id = session_search.id_session
         left join dbo.session_account with (nolock)
                   on session.date_diff = session_account.date_diff
                       and session.id = session_account.id_session
         left join
     (
         Select session_away.date_diff,
                session_away.id_session,
                count(distinct session_away.id)            as away_cnt,
                sum(coalesce(session_away.click_price, 0)) as revenue_usd
         from dbo.session_away with (nolock)
                  left join dbo.info_currency with (nolock)
                            on session_away.id_currency = info_currency.id
         group by session_away.date_diff,
                  session_away.id_session
     ) as session_away
     on session.date_diff = session_away.date_diff
         and session.id = session_away.id_session
         left join dbo.session_jdp with (nolock)
                   on session.date_diff = session_jdp.date_diff
                       and session.id = session_jdp.id_session

         left join dbo.session_alertview with (nolock)
                   on session.date_diff = session_alertview.date_diff
                       and session.id = session_alertview.id_session

         left join dbo.session_apply_action with (nolock)
                   on session_jdp.date_diff = session_apply_action.date_diff
                       and session_jdp.id = session_apply_action.id_jdp

         left join dbo.session_apply with (nolock)
                   on session_apply_action.date_diff = session_apply.date_diff
                       and session_apply_action.id = session_apply.id_src_jdp_action
where session.flags & 1 = 0
  and session.date_diff between @dt_begin and @dt_end
group by session.date_diff,
         first_visit_date_diff,
         session.id,
         session.id_traf_source,
         session.id_current_traf_source,
         session.session_create_page_type,
         ip_cc,
         session.flags,
         cookie_label,
         session_account.id_account,
         session_away.away_cnt,
         session_away.revenue_usd;

