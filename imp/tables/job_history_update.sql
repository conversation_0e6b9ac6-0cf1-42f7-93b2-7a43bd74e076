declare @dt_begin date = cast(getdate()-2 as date)

select  1 as country,
		id,
		id_similar_group,
		id_project,
		date_created,
		date_updated,
		date_expired,
		title,
		company_name,
		salary_val1,
		salary_val2,
		id_currency,
		id_salary_rate,
		job_type1,
		job_type2,
		id_language,
		html_desc_mode,
		checksum,
		is_pdf,
		emails,
		emails_src,
		lang_title,
		lang_text,
		company_name_hash64,
		partial_update,
		telephones,
		phone_src,
		state_flags,
		remote_type,
		text_hash64,
		html_hash64
from job with(nolock)
--where date_created >= @dt_begin or date_updated >= @dt_begin