declare @dt_begin int = ${dt_begin},
		@dt_end int = ${dt_end},
		@country_id int = ${country_id};

select @country_id as country,
       si.id,
       si.date,
       si.id_session,
       si.id_search,
       si.id_alertview,
       si.page,
       si.uid_job,
       si.position,
       si.rel_bonus,
       si.job_age,
       si.score,
       si.job_destination,
       si.visual_flags,
       si.region_q_relation,
       si.click_price,
       si.serp_click_value
from session_impression si
join session s on si.date = s.date_diff and si.id_session = s.id
where si.date between @dt_begin and @dt_end and s.flags & 64 = 64