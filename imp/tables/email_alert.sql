declare @dt_begin date = cast(dbo.ex_getdate()-1 as date),
		@country_id int = ${country_id}

select @country_id as country,
		el.id,
		el.id_account,
		el.date_add,
		el.id_traf_src,
		el.unsub_date,
		el.id_type_unsub,
		iif(charindex(nchar(0x00) collate Latin1_General_BIN, el.search collate Latin1_General_BIN) > 0,
					left(replace(el.search collate Latin1_General_BIN, nchar(0x00) collate Latin1_General_BIN ,''),
								(charindex(nchar(0x00) collate Latin1_General_BIN, el.search collate Latin1_General_BIN)) - 1),
										el.search) as search,
		el.id_region,
		el.txt_region,
		el.id_lang,
		el.symbols,
		el.[ip],
		el.symbols_hash64,
		el.query_hash64,
		el.usr_id64,
		el.id_alert_type,
		el.is_sent,
		el.radius,
		el.salary,
		el.radius_km,
		el.token_hash64
from email_alert el with (nolock)
where cast(el.date_add as date) = @dt_begin or cast(unsub_date as date) = @dt_begin