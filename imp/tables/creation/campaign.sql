-- auto-generated definition
create table campaign
(
    country          smallint       not null,
    id               integer        not null,
    id_project       integer        not null,
    click_price      numeric(14, 5),
    currency         integer        not null,
    name             varchar(500)   not null,
    budget           numeric(14, 5) not null,
    utm              varchar(500),
    id_site          integer        not null,
    campaign_status  smallint,
    flags            integer        not null,
    date_end         timestamp,
    date_start       timestamp,
    utm_template     varchar(500),
    is_price_per_job boolean,
    daily_budget     numeric(14, 5),
    daily_time_start varchar(100),
    action_price     numeric(14, 5),
    conversion_rate  numeric(14, 5),
    constraint pk_campaign
        primary key (country, id)
);