-- auto-generated definition
create table profile_accounts
(
    country     smallint not null,
    id          serial,
    id_profile  integer  not null,
    id_accounts integer  not null,
    constraint pk_profile_accounts_id
        primary key (country, id_profile, id_accounts)
);

alter table profile_accounts
    owner to postgres;

grant select on sequence profile_accounts_id_seq to npo;

create index ind_profile_accounts_pr
    on profile_accounts (id_profile);

create index ind_profile_accounts_acc
    on profile_accounts (id_accounts);
