create table imp.email_open
(
	country smallint not null,
	id_account integer not null,
	date_diff integer not null,
	date timestamp,
	email_date_diff integer not null,
	letter_type integer not null,
	id_message varchar(100)

) partition by range (country)
  tablespace data_old;

alter table imp.email_open owner to postgres;

create index idx_email_open_id_mess
	on imp.email_open (id_message);

create index idx_email_open_id_pk
	on imp.email_open (country, id_account, date_diff, email_date_diff, letter_type);


create table imp.email_open_pt_ua partition of imp.email_open
    for values from (1) to (2);

create table imp.email_open_pt_2_7 partition of imp.email_open
    for values from (2) to (8);

create table imp.email_open_pt_8_14 partition of imp.email_open
    for values from (8) to (15);

create table imp.email_open_pt_15_24 partition of imp.email_open
    for values from (15) to (25);

create table imp.email_open_pt_25_49 partition of imp.email_open
    for values from (25) to (50);

create table imp.email_open_pt_50_71 partition of imp.email_open
    for values from (50) to (72);

create table imp.email_open_pt_def partition of imp.email_open
    default;
