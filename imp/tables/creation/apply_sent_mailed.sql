-- auto-generated definition
create table apply_sent_mailed
(
    country         smallint  not null,
    id              integer   not null,
    date_sent       timestamp not null,
    date_diff       integer   not null,
    uid_job_applied bigint    not null,
    email_hash64    bigint,
    id_message      varchar(100),
    daterec         timestamp,
    userrec         varchar(5),
    constraint pk_apply_sent_mailed_id
        primary key (id, country)
);

alter table apply_sent_mailed
    owner to postgres;

create index idx_apply_sent_mailed_datediff
    on apply_sent_mailed (date_diff);
