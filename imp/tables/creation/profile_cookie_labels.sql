-- auto-generated definition
create table profile_cookie_labels
(
    country       smallint not null,
    id_profile    integer  not null,
    cookie_labels bigint   not null,
    constraint pk_profile_cookie_labels_id
        primary key (country, id_profile, cookie_labels)
);

alter table profile_cookie_labels
    owner to postgres;

create index ind_profile_cookie_labels_pr
    on profile_cookie_labels (id_profile);

create index ind_profile_cookie_labels_cl
    on profile_cookie_labels (cookie_labels);