-- auto-generated definition
create table session_attached_session_away
(
    session_away_country   smallint not null,
    session_away_id        bigint   not null,
    session_away_date_diff integer  not null,
    id_session             bigint,
    uid_job                bigint,
    id_project             integer,
    date                   timestamp,
    session_away_flags     integer,
    id_click               bigint,
    id_jdp                 bigint,
    id_click_no_serp       bigint,
    base_score             real,
    score                  real,
    base_rel_bonus         integer,
    rel_bonus              integer,
    id_campaign            integer,
    click_price            numeric(14, 5),
    id_currency            integer,
    id_cdp                 bigint,
    letter_type            integer,
    session_country        smallint,
    session_id             bigint,
    session_date_diff      integer,
    start_date             timestamp,
    cookie_label           bigint,
    ip                     varchar(256),
    session_flags          integer,
    user_agent_hash64      bigint,
    id_traf_source         integer,
    ip_cc                  varchar(2),
    first_visit_date_diff  integer,
    id_current_traf_source integer,
    constraint pk_session_session_away_id
        primary key (session_away_country, session_away_id, session_away_date_diff)
)
    partition by RANGE (session_away_country);

alter table session_attached_session_away
    owner to postgres;

grant select on session_attached_session_away to readonly;

create table session_attached_session_away_pt_ua
    partition of session_attached_session_away
        (
            constraint session_attached_session_away_pt_ua_pkey
                primary key (session_away_country, session_away_id, session_away_date_diff)
            )
        FOR VALUES FROM ('1') TO ('2');

alter table session_attached_session_away_pt_ua
    owner to yiv;

create index session_attached_session_away_session_away_country_id_proje_idx
    on session_attached_session_away_pt_ua (session_away_country, id_project);

grant select on session_attached_session_away_pt_ua to readonly;

create table session_attached_session_away_pt_2_3
    partition of session_attached_session_away
        (
            constraint session_attached_session_away_pt_2_3_pkey
                primary key (session_away_country, session_away_id, session_away_date_diff)
            )
        FOR VALUES FROM ('2') TO ('4');

alter table session_attached_session_away_pt_2_3
    owner to yiv;

create index session_attached_session_away_session_away_country_id_proj_idx1
    on session_attached_session_away_pt_2_3 (session_away_country, id_project);

grant select on session_attached_session_away_pt_2_3 to readonly;

create table session_attached_session_away_pt_4
    partition of session_attached_session_away
        (
            constraint session_attached_session_away_pt_4_pkey
                primary key (session_away_country, session_away_id, session_away_date_diff)
            )
        FOR VALUES FROM ('4') TO ('5');

alter table session_attached_session_away_pt_4
    owner to yiv;

create index session_attached_session_away_session_away_country_id_proj_idx2
    on session_attached_session_away_pt_4 (session_away_country, id_project);

grant select on session_attached_session_away_pt_4 to readonly;

create table session_attached_session_away_pt_5_7
    partition of session_attached_session_away
        (
            constraint session_attached_session_away_pt_5_7_pkey
                primary key (session_away_country, session_away_id, session_away_date_diff)
            )
        FOR VALUES FROM ('5') TO ('8');

alter table session_attached_session_away_pt_5_7
    owner to yiv;

create index session_attached_session_away_session_away_country_id_proj_idx3
    on session_attached_session_away_pt_5_7 (session_away_country, id_project);

grant select on session_attached_session_away_pt_5_7 to readonly;

create table session_attached_session_away_pt_8
    partition of session_attached_session_away
        (
            constraint session_attached_session_away_pt_8_pkey
                primary key (session_away_country, session_away_id, session_away_date_diff)
            )
        FOR VALUES FROM ('8') TO ('9');

alter table session_attached_session_away_pt_8
    owner to yiv;

create index session_attached_session_away_session_away_country_id_proj_idx4
    on session_attached_session_away_pt_8 (session_away_country, id_project);

grant select on session_attached_session_away_pt_8 to readonly;

create table session_attached_session_away_pt_9
    partition of session_attached_session_away
        (
            constraint session_attached_session_away_pt_9_pkey
                primary key (session_away_country, session_away_id, session_away_date_diff)
            )
        FOR VALUES FROM ('9') TO ('10');

alter table session_attached_session_away_pt_9
    owner to yiv;

create index session_attached_session_away_session_away_country_id_proj_idx5
    on session_attached_session_away_pt_9 (session_away_country, id_project);

grant select on session_attached_session_away_pt_9 to readonly;

create table session_attached_session_away_pt_12_14
    partition of session_attached_session_away
        (
            constraint session_attached_session_away_pt_12_14_pkey
                primary key (session_away_country, session_away_id, session_away_date_diff)
            )
        FOR VALUES FROM ('12') TO ('15');

alter table session_attached_session_away_pt_12_14
    owner to yiv;

create index session_attached_session_away_session_away_country_id_proj_idx7
    on session_attached_session_away_pt_12_14 (session_away_country, id_project);

grant select on session_attached_session_away_pt_12_14 to readonly;

create table session_attached_session_away_pt_15
    partition of session_attached_session_away
        (
            constraint session_attached_session_away_pt_15_pkey
                primary key (session_away_country, session_away_id, session_away_date_diff)
            )
        FOR VALUES FROM ('15') TO ('16');

alter table session_attached_session_away_pt_15
    owner to yiv;

create index session_attached_session_away_session_away_country_id_proj_idx8
    on session_attached_session_away_pt_15 (session_away_country, id_project);

grant select on session_attached_session_away_pt_15 to readonly;

create table session_attached_session_away_pt_19
    partition of session_attached_session_away
        (
            constraint session_attached_session_away_pt_19_pkey
                primary key (session_away_country, session_away_id, session_away_date_diff)
            )
        FOR VALUES FROM ('19') TO ('20');

alter table session_attached_session_away_pt_19
    owner to yiv;

create index session_attached_session_away_session_away_country_id_proj_idx9
    on session_attached_session_away_pt_19 (session_away_country, id_project);

grant select on session_attached_session_away_pt_19 to readonly;

create table session_attached_session_away_pt_26_35
    partition of session_attached_session_away
        (
            constraint session_attached_session_away_pt_26_35_pkey
                primary key (session_away_country, session_away_id, session_away_date_diff)
            )
        FOR VALUES FROM ('26') TO ('36');

alter table session_attached_session_away_pt_26_35
    owner to yiv;

create index session_attached_session_awa_session_away_country_id_proj_idx10
    on session_attached_session_away_pt_26_35 (session_away_country, id_project);

grant select on session_attached_session_away_pt_26_35 to readonly;

create table session_attached_session_away_pt_36_50
    partition of session_attached_session_away
        (
            constraint session_attached_session_away_pt_36_50_pkey
                primary key (session_away_country, session_away_id, session_away_date_diff)
            )
        FOR VALUES FROM ('36') TO ('51');

alter table session_attached_session_away_pt_36_50
    owner to yiv;

create index session_attached_session_awa_session_away_country_id_proj_idx11
    on session_attached_session_away_pt_36_50 (session_away_country, id_project);

grant select on session_attached_session_away_pt_36_50 to readonly;

create table session_attached_session_away_pt_51_60
    partition of session_attached_session_away
        (
            constraint session_attached_session_away_pt_51_60_pkey
                primary key (session_away_country, session_away_id, session_away_date_diff)
            )
        FOR VALUES FROM ('51') TO ('61');

alter table session_attached_session_away_pt_51_60
    owner to yiv;

create index session_attached_session_awa_session_away_country_id_proj_idx12
    on session_attached_session_away_pt_51_60 (session_away_country, id_project);

grant select on session_attached_session_away_pt_51_60 to readonly;

create table session_attached_session_away_pt_61_71
    partition of session_attached_session_away
        (
            constraint session_attached_session_away_pt_61_71_pkey
                primary key (session_away_country, session_away_id, session_away_date_diff)
            )
        FOR VALUES FROM ('61') TO ('72');

alter table session_attached_session_away_pt_61_71
    owner to yiv;

create index session_attached_session_awa_session_away_country_id_proj_idx13
    on session_attached_session_away_pt_61_71 (session_away_country, id_project);

grant select on session_attached_session_away_pt_61_71 to readonly;

create table session_attached_session_away_pt_def
    partition of session_attached_session_away
        (
            constraint session_attached_session_away_pt_def_pkey
                primary key (session_away_country, session_away_id, session_away_date_diff)
            )
        DEFAULT;

alter table session_attached_session_away_pt_def
    owner to yiv;

create index session_attached_session_awa_session_away_country_id_proj_idx14
    on session_attached_session_away_pt_def (session_away_country, id_project);

grant select on session_attached_session_away_pt_def to readonly;

create table session_attached_session_away_pt_10_11
    partition of session_attached_session_away
        (
            constraint session_attached_session_away_pt_10_11_pkey
                primary key (session_away_country, session_away_id, session_away_date_diff)
            )
        FOR VALUES FROM ('10') TO ('12');

alter table session_attached_session_away_pt_10_11
    owner to yiv;

create index session_attached_session_away_session_away_country_id_proj_idx6
    on session_attached_session_away_pt_10_11 (session_away_country, id_project);

grant select on session_attached_session_away_pt_10_11 to readonly;

create table session_attached_session_away_pt_16_18
    partition of session_attached_session_away
        (
            constraint session_attached_session_away_pt_16_18_pkey
                primary key (session_away_country, session_away_id, session_away_date_diff)
            )
        FOR VALUES FROM ('16') TO ('19');

alter table session_attached_session_away_pt_16_18
    owner to yiv;

create index session_attached_session_awa_session_away_country_id_proj_idx15
    on session_attached_session_away_pt_16_18 (session_away_country, id_project);

grant select on session_attached_session_away_pt_16_18 to readonly;

create table session_attached_session_away_pt_20_25
    partition of session_attached_session_away
        (
            constraint session_attached_session_away_pt_20_25_pkey
                primary key (session_away_country, session_away_id, session_away_date_diff)
            )
        FOR VALUES FROM ('20') TO ('26');

alter table session_attached_session_away_pt_20_25
    owner to yiv;

create index session_attached_session_awa_session_away_country_id_proj_idx16
    on session_attached_session_away_pt_20_25 (session_away_country, id_project);

grant select on session_attached_session_away_pt_20_25 to readonly;

