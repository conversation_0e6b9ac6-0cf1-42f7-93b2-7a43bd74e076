-- auto-generated definition
create table job_history_update
(
    country             smallint      not null,
    id                  bigint        not null,
    id_similar_group    integer       not null,
    id_project          integer       not null,
    date_created        timestamp     not null,
    date_updated        timestamp     not null,
    date_expired        timestamp,
    title               varchar(2048) not null,
    company_name        varchar(2048),
    salary_val1         numeric(19, 3),
    salary_val2         numeric(19, 3),
    id_currency         smallint,
    id_salary_rate      smallint,
    job_type1           smallint,
    job_type2           smallint,
    id_language         smallint      not null,
    html_desc_mode      smallint,
    checksum            bigint        not null,
    is_pdf              boolean,
    emails              varchar(2048),
    emails_src          smallint,
    lang_title          varchar(10),
    lang_text           varchar(10),
    company_name_hash64 bigint,
    partial_update      boolean,
    telephones          varchar(2048),
    phone_src           smallint,
    state_flags         integer,
    remote_type         integer,
    text_hash64         bigint,
    html_hash64         bigint,
    constraint pk_job_history_update_sn_uid
        primary key (country, id)
);
