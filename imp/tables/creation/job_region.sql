-- auto-generated definition
create table job_region
(
    country       smallint      not null,
    uid           bigint        not null,
    id_job        bigint        not null,
    url           varchar(2048) not null,
    id_region     integer       not null,
    id_campaign   integer,
    inactive      integer,
    rel_bonus     integer       not null,
    last_id_batch bigint,
    constraint pk_job_region_uid
        primary key (country, uid)
);

alter table job_region
    owner to rlu;

create index ind_job_region_id_job
    on job_region (id_job);

create index ind_job_region_id_region
    on job_region (id_region);

create index ind_job_region_last_id_batch
    on job_region (last_id_batch);