create table imp.session_away_message
(
	country smallint not null,
	id_away bigint not null,
	id_session bigint not null,
	date_diff integer not null,
	position smallint,
	id_message varchar(100) not null,

	constraint pk_session_away_message_id
		primary key (country, date_diff, id_away, id_message)

) partition by range (country)
  tablespace data_old;

alter table imp.session_away_message owner to postgres;


create table imp.session_away_message_pt_ua partition of imp.session_away_message
    for values from (1) to (2);

create table imp.session_away_message_pt_2_10 partition of imp.session_away_message
    for values from (2) to (11);

create table imp.session_away_message_pt_11_29 partition of imp.session_away_message
    for values from (11) to (30);

create table imp.session_away_message_pt_30_71 partition of imp.session_away_message
    for values from (30) to (72);

create table imp.session_away_message_pt_def partition of imp.session_away_message
    default;
