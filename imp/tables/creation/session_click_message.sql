create table imp.session_click_message
(
	country smallint not null,
	id_click bigint not null,
	id_message varchar(100) not null,
	date_diff integer,
	id_session bigint,

	constraint pk_session_click_message_id
		primary key (country, date_diff, id_click, id_message)

) partition by range (country);

alter table imp.session_click_message owner to postgres;

create index idx_session_click_mess
	on imp.session_click_message (id_message);

create index idx_session_click_mess_id_click
	on imp.session_click_message (id_click);


create table imp.session_click_message_pt_ua partition of imp.session_click_message
    for values from (1) to (2) partition by range (date_diff);

    create table imp.session_click_message_pt_UA_2020 partition of imp.session_click_message_pt_ua
        for values from (43707) to (44195);

    create table imp.session_click_message_pt_UA_2021_1 partition of imp.session_click_message_pt_ua
        for values from (44195) to (44285);

    create table imp.session_click_message_pt_UA_2021_2 partition of imp.session_click_message_pt_ua
        for values from (44285) to (44376);

    create table imp.session_click_message_pt_UA_2021_3 partition of imp.session_click_message_pt_ua
        for values from (44376) to (44468);

    create table imp.session_click_message_pt_UA_2021_4 partition of imp.session_click_message_pt_ua
        for values from (44468) to (44560);

create table imp.session_click_message_pt_2_10 partition of imp.session_click_message
    for values from (2) to (11);

create table imp.session_click_message_pt_11_29 partition of imp.session_click_message
    for values from (11) to (30);

create table imp.session_click_message_pt_30_71 partition of imp.session_click_message
    for values from (30) to (72);

create table imp.session_click_message_pt_def partition of imp.session_click_message
    default;
