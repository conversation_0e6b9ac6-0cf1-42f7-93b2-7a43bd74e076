-- auto-generated definition
create table cv_build_action
(
    country          smallint not null,
    id               bigint   not null,
    date_diff        integer  not null,
    date             timestamp,
    id_account       integer,
    id_session       bigint   not null,
    id_session_build bigint   not null,
    id_image         bigint,
    json             text,
    id_jdp           bigint,
    type             integer  not null,
    flags            integer  not null,
    step             integer,
    data             varchar(512),
    daterec          timestamp,
    userrec          varchar(5),
    constraint pk_cv_build_action
        primary key (country, date_diff, id)
);

alter table cv_build_action
    owner to postgres;

create index ind_cv_build_action_dd
    on cv_build_action (date_diff);

create index ind_cv_build_action_id
    on cv_build_action (id);

create index ind_cv_build_action_jdp
    on cv_build_action (id_jdp);