-- auto-generated definition
create table session_auth
(
    country    smallint not null,
    id         bigint   not null,
    date_diff  integer  not null,
    date       timestamp,
    id_session bigint,
    source     integer,
    flags      integer,
    method     smallint,
    data       varchar,
    constraint pk_session_auth_id
        primary key (date_diff, id, country)
);

alter table session_auth
    owner to postgres;

create index ind_session_auth_s
    on session_auth (id_session);