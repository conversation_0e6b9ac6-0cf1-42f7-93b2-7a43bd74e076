-- auto-generated definition
create table email_account_interactions
(
    country          smallint  not null,
    id               bigint    not null,
    id_account       integer   not null,
    id_session       bigint    not null,
    interaction_type integer   not null,
    date             timestamp not null,
    date_diff        integer   not null,
    daterec          timestamp,
    userrec          varchar(5),
    constraint pk_email_account_interactions_id
        primary key (id, country)
);

alter table email_account_interactions
    owner to postgres;

create index idx_email_account_interactions
    on email_account_interactions (country, id_session, date_diff);

create index idx_email_account_interactions_sess
    on email_account_interactions (id_session);
