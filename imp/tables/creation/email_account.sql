-- auto-generated definition
create table email_account
(
    country            smallint not null,
    id                 integer  not null,
    email              varchar(256),
    is_verified        smallint,
    date_add           timestamp,
    id_traf_src        integer,
    unsub_date         timestamp,
    id_unsub_type      integer,
    email_hash64       bigint,
    domain_hash        integer,
    day_alarm          integer,
    id_test            integer,
    is_test_default    integer,
    verify_date        timestamp,
    last_visit         integer,
    id_serp_test       smallint,
    id_serp_test_group integer,
    send_interval      integer  not null,
    last_bounce        timestamp,
    flags              integer,
    confirm_type       integer,
    password           char(32),
    name               varchar(256),
    last_name          varchar(256),
    last_session       varchar(256),
    is_deleted         smallint,
    confirm_device     integer,
    id_lang            integer,
    nps_last_show_date timestamp,
    nps_show_count     integer,
    adsense_version    integer,
    phone              varchar(256),
    phone_hash64       bigint,
    daterec            timestamp,
    userrec            varchar(5),
    constraint pk_email_account_id
        primary key (id, country)
);

alter table email_account
    owner to postgres;
