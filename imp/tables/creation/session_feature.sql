create table imp.session_feature
(
    country_id integer,
    id         bigint    not null,
    type       int       not null,
    source     int       not null,
    date_diff  int       not null,
    date       timestamp not null,
    id_account int,
    id_session bigint    not null,
    version    int       not null,
    flags      int       not null,
    constraint pk_session_feature
        primary key (country_id, date_diff, id)
) partition by range (country_id)
    tablespace data_old;

create table if not exists imp.session_feature_pt_ua
    partition of imp.session_feature
        for values from ('1') to ('2');


create table if not exists imp.session_feature_pt_2_10
    partition of imp.session_feature
        for values from ('2') to ('11');


create table if not exists imp.session_feature_pt_11_29
    partition of imp.session_feature
        for values from ('11') to ('30');


create table if not exists imp.session_feature_pt_30_50
    partition of imp.session_feature
        for values from ('30') to ('51');


create table if not exists imp.session_feature_pt_51_71
    partition of imp.session_feature
        for values from ('51') to ('72');
