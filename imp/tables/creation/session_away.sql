create table imp.session_away
(
	country smallint not null,
	id bigint not null,
	date_diff integer not null,
	id_session bigint not null,
	uid_job bigint not null,
	id_project integer not null,
	date timestamp not null,
	flags integer,
	id_click bigint,
	id_jdp bigint,
	id_click_no_serp bigint,
	base_score real,
	score real,
	base_rel_bonus integer,
	rel_bonus integer,
	id_campaign integer,
	click_price numeric(14,5),
	id_currency integer,
	id_cdp bigint,
	letter_type integer,

	constraint pk_session_away_id
		primary key (country, date_diff, id)

) partition by range (country)
  tablespace data_old;

alter table imp.session_away owner to postgres;



create table imp.session_away_pt_ua partition of imp.session_away
    for values from (1) to (2);

create table imp.session_away_pt_2_3 partition of imp.session_away
    for values from (2) to (4);

create table imp.session_away_pt_4 partition of imp.session_away
    for values from (4) to (5);

create table imp.session_away_pt_5_7 partition of imp.session_away
    for values from (5) to (8);

create table imp.session_away_pt_8 partition of imp.session_away
    for values from (8) to (9);

create table imp.session_away_pt_9 partition of imp.session_away
    for values from (9) to (10);

create table imp.session_away_pt_10_11 partition of imp.session_away
    for values from (10) to (12);

create table imp.session_away_pt_12_14 partition of imp.session_away
    for values from (12) to (15);

create table imp.session_away_pt_15 partition of imp.session_away
    for values from (15) to (16);

create table imp.session_away_pt_16_18 partition of imp.session_away
    for values from (16) to (19);

create table imp.session_away_pt_19_20 partition of imp.session_away
    for values from (19) to (21);

create table imp.session_away_pt_21_22 partition of imp.session_away
    for values from (21) to (23);

create table imp.session_away_pt_23_26 partition of imp.session_away
    for values from (23) to (27);

create table imp.session_away_pt_27_35 partition of imp.session_away
    for values from (27) to (36);

create table imp.session_away_pt_26_40 partition of imp.session_away
    for values from (36) to (41);

create table imp.session_away_pt_41_71 partition of imp.session_away
    for values from (41) to (72);

create table imp.session_away_pt_def partition of imp.session_away
    default;
