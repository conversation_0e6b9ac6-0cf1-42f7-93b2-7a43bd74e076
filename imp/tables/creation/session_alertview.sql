-- auto-generated definition
create table session_alertview
(
    country                smallint  not null,
    id                     bigint    not null,
    date_diff              integer   not null,
    id_session             bigint    not null,
    date                   timestamp not null,
    results_total          integer   not null,
    results_on_page        integer   not null,
    service_flags          integer,
    elastic_query_time_ms  integer,
    sub_id_alert           integer   not null,
    sub_date_from          timestamp,
    sub_date_to            timestamp,
    visible_results_count  integer,
    showmore_pressed       boolean,
    elastic_search_time_ms integer,
    email_test_id          integer,
    email_test_group       integer,
    elastic_took_time_ms   integer,
    daterec                timestamp,
    userrec                varchar(5),
    constraint pk_session_alertview_date_diff_id
        primary key (date_diff, id, country)
);

alter table session_alertview
    owner to postgres;

create index idx_session_alertview_sess
    on session_alertview (id_session);

create index idx_session_alertview_diff
    on session_alertview (date_diff);