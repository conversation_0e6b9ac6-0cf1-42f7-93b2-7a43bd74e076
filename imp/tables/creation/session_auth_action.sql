-- auto-generated definition
create table session_auth_action
(
    country    smallint not null,
    id         bigint   not null,
    date_diff  integer  not null,
    date       timestamp,
    id_auth    bigint,
    type       integer,
    flags      integer,
    data       varchar(512),
    id_account integer,
    screen     smallint,
    constraint pk_session_auth_action_id
        primary key (date_diff, id, country)
);

alter table session_auth_action
    owner to postgres;

create index ind_session_auth_action_au
    on session_auth_action (id_auth);