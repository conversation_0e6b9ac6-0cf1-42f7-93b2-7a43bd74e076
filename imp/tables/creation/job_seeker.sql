create table imp.job_seeker
(
	id integer not null
		constraint pk_job_seeker_id
			primary key,
	name varchar,
	channel_id integer not null,
	chat_id varchar,
	phone varchar,
	is_auth integer not null,
	is_deleted_bot integer not null,
	created_on timestamp not null,
	deleted_on timestamp,
	auth_date timestamp,
	is_searching integer not null
);

alter table imp.job_seeker owner to postgres;

grant select on imp.job_seeker to readonly;

