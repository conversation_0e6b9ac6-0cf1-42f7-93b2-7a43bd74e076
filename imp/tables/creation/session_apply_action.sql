-- auto-generated definition
create table session_apply_action
(
    country   smallint  not null,
    id        bigint    not null,
    date_diff integer   not null,
    id_jdp    bigint    not null,
    date      timestamp not null,
    type      integer   not null,
    flags     integer   not null,
    screen    integer   not null,
    constraint pk_session_apply_action
        primary key (country, date_diff, id)
);

alter table session_apply_action
    owner to rlu;

create index ind_session_apply_action_jdp
    on session_apply_action (id_jdp);

create index ind_session_apply_action_t
    on session_apply_action (type);