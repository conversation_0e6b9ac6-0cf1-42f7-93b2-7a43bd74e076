create table imp.session_impression_agg
(
    country_id                   smallint not null,
    impression_datediff          integer  not null,
    session_id                   bigint   not null,
    search_id                    bigint   not null,
    impression_cnt               integer,
    away_job_cnt                 integer,
    dte_job_cnt                  integer,
    new_job_cnt                  integer,
    few_applies_job_cnt          integer,
    premium_job_cnt              integer,
    address_cnt                  integer,
    exact_address_dte_job_cnt    integer,
    remote_job_cnt               integer,
    set_jobtype_job_cnt          integer,
    full_time_job_cnt            integer,
    temporary_employment_job_cnt integer,
    part_time_job_cnt            integer,
    internship_job_cnt           integer,
    constraint pk_session_impression_id
        primary key (country_id, impression_datediff, session_id, search_id)

) partition by range (country_id)
  tablespace data_old;

alter table imp.session_impression_agg
    owner to postgres;


create table imp.session_impression_agg_pt_ua partition of imp.session_impression_agg
    for values from (1) to (2) partition by range (impression_datediff);

    create table imp.session_impression_agg_pt_UA_2020 partition of imp.session_impression_agg_pt_ua
        for values from (43707) to (44195);

    create table imp.session_impression_agg_pt_UA_2021_1 partition of imp.session_impression_agg_pt_ua
        for values from (44195) to (44285);

    create table imp.session_impression_agg_pt_UA_2021_2 partition of imp.session_impression_agg_pt_ua
        for values from (44285) to (44376);

    create table imp.session_impression_agg_pt_UA_2021_3 partition of imp.session_impression_agg_pt_ua
        for values from (44376) to (44468);

    create table imp.session_impression_agg_pt_UA_2021_4 partition of imp.session_impression_agg_pt_ua
        for values from (44468) to (44560);

create table imp.session_impression_agg_pt_def partition of imp.session_impression_agg
    default;
