-- auto-generated definition
create table cv_created
(
    country    smallint not null,
    id         bigint   not null,
    date_diff  integer  not null,
    date       timestamp,
    id_account integer,
    id_session bigint   not null,
    id_image   bigint,
    json       text,
    id_jdp     bigint,
    daterec    timestamp,
    userrec    varchar(5),
    constraint pk_cv_created
        primary key (country, date_diff, id)
);

alter table cv_created
    owner to postgres;

create index ind_cv_created_acc
    on cv_created (id_account);

create index ind_cv_created_id_j
    on cv_created (id_jdp);
