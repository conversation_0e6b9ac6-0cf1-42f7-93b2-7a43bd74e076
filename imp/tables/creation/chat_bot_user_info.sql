create table imp.chat_bot_user_info
(
	id integer not null,
	name varchar,
	channel_id integer not null,
	chat_id varchar,
	phone varchar,
	is_auth integer not null,
	is_deleted_bot integer not null,
	created_on timestamp not null,
	deleted_on timestamp,
	auth_date timestamp,
	is_searching integer not null,
	date_diff integer not null,
	constraint pk_chat_bot_user_info
		primary key (id, date_diff)
);

alter table imp.chat_bot_user_info owner to postgres;

grant select on imp.chat_bot_user_info to readonly;

