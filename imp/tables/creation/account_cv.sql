-- auto-generated definition
create table account_cv
(
    country        smallint     not null,
    id             integer      not null,
    id_account     integer,
    source         integer,
    flags          integer,
    date_created   timestamp,
    json           text,
    filename       varchar(1000),
    id_pwa_account bigint,
    pwa_date_diff  integer,
    is_deleted     boolean      not null,
    score          integer,
    json_checksum  bigint,
    cookie_label   bigint,
    id_file        varchar(100) not null,
    daterec        timestamp,
    userrec        varchar(5),
    constraint pk_account_cv_id
        primary key (country, id)
);

create index ind_account_cv_ia
    on account_cv (id_account) include (is_deleted, date_created);

create index ind_account_cv_clb
    on account_cv (cookie_label);

