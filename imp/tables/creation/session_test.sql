create table imp.session_test
(
	country smallint not null,
	id_session bigint not null,
	id_test integer not null,
	"group" integer not null,
	date_diff integer not null,
	iteration integer,

	constraint pk_session_test
		primary key (country, date_diff, id_session, id_test)

) partition by range (country)
  tablespace data_old;

alter table imp.session_test owner to postgres;

create index ind_session_test_test
	on imp.session_test (id_test);

create index ind_session_test_sess
	on imp.session_test (id_session);



create table imp.session_test_pt_ua partition of imp.session_test
    for values from (1) to (2);

create table imp.session_test_pt_2_3 partition of imp.session_test
    for values from (2) to (4);

create table imp.session_test_pt_4 partition of imp.session_test
    for values from (4) to (5);

create table imp.session_test_pt_5_7 partition of imp.session_test
    for values from (5) to (8);

create table imp.session_test_pt_8 partition of imp.session_test
    for values from (8) to (9);

create table imp.session_test_pt_9_14 partition of imp.session_test
    for values from (9) to (15);

create table imp.session_test_pt_def partition of imp.session_test
    default;
