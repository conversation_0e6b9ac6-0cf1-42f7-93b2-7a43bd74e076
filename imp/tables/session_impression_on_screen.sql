declare @dt_begin int = ${dt_begin},
		@dt_end int = ${dt_end},
		@country_id int = ${country_id}

select @country_id as country,
	   sis.id_impression,
	   sis.date_diff,
	   cast(sis.[time] as datetime) as [time]
from session_impression_on_screen sis
join session_impression si on sis.id_impression = si.id and sis.date_diff = si.date
join session s on si.date = s.date_diff and si.id_session = s.id
where sis.date_diff between @dt_begin and @dt_end and s.flags & 64 = 64