declare @dt_begin int = ${dt_begin},
		@dt_end int = ${dt_end}

select  1 as country,
		id,
		date_diff,
		id_session,
		id_search,
		id_alertview,
		uid_job,
		[date],
		position,
		rel_bonus,
		job_age,
		flags,
		id_project,
		id_job,
		id_campaign,
		base_score,
		score,
		base_rel_bonus,
		id_impression,
		click_price,
		id_currency,
		job_destination,
		id_recommend,
		id_impression_recommend
from session_click with (nolock)
where date_diff between @dt_begin and @dt_end
