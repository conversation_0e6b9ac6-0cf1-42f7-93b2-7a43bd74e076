SET NOCOUNT ON;

SELECT u.id,
       email,
       password_hash,
       communication_email,
       account_balance,
       company,
       currency,
       flags,
       secret_key,
       symbols_count_after_dot,
       api_key,
       budget,
       disabled_subscribes,
       daily_budget,
       daily_time_start,
       uid_client,
       MAX(ll.date) AS last_login_date
FROM auction.[user] u WITH (NOLOCK)
         LEFT JOIN auction.login_log ll WITH (NOLOCK) ON u.id = ll.id_user
GROUP BY u.id, email, password_hash, communication_email, account_balance, company, currency, flags, secret_key,
         symbols_count_after_dot, api_key, budget, disabled_subscribes, daily_budget, daily_time_start, uid_client;
