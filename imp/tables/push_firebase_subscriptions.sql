SET NOCOUNT ON;

DE<PERSON>AR<PERSON> @datediff_start int = :to_sqlcode_date_or_datediff_start,
        @datediff_end int = :to_sqlcode_date_or_datediff_end;

SELECT token_hash64,
       id_session,
       cookie_label,
       CAST(date AS datetime)        AS date,
       date_diff,
       token,
       id_lang,
       id_account,
       token_type,
       CAST(expire_date AS datetime) AS expire_date
FROM dbo.push_firebase_subscriptions
WHERE date_diff BETWEEN @datediff_start AND @datediff_end
;
