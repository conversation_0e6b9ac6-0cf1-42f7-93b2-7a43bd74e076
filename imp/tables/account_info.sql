declare @dt_start date = cast(dbo.ex_getdate() - 1 as date),
		@country_id int = ${country_id}

select  @country_id as country,
		i.id_account,
		i.adsense_version,
		i.id_traf_src,
		i.unsub_date,
		i.id_unsub_type,
		i.last_visit,
		i.day_alarm,
		i.date_modified
from account a with(nolock)
left join account_info i with(nolock) on a.id = i.id_account
where (a.date_add >= @dt_start or i.date_modified >= @dt_start) and i.id_account is not null