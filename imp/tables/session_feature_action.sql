declare @dt_begin int = :to_sqlcode_datediff_start,
        @dt_end int = :to_sqlcode_datediff_start,
        @country_id int = :to_sql_country_id;

select @country_id as country_id,
       id,
       date_diff,
       date,
       id_session_feature,
       screen,
       type,
       flags,
       --action_data
	   iif(charindex(nchar(0x00) collate Latin1_General_BIN, action_data collate Latin1_General_BIN) > 0,
					left(replace(action_data collate Latin1_General_BIN, nchar(0x00) collate Latin1_General_BIN ,''),
								(charindex(nchar(0x00) collate Latin1_General_BIN, action_data collate Latin1_General_BIN)) - 1),
										action_data) as action_data
from session_feature_action with (nolock)
where date_diff between @dt_begin and @dt_end
;
