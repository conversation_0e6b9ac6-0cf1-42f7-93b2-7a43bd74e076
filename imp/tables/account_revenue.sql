declare @dt_begin int = ${dt_begin},
		@dt_end int = ${dt_end},
		@country_id int = ${country_id};

select @country_id as country,
       id_account,
       date_diff,
       away_clicks_free,
       away_clicks_premium,
       dte_clicks_free,
       dte_clicks_premium,
       total_revenue,
       email_revenue,
       serp_revenue
from account_revenue with(nolock)
where date_diff between @dt_begin and @dt_end
