create view session_cv(cookie_label, start_date, country, n_client_cvs) as
SELECT s_cv.cookie_label,
       s_cv.start_date,
       s_cv.country,
       count(cvc.id) AS n_client_cvs
FROM imp.cv_created cvc
         JOIN imp.session s_cv
              ON s_cv.id = cvc.id_session AND s_cv.date_diff = cvc.date_diff AND s_cv.country = cvc.country
GROUP BY s_cv.cookie_label, s_cv.start_date, s_cv.country;