create view dreddv_stat_significance_email_abtest
            (test_id, start_date, end_date, task_id, country_code, name_country_eng, letter_type, account_age,
             base_group_id, competing_group_id, metric_group, metric_name, metric_value, delta, delta_ci_lower,
             delta_ci_upper, p_value, confidence_level, statistical_method)
as
with report_temp      as (select id,
                                 test_id,
                                 start_date,
                                 end_date,
                                 record_add_datetime,
                                 min(start_date)
                                 over (partition by test_id)                                                        as test_start_date,
                                 1 = row_number()
                                     over (partition by test_id, start_date, end_date order by record_add_datetime) as is_latest_report_for_date
                          from dredd.abtest_report_task
                          where (current_date - end_date) >= 0
                            and (current_date - end_date) <= 60 -- include only A/B tests, which ended sooner than 60 days ago
                            and author = 'dredd'                -- include only reports, generated by daily Dredd Significance Tool job
                            and test_type = 0 -- include only Email A/B tests (0 would be Email A/B tests)
),
     report           as (select id,
                                 test_id,
                                 start_date,
                                 end_date,
                                 record_add_datetime,
                                 test_start_date,
                                 is_latest_report_for_date
                          from report_temp
                          where is_latest_report_for_date),
     report_countries as (select distinct
                                 report.test_id,
                                 cg.country_code
                          from report
                               join dredd.abtest_report_compare_groups cg
                               on report.id = cg.task_id),
     report_groups    as (select distinct
                                 'Main metrics' as metric_group,
                                 report.test_id,
                                 cg.base_group_id,
                                 cg.competing_group_id
                          from report
                               join dredd.abtest_report_compare_groups cg
                               on report.id = cg.task_id
                          union all
                          (select distinct
                                  'LTV metrics' as metric_group,
                                  report.test_id,
                                  cg.base_group_id,
                                  cg.competing_group_id
                           from report
                                join dredd.abtest_report_compare_groups cg
                                on report.id = cg.task_id
                           union all
                           select distinct
                                  'LTV metrics'    as metric_group,
                                  report.test_id,
                                  cg.base_group_id,
                                  cg.base_group_id as competing_group_id
                           from report
                                join dredd.abtest_report_compare_groups cg
                                on report.id = cg.task_id)),
     metrics          as (select 'Main metrics'            as metric_group,
                                 unnest(array ['Letter Open Rate %',
                                     'Letter Click Rate %',
                                     'Letter Away Rate %',
                                     'Conversion Rate % (email)',
                                     'Account Revenue per Account',
                                     'Email Revenue per Account',
                                     'Letters Clicked per Account',
                                     'Conversions per Account',
                                     'Aways per Account']) as metric_name
                          union all
                          select 'LTV metrics',
                                 unnest(array ['Average Revenue per Account (3 days)',
                                     'Average Revenue per Account (7 days)'])),
     letter_types     as (select 'Main metrics'                                      as metric_group,
                                 unnest(array ['Total', '1', '8', '71', '-2', '-1']) as letter_type
                          union all
                          select 'LTV metrics',
                                 unnest(array ['Total'])),
     account_ages     as (select 'Main metrics'                                          as metric_group,
                                 unnest(array ['Total', 'new', '8-29 days', '30+ days']) as account_age
                          union all
                          select 'LTV metrics',
                                 unnest(array ['Total', '0 days', '1-7 days', '8-29 days'])),
     statistics       as (select 'Main metrics'                                                as metric_group,
                                 cg.task_id,
                                 cg.country_code,
                                 coalesce(cg.dimensions_additional ->> 'letter_type', 'Total') as letter_type,
                                 coalesce(cg.dimensions_additional ->> 'account_age', 'Total') as account_age,
                                 cg.base_group_id,
                                 cg.competing_group_id,
                                 cg.metric_name,
                                 cg.metric_value,
                                 cg.delta,
                                 cg.delta_ci_lower,
                                 cg.delta_ci_upper,
                                 cg.p_value,
                                 (cg.stat_parameters ->> 'confidence_level')::double precision as confidence_level,
                                 cg.stat_parameters ->> 'method'                               as statistical_method
                          from report
                               join dredd.abtest_report_compare_groups cg
                               on report.id = cg.task_id
                          where coalesce(cg.dimensions_additional ->> 'is_dte_country', 'Total') = 'Total'
                          union all
                          select 'LTV metrics'                                                 as metric_group,
                                 mv.task_id,
                                 mv.country_code,
                                 'Total'                                                       as letter_type,
                                 coalesce(mv.dimensions_additional ->> 'account_age', 'Total') as account_age,
                                 coalesce(cg.base_group_id, mv.group_id)                       as base_group_id,
                                 mv.group_id                                                   as competing_group_id,
                                 mv.metric_name,
                                 mv.metric_value,
                                 case
                                     when cg.competing_group_id is null then 0
                                     else cg.delta
                                     end                                                       as delta,
                                 case
                                     when cg.competing_group_id is null then 0
                                     else cg.delta_ci_upper
                                     end                                                       as delta_ci_upper,
                                 case
                                     when cg.competing_group_id is null then 0
                                     else cg.delta_ci_lower
                                     end                                                       as delta_ci_lower,
                                 cg.p_value,
                                 (cg.stat_parameters ->> 'confidence_level')::double precision as confidence_level,
                                 cg.stat_parameters ->> 'method'                               as statistical_method
                          from report
                               join dredd.abtest_report_metric_value mv
                               on report.id = mv.task_id
                               left join dredd.abtest_report_compare_groups cg
                               on mv.task_id = cg.task_id and mv.metric_name = cg.metric_name and
                                  coalesce(mv.country_code, 'Total') =
                                  coalesce(cg.country_code, 'Total') and
                                  coalesce(mv.dimensions_additional ->> 'account_age', 'Total') =
                                  coalesce(cg.dimensions_additional ->> 'account_age', 'Total') and
                                  mv.group_id = cg.competing_group_id
                          where mv.metric_name in
                                ('Average Revenue per Account (3 days)', 'Average Revenue per Account (7 days)')
                            and coalesce(cg.dimensions_additional ->> 'is_dte_country', 'Total') = 'Total')
select report.test_id,
       report.start_date,
       report.end_date,
       report.id as task_id,
       report_countries.country_code,
       c.name_country_eng,
       case
           when letter_types.letter_type = '-1' then null
           when letter_types.letter_type = '-2' then 'other'
           when letter_types.letter_type = 'Total' then letter_types.letter_type
           else letter_types.letter_type
           end   as letter_type,
       account_ages.account_age,
       report_groups.base_group_id,
       report_groups.competing_group_id,
       metrics.metric_group,
       metrics.metric_name,
       statistics.metric_value,
       statistics.delta,
       statistics.delta_ci_lower,
       statistics.delta_ci_upper,
       statistics.p_value,
       statistics.confidence_level,
       statistics.statistical_method
from report
     cross join metrics
     left join report_countries
     on report.test_id = report_countries.test_id
     left join report_groups
     on report.test_id = report_groups.test_id and metrics.metric_group = report_groups.metric_group
     left join letter_types
     on metrics.metric_group = letter_types.metric_group
     left join account_ages
     on metrics.metric_group = account_ages.metric_group
     left join statistics
     on report.id = statistics.task_id
         and metrics.metric_group = statistics.metric_group
         and coalesce(report_countries.country_code, 'Total') = coalesce(statistics.country_code, 'Total')
         and metrics.metric_name = statistics.metric_name
         and letter_types.letter_type = statistics.letter_type
         and account_ages.account_age = statistics.account_age
         and report_groups.base_group_id = statistics.base_group_id
         and report_groups.competing_group_id = statistics.competing_group_id
     left join dimension.countries c
     on report_countries.country_code = lower(c.alpha_2);
