
/* CREATION */

create table dredd.abtest_report_compare_groups
(
    task_id               bigint       not null
        constraint fk_abtest_report_task_abtest_report_compare_groups
            references dredd.abtest_report_task,
    metric_name           varchar(255) not null,
    country_code          varchar(2),
    device_type_id        smallint,
    dimensions_additional json,
    base_group_id         numeric(2, 1),
    competing_group_id    numeric(2, 1),
    metric_value          double precision,
    delta                 double precision,
    delta_ci_lower        double precision,
    delta_ci_upper        double precision,
    p_value               double precision,
    stat_parameters       json
);

/* UPDATING 
New data is inserted by Dredd Significance Tool.
*/


