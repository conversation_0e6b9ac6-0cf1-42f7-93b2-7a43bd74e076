/* CREATION */
create table abtest_report_metric_value
(
    task_id               bigint        not null
        constraint fk_abtest_report_task_abtest_report_metric_value
            references abtest_report_task,
    group_id              numeric(2, 1) not null,
    metric_name           varchar(255)  not null,
    country_code          varchar(2),
    device_type_id        smallint,
    dimensions_additional json,
    metric_value          double precision,
    metric_value_ci_lower double precision,
    metric_value_ci_upper double precision,
    stat_parameters       json,
    numerator_value       numeric,
    denominator_value     numeric
);

/* UPDATING 
New data is inserted by Dredd Significance Tool.
*/
