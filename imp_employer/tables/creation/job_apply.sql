create table imp_employer.job_apply
(
	id bigint,
	id_job bigint not null,
	file_name varchar(2048) null,
	email varchar(2048) null,
	name varchar(2048) null,
	phone varchar(2048) null,
	flags int null,
	date timestamp not null,
	id_session_apply bigint null,
	dd_session_apply int null,
	date_seen timestamp null,
	date_deleted timestamp null,
	id_cv int null,
	converting_result int not null,
	view_source smallint not null,
	cv_hash bigint null,
	questions_count int not null,
	acceptable_answers_count int not null,
	status int not null,
	date_status_changed timestamp null,
	sources smallint not null,

	constraint pk_job_apply_id
		primary key (id, sources)
) /*partition by range (sources)*/;

alter table imp_employer.job_apply owner to postgres;

create index if not exists idx_job_date
	on imp_employer.job_apply (date);

create index if not exists idx_job_id_job
	on imp_employer.job_apply (id_job);

create index if not exists idx_session_apply
	on imp_employer.job_apply (dd_session_apply, id_session_apply);



create table imp_employer.job_apply_pt_1 partition of imp_employer.job_apply
    for values from (1) to (2);

create table imp_employer.job_apply_pt_2 partition of imp_employer.job_apply
    for values from (2) to (3);
