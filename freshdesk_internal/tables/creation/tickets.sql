create table freshdesk_internal.tickets
(
    id              integer   primary key,
    agent_id        integer references freshdesk_internal.agents(id),
    group_id        integer references freshdesk_internal.groups(id),
    status_id       integer   not null references freshdesk_internal.statuses(id),
    channel_id      integer   not null references freshdesk_internal.channels(id),
    priority_id     integer   not null references freshdesk_internal.priorities(id),
    tags            text,
    created_at      timestamp not null,
    resolved_at     timestamp,
    first_reply_at  timestamp,
    val_result      boolean,
    source_type     integer references freshdesk_internal.source_types(id),
    vvt_flags       integer,
    country         varchar(50),
    id_project      integer[],
    domain_project  varchar(100),
    id_user_auction integer[],
    login_auction   varchar(100),
    id_employer     integer[],
    requester_id    integer references freshdesk_internal.requesters(id),
    type_client     integer references freshdesk_internal.client_types(id),
    type_category   integer references freshdesk_internal.category_types(id),
    type_request    integer references freshdesk_internal.request_types(id),
    ticket_type     integer references freshdesk_internal.ticket_types(id)
);