create or replace procedure freshdesk_internal.delete_old_logs()
    language plpgsql
as
$$
begin

    with
        t as (
            select
                type,
                max(update_time) as max_update_time
            from
                freshdesk_internal.update_history
            group by type
        )
    delete
    from
        freshdesk_internal.update_history
        using t
    where
          update_time::date < current_date - 30
      and t.max_update_time != update_history.update_time
      and t.type = update_history.type;


end;

$$;

alter procedure freshdesk_internal.delete_old_logs() owner to ypr;

