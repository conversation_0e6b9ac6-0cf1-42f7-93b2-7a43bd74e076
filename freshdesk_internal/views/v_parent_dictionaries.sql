create view freshdesk_internal.v_parent_dictionaries(table_name, key, value) as
select
    'agents'::text                         as table_name,
    agents.id_freshdesk::character varying as key,
    agents.id::character varying           as value
from
    freshdesk_internal.agents
union all
select
    'groups'::text                         as table_name,
    groups.id_freshdesk::character varying as key,
    groups.id::character varying           as value
from
    freshdesk_internal.groups
union all
select
    'requesters'::text                         as table_name,
    requesters.id_freshdesk::character varying as key,
    requesters.id::character varying           as value
from
    freshdesk_internal.requesters
union all
select
    'statuses'::text               as table_name,
    statuses.id::character varying as key,
    statuses.name                  as value
from
    freshdesk_internal.statuses
union all
select
    'channels'::text               as table_name,
    channels.id::character varying as key,
    channels.name                  as value
from
    freshdesk_internal.channels
union all
select
    'priorities'::text               as table_name,
    priorities.id::character varying as key,
    priorities.name                  as value
from
    freshdesk_internal.priorities
union all
select
    'client_types'::text               as table_name,
    client_types.name                  as key,
    client_types.id::character varying as value
from
    freshdesk_internal.client_types
union all
select
    'ticket_types'::text               as table_name,
    ticket_types.name                  as key,
    ticket_types.id::character varying as value
from
    freshdesk_internal.ticket_types
union all
select
    'vvt_flags'::text                 as table_name,
    vvt_flags.ticket_field            as key,
    vvt_flags.flag::character varying as value
from
    freshdesk_internal.vvt_flags
where
    vvt_flags.is_deleted = false;