create view freshdesk_internal.v_classification_dictionary(table_name, key1, key2, value) as
select
    'category_types'::text   as table_name,
    category_types.client_id as key1,
    category_types.name      as key2,
    category_types.id        as value
from
    freshdesk_internal.category_types
union all
select
    'request_types'::text     as table_name,
    request_types.category_id as key1,
    request_types.name        as key2,
    request_types.id          as value
from
    freshdesk_internal.request_types;