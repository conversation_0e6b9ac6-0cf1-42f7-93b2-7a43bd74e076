create or replace procedure imp_statistic.adwords_data_verification(_compare_date date)
language 'plpgsql'
as
$$
declare
    date_to_compare date := _compare_date;
    count_adwords_to_compare bigint;
    count_adwords_dwh bigint;
    message text;
begin

    -- Data from python job (pure google ads API request)
    create temp table tmp_adwords_to_compare as
    select count(*) as count
    from imp_statistic.adwords_to_compare
    where day = date_to_compare;

    -- Data from warehouse
    create temp table tmp_adwords_dwh as
    select count(*) as count
    from imp_statistic.adwords
    where day = date_to_compare;

    -- Retrieve counts into variables
    select count into count_adwords_to_compare from tmp_adwords_to_compare;
    select count into count_adwords_dwh from tmp_adwords_dwh;

    -- Compare the results
    if count_adwords_to_compare = count_adwords_dwh then
        raise notice 'Counts match: %', count_adwords_to_compare;
    else
        message := format('*Adwords verification*, counts do not match: date = %s, adwords_to_compare = %s, adwords_dwh = %s', date_to_compare, count_adwords_to_compare, count_adwords_dwh);
        raise warning '%', message;
    end if;

    -- Drop temporary tables
    drop table if exists tmp_adwords_to_compare;
    drop table if exists tmp_adwords_dwh;

end;
$$;
