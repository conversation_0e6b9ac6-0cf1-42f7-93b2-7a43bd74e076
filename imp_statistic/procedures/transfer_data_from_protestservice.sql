create or replace procedure imp_statistic.transfer_data_from_protestservice(_datediff integer)
    language plpgsql
as
$$

        declare _max_date_dwh timestamp := (select max(date_time_ended) from imp_statistic.test_iterations);

begin

        perform dblink_connect('myconnPRS', 'dbname=protest-service host=********** user= ''pentaho'' password= ''!faP@jhWYWb&3rwv'' options=-csearch_path=');

        insert into imp_statistic.test_iterations (uid, country_code, id, iteration_number, date_time_started, date_time_ended, platform, title, buckets, load_datetime)
        select uid, country_code, id, iteration_number, date_time_started, date_time_ended, platform, title, buckets, now()
        from dblink('myconnPRS',
             'select uid,
                     country_code,
                     id,
                     iteration_number,
                     date_time_started,
                     date_time_ended,
                     platform,
                     title,
                     buckets
              from public.test_iterations') as test_iterations (uid integer, country_code char(2), id integer, iteration_number smallint, date_time_started timestamp with time zone, date_time_ended timestamp with time zone, platform smallint, title varchar(1024), buckets bytea)
        where date_time_ended is null or date_time_ended > _max_date_dwh;


        truncate table imp_statistic.test_group;

        insert into imp_statistic.test_group(uid, number, test_iteration_uid, ad_sense_channel, weight)
        select uid, number, test_iteration_uid, ad_sense_channel, weight
        from dblink('myconnPRS',
             'select uid,
                     number,
                     test_iteration_uid,
                     ad_sense_channel,
                     weight
              from public.test_group') as test_group (uid integer, number smallint, test_iteration_uid integer, ad_sense_channel varchar(256), weight numeric(7, 3));

end;

$$;

alter procedure imp_statistic.transfer_data_from_protestservice(integer) owner to rlu;
