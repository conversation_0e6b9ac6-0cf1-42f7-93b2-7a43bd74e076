create view v_agreement_balance
            (account_id, employer_id, company_name, packet_name, agreement_balance, name, payment_method,
             competitros_jobs, active_jobs_jooble, last_contact)
as
SELECT cla.id               AS account_id,
       cla.employer_id,
       cla.name             AS company_name,
       ps.packet_name,
       ps.agreement_balance,
       u.name,
       ps.payment_method,
       COALESCE(jc.jobs, 0) AS competitros_jobs,
       j.jobs               AS active_jobs_jooble,
       act.last_contact
FROM imp_statistic.crm_lancer_account cla
         LEFT JOIN (SELECT ps_1.id_employer,
                           ps_1.id_agreement,
                           p.name                                       AS packet_name,
                           abh.value - COALESCE(abh1.value, 0::numeric) AS agreement_balance,
                           bi_1.payment_method
                    FROM imp_employer.packet_subscription ps_1
                             LEFT JOIN (SELECT bi_1_1.id_employer,
                                               max(ba.payment_method) AS payment_method
                                        FROM imp_employer.billing_info bi_1_1
                                                 JOIN imp_employer.billing_agreement ba
                                                      ON bi_1_1.id = ba.id_billing_info AND bi_1_1.sources = ba.sources
                                        GROUP BY bi_1_1.id_employer) bi_1 ON ps_1.id_employer = bi_1.id_employer
                             LEFT JOIN (SELECT agreement_balance_history.id_agreement,
                                               sum(agreement_balance_history.value) AS value
                                        FROM imp_employer.agreement_balance_history
                                        WHERE (agreement_balance_history.change_type = ANY (ARRAY [1, 0]))
                                          AND agreement_balance_history.value > 0::numeric
                                        GROUP BY agreement_balance_history.id_agreement) abh
                                       ON ps_1.id_agreement = abh.id_agreement
                             LEFT JOIN (SELECT agreement_balance_history.id_agreement,
                                               abs(sum(agreement_balance_history.value)) AS value
                                        FROM imp_employer.agreement_balance_history
                                        WHERE agreement_balance_history.change_type = ANY (ARRAY [2, 10])
                                        GROUP BY agreement_balance_history.id_agreement) abh1
                                       ON ps_1.id_agreement = abh1.id_agreement
                             JOIN imp_employer.packet p ON ps_1.id_packet = p.id AND ps_1.sources = p.sources
                    WHERE ps_1.status = 2
                    GROUP BY ps_1.id_employer, ps_1.id_agreement, p.name, bi_1.payment_method, abh.value, abh1.value) ps
                   ON cla.employer_id = ps.id_employer
         JOIN imp_statistic."user" u ON u.id = cla.user_id
         LEFT JOIN (SELECT trigger_competitors.account_id,
                           max(trigger_competitors.all_vac) AS jobs,
                           sum(trigger_competitors.cost)    AS cost
                    FROM imp_statistic.trigger_competitors
                    GROUP BY trigger_competitors.account_id) jc ON jc.account_id = cla.id
         LEFT JOIN (SELECT job.id_employer,
                           count(*) AS jobs
                    FROM imp_employer.job
                    WHERE job.sources = 1
                      AND (job.flags & 1) = 1
                    GROUP BY job.id_employer) j ON cla.employer_id = j.id_employer
         LEFT JOIN (SELECT date(max(activity.created_on)) AS last_contact,
                           activity.account_id
                    FROM imp_statistic.activity
                    WHERE activity.type_id = 3
                      AND activity.is_deleted = 0
                    GROUP BY activity.account_id) act ON act.account_id = cla.id
WHERE ps.agreement_balance > 0::numeric;

alter table v_agreement_balance
    owner to npo;

grant select on v_agreement_balance to readonly;

