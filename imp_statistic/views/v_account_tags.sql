create view v_accouting
            (id, employer_id, subscription_id, date_cancellation, points_available, last_order_date, next_order_date,
             packet_name, packet_points, packet_price, job_cnt, subscription_month_count, not_viewed_responses,
             payment_method, status, company, balance, competitors_jobs, last_contact, penultimate_date, name, manager,
             number, agreement_balance)
as
SELECT a.id,
       a.employer_id,
       s.id                                               AS subscription_id,
       date(e.date_cancellation)                          AS date_cancellation,
       s.points_available,
       date(s.last_order_date)                            AS last_order_date,
       date(s.next_order_date)                            AS next_order_date,
       p.name                                             AS packet_name,
       p.points                                           AS packet_points,
       p.price                                            AS packet_price,
       COALESCE(j.jobs, 0::bigint)                        AS job_cnt,
       pp.month_count                                     AS subscription_month_count,
       COALESCE(jj.count_not_viewed_responses, 0::bigint) AS not_viewed_responses,
       bi.payment_method,
       s.status,
       a.name                                             AS company,
       eb.balance,
       COALESCE(jc.jobs, 0)                               AS competitors_jobs,
       date(act.last_contact)                             AS last_contact,
       pss.date                                           AS penultimate_date,
       pss.name,
       u.name                                             AS manager,
       pss.number,
       abh.value - abh1.value                             AS agreement_balance
FROM imp_statistic.crm_lancer_account a
         LEFT JOIN imp_employer.packet_subscription s ON a.employer_id = s.id_employer
         LEFT JOIN (SELECT agreement_balance_history.id_agreement,
                           sum(agreement_balance_history.value) AS value
                    FROM imp_employer.agreement_balance_history
                    WHERE agreement_balance_history.change_type = 1
                    GROUP BY agreement_balance_history.id_agreement) abh ON s.id_agreement = abh.id_agreement
         LEFT JOIN (SELECT agreement_balance_history.id_agreement,
                           abs(sum(agreement_balance_history.value)) AS value
                    FROM imp_employer.agreement_balance_history
                    WHERE agreement_balance_history.change_type = 10
                    GROUP BY agreement_balance_history.id_agreement) abh1 ON s.id_agreement = abh1.id_agreement
         LEFT JOIN imp_employer.packet p ON s.id_packet = p.id AND p.sources = s.sources
         LEFT JOIN imp_employer.packet_period pp ON s.id_period = pp.id AND pp.sources = p.sources
         LEFT JOIN (SELECT job.id_employer,
                           count(*) AS jobs
                    FROM imp_employer.job
                    WHERE job.sources = 1
                      AND (job.flags & 1) = 1
                    GROUP BY job.id_employer) j ON a.employer_id = j.id_employer
         LEFT JOIN (SELECT bi_1.id_employer,
                           max(ba.payment_method) AS payment_method
                    FROM imp_employer.billing_info bi_1
                             JOIN imp_employer.billing_agreement ba
                                  ON bi_1.id = ba.id_billing_info AND bi_1.sources = ba.sources
                             LEFT JOIN imp_employer.packet_subscription ps
                                       ON bi_1.id_employer = ps.id_employer AND bi_1.sources = ps.sources
                             LEFT JOIN imp_employer.subscription_order so
                                       ON ps.id = so.id_subscription AND ps.sources = so.sources
                    WHERE ps.status = 2
                    GROUP BY bi_1.id_employer) bi ON a.employer_id = bi.id_employer
         LEFT JOIN (SELECT jj_1.id_employer,
                           count(DISTINCT ja.id) AS count_not_viewed_responses
                    FROM imp_employer.job jj_1
                             JOIN imp_employer.job_apply ja ON jj_1.sources = ja.sources AND jj_1.id = ja.id_job
                             JOIN imp_employer.account_service_account asa
                                  ON jj_1.sources = asa.sources AND jj_1.id_account = asa.id
                    WHERE jj_1.sources = 1
                      AND ja.date::date = (CURRENT_DATE - 1)
                      AND (ja.flags & 1024) = 0
                      AND ja.date_seen IS NULL
                    GROUP BY jj_1.id_employer) jj ON a.employer_id = jj.id_employer
         JOIN imp_statistic."user" u ON a.user_id = u.id
         LEFT JOIN (SELECT trigger_competitors.account_id,
                           max(trigger_competitors.all_vac) AS jobs,
                           sum(trigger_competitors.cost)    AS cost
                    FROM imp_statistic.trigger_competitors
                    GROUP BY trigger_competitors.account_id) jc ON jc.account_id = a.id
         LEFT JOIN (SELECT employer_balance.id_employer,
                           employer_balance.balance
                    FROM imp_employer.employer_balance
                    WHERE employer_balance.balance > 0::numeric
                    GROUP BY employer_balance.id_employer, employer_balance.balance) eb
                   ON s.id_employer = eb.id_employer
         LEFT JOIN (SELECT e_1.id,
                           max(sc.date) AS date_cancellation
                    FROM imp_employer.employer e_1
                             JOIN imp_employer.subscription_cancellation sc ON e_1.id_account = sc.id_account
                    GROUP BY e_1.id) e ON a.employer_id = e.id
         LEFT JOIN (SELECT max(activity.created_on) AS last_contact,
                           activity.account_id
                    FROM imp_statistic.activity
                    WHERE activity.type_id = 3
                      AND activity.is_deleted = 0
                    GROUP BY activity.account_id) act ON act.account_id = a.id
         LEFT JOIN (SELECT pss_1.id_employer,
                           date(so.date_paid)                                                            AS date,
                           row_number() OVER (PARTITION BY pss_1.id_employer ORDER BY so.date_paid DESC) AS number,
                           p_1.name
                    FROM imp_employer.packet_subscription pss_1
                             LEFT JOIN imp_employer.subscription_order so
                                       ON pss_1.id = so.id_subscription AND pss_1.sources = so.sources
                             JOIN imp_employer.packet p_1 ON pss_1.id_packet = p_1.id AND pss_1.sources = p_1.sources
                    WHERE so.status = 2
                    GROUP BY pss_1.id_employer, so.date_paid, p_1.name) pss ON a.employer_id = pss.id_employer
WHERE a.department_id = 1
  AND (u.type_id = ANY (ARRAY [3, 4]))
  AND u.is_deleted = 0
  AND (u.id <> ALL (ARRAY [48, 110]))
  AND u.country_code::text = 'ua'::text
  AND (s.status = ANY (ARRAY [1, 2]))
  AND ((pss.number = ANY (ARRAY [1::bigint, 2::bigint])) OR pss.number IS NULL)
  AND a.is_deleted = 0
  AND (a.moderation_status_id = ANY (ARRAY [1, 2]));

alter table v_accouting
    owner to npo;

grant select on v_accouting to readonly;

