SET NOCOUNT ON;

DECLARE @start_date int = :to_sqlcode_date_or_datediff_start,
        @end_date int = :to_sqlcode_date_or_datediff_end;

SELECT si.date  AS date_diff,
       jr.id_campaign,
       COUNT(*) AS impressions_count
FROM dbo.session_impression si WITH (NOLOCK)
         JOIN dbo.session s WITH (NOLOCK) ON s.id = si.id_session AND s.date_diff = si.date
         JOIN dbo.job_region jr WITH (NOLOCK) ON jr.uid = si.uid_job
WHERE (s.flags & 1) = 0
  AND si.date BETWEEN @start_date AND @end_date
GROUP BY si.date,
         jr.id_campaign
;
