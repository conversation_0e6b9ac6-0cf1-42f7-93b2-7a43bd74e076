create table imp_statistic.google_analytics_general (
        name varcha<PERSON>(255) not null,
        date date not null,
        channelGrouping varchar(255) not null,
        sourceMedium varchar(255) not null,
        deviceCategory varchar(255) not null,
        country varchar(255) not null,
        users int null,
        sessions int null,
        id_new int not null,

    constraint pk_google_analytics_general_id
        primary key (id_new)

);

alter table imp_statistic.google_analytics_general owner to postgres;

create index ind_google_analytics_general_d_s on imp_statistic.google_analytics_general (date, sessions);
