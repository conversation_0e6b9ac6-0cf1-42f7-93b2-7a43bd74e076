select distinct 1 as country_id,
        a.profile_id ,
        a.[name] as [user_name],
        c.out_number,
        c.[datetime] as call_time ,
        t.[name] as call_status_name,
        a.source
from JS_CRM.dbo.calls as c with(nolock)
left join JS_CRM.dbo.[user] as u with(nolock) on CAST (u.inner_number as nvarchar(max)) = c.in_number
left join JS_CRM.dbo.activity act with(nolock) on act.id = c.activity_id
left join JS_CRM.dbo.account a with(nolock) on a.id = act.account_id
left join JS_CRM.dbo.account_tags ata with(nolock) on ata.account_id = a.id
left join JS_CRM.dbo.tag t with(nolock) on ata.tag_id = t.id
where u.department_id = 3
AND a.source IN (7,8) AND ata.is_deleted=0
