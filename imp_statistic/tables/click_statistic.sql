declare @dt date = dbo.ex_getdate() - 1


select 1 as country_id,
       country,
       cast([date] as datetime) as [date],
       id_project,
       site,
       contractor,
       id_campaign,
       campaign,
       click_count,
       click_price,
       currency,
       rel_bonus,
       job_count,
       session_count,
       ip_count,
       to_jdp_count,
       from_jdp_count,
       total_value,
       id_user,
       test_count,
       organic_count,
       paid_overflow_count,
       flags
from auction.click_statistic cs with (nolock)
where [date] = @dt
