create or replace view aggregation.v_subscription_model_funnel
            (stage, id, country_code, company_name, step, has_been_in_moderation_queue, additional_confirmation,
             successfull_moderation, email_is_confirmed, has_job, conf_type, billing_info, card_info, bought_sub,
             failed_sub, used_trial, used_start, register_device, moderation_status, registration_date,traffic_source_id,
             traffic_source, employer_id, date_paid, channel_current_name, has_account, total_users, header_users, banner_users)
as
SELECT 'pre-registration'::text AS stage,
       NULL::bigint             AS id,
       'rs'::text               AS country_code,
       NULL::text               AS company_name,
       NULL::bigint             AS step,
       NULL::bigint             AS has_been_in_moderation_queue,
       NULL::bigint             AS additional_confirmation,
       NULL::bigint             AS successfull_moderation,
       NULL::double precision   AS email_is_confirmed,
       NULL::bigint             AS has_job,
       NULL::bigint             AS conf_type,
       NULL::bigint             AS billing_info,
       NULL::bigint             AS card_info,
       NULL::bigint             AS bought_sub,
       NULL::bigint             AS failed_sub,
       NULL::bigint             AS used_trial,
       NULL::bigint             AS used_start,
       r.is_mobile              AS register_device,
       NULL::bigint             AS moderation_status,
       ic.dt                    AS registration_date,
       NULL::integer            AS traffic_source_id,
       NULL::varchar            AS traffic_source,
       NULL::integer            AS employer_id,
       NULL::date               AS date_paid,
       r.channel_current_name,
       r.has_account,
       sum(r.total_users)       AS total_users,
       sum(r.header_users)      AS header_users,
       sum(r.banner_users)      AS banner_users
FROM vnov.subscription_model_funnel_pre_registration r
         LEFT JOIN dimension.info_calendar ic ON r.date_diff = ic.date_diff
GROUP BY r.is_mobile, ic.dt, r.channel_current_name, r.has_account
UNION ALL
SELECT 'registration'::text AS stage,
       j.id,
       j.country_code,
       j.company_name,
       j.step,
       j.has_been_in_moderation_queue,
       j.additional_confirmation,
       j.successfull_moderation,
       j.email_is_confirmed,
       j.has_job,
       j.conf_type,
       j.billing_info,
       j.card_info,
       j.bought_sub,
       j.failed_sub,
       j.used_trial,
       j.used_start,
       j.register_device,
       j.moderation_status,
       j.registration_date,
       j.traffic_source_id,
       j.traffic_source,
       p.employer_id,
       p.date_paid,
       NULL::bpchar         AS channel_current_name,
       NULL::integer        AS has_account,
       NULL::bigint         AS total_users,
       NULL::bigint         AS header_users,
       NULL::bigint         AS banner_users
FROM aggregation.subscription_model_funnel_full j
         LEFT JOIN (SELECT sm.employer_id,
                           min(sm.date_paid) AS date_paid
                    FROM aggregation.subscription_model sm
                    GROUP BY sm.employer_id) p ON j.id = p.employer_id

;

alter table aggregation.v_subscription_model_funnel
    owner to vnov;

grant select on aggregation.v_subscription_model_funnel to readonly;


