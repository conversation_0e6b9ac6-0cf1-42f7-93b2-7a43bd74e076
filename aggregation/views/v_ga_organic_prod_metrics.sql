create or replace view aggregation.v_ga_organic_prod_metrics
            (country, channel, traf_name, date, away_type, click_is_paid, project_name, revenue_usd, jdp_away_count,
             away_conv_cnt, conversions, ga_session_cnt, internal_session_cnt, total_search_cnt, search_w_click_cnt,
             search_w_away_cnt, retention_email_revenue, new_account_cnt)
as
WITH funnel_raw AS (
    SELECT funnel_agg.action_date,
           CASE
               WHEN funnel_agg.project_name::text = ANY
                    (ARRAY ['indeediq.com'::character varying::text, 'stepstone.de'::character varying::text, 'stellenanzeigen.de'::character varying::text, 'monster.de'::character varying::text])
                   THEN funnel_agg.project_name
               ELSE 'other'::character varying
               END                      AS project_name,
           u_traffic_source.channel,
           CASE
               WHEN u_traffic_source.name::text = 'Google_for_Jobs'::text THEN 'Google_for_Jobs'::text
               ELSE 'other'::text
               END                      AS traf_name,
           sum(funnel_agg.session_cnt)  AS session_cnt,
           sum(funnel_agg.searches_cnt) AS searches_cnt,
           sum(funnel_agg.event_cnt)    AS event_cnt,
           CASE
               WHEN strpos(replace(funnel_agg.step::text, ' '::text, ''::text), 'Lettertype1'::text) = 1 THEN replace(
                       replace(replace(funnel_agg.step::text, ' '::text, ''::text), 'Lettertype1->'::text, ''::text),
                       'Lettertype'::text, 'Letter type '::text)
               ELSE replace(replace(funnel_agg.step::text, ' '::text, ''::text), 'Lettertype'::text,
                            'Letter type '::text)
               END                      AS step,
           countries.name_country_eng   AS country
    FROM aggregation.funnel_agg
             JOIN dimension.countries ON funnel_agg.country_id = countries.id
             LEFT JOIN dimension.u_traffic_source ON funnel_agg.country_id = u_traffic_source.country AND
                                                     funnel_agg.traffic_source_name::text = u_traffic_source.name::text
    WHERE funnel_agg.action_date::date >= '2024-07-01'::date
      AND (countries.id = ANY (ARRAY [6, 3, 2, 4, 5, 20, 15, 14, 9, 39, 19, 16, 35, 12, 17, 13]))
      AND (u_traffic_source.channel::text = ANY
           (ARRAY ['Organic Search'::character varying::text, 'Paid Search'::character varying::text]))
    GROUP BY funnel_agg.action_date,
             (
                 CASE
                     WHEN funnel_agg.project_name::text = ANY
                          (ARRAY ['indeediq.com'::character varying::text, 'stepstone.de'::character varying::text, 'stellenanzeigen.de'::character varying::text, 'monster.de'::character varying::text])
                         THEN funnel_agg.project_name
                     ELSE 'other'::character varying
                     END), u_traffic_source.channel,
             (
                 CASE
                     WHEN u_traffic_source.name::text = 'Google_for_Jobs'::text THEN 'Google_for_Jobs'::text
                     ELSE 'other'::text
                     END),
             (
                 CASE
                     WHEN strpos(replace(funnel_agg.step::text, ' '::text, ''::text), 'Lettertype1'::text) = 1
                         THEN replace(
                             replace(replace(funnel_agg.step::text, ' '::text, ''::text), 'Lettertype1->'::text,
                                     ''::text), 'Lettertype'::text, 'Letter type '::text)
                     ELSE replace(replace(funnel_agg.step::text, ' '::text, ''::text), 'Lettertype'::text,
                                  'Letter type '::text)
                     END), countries.name_country_eng
),
     funnel_final AS (
         SELECT funnel.country,
                funnel.channel,
                funnel.traf_name,
                funnel.action_date::date AS date,
                NULL::character varying  AS away_type,
                NULL::character varying  AS click_is_paid,
                CASE
                    WHEN funnel.project_name::text = ANY
                         (ARRAY ['indeediq.com'::character varying::text, 'stepstone.de'::character varying::text, 'stellenanzeigen.de'::character varying::text, 'monster.de'::character varying::text])
                        THEN funnel.project_name
                    ELSE 'other'::character varying
                    END                  AS project_name,
                0                        AS revenue_usd,
                0                        AS jdp_away_count,
                0                        AS away_conv_cnt,
                0                        AS conversions,
                0                        AS ga_session_cnt,
                0                        AS internal_session_cnt,
                sum(
                        CASE
                            WHEN funnel.step = 'Serp->'::text THEN funnel.searches_cnt
                            ELSE NULL::bigint
                            END)         AS total_search_cnt,
                sum(
                        CASE
                            WHEN funnel.step = 'Serp->JDP/Away'::text THEN funnel.searches_cnt
                            ELSE NULL::bigint
                            END)         AS search_w_click_cnt,
                sum(
                        CASE
                            WHEN funnel.step = 'SerpAwayTotal'::text OR funnel.step = 'Serp->JDP->Apply'::text
                                THEN funnel.searches_cnt
                            ELSE NULL::bigint
                            END)         AS search_w_away_cnt,
                0                        AS retention_email_revenue,
                0                        AS new_account_cnt
         FROM funnel_raw funnel
         GROUP BY funnel.country, funnel.channel, funnel.traf_name, (funnel.action_date::date),
                  (
                      CASE
                          WHEN funnel.project_name::text = ANY
                               (ARRAY ['indeediq.com'::character varying::text, 'stepstone.de'::character varying::text, 'stellenanzeigen.de'::character varying::text, 'monster.de'::character varying::text])
                              THEN funnel.project_name
                          ELSE 'other'::character varying
                          END)
     ),
     jdp_away_clicks_agg AS (
         SELECT countries.name_country_eng AS country,
                u_traffic_source_current.channel,
                CASE
                    WHEN u_traffic_source_current.name::text = 'Google_for_Jobs'::text THEN 'Google_for_Jobs'::text
                    ELSE 'other'::text
                    END                    AS traf_name,
                ic.dt                      AS date,
                click.away_type,
                click.is_paid              AS click_is_paid,
                CASE
                    WHEN click.project_name::text = ANY
                         (ARRAY ['indeediq.com'::character varying::text, 'stepstone.de'::character varying::text, 'stellenanzeigen.de'::character varying::text, 'monster.de'::character varying::text])
                        THEN click.project_name
                    ELSE 'other'::character varying
                    END                    AS project_name,
                sum(click.revenue_usd)     AS revenue_usd,
                sum(click.jdp_away_count)  AS jdp_away_count,
                0                          AS away_conv_cnt,
                0                          AS conversions,
                0                          AS ga_session_cnt,
                0                          AS internal_session_cnt,
                0                          AS total_search_cnt,
                0                          AS search_w_click_cnt,
                0                          AS search_w_away_cnt,
                0                          AS retention_email_revenue,
                0                          AS new_account_cnt
         FROM aggregation.jdp_away_clicks_agg click
                  LEFT JOIN dimension.info_calendar ic ON ic.date_diff = click.action_datediff
                  JOIN dimension.countries ON click.country_id = countries.id
                  LEFT JOIN dimension.u_traffic_source u_traffic_source_current
                            ON click.country_id = u_traffic_source_current.country AND
                               click.id_current_traf_source = u_traffic_source_current.id
         WHERE ic.dt between '2024-07-01'::date AND '2024-09-30'::date
           AND (countries.id = ANY
                (ARRAY [6, 3, 2, 4, 5, 20, 15, 14, 9, 39, 19, 16, 35, 12, 17, 13, 18, 23, 63, 64, 66]))
           AND (u_traffic_source_current.channel::text = ANY
                (ARRAY ['Organic Search'::character varying::text, 'Paid Search'::character varying::text]))
         GROUP BY countries.name_country_eng, u_traffic_source_current.channel,
                  (
                      CASE
                          WHEN u_traffic_source_current.name::text = 'Google_for_Jobs'::text THEN 'Google_for_Jobs'::text
                          ELSE 'other'::text
                          END), ic.dt, click.away_type, click.is_paid,
                  (
                      CASE
                          WHEN click.project_name::text = ANY
                               (ARRAY ['indeediq.com'::character varying::text, 'stepstone.de'::character varying::text, 'stellenanzeigen.de'::character varying::text, 'monster.de'::character varying::text])
                              THEN click.project_name
                          ELSE 'other'::character varying
                          END)
     ),
new_revenue_conv_data as (
         SELECT countries.name_country_eng AS country,
                u_traffic_source_current.channel,
                CASE
                    WHEN u_traffic_source_current.name::text = 'Google_for_Jobs'::text THEN 'Google_for_Jobs'::text
                    ELSE 'other'::text
                    END                    AS traf_name,
                ic.dt                      AS date,
                CASE
                    WHEN click.job_destination = 4::double precision THEN 'Away from Jdp'::text
                    WHEN click.job_destination = 1::double precision THEN 'Other Aways'::text
                    WHEN click.job_destination = ANY (ARRAY [2::double precision, 3::double precision]) THEN 'JDP'::text
                    ELSE NULL::text
                    END::character varying              AS away_type,
                click.is_paid                           AS click_is_paid,
                CASE
                    WHEN info_project.name::text = ANY
                         (ARRAY ['indeediq.com'::character varying::text, 'stepstone.de'::character varying::text, 'stellenanzeigen.de'::character varying::text, 'monster.de'::character varying::text])
                        THEN info_project.name
                    ELSE 'other'::character varying
                    END                                 AS project_name,
                sum(click.revenue_usd)::numeric         AS revenue_usd,
                sum(click.jdp_away_count)::bigint       AS jdp_away_count,
                sum(
               CASE
                   WHEN pr_with_conversions.id_project IS NOT NULL AND (campaign.flags & 4) <> 4 AND
                        campaign.name IS NOT NULL AND
                        (click.job_destination = ANY (ARRAY [1::double precision, 4::double precision]))
                       THEN click.jdp_away_count
                   ELSE NULL
                   END)::bigint            AS away_conv_cnt,
                sum(
               CASE
                   WHEN pr_with_conversions.id_project IS NOT NULL AND (campaign.flags & 4) <> 4 AND
                        campaign.name IS NOT NULL AND
                        (click.job_destination = ANY (ARRAY [1::double precision, 4::double precision]))
                       THEN click.unic_client_conversion_count
                   ELSE NULL
                   END)::bigint            AS conversions,
                0                          AS ga_session_cnt,
                0                          AS internal_session_cnt,
                0                          AS total_search_cnt,
                0                          AS search_w_click_cnt,
                0                          AS search_w_away_cnt,
                0                          AS retention_email_revenue,
                0                          AS new_account_cnt
         FROM aggregation.click_data_agg click
                  LEFT JOIN dimension.info_calendar ic ON ic.date_diff = click.action_datediff
                  JOIN dimension.countries ON click.country_id = countries.id
                  LEFT JOIN dimension.info_project ON info_project.country = click.country_id AND info_project.id = click.id_project
                  LEFT JOIN dimension.u_traffic_source u_traffic_source_current
                            ON click.country_id = u_traffic_source_current.country AND
                               click.id_current_traf_source = u_traffic_source_current.id
                  LEFT JOIN imp.campaign ON campaign.country = click.country_id AND campaign.id::double precision = click.id_campaign
                  LEFT JOIN dimension.dic_conversion_projects pr_with_conversions
                            ON click.country_id = pr_with_conversions.id_country AND click.id_project = pr_with_conversions.id_project
         WHERE ic.dt >= '2024-10-01'::date
           AND (click.country_id = ANY
                (ARRAY [6, 3, 2, 4, 5, 20, 15, 14, 9, 39, 19, 16, 35, 12, 17, 13, 18, 23, 63, 64, 66]))
           AND (u_traffic_source_current.channel::text = ANY
                (ARRAY ['Organic Search'::character varying::text, 'Paid Search'::character varying::text]))
         GROUP BY countries.name_country_eng, u_traffic_source_current.channel,
                  (
                      CASE
                          WHEN u_traffic_source_current.name::text = 'Google_for_Jobs'::text THEN 'Google_for_Jobs'::text
                          ELSE 'other'::text
                          END), ic.dt,
                  CASE
                    WHEN click.job_destination = 4::double precision THEN 'Away from Jdp'::text
                    WHEN click.job_destination = 1::double precision THEN 'Other Aways'::text
                    WHEN click.job_destination = ANY (ARRAY [2::double precision, 3::double precision]) THEN 'JDP'::text
                    ELSE NULL::text
                    END::character varying,
                  click.is_paid,
                  (
                      CASE
                          WHEN info_project.name::text = ANY
                               (ARRAY ['indeediq.com'::character varying::text, 'stepstone.de'::character varying::text, 'stellenanzeigen.de'::character varying::text, 'monster.de'::character varying::text])
                              THEN info_project.name
                          ELSE 'other'::character varying
                          END)
     ),
     project_conversions_daily AS (
         SELECT countries.name_country_eng AS country,
                u_traffic_source.channel,
                CASE
                    WHEN u_traffic_source.name::text = 'Google_for_Jobs'::text THEN 'Google_for_Jobs'::text
                    ELSE 'other'::text
                    END                    AS traf_name,
                conv.session_date          AS date,
                conv.metric                AS away_type,
                NULL::character varying    AS click_is_paid,
                CASE
                    WHEN conv.project_name::text = ANY
                         (ARRAY ['indeediq.com'::character varying::text, 'stepstone.de'::character varying::text, 'stellenanzeigen.de'::character varying::text, 'monster.de'::character varying::text])
                        THEN conv.project_name
                    ELSE 'other'::character varying
                    END                    AS project_name,
                0                          AS revenue_usd,
                0                          AS jdp_away_count,
                sum(conv.aways)            AS away_conv_cnt,
                sum(conv.conversions)      AS conversions,
                0                          AS ga_session_cnt,
                0                          AS internal_session_cnt,
                0                          AS total_search_cnt,
                0                          AS search_w_click_cnt,
                0                          AS search_w_away_cnt,
                0                          AS retention_email_revenue,
                0                          AS new_account_cnt
         FROM aggregation.project_conversions_daily conv
                  JOIN dimension.countries ON conv.country_id = countries.id
                  LEFT JOIN dimension.u_traffic_source ON conv.country_id = u_traffic_source.country AND
                                                          conv.id_current_traf_source = u_traffic_source.id
         WHERE conv.session_date >= '2024-07-01'::date
           AND (countries.id = ANY
                (ARRAY [6, 3, 2, 4, 5, 20, 15, 14, 9, 39, 19, 16, 35, 12, 17, 13, 18, 23, 63, 64, 66]))
           AND (u_traffic_source.channel::text = ANY
                (ARRAY ['Organic Search'::character varying::text, 'Paid Search'::character varying::text]))
           AND conv.campaign_id IS NOT NULL
         GROUP BY countries.name_country_eng, u_traffic_source.channel, conv.session_date, conv.metric,
                  (
                      CASE
                          WHEN u_traffic_source.name::text = 'Google_for_Jobs'::text THEN 'Google_for_Jobs'::text
                          ELSE 'other'::text
                          END),
                  (
                      CASE
                          WHEN conv.project_name::text = ANY
                               (ARRAY ['indeediq.com'::character varying::text, 'stepstone.de'::character varying::text, 'stellenanzeigen.de'::character varying::text, 'monster.de'::character varying::text])
                              THEN conv.project_name
                          ELSE 'other'::character varying
                          END)
     ),
     ga_session_data AS (
         SELECT countries.name_country_eng AS country,
                ga.channelgrouping         AS channel,
                CASE
                    WHEN ga.sourcemedium::text = 'google_jobs_apply / organic'::text THEN 'Google_for_Jobs'::text
                    ELSE 'other'::text
                    END                    AS traf_name,
                ga.date,
                NULL::character varying    AS away_type,
                NULL::character varying    AS click_is_paid,
                NULL::character varying    AS project_name,
                0                          AS revenue_usd,
                0                          AS jdp_away_count,
                0                          AS away_conv_cnt,
                0                          AS conversions,
                sum(ga.session_cnt)        AS ga_session_cnt,
                0                          AS internal_session_cnt,
                0                          AS total_search_cnt,
                0                          AS search_w_click_cnt,
                0                          AS search_w_away_cnt,
                0                          AS retention_email_revenue,
                0                          AS new_account_cnt
         FROM aggregation.v_ga_session_data ga
                  JOIN dimension.countries ON ga.country_code = lower(countries.alpha_2::text)
         WHERE ga.date >= '2024-07-01'::date
           AND (countries.id = ANY
                (ARRAY [6, 3, 2, 4, 5, 20, 15, 14, 9, 39, 19, 16, 35, 12, 17, 13, 18, 23, 63, 64, 66]))
           AND (ga.channelgrouping::text = ANY
                (ARRAY ['Organic Search'::character varying::text, 'Paid Search'::character varying::text]))
         GROUP BY countries.name_country_eng, ga.channelgrouping,
                  (
                      CASE
                          WHEN ga.sourcemedium::text = 'google_jobs_apply / organic'::text THEN 'Google_for_Jobs'::text
                          ELSE 'other'::text
                          END), ga.date
     ),
     session_daily_agg AS (
         SELECT v_country_info.country_name                                 AS country,
                u_traffic_source_current.channel::text                      AS channel,
                CASE
                    WHEN u_traffic_source_current.name::text = 'Google_for_Jobs'::text THEN 'Google_for_Jobs'::text
                    ELSE 'other'::text
                    END                                                     AS traf_name,
                ic.dt                                                       AS date,
                NULL::character varying                                     AS away_type,
                NULL::character varying                                     AS click_is_paid,
                NULL::character varying                                     AS project_name,
                0                                                           AS revenue_usd,
                0                                                           AS jdp_away_count,
                0                                                           AS away_conv_cnt,
                0                                                           AS conversions,
                0                                                           AS ga_session_cnt,
                sum(si.total_session_cnt - COALESCE(si.bot_session_cnt, 0)) AS internal_session_cnt,
                0                                                           AS total_search_cnt,
                0                                                           AS search_w_click_cnt,
                0                                                           AS search_w_away_cnt,
                0                                                           AS retention_email_revenue,
                0                                                           AS new_account_cnt
         FROM aggregation.session_daily_agg si
                  LEFT JOIN dimension.info_calendar ic ON si.action_datediff = ic.date_diff
                  JOIN dimension.v_country_info ON si.country_id = v_country_info.country_id
                  LEFT JOIN dimension.u_traffic_source u_traffic_source_current
                            ON si.country_id = u_traffic_source_current.country AND
                               si.id_current_traf_source = u_traffic_source_current.id
         WHERE ic.dt >= '2024-07-01'::date
           AND ic.dt <= CURRENT_DATE
           AND (v_country_info.country_id = ANY
                (ARRAY [6, 3, 2, 4, 5, 20, 15, 14, 9, 39, 19, 16, 35, 12, 17, 13, 18, 23, 63, 64, 66]))
           AND (u_traffic_source_current.channel::text = ANY (ARRAY ['Organic Search'::text, 'Paid Search'::text]))
         GROUP BY v_country_info.country_name, (u_traffic_source_current.channel::text), ic.dt,
                  (
                      CASE
                          WHEN u_traffic_source_current.name::text = 'Google_for_Jobs'::text THEN 'Google_for_Jobs'::text
                          ELSE 'other'::text
                          END)
     ),
     account_revenue AS (
         SELECT countries.name_country_eng           AS country,
                u_traffic_source.channel,
                CASE
                    WHEN u_traffic_source.name::text = 'Google_for_Jobs'::text THEN 'Google_for_Jobs'::text
                    ELSE 'other'::text
                    END                              AS traf_name,
                account_revenue.account_date         AS date,
                NULL::character varying              AS away_type,
                NULL::character varying              AS click_is_paid,
                NULL::character varying              AS project_name,
                0                                    AS revenue_usd,
                0                                    AS jdp_away_count,
                0                                    AS away_conv_cnt,
                0                                    AS conversions,
                0                                    AS ga_session_cnt,
                0                                    AS internal_session_cnt,
                0                                    AS total_search_cnt,
                0                                    AS search_w_click_cnt,
                0                                    AS search_w_away_cnt,
                sum(
                        CASE
                            WHEN account_revenue.revenue_date::text >= '2022-03-01'::text
                                THEN account_revenue.email_revenue
                            ELSE NULL::numeric
                            END)                     AS retention_email_revenue,
                sum(account_revenue.new_account_cnt) AS new_account_cnt
         FROM aggregation.account_revenue
                  LEFT JOIN dimension.countries ON account_revenue.country_id = countries.id
                  LEFT JOIN dimension.auth_source ON account_revenue.source = auth_source.id
                  LEFT JOIN dimension.u_traffic_source ON account_revenue.country_id = u_traffic_source.country AND
                                                          u_traffic_source.id = account_revenue.id_traf_src
         WHERE account_revenue.account_date >= '2024-07-01'::date
           AND (account_revenue.revenue_date IS NULL OR account_revenue.revenue_date >= account_revenue.account_date)
           AND (u_traffic_source.channel::text = ANY (ARRAY ['Organic Search'::text, 'Paid Search'::text]))
           AND (countries.id = ANY
                (ARRAY [6, 3, 2, 4, 5, 20, 15, 14, 9, 39, 19, 16, 35, 12, 17, 13, 18, 23, 63, 64, 66]))
         GROUP BY countries.name_country_eng, u_traffic_source.channel,
                  (
                      CASE
                          WHEN u_traffic_source.name::text = 'Google_for_Jobs'::text THEN 'Google_for_Jobs'::text
                          ELSE 'other'::text
                          END), account_revenue.account_date
     )
SELECT f.country,
       f.channel,
       f.traf_name,
       f.date,
       f.away_type,
       f.click_is_paid,
       f.project_name,
       f.revenue_usd,
       f.jdp_away_count,
       f.away_conv_cnt,
       f.conversions,
       f.ga_session_cnt,
       f.internal_session_cnt,
       f.total_search_cnt,
       f.search_w_click_cnt,
       f.search_w_away_cnt,
       f.retention_email_revenue,
       f.new_account_cnt
FROM funnel_final f
UNION ALL
SELECT f.country,
       f.channel,
       f.traf_name,
       f.date,
       f.away_type,
       f.click_is_paid,
       f.project_name,
       f.revenue_usd,
       f.jdp_away_count,
       f.away_conv_cnt,
       f.conversions,
       f.ga_session_cnt,
       f.internal_session_cnt,
       f.total_search_cnt,
       f.search_w_click_cnt,
       f.search_w_away_cnt,
       f.retention_email_revenue,
       f.new_account_cnt
FROM jdp_away_clicks_agg f
UNION ALL
SELECT f.country,
       f.channel,
       f.traf_name,
       f.date,
       f.away_type,
       f.click_is_paid,
       f.project_name,
       f.revenue_usd,
       f.jdp_away_count,
       f.away_conv_cnt,
       f.conversions,
       f.ga_session_cnt,
       f.internal_session_cnt,
       f.total_search_cnt,
       f.search_w_click_cnt,
       f.search_w_away_cnt,
       f.retention_email_revenue,
       f.new_account_cnt
FROM project_conversions_daily f
UNION ALL
SELECT f.country,
       f.channel,
       f.traf_name,
       f.date,
       f.away_type,
       f.click_is_paid,
       f.project_name,
       f.revenue_usd,
       f.jdp_away_count,
       f.away_conv_cnt,
       f.conversions,
       f.ga_session_cnt,
       f.internal_session_cnt,
       f.total_search_cnt,
       f.search_w_click_cnt,
       f.search_w_away_cnt,
       f.retention_email_revenue,
       f.new_account_cnt
FROM new_revenue_conv_data f
UNION ALL
SELECT f.country,
       f.channel,
       f.traf_name,
       f.date,
       f.away_type,
       f.click_is_paid,
       f.project_name,
       f.revenue_usd,
       f.jdp_away_count,
       f.away_conv_cnt,
       f.conversions,
       f.ga_session_cnt,
       f.internal_session_cnt,
       f.total_search_cnt,
       f.search_w_click_cnt,
       f.search_w_away_cnt,
       f.retention_email_revenue,
       f.new_account_cnt
FROM ga_session_data f
UNION ALL
SELECT f.country,
       f.channel,
       f.traf_name,
       f.date,
       f.away_type,
       f.click_is_paid,
       f.project_name,
       f.revenue_usd,
       f.jdp_away_count,
       f.away_conv_cnt,
       f.conversions,
       f.ga_session_cnt,
       f.internal_session_cnt,
       f.total_search_cnt,
       f.search_w_click_cnt,
       f.search_w_away_cnt,
       f.retention_email_revenue,
       f.new_account_cnt
FROM session_daily_agg f
UNION ALL
SELECT f.country,
       f.channel,
       f.traf_name,
       f.date,
       f.away_type,
       f.click_is_paid,
       f.project_name,
       f.revenue_usd,
       f.jdp_away_count,
       f.away_conv_cnt,
       f.conversions,
       f.ga_session_cnt,
       f.internal_session_cnt,
       f.total_search_cnt,
       f.search_w_click_cnt,
       f.search_w_away_cnt,
       f.retention_email_revenue,
       f.new_account_cnt
FROM account_revenue f
UNION ALL
SELECT f.country,
       f.channel,
       f.traf_name,
       f.date,
       f.away_type,
       f.click_is_paid,
       f.project_name,
       f.revenue_usd,
       f.jdp_away_count,
       f.away_conv_cnt,
       f.conversions,
       f.ga_session_cnt,
       f.internal_session_cnt,
       f.total_search_cnt,
       f.search_w_click_cnt,
       f.search_w_away_cnt,
       f.retention_email_revenue,
       f.new_account_cnt
FROM archive.m_ga_organic_prod_metrics f;

alter table aggregation.v_ga_organic_prod_metrics
    owner to vnov;

grant select on aggregation.v_ga_organic_prod_metrics to readonly;

grant select on aggregation.v_ga_organic_prod_metrics to math;

grant select on aggregation.v_ga_organic_prod_metrics to write_ono;

grant select on aggregation.v_ga_organic_prod_metrics to readonly_aggregation;

grant select on aggregation.v_ga_organic_prod_metrics to dredd_etl;

grant select on aggregation.v_ga_organic_prod_metrics to kristianpetrych;

grant select on aggregation.v_ga_organic_prod_metrics to cabacus;
