create or replace view aggregation.v_jdp_away_clicks_agg
            (country, date, traffic_source_name, placement, away_type, project_id, project_name, campaign_name, ip_cc,
             is_paid, is_mobile, revenue_usd, jdp_away_count, sale_manager, channel, current_channel, is_local,
             current_traffic_source, current_traffic_source_is_paid, parent_category_name, partner_id, country_alpha_2)
as
SELECT c.name_country_eng                                         AS country,
       date(fn_get_timestamp_from_date_diff(jac.action_datediff)) AS date,
       NULL::text                                                 AS traffic_source_name,
       jac.placement,
       jac.away_type,
       jac.project_id,
       jac.project_name,
       jac.campaign_name,
       NULL::text                                                 AS ip_cc,
       jac.is_paid,
       jac.is_mobile,
       sum(jac.revenue_usd)                                       AS revenue_usd,
       sum(jac.jdp_away_count)                                    AS jdp_away_count,
       v_sale_manager.sale_manager,
       NULL::text                                                 AS channel,
       u_traffic_source_current.channel                           AS current_channel,
       CASE
           WHEN jac.ip_cc::text = lower(c.alpha_2::text) OR
                jac.ip_cc::text = 'gb'::text AND lower(c.alpha_2::text) = 'uk'::text THEN 1
           ELSE 0
           END                                                    AS is_local,
       u_traffic_source_current.name                              AS current_traffic_source,
       u_traffic_source_current.is_paid                           AS current_traffic_source_is_paid,
       v_job_kaiju_category.parent_category_name,
       aps.id_local_partner                                       AS partner_id,
       c.alpha_2                                                  AS country_alpha_2
FROM aggregation.jdp_away_clicks_agg jac
         LEFT JOIN dimension.countries c ON c.id = jac.country_id
         LEFT JOIN aggregation.v_sale_manager
                   ON jac.country_id = v_sale_manager.country AND jac.project_id::text = v_sale_manager.id_project::text
         LEFT JOIN (SELECT u_traffic_source.country,
                           u_traffic_source.name,
                           u_traffic_source.channel,
                           u_traffic_source.num
                    FROM (SELECT u_traffic_source_1.country,
                                 u_traffic_source_1.name,
                                 u_traffic_source_1.channel,
                                 row_number()
                                 OVER (PARTITION BY u_traffic_source_1.name, u_traffic_source_1.country ORDER BY u_traffic_source_1.id DESC) AS num
                          FROM dimension.u_traffic_source u_traffic_source_1) u_traffic_source
                    WHERE u_traffic_source.num = 1) traffic_source
                   ON jac.country_id = traffic_source.country AND jac.traffic_source_name::text = traffic_source.name::text
         LEFT JOIN dimension.u_traffic_source u_traffic_source_current
                   ON jac.country_id = u_traffic_source_current.country AND
                      jac.id_current_traf_source = u_traffic_source_current.id
         LEFT JOIN aggregation.v_job_kaiju_category ON jac.job_category_id = v_job_kaiju_category.child_category_id
         LEFT JOIN affiliate.partner_settings aps
                   ON lower(c.alpha_2::text) = aps.country::text AND u_traffic_source_current.name::text = aps.partner::text
WHERE jac.action_datediff >= (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) - 90)
GROUP BY c.name_country_eng, (date(fn_get_timestamp_from_date_diff(jac.action_datediff))), jac.placement, jac.away_type, jac.project_id, jac.project_name, jac.campaign_name, jac.is_paid, jac.is_mobile, v_sale_manager.sale_manager,
         u_traffic_source_current.channel,
         (
             CASE
                 WHEN jac.ip_cc::text = lower(c.alpha_2::text) OR
                      jac.ip_cc::text = 'gb'::text AND lower(c.alpha_2::text) = 'uk'::text THEN 1
                 ELSE 0
                 END), u_traffic_source_current.name, u_traffic_source_current.is_paid,
         v_job_kaiju_category.parent_category_name, aps.id_local_partner, c.alpha_2;

alter table aggregation.v_jdp_away_clicks_agg
    owner to ono;

grant select on aggregation.v_jdp_away_clicks_agg to readonly;

grant delete, insert, select, update on aggregation.v_jdp_away_clicks_agg to write_ono;

grant select on aggregation.v_jdp_away_clicks_agg to tma;

grant select on aggregation.v_jdp_away_clicks_agg to ypr;

grant select on aggregation.v_jdp_away_clicks_agg to vnov;

grant select on aggregation.v_jdp_away_clicks_agg to readonly_aggregation;

grant select on aggregation.v_jdp_away_clicks_agg to "pavlo.kvasnii";