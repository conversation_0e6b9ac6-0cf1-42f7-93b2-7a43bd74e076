create or replace view aggregation.v_ga_ahrefs_seo
            (date, country, name_country_eng, domain, traffic, refdomains, backlinks, cost, keywords, keywords_top3) as
WITH union_1 AS (
    SELECT date(st.date)      AS date,
           d.country,
           st.dm              AS domain,
           st.organic_traffic AS traffic,
           st.organic_kw      AS traffic_keyword,
           st.rd_total        AS refdomains,
           st.backlinks_total AS backlinks
    FROM traffic.ahrefs_statistic st
             LEFT JOIN traffic.ahrefs_domains d ON d.domain::text = st.dm::text
    WHERE date(st.date) = '2019-01-31'::date
      AND d.country IS NOT NULL
    UNION
    SELECT '2019-02-28'::date AS date,
           d.country,
           st.dm              AS domain,
           st.organic_traffic AS traffic,
           st.organic_kw      AS traffic_keyword,
           st.rd_total        AS refdomains,
           st.backlinks_total AS backlinks
    FROM traffic.ahrefs_statistic st
             LEFT JOIN traffic.ahrefs_domains d ON d.domain::text = st.dm::text
    WHERE date(st.date) = '2019-01-31'::date
      AND d.country IS NOT NULL
    UNION
    SELECT date(st.date)      AS date,
           d.country,
           st.dm              AS domain,
           st.organic_traffic AS traffic,
           st.organic_kw      AS traffic_keyword,
           st.rd_total        AS refdomains,
           st.backlinks_total AS backlinks
    FROM traffic.ahrefs_statistic st
             LEFT JOIN traffic.ahrefs_domains d ON d.domain::text = st.dm::text
    WHERE date(st.date) < '2021-04-30'::date
      AND date(st.date) > '2019-02-28'::date
      AND d.country IS NOT NULL
),
     union_2 AS (
         SELECT ao.date,
                ao.country,
                ao.domain,
                ao.traffic,
                NULL::integer AS cost,
                NULL::integer AS keywords,
                NULL::integer AS keywords_top3
         FROM traffic.ahrefs_organic ao
         WHERE ao.date > '2021-04-30'::date
           AND ao.date < '2023-07-01'::date
         UNION ALL
         SELECT v3.date_add         AS date,
                d.country,
                d.domain,
                v3.org_traffic,
                v3.org_cost,
                v3.org_keywords,
                v3.org_keywords_1_3 AS keywords_top3
         FROM traffic.ahrefs_organic_v3 v3
                  JOIN traffic.ahrefs_domains d ON d.id = v3.id_ahrefs_domains
         WHERE v3.date_add >= '2023-07-01'::date
     ),
     union_3 AS (
         SELECT CASE
                    WHEN a.date_add = '2021-07-31'::date THEN '2021-07-30'::date
                    ELSE a.date_add
                    END AS date,
                a.country,
                a.domain,
                a.backlinks,
                a.refdomains
         FROM traffic.ahrefs_metrics_extended a
         WHERE a.date_add > '2021-04-30'::date
           AND a.date_add < '2023-07-01'::date
         UNION ALL
         SELECT v3.date_add           AS date,
                d.country,
                d.domain,
                v3.backlinks::bigint  AS backlinks,
                v3.refdomains::bigint AS refdomains
         FROM traffic.ahrefs_metrics_extended_v3 v3
                  JOIN traffic.ahrefs_domains d ON d.id = v3.id_ahrefs_domains
         WHERE v3.date_add >= '2023-07-01'::date
     ),
     final_union AS (
         SELECT u.date,
                u.country,
                u.domain,
                u.traffic,
                u.refdomains,
                u.backlinks,
                NULL::bigint AS cost,
                NULL::bigint AS keywords,
                NULL::bigint AS keywords_top3
         FROM union_1 u
         UNION ALL
         SELECT uu.date,
                uu.country,
                uu.domain,
                uu.traffic,
                NULL::bigint AS refdomains,
                NULL::bigint AS backlinks,
                uu.cost,
                uu.keywords,
                uu.keywords_top3
         FROM union_2 uu
         UNION ALL
         SELECT un.date,
                un.country,
                un.domain,
                NULL::bigint AS traffic,
                un.refdomains,
                un.backlinks,
                NULL::bigint AS cost,
                NULL::bigint AS keywords,
                NULL::bigint AS keywords_top3
         FROM union_3 un
     )
SELECT f.date,
       f.country,
       c.name_country_eng,
       f.domain,
       f.traffic,
       f.refdomains,
       f.backlinks,
       f.cost,
       f.keywords,
       f.keywords_top3
FROM final_union f
         LEFT JOIN dimension.countries c ON f.country::text = lower(c.alpha_2::text);

alter table aggregation.v_ga_ahrefs_seo
    owner to vnov;

grant select on aggregation.v_ga_ahrefs_seo to readonly;

grant select on aggregation.v_ga_ahrefs_seo to readonly_aggregation;


