WITH contractor_data_raw AS (SELECT auc.contractor,
                                    sum(auc.total_value)                                                            AS revenue_usd,
                                    sum(auc.away_count)                                                             AS away_cnt,
                                    sum(auc.click_count)                                                            AS click_count,
                                    count(DISTINCT make_date(date_part('year'::text, auc.date::date)::integer,
                                                             date_part('month'::text, auc.date::date)::integer,
                                                             1))                                                    AS months_contractor,
                                    sum(auc.total_value) / count(DISTINCT make_date(
                                            date_part('year'::text, auc.date::date)::integer,
                                            date_part('month'::text, auc.date::date)::integer,
                                            1))::numeric                                                            AS avg_revenue_usd
                                FROM aggregation.auction_click_statistic_analytics auc
                               WHERE auc.date::date >= to_char(CURRENT_DATE - '6 mons'::interval, 'YYYY-MM-01'::text)::date
                                 AND auc.date::date <= to_char(CURRENT_DATE::timestamp with time zone, 'YYYY-MM-01'::text)::date
                                 AND auc.click_count > 0
                               GROUP BY auc.contractor),
     contractor_size AS (SELECT c.contractor,
                                c.click_count,
                                c.away_cnt,
                                c.revenue_usd,
                                c.avg_revenue_usd,
                                CASE
                                    WHEN c.contractor IS NULL THEN 'Not our client'::text
                                    WHEN c.avg_revenue_usd > 10000::numeric THEN 'XL > $10000'::text
                                    WHEN c.avg_revenue_usd > 5000::numeric THEN 'L $5000 - $9999'::text
                                    WHEN c.avg_revenue_usd > 1000::numeric THEN 'M $1000 - $4999'::text
                                    WHEN c.avg_revenue_usd > 100::numeric THEN 'S $100 - $999'::text
                                    WHEN c.avg_revenue_usd > 0::numeric THEN 'XS $0.01 - $100'::text
                                    ELSE 'Not active $0'::text
                                    END AS contractor_size
                         FROM contractor_data_raw c),
     aways_raw AS (SELECT make_date(date_part('year'::text, auc.date::date)::integer,
                                    date_part('month'::text, auc.date::date)::integer, 1) AS month,
                          cc_1.alpha_2                                                    AS country_cc,
                          auc.country_id,
                          auc.id_project,
                          auc.site,
                          auc.contractor,
                          sum(auc.away_count)                                             AS away_cnt
                   FROM aggregation.auction_click_statistic_analytics auc
                            LEFT JOIN dimension.v_country_info cc_1 ON cc_1.country_id = auc.country_id
                   WHERE auc.date::text >= '2023-09-01'::text
                   GROUP BY (make_date(date_part('year'::text, auc.date::date)::integer,
                                       date_part('month'::text, auc.date::date)::integer, 1)), cc_1.alpha_2,
                            auc.country_id, auc.id_project, auc.site, auc.contractor),
     away_percent AS (SELECT aways.month,
                             aways.country_cc,
                             aways.id_project,
                             aways.site,
                             aways.away_cnt                                                         AS aways_per_pr,
                             sum(aways.away_cnt)
                             OVER (PARTITION BY aways.month, aways.country_cc ORDER BY aways.month) AS aways_cc
                      FROM aways_raw aways
                      GROUP BY aways.month, aways.country_cc, aways.id_project, aways.site, aways.away_cnt),
     budget_revenue_daily_agg_1 AS (SELECT budget.country_id,
                                           budget.action_date,
                                           budget.user_id,
                                           budget.country_code,
                                           budget.project_id,
                                           budget.site_url,
                                           budget.is_unlim_cmp,
                                           budget.session_cnt,
                                           budget.cpc_usd,
                                           budget.click_cnt,
                                           budget.organic_click_cnt,
                                           budget.paid_click_cnt,
                                           budget.revenue_usd,
                                           budget.organic_revenue_usd,
                                           budget.paid_revenue_usd,
                                           budget.potential_revenue_usd,
                                           budget.job_cnt,
                                           budget.paid_overflow_cnt,
                                           budget.user_budget_month_usd,
                                           budget.campaign_budget_month_usd,
                                           budget.revenue_budget_diff,
                                           budget.potential_revenue_budget_diff,
                                           budget.test_click_cnt,
                                           budget.test_click_revenue_usd,
                                           budget.external_coefficient,
                                           budget.dublicated_click_revenue_usd,
                                           budget.id_campaign,
                                           budget.campaign,
                                           budget.is_cmp_with_daily_budget,
                                           budget.is_user_with_daily_budget,
                                           budget.is_paid_affiliate,
                                           budget.affiliate_revenue,
                                           budget.user_budget_month,
                                           budget.campaign_budget_month,
                                           budget.free_clicks,
                                           budget.paid_clicks,
                                           countries.country_name,
                                           countries.alpha_2
                                    FROM aggregation.budget_revenue_daily_agg budget
                                             LEFT JOIN dimension.v_country_info countries
                                                       ON budget.country_id = countries.country_id
                                    WHERE budget.action_date >= '2023-01-01'::date),
     budget_revenue_daily_agg AS (SELECT budget_revenue_daily_agg_1.country_id,
                                         budget_revenue_daily_agg_1.action_date,
                                         budget_revenue_daily_agg_1.user_id,
                                         budget_revenue_daily_agg_1.country_name                                AS country,
                                         budget_revenue_daily_agg_1.alpha_2                                     AS country_code,
                                         budget_revenue_daily_agg_1.project_id,
                                         budget_revenue_daily_agg_1.site_url,
                                         max(budget_revenue_daily_agg_1.is_unlim_cmp)                           AS is_unlim_cmp,
                                         sum(budget_revenue_daily_agg_1.paid_click_cnt)                         AS paid_click_cnt,
                                         sum(budget_revenue_daily_agg_1.paid_revenue_usd -
                                             COALESCE(budget_revenue_daily_agg_1.paid_revenue_usd *
                                                      CASE
                                                          WHEN
                                                              NULLIF(budget_revenue_daily_agg_1.revenue_usd, 0::double precision) =
                                                              0::double precision THEN 0::double precision
                                                          ELSE
                                                              budget_revenue_daily_agg_1.dublicated_click_revenue_usd::double precision /
                                                              NULLIF(budget_revenue_daily_agg_1.revenue_usd, 0::double precision)
                                                          END,
                                                      0::double precision))                                     AS paid_revenue_usd,
                                         sum(budget_revenue_daily_agg_1.revenue_usd -
                                             CASE
                                                 WHEN budget_revenue_daily_agg_1.action_date < '2023-04-28'::date
                                                     THEN COALESCE(
                                                         budget_revenue_daily_agg_1.dublicated_click_revenue_usd,
                                                         0::numeric)::double precision
                                                 ELSE 0::double precision
                                                 END + (budget_revenue_daily_agg_1.revenue_usd -
                                                        CASE
                                                            WHEN budget_revenue_daily_agg_1.action_date < '2023-04-28'::date
                                                                THEN COALESCE(
                                                                    budget_revenue_daily_agg_1.dublicated_click_revenue_usd,
                                                                    0::numeric)::double precision
                                                            ELSE 0::double precision
                                                            END) / date_part('day'::text, CURRENT_DATE - 1) *
                                                       (make_date(
                                                                date_part('year'::text, CURRENT_DATE - 1 + '1 mon'::interval)::integer,
                                                                date_part('month'::text, CURRENT_DATE - 1 + '1 mon'::interval)::integer,
                                                                1) - (CURRENT_DATE - 1) -
                                                        1)::double precision)                                   AS potential_revenue_usd,
                                         sum(
                                                 CASE
                                                     WHEN budget_revenue_daily_agg_1.action_date < '2023-04-28'::date
                                                         THEN COALESCE(budget_revenue_daily_agg_1.revenue_usd,
                                                                       0::double precision) - COALESCE(
                                                                      budget_revenue_daily_agg_1.dublicated_click_revenue_usd,
                                                                      0::numeric)::double precision
                                                     ELSE budget_revenue_daily_agg_1.revenue_usd
                                                     END)                                                       AS revenue_usd,
                                         sum(budget_revenue_daily_agg_1.paid_overflow_cnt)                      AS paid_overflow_cnt,
                                         max(budget_revenue_daily_agg_1.user_budget_month_usd)                  AS user_budget_month_usd,
                                         sum(COALESCE(budget_revenue_daily_agg_1.campaign_budget_month_usd,
                                                      0::double precision))                                     AS campaign_budget_month_usd,
                                         count(DISTINCT budget_revenue_daily_agg_1.id_campaign)                 AS campaign_cnt,
                                         count(DISTINCT
                                               CASE
                                                   WHEN COALESCE(budget_revenue_daily_agg_1.campaign_budget_month_usd,
                                                                 0::double precision) > 0::double precision
                                                       THEN budget_revenue_daily_agg_1.id_campaign
                                                   ELSE NULL::character varying
                                                   END)                                                         AS campaign_with_budget_cnt,
                                         count(DISTINCT
                                               CASE
                                                   WHEN budget_revenue_daily_agg_1.paid_clicks > 0
                                                       THEN budget_revenue_daily_agg_1.id_campaign
                                                   ELSE NULL::character varying
                                                   END)                                                         AS paid_campaign_cnt,
                                         info_currency.name                                                     AS user_curr_name,
                                         info_currency.id                                                       AS user_curr_id
                                  FROM budget_revenue_daily_agg_1 budget_revenue_daily_agg_1
                                           LEFT JOIN imp.auction_user
                                                     ON budget_revenue_daily_agg_1.country_id = auction_user.country AND
                                                        budget_revenue_daily_agg_1.user_id = auction_user.id
                                           LEFT JOIN dimension.info_currency
                                                     ON auction_user.country = info_currency.country AND
                                                        auction_user.currency = info_currency.id
                                  GROUP BY budget_revenue_daily_agg_1.action_date, budget_revenue_daily_agg_1.user_id,
                                           budget_revenue_daily_agg_1.project_id, budget_revenue_daily_agg_1.site_url,
                                           budget_revenue_daily_agg_1.country_id,
                                           budget_revenue_daily_agg_1.country_name, budget_revenue_daily_agg_1.alpha_2,
                                           info_currency.name, info_currency.id),
     budget_agg AS (SELECT budget_revenue_daily_agg.action_date AS date,
                           budget_revenue_daily_agg.country,
                           budget_revenue_daily_agg.country_code,
                           budget_revenue_daily_agg.country_id,
                           budget_revenue_daily_agg.user_curr_name,
                           budget_revenue_daily_agg.user_curr_id,
                           budget_revenue_daily_agg.user_id,
                           budget_revenue_daily_agg.project_id,
                           budget_revenue_daily_agg.site_url    AS project_name,
                           budget_revenue_daily_agg.paid_revenue_usd,
                           budget_revenue_daily_agg.user_budget_month_usd,
                           budget_revenue_daily_agg.campaign_budget_month_usd,
                           CASE
                               WHEN budget_revenue_daily_agg.campaign_cnt =
                                    budget_revenue_daily_agg.campaign_with_budget_cnt AND
                                    budget_revenue_daily_agg.campaign_budget_month_usd <
                                    budget_revenue_daily_agg.user_budget_month_usd
                                   THEN budget_revenue_daily_agg.campaign_budget_month_usd
                               WHEN budget_revenue_daily_agg.campaign_budget_month_usd > 0::double precision AND
                                    budget_revenue_daily_agg.campaign_budget_month_usd <
                                    budget_revenue_daily_agg.user_budget_month_usd
                                   THEN budget_revenue_daily_agg.campaign_budget_month_usd
                               WHEN budget_revenue_daily_agg.is_unlim_cmp = 1 AND
                                    budget_revenue_daily_agg.user_budget_month_usd >
                                    budget_revenue_daily_agg.campaign_budget_month_usd
                                   THEN budget_revenue_daily_agg.user_budget_month_usd
                               WHEN budget_revenue_daily_agg.user_budget_month_usd = 0::double precision
                                   THEN budget_revenue_daily_agg.campaign_budget_month_usd
                               ELSE budget_revenue_daily_agg.user_budget_month_usd
                               END                              AS budget,
                           budget_revenue_daily_agg.revenue_usd,
                           budget_revenue_daily_agg.potential_revenue_usd
                    FROM budget_revenue_daily_agg),
     crm_raw AS (SELECT CASE
                            WHEN crm_1.account_name = 'xing.com'::text THEN 'DE'::text
                            WHEN crm_1.account_name = 'sitly.com (main)'::text THEN 'NL'::text
                            WHEN crm_1.account_name = 'thejobnetwork.com'::text THEN 'US'::text
                            WHEN lower(crm_1.account_name) = 'vonq.albertheijn.com'::text OR
                                 crm_1.account_name = 'vonq.albertheijn'::text THEN 'NL'::text
                            WHEN crm_1.account_name = 'vonq.com'::text THEN 'US'::text
                            WHEN crm_1.account_name = 'jobted.com'::text THEN 'DE'::text
                            WHEN crm_1.country_cc = 'GB'::text THEN 'UK'::text
                            ELSE crm_1.country_cc
                            END::character varying AS country_cc_updated,
                        crm_1.country_cc           AS country_cc_initial,
                        CASE
                            WHEN lower(crm_1.account_name) = 'vonq.albertheijn.com'::text THEN 'vonq.albertheijn'::text
                            ELSE lower(crm_1.account_name)
                            END                    AS account_name_updated,
                        CASE
                            WHEN "left"(crm_1.stage_id, 4) = 'f93f'::text THEN '1 New'::text
                            WHEN "left"(crm_1.stage_id, 4) = 'f5f4'::text THEN '1 First contact'::text
                            WHEN "left"(crm_1.stage_id, 4) = '3b33'::text THEN '2 In progress'::text
                            WHEN "left"(crm_1.stage_id, 4) = 'be8a'::text OR "left"(crm_1.stage_id, 4) = '347c'::text
                                THEN '2 Getting feedback'::text
                            WHEN "left"(crm_1.stage_id, 4) = 'f1d8'::text OR "left"(crm_1.stage_id, 4) = 'f70f'::text
                                THEN '3 Processing feedback'::text
                            WHEN "left"(crm_1.stage_id, 4) = '0d6a'::text THEN '3 Waiting for response'::text
                            WHEN "left"(crm_1.stage_id, 4) = '8a75'::text THEN '4 Rejection handling'::text
                            WHEN "left"(crm_1.stage_id, 4) = '4f12'::text THEN '5 Resolved'::text
                            WHEN "left"(crm_1.stage_id, 4) = '5da0'::text THEN '5 Successfully completed'::text
                            WHEN "left"(crm_1.stage_id, 4) = '7c30'::text THEN '5 Unsuccessfully completed'::text
                            WHEN "left"(crm_1.stage_id, 4) = '2a15'::text THEN '5 Cancelled'::text
                            ELSE crm_1.stage_name
                            END                    AS stage_name_updated,
                        crm_1.case_number,
                        crm_1.case_row_id,
                        crm_1.date_created,
                        crm_1.date_modified,
                        crm_1.account_name,
                        crm_1.account_soska_project,
                        crm_1.account_id_1c,
                        crm_1.country_cc,
                        crm_1.account_segment,
                        crm_1.owner_name,
                        crm_1.contact_name,
                        crm_1.contact_email,
                        crm_1.category_name,
                        crm_1.sub_category_name,
                        crm_1.stage_id,
                        crm_1.stage_name,
                        crm_1.case_desc,
                        crm_1.case_resolution,
                        crm_1.client_feedback,
                        crm_1.initiated_by,
                        crm_1.case_priority,
                        crm_1.offered_budget,
                        crm_1.confirmed_budget,
                        crm_1.offered_cpc,
                        crm_1.confirmed_cpc,
                        crm_1.currency_1,
                        crm_1.currency_2,
                        crm_1.tableau_traffic_by_source_dashboard,
                        crm_1.tableau_serp_competition_dashboard,
                        crm_1.tableau_revenue_and_budget_dashboard,
                        crm_1.tableau_cpc_segmentation,
                        crm_1.tableau_conversion_dashboard,
                        crm_1.tableau_affiliate_client_cpc_analysis,
                        crm_1.change_gap,
                        crm_1.add_new_affiliate_sources,
                        crm_1.allow_affiliate_sources,
                        crm_1.affiliate_flag_32_forecast,
                        crm_1.adding_more_paid_jobs,
                        crm_1.soska_cpc_in_similar_groups,
                        crm_1.set_lower_cpc_flag_32,
                        crm_1.set_higher_cpc,
                        crm_1.other,
                        crm_1.other_detail,
                        crm_1.ticket,
                        crm_1.catego,
                        crm_1.add_new_ppc_sources,
                        crm_1.ppc_recommendation,
                        crm_1.ppc_flag_32_forecast,
                        crm_1.auction_analytics,
                        crm_1.auction_segmentation,
                        crm_1.auction_campaign_settings,
                        crm_1.without_reason,
                        crm_1.no_additional_paid_jobs_available,
                        crm_1.gap_change_not_approved,
                        crm_1.not_enough_traffic_from_internal_sources,
                        crm_1.affiliate_is_forbidden,
                        crm_1.no_additional_budget,
                        crm_1.recommended_cpc_too_expensive,
                        crm_1.cpa_too_expensive,
                        crm_1.other_blockers,
                        crm_1.other_blockers_details,
                        crm_1.min_cpc_too_expensive,
                        crm_1.postback_url,
                        crm_1.utm_source,
                        crm_1.regular_conversion,
                        crm_1.manual_data_gathering,
                        crm_1.appcast_api,
                        crm_1.email_cnt,
                        crm_1.call_cnt,
                        crm_1.visit_cnt,
                        crm_1.task_cnt,
                        crm_1.account_id,
                        crm_1.lack_of_tech_team_free_resources,
                        crm_1.client_not_know_howtodo,
                        crm_1.tracking_is_prohibited_by_policy,
                        crm_1.legal_gdpr,
                        crm_1.blocker_competitor,
                        crm_1.tracking_methods_comparison,
                        crm_1.cs_team_help,
                        crm_1.consultation_from_jooble,
                        crm_1.communication_with_client_specialist,
                        crm_1.tech_documentation,
                        crm_1.client_switched_to_our_other_client,
                        crm_1.refuses_to_pay,
                        crm_1.paused_due_to_overdue_payments,
                        crm_1.low_traffic_volumes,
                        crm_1.no_marketing_budget,
                        crm_1.no_budget_for_jooble,
                        crm_1.no_client_for_jooble,
                        crm_1.minimal_budget_is_too_high,
                        crm_1.switched_to_pja,
                        crm_1.policy_change,
                        crm_1.change_in_the_company,
                        crm_1.refuses_to_communicate_details,
                        crm_1.joobles_decision,
                        crm_1.technical_issues
                 FROM aggregation.crm_cases_agg crm_1
                 WHERE crm_1.case_number <> ALL
                       (ARRAY ['CS-00015'::text, 'CS-00016'::text, 'CS-00018'::text, 'CS-00026'::text, 'CS-00107'::text, 'CS-02539'::text, 'CS-03220'::text, 'CS-02557'::text, ''::text])),
     crm_raw_1 AS (SELECT CASE
                              WHEN crm_1.account_soska_project = ''::text THEN
                                  (('http://soska.jooble.com/jproject/'::text || cc_1.alpha_2::text) || '/'::text) ||
                                  ip.id
                              ELSE crm_1.account_soska_project
                              END AS account_soska_project_updated,
                          crm_1.country_cc_updated,
                          crm_1.country_cc_initial,
                          crm_1.account_name_updated,
                          crm_1.stage_name_updated,
                          crm_1.case_number,
                          crm_1.case_row_id,
                          crm_1.date_created,
                          crm_1.date_modified,
                          crm_1.account_name,
                          crm_1.account_soska_project,
                          crm_1.account_id_1c,
                          crm_1.country_cc,
                          crm_1.account_segment,
                          crm_1.owner_name,
                          crm_1.contact_name,
                          crm_1.contact_email,
                          crm_1.category_name,
                          crm_1.sub_category_name,
                          crm_1.stage_id,
                          crm_1.stage_name,
                          crm_1.case_desc,
                          crm_1.case_resolution,
                          crm_1.client_feedback,
                          crm_1.initiated_by,
                          crm_1.case_priority,
                          crm_1.offered_budget,
                          crm_1.confirmed_budget,
                          crm_1.offered_cpc,
                          crm_1.confirmed_cpc,
                          crm_1.currency_1,
                          crm_1.currency_2,
                          crm_1.tableau_traffic_by_source_dashboard,
                          crm_1.tableau_serp_competition_dashboard,
                          crm_1.tableau_revenue_and_budget_dashboard,
                          crm_1.tableau_cpc_segmentation,
                          crm_1.tableau_conversion_dashboard,
                          crm_1.tableau_affiliate_client_cpc_analysis,
                          crm_1.change_gap,
                          crm_1.add_new_affiliate_sources,
                          crm_1.allow_affiliate_sources,
                          crm_1.affiliate_flag_32_forecast,
                          crm_1.adding_more_paid_jobs,
                          crm_1.soska_cpc_in_similar_groups,
                          crm_1.set_lower_cpc_flag_32,
                          crm_1.set_higher_cpc,
                          crm_1.other,
                          crm_1.other_detail,
                          crm_1.ticket,
                          crm_1.catego,
                          crm_1.add_new_ppc_sources,
                          crm_1.ppc_recommendation,
                          crm_1.ppc_flag_32_forecast,
                          crm_1.auction_analytics,
                          crm_1.auction_segmentation,
                          crm_1.auction_campaign_settings,
                          crm_1.without_reason,
                          crm_1.no_additional_paid_jobs_available,
                          crm_1.gap_change_not_approved,
                          crm_1.not_enough_traffic_from_internal_sources,
                          crm_1.affiliate_is_forbidden,
                          crm_1.no_additional_budget,
                          crm_1.recommended_cpc_too_expensive,
                          crm_1.cpa_too_expensive,
                          crm_1.other_blockers,
                          crm_1.other_blockers_details,
                          crm_1.min_cpc_too_expensive,
                          crm_1.postback_url,
                          crm_1.utm_source,
                          crm_1.regular_conversion,
                          crm_1.manual_data_gathering,
                          crm_1.appcast_api,
                          crm_1.email_cnt,
                          crm_1.call_cnt,
                          crm_1.visit_cnt,
                          crm_1.task_cnt,
                          crm_1.account_id,
                          cc_1.country_id,
                          crm_1.lack_of_tech_team_free_resources,
                          crm_1.client_not_know_howtodo,
                          crm_1.tracking_is_prohibited_by_policy,
                          crm_1.legal_gdpr,
                          crm_1.blocker_competitor,
                          crm_1.tracking_methods_comparison,
                          crm_1.cs_team_help,
                          crm_1.consultation_from_jooble,
                          crm_1.communication_with_client_specialist,
                          crm_1.tech_documentation,
                          crm_1.client_switched_to_our_other_client,
                          crm_1.refuses_to_pay,
                          crm_1.paused_due_to_overdue_payments,
                          crm_1.low_traffic_volumes,
                          crm_1.no_marketing_budget,
                          crm_1.no_budget_for_jooble,
                          crm_1.no_client_for_jooble,
                          crm_1.minimal_budget_is_too_high,
                          crm_1.switched_to_pja,
                          crm_1.policy_change,
                          crm_1.change_in_the_company,
                          crm_1.refuses_to_communicate_details,
                          crm_1.joobles_decision,
                          crm_1.technical_issues
                   FROM crm_raw crm_1
                            LEFT JOIN dimension.v_country_info cc_1 ON cc_1.alpha_2::text = crm_1.country_cc_updated::text
                            LEFT JOIN dimension.info_project ip
                                      ON ip.country = cc_1.country_id AND ip.name::text = crm_1.account_name_updated AND
                                         (ip.is_active = 1 OR ip.is_active = 0 AND ip.is_daily_run = 1)),
     crm_raw_2 AS (SELECT crm_1.account_soska_project_updated,
                          crm_1.country_cc_updated,
                          crm_1.country_cc_initial,
                          crm_1.account_name_updated,
                          crm_1.stage_name_updated,
                          crm_1.case_number,
                          crm_1.case_row_id,
                          crm_1.date_created,
                          crm_1.date_modified,
                          crm_1.account_name,
                          crm_1.account_soska_project,
                          crm_1.account_id_1c,
                          crm_1.country_cc,
                          crm_1.account_segment,
                          crm_1.owner_name,
                          crm_1.contact_name,
                          crm_1.contact_email,
                          crm_1.category_name,
                          crm_1.sub_category_name,
                          crm_1.stage_id,
                          crm_1.stage_name,
                          crm_1.case_desc,
                          crm_1.case_resolution,
                          crm_1.client_feedback,
                          crm_1.initiated_by,
                          crm_1.case_priority,
                          crm_1.offered_budget,
                          crm_1.confirmed_budget,
                          crm_1.offered_cpc,
                          crm_1.confirmed_cpc,
                          crm_1.currency_1,
                          crm_1.currency_2,
                          crm_1.tableau_traffic_by_source_dashboard,
                          crm_1.tableau_serp_competition_dashboard,
                          crm_1.tableau_revenue_and_budget_dashboard,
                          crm_1.tableau_cpc_segmentation,
                          crm_1.tableau_conversion_dashboard,
                          crm_1.tableau_affiliate_client_cpc_analysis,
                          crm_1.change_gap,
                          crm_1.add_new_affiliate_sources,
                          crm_1.allow_affiliate_sources,
                          crm_1.affiliate_flag_32_forecast,
                          crm_1.adding_more_paid_jobs,
                          crm_1.soska_cpc_in_similar_groups,
                          crm_1.set_lower_cpc_flag_32,
                          crm_1.set_higher_cpc,
                          crm_1.other,
                          crm_1.other_detail,
                          crm_1.ticket,
                          crm_1.catego,
                          crm_1.add_new_ppc_sources,
                          crm_1.ppc_recommendation,
                          crm_1.ppc_flag_32_forecast,
                          crm_1.auction_analytics,
                          crm_1.auction_segmentation,
                          crm_1.auction_campaign_settings,
                          crm_1.without_reason,
                          crm_1.no_additional_paid_jobs_available,
                          crm_1.gap_change_not_approved,
                          crm_1.not_enough_traffic_from_internal_sources,
                          crm_1.affiliate_is_forbidden,
                          crm_1.no_additional_budget,
                          crm_1.recommended_cpc_too_expensive,
                          crm_1.cpa_too_expensive,
                          crm_1.other_blockers,
                          crm_1.other_blockers_details,
                          crm_1.min_cpc_too_expensive,
                          crm_1.postback_url,
                          crm_1.utm_source,
                          crm_1.regular_conversion,
                          crm_1.manual_data_gathering,
                          crm_1.appcast_api,
                          crm_1.email_cnt,
                          crm_1.call_cnt,
                          crm_1.visit_cnt,
                          crm_1.task_cnt,
                          crm_1.account_id,
                          crm_1.lack_of_tech_team_free_resources,
                          crm_1.client_not_know_howtodo,
                          crm_1.tracking_is_prohibited_by_policy,
                          crm_1.legal_gdpr,
                          crm_1.blocker_competitor,
                          crm_1.tracking_methods_comparison,
                          crm_1.cs_team_help,
                          crm_1.consultation_from_jooble,
                          crm_1.communication_with_client_specialist,
                          crm_1.tech_documentation,
                          crm_1.client_switched_to_our_other_client,
                          crm_1.refuses_to_pay,
                          crm_1.paused_due_to_overdue_payments,
                          crm_1.low_traffic_volumes,
                          crm_1.no_marketing_budget,
                          crm_1.no_budget_for_jooble,
                          crm_1.no_client_for_jooble,
                          crm_1.minimal_budget_is_too_high,
                          crm_1.switched_to_pja,
                          crm_1.policy_change,
                          crm_1.change_in_the_company,
                          crm_1.refuses_to_communicate_details,
                          crm_1.joobles_decision,
                          crm_1.technical_issues,
                          crm_1.country_id,
                          crm_1.date_created::date                                                     AS date,
                          btrim(crm_1.account_id_1c, ' '::text)                                        AS account_id_1c_updated,
                          CASE
                              WHEN crm_1.currency_1::text = '978'::text THEN NULL::character varying
                              ELSE crm_1.currency_1
                              END                                                                      AS currency_1_updated,
                          "substring"(crm_1.account_soska_project_updated, '/([0-9]+)'::text)::integer AS project_id,
                          CASE
                              WHEN crm_1.account_soska_project_updated IS NULL THEN crm_1.country_cc_updated
                              WHEN crm_1.account_soska_project_updated IS NOT NULL THEN upper("substring"(replace(
                                                                                                                  replace(
                                                                                                                          crm_1.account_soska_project_updated,
                                                                                                                          'http://soska.jooble.com/jproject/'::text,
                                                                                                                          ''::text),
                                                                                                                  'http://soska.jooble.com/auction/'::text,
                                                                                                                  ''::text),
                                                                                                          '([A-Za-z]+)(/|$)'::text))::character varying
                              WHEN crm_1.country_cc_updated IS NULL OR crm_1.country_cc_updated::text = ''::text
                                  THEN 'na'::character varying
                              ELSE crm_1.country_cc_updated
                              END                                                                      AS country_cc_final
                   FROM crm_raw_1 crm_1)
SELECT crm.date,
       crm.date_created,
       crm.case_number,
       lower(crm.account_name_updated)                             AS account_name,
       crm.country_cc_initial,
       crm.project_id,
       crm.country_cc_final                                        AS country_cc,
       crm.account_id_1c_updated                                   AS account_id_1c,
       crm.account_soska_project_updated                           AS account_soska_project,
       concat('https://jooble.creatio.com/0/Nui/ViewModule.aspx#CardModuleV2/UsrAccountPageV21/edit/',
              crm.account_id)                                      AS crm_link,
       budget_agg.user_id,
       crm.account_id,
       crm.stage_name_updated                                      AS stage_name,
       crm.offered_budget,
       crm.confirmed_budget,
       COALESCE(crm.currency_1_updated, budget_agg.user_curr_name) AS currency,
       budget_agg.budget::bigint                                   AS budget_fact_usd,
       budget_agg.revenue_usd::bigint                              AS revenue_usd,
       budget_agg.potential_revenue_usd::bigint                    AS potential_revenue_usd,
       max(ic.value_to_usd)                                        AS curr_value_to_usd,
       CASE
           WHEN COALESCE(crm.currency_1_updated, budget_agg.user_curr_name) IS NULL THEN budget_agg.budget
           ELSE budget_agg.budget / max(ic.value_to_usd)::double precision
           END::bigint                                             AS budget_fact_origin,
       crm.case_row_id,
       crm.date_modified,
       cs.contractor_size                                          AS account_segment,
       crm.owner_name,
       crm.contact_name,
       crm.contact_email,
       crm.category_name,
       crm.sub_category_name,
       crm.stage_id,
       crm.case_desc,
       crm.case_resolution,
       crm.client_feedback,
       crm.initiated_by,
       crm.case_priority,
       crm.offered_cpc,
       crm.confirmed_cpc,
       replace(((((((((((((((((((((((((((
                                            CASE
                                                WHEN crm.auction_analytics IS TRUE THEN ' - Auction analytics'::text
                                                ELSE '/'::text
                                                END ||
                                            CASE
                                                WHEN crm.tableau_revenue_and_budget_dashboard IS TRUE
                                                    THEN ' - Tableau Revenue and Budget dashboard'::text
                                                ELSE '/'::text
                                                END) ||
                                        CASE
                                            WHEN crm.ppc_flag_32_forecast IS TRUE THEN ' - PPC Flag 32 Forecast'::text
                                            ELSE '/'::text
                                            END) ||
                                       CASE
                                           WHEN crm.tableau_serp_competition_dashboard IS TRUE
                                               THEN ' - Tableau SERP competition dashboard'::text
                                           ELSE '/'::text
                                           END) ||
                                      CASE
                                          WHEN crm.tableau_conversion_dashboard IS TRUE
                                              THEN ' - Tableau conversion dashboard'::text
                                          ELSE '/'::text
                                          END) ||
                                     CASE
                                         WHEN crm.tableau_cpc_segmentation IS TRUE THEN ' - Tableau CPC segmentation'::text
                                         ELSE '/'::text
                                         END) ||
                                    CASE
                                        WHEN crm.allow_affiliate_sources IS TRUE THEN ' - Allow Affiliate sources'::text
                                        ELSE '/'::text
                                        END) ||
                                   CASE
                                       WHEN crm.adding_more_paid_jobs IS TRUE THEN ' - Adding more paid jobs'::text
                                       ELSE '/'::text
                                       END) ||
                                  CASE
                                      WHEN crm.affiliate_flag_32_forecast IS TRUE THEN ' - Affiliate Flag 32 Forecast '::text
                                      ELSE '/'::text
                                      END) ||
                                 CASE
                                     WHEN crm.change_gap IS TRUE THEN ' - Change Gap'::text
                                     ELSE '/'::text
                                     END) ||
                                CASE
                                    WHEN crm.add_new_affiliate_sources IS TRUE THEN ' - Add new Affiliate sources'::text
                                    ELSE '/'::text
                                    END) ||
                               CASE
                                   WHEN crm.ppc_recommendation IS TRUE THEN ' - PPC recommendation'::text
                                   ELSE '/'::text
                                   END) ||
                              CASE
                                  WHEN crm.add_new_ppc_sources IS TRUE THEN ' - Add new PPC sources'::text
                                  ELSE '/'::text
                                  END) ||
                             CASE
                                 WHEN crm.tableau_affiliate_client_cpc_analysis IS TRUE
                                     THEN ' - Tableau Affiliate Client CPC analysis'::text
                                 ELSE '/'::text
                                 END) ||
                            CASE
                                WHEN crm.tableau_traffic_by_source_dashboard IS TRUE
                                    THEN ' - Tableau Traffic by source dashboard'::text
                                ELSE '/'::text
                                END) ||
                           CASE
                               WHEN crm.catego IS TRUE THEN ' - Catego'::text
                               ELSE '/'::text
                               END) ||
                          CASE
                              WHEN crm.ticket IS TRUE THEN ' - Ticket'::text
                              ELSE '/'::text
                              END) ||
                         CASE
                             WHEN crm.other IS TRUE THEN ' - Other'::text
                             ELSE '/'::text
                             END) ||
                        CASE
                            WHEN crm.auction_segmentation IS TRUE THEN ' - Auction segmentation'::text
                            ELSE '/'::text
                            END) ||
                       CASE
                           WHEN crm.auction_campaign_settings IS TRUE THEN ' - Auction campaign settings'::text
                           ELSE '/'::text
                           END) ||
                      CASE
                          WHEN crm.set_higher_cpc IS TRUE THEN ' - Set higher CPC'::text
                          ELSE '/'::text
                          END) ||
                     CASE
                         WHEN crm.set_lower_cpc_flag_32 IS TRUE THEN ' - Set lower CPC (Flag32)'::text
                         ELSE '/'::text
                         END) ||
                    CASE
                        WHEN crm.tech_documentation IS TRUE THEN ' - Tech documentation'::text
                        ELSE '/'::text
                        END) ||
                   CASE
                       WHEN crm.communication_with_client_specialist IS TRUE
                           THEN ' - Communication with clients tech specialist'::text
                       ELSE '/'::text
                       END) ||
                  CASE
                      WHEN crm.consultation_from_jooble IS TRUE THEN ' - Consultation from Jooble tech team'::text
                      ELSE '/'::text
                      END) ||
                 CASE
                     WHEN crm.cs_team_help IS TRUE THEN ' - CS team help'::text
                     ELSE '/'::text
                     END) ||
                CASE
                    WHEN crm.tracking_methods_comparison IS TRUE THEN ' - Tracking methods comparison'::text
                    ELSE '/'::text
                    END) ||
               CASE
                   WHEN crm.soska_cpc_in_similar_groups IS TRUE THEN ' - Soska CPC in similar groups'::text
                   ELSE '/'::text
                   END, '/'::text, ''::text)                       AS tools,
       crm.tableau_traffic_by_source_dashboard,
       crm.tableau_serp_competition_dashboard,
       crm.tableau_revenue_and_budget_dashboard,
       crm.tableau_cpc_segmentation,
       crm.tableau_conversion_dashboard,
       crm.tableau_affiliate_client_cpc_analysis,
       crm.change_gap,
       crm.add_new_affiliate_sources,
       crm.allow_affiliate_sources,
       crm.affiliate_flag_32_forecast,
       crm.adding_more_paid_jobs,
       crm.soska_cpc_in_similar_groups,
       crm.set_lower_cpc_flag_32,
       crm.set_higher_cpc,
       crm.other,
       crm.other_detail,
       crm.ticket,
       crm.catego,
       crm.add_new_ppc_sources,
       crm.ppc_recommendation,
       crm.ppc_flag_32_forecast,
       crm.auction_analytics,
       crm.auction_segmentation,
       crm.auction_campaign_settings,
       replace((((((((((((((
                               CASE
                                   WHEN crm.no_additional_budget IS TRUE THEN ' - No additional budget'::text
                                   ELSE '/'::text
                                   END ||
                               CASE
                                   WHEN crm.other_blockers IS TRUE THEN ' - Other blockers'::text
                                   ELSE '/'::text
                                   END) ||
                           CASE
                               WHEN crm.cpa_too_expensive IS TRUE THEN ' - CPA too expensive'::text
                               ELSE '/'::text
                               END) ||
                          CASE
                              WHEN crm.min_cpc_too_expensive IS TRUE THEN ' - Min CPC too expensive'::text
                              ELSE '/'::text
                              END) ||
                         CASE
                             WHEN crm.recommended_cpc_too_expensive IS TRUE THEN ' - Recommended CPC too expensive'::text
                             ELSE '/'::text
                             END) ||
                        CASE
                            WHEN crm.gap_change_not_approved IS TRUE THEN ' - Gap change not approved'::text
                            ELSE '/'::text
                            END) ||
                       CASE
                           WHEN crm.no_additional_paid_jobs_available IS TRUE
                               THEN ' - No additional paid jobs available'::text
                           ELSE '/'::text
                           END) ||
                      CASE
                          WHEN crm.not_enough_traffic_from_internal_sources IS TRUE
                              THEN ' - Not enough traffic from internal sources'::text
                          ELSE '/'::text
                          END) ||
                     CASE
                         WHEN crm.affiliate_is_forbidden IS TRUE THEN ' - Affiliate is forbidden'::text
                         ELSE '/'::text
                         END) ||
                    CASE
                        WHEN crm.lack_of_tech_team_free_resources IS TRUE THEN ' - Lack of tech team free resources'::text
                        ELSE '/'::text
                        END) ||
                   CASE
                       WHEN crm.client_not_know_howtodo IS TRUE THEN ' - Client does not know how to do it'::text
                       ELSE '/'::text
                       END) ||
                  CASE
                      WHEN crm.tracking_is_prohibited_by_policy IS TRUE THEN ' - Tracking is prohibited by policy'::text
                      ELSE '/'::text
                      END) ||
                 CASE
                     WHEN crm.legal_gdpr IS TRUE THEN ' - Legal_GDPR'::text
                     ELSE '/'::text
                     END) ||
                CASE
                    WHEN crm.blocker_competitor IS TRUE THEN ' - Client-competitor doesnt want to help us'::text
                    ELSE '/'::text
                    END) ||
               CASE
                   WHEN crm.without_reason IS TRUE THEN ' - Without reason'::text
                   ELSE '/'::text
                   END, '/'::text, ''::text)                       AS blockers,
       crm.without_reason,
       crm.no_additional_paid_jobs_available,
       crm.gap_change_not_approved,
       crm.not_enough_traffic_from_internal_sources,
       crm.affiliate_is_forbidden,
       crm.no_additional_budget,
       crm.recommended_cpc_too_expensive,
       crm.cpa_too_expensive,
       crm.other_blockers,
       crm.other_blockers_details,
       crm.min_cpc_too_expensive,
       crm.postback_url,
       crm.utm_source,
       crm.regular_conversion,
       crm.manual_data_gathering,
       crm.appcast_api,
       crm.email_cnt,
       crm.call_cnt,
       crm.visit_cnt,
       crm.task_cnt,
       stlog.date_stg_start,
       stlog.date_stg_end,
       away_percent.aways_per_pr,
       away_percent.aways_cc,
       crm.lack_of_tech_team_free_resources,
       crm.client_not_know_howtodo,
       crm.tracking_is_prohibited_by_policy,
       crm.legal_gdpr,
       crm.blocker_competitor,
       crm.tracking_methods_comparison,
       crm.cs_team_help,
       crm.consultation_from_jooble,
       crm.communication_with_client_specialist,
       crm.tech_documentation,
       crm.client_switched_to_our_other_client,
       crm.refuses_to_pay,
       crm.paused_due_to_overdue_payments,
       crm.low_traffic_volumes,
       crm.no_marketing_budget,
       crm.no_budget_for_jooble,
       crm.no_client_for_jooble,
       crm.minimal_budget_is_too_high,
       crm.switched_to_pja,
       crm.policy_change,
       crm.change_in_the_company,
       crm.refuses_to_communicate_details,
       crm.joobles_decision,
       crm.technical_issues
FROM crm_raw_2 crm
         LEFT JOIN dimension.v_country_info cc ON cc.alpha_2::text = crm.country_cc_final::text
         LEFT JOIN budget_agg ON crm.country_cc_final::text = budget_agg.country_code::text AND
                                 crm.project_id = budget_agg.project_id AND (crm.date - 1) = budget_agg.date
         LEFT JOIN dimension.info_currency currency_2
                   ON cc.country_id = currency_2.country AND crm.currency_1_updated::text = currency_2.name::text
         LEFT JOIN dimension.info_currency_history ic
                   ON (crm.date - 1) = ic.date::date AND ic.country = cc.country_id AND
                      ic.id_currency = COALESCE(currency_2.id, budget_agg.user_curr_id)
         LEFT JOIN contractor_size cs ON cs.contractor::text = crm.account_id_1c_updated
         LEFT JOIN aggregation.crm_cases_stage_log stlog
                   ON stlog.case_number = crm.case_number AND stlog.stage_id::text = crm.stage_id
         LEFT JOIN away_percent ON away_percent.country_cc::text = crm.country_cc_final::text AND
                                   away_percent.id_project = crm.project_id AND
                                   date_trunc('month'::text, crm.date - '1 mon'::interval month)::date =
                                   away_percent.month
GROUP BY crm.date, crm.date_created, crm.case_number, (lower(crm.account_name_updated)), crm.country_cc_initial,
         crm.project_id, crm.country_cc_final, crm.account_id_1c_updated, crm.account_soska_project_updated,
         crm.offered_budget, crm.confirmed_budget, crm.currency_1_updated, crm.stage_name_updated,
         budget_agg.user_curr_name, crm.case_row_id, crm.date_modified, cs.contractor_size, crm.owner_name,
         crm.contact_name, crm.contact_email, crm.category_name, crm.sub_category_name, crm.stage_id, crm.case_desc,
         crm.case_resolution, crm.client_feedback, crm.initiated_by, crm.case_priority, crm.offered_cpc,
         crm.confirmed_cpc, crm.tableau_traffic_by_source_dashboard, crm.tableau_serp_competition_dashboard,
         crm.tableau_revenue_and_budget_dashboard, crm.tableau_cpc_segmentation, crm.tableau_conversion_dashboard,
         crm.tableau_affiliate_client_cpc_analysis, crm.change_gap, crm.add_new_affiliate_sources,
         crm.allow_affiliate_sources, crm.affiliate_flag_32_forecast, crm.adding_more_paid_jobs,
         crm.soska_cpc_in_similar_groups, crm.set_lower_cpc_flag_32, crm.set_higher_cpc, crm.other, crm.other_detail,
         crm.ticket, crm.catego, crm.add_new_ppc_sources, crm.ppc_recommendation, crm.ppc_flag_32_forecast,
         crm.auction_analytics, crm.auction_segmentation, crm.auction_campaign_settings, crm.without_reason,
         crm.no_additional_paid_jobs_available, crm.gap_change_not_approved,
         crm.not_enough_traffic_from_internal_sources, crm.affiliate_is_forbidden, crm.no_additional_budget,
         crm.recommended_cpc_too_expensive, crm.cpa_too_expensive, crm.other_blockers, crm.other_blockers_details,
         crm.min_cpc_too_expensive, crm.postback_url, crm.utm_source, crm.regular_conversion, crm.manual_data_gathering,
         crm.appcast_api, crm.email_cnt, crm.call_cnt, crm.visit_cnt, crm.task_cnt, budget_agg.budget, budget_agg.revenue_usd,
         budget_agg.potential_revenue_usd,
         (concat('https://jooble.creatio.com/0/Nui/ViewModule.aspx#CardModuleV2/UsrAccountPageV21/edit/',
                 crm.account_id)), crm.account_id, budget_agg.user_id,
         (replace(((((((((((((((((((((((((((
                                               CASE
                                                   WHEN crm.auction_analytics IS TRUE THEN ' - Auction analytics'::text
                                                   ELSE '/'::text
                                                   END ||
                                               CASE
                                                   WHEN crm.tableau_revenue_and_budget_dashboard IS TRUE
                                                       THEN ' - Tableau Revenue and Budget dashboard'::text
                                                   ELSE '/'::text
                                                   END) ||
                                           CASE
                                               WHEN crm.ppc_flag_32_forecast IS TRUE THEN ' - PPC Flag 32 Forecast'::text
                                               ELSE '/'::text
                                               END) ||
                                          CASE
                                              WHEN crm.tableau_serp_competition_dashboard IS TRUE
                                                  THEN ' - Tableau SERP competition dashboard'::text
                                              ELSE '/'::text
                                              END) ||
                                         CASE
                                             WHEN crm.tableau_conversion_dashboard IS TRUE
                                                 THEN ' - Tableau conversion dashboard'::text
                                             ELSE '/'::text
                                             END) ||
                                        CASE
                                            WHEN crm.tableau_cpc_segmentation IS TRUE THEN ' - Tableau CPC segmentation'::text
                                            ELSE '/'::text
                                            END) ||
                                       CASE
                                           WHEN crm.allow_affiliate_sources IS TRUE THEN ' - Allow Affiliate sources'::text
                                           ELSE '/'::text
                                           END) ||
                                      CASE
                                          WHEN crm.adding_more_paid_jobs IS TRUE THEN ' - Adding more paid jobs'::text
                                          ELSE '/'::text
                                          END) ||
                                     CASE
                                         WHEN crm.affiliate_flag_32_forecast IS TRUE
                                             THEN ' - Affiliate Flag 32 Forecast '::text
                                         ELSE '/'::text
                                         END) ||
                                    CASE
                                        WHEN crm.change_gap IS TRUE THEN ' - Change Gap'::text
                                        ELSE '/'::text
                                        END) ||
                                   CASE
                                       WHEN crm.add_new_affiliate_sources IS TRUE THEN ' - Add new Affiliate sources'::text
                                       ELSE '/'::text
                                       END) ||
                                  CASE
                                      WHEN crm.ppc_recommendation IS TRUE THEN ' - PPC recommendation'::text
                                      ELSE '/'::text
                                      END) ||
                                 CASE
                                     WHEN crm.add_new_ppc_sources IS TRUE THEN ' - Add new PPC sources'::text
                                     ELSE '/'::text
                                     END) ||
                                CASE
                                    WHEN crm.tableau_affiliate_client_cpc_analysis IS TRUE
                                        THEN ' - Tableau Affiliate Client CPC analysis'::text
                                    ELSE '/'::text
                                    END) ||
                               CASE
                                   WHEN crm.tableau_traffic_by_source_dashboard IS TRUE
                                       THEN ' - Tableau Traffic by source dashboard'::text
                                   ELSE '/'::text
                                   END) ||
                              CASE
                                  WHEN crm.catego IS TRUE THEN ' - Catego'::text
                                  ELSE '/'::text
                                  END) ||
                             CASE
                                 WHEN crm.ticket IS TRUE THEN ' - Ticket'::text
                                 ELSE '/'::text
                                 END) ||
                            CASE
                                WHEN crm.other IS TRUE THEN ' - Other'::text
                                ELSE '/'::text
                                END) ||
                           CASE
                               WHEN crm.auction_segmentation IS TRUE THEN ' - Auction segmentation'::text
                               ELSE '/'::text
                               END) ||
                          CASE
                              WHEN crm.auction_campaign_settings IS TRUE THEN ' - Auction campaign settings'::text
                              ELSE '/'::text
                              END) ||
                         CASE
                             WHEN crm.set_higher_cpc IS TRUE THEN ' - Set higher CPC'::text
                             ELSE '/'::text
                             END) ||
                        CASE
                            WHEN crm.set_lower_cpc_flag_32 IS TRUE THEN ' - Set lower CPC (Flag32)'::text
                            ELSE '/'::text
                            END) ||
                       CASE
                           WHEN crm.tech_documentation IS TRUE THEN ' - Tech documentation'::text
                           ELSE '/'::text
                           END) ||
                      CASE
                          WHEN crm.communication_with_client_specialist IS TRUE
                              THEN ' - Communication with clients tech specialist'::text
                          ELSE '/'::text
                          END) ||
                     CASE
                         WHEN crm.consultation_from_jooble IS TRUE THEN ' - Consultation from Jooble tech team'::text
                         ELSE '/'::text
                         END) ||
                    CASE
                        WHEN crm.cs_team_help IS TRUE THEN ' - CS team help'::text
                        ELSE '/'::text
                        END) ||
                   CASE
                       WHEN crm.tracking_methods_comparison IS TRUE THEN ' - Tracking methods comparison'::text
                       ELSE '/'::text
                       END) ||
                  CASE
                      WHEN crm.soska_cpc_in_similar_groups IS TRUE THEN ' - Soska CPC in similar groups'::text
                      ELSE '/'::text
                      END, '/'::text, ''::text)),
         (replace((((((((((((((
                                  CASE
                                      WHEN crm.no_additional_budget IS TRUE THEN ' - No additional budget'::text
                                      ELSE '/'::text
                                      END ||
                                  CASE
                                      WHEN crm.other_blockers IS TRUE THEN ' - Other blockers'::text
                                      ELSE '/'::text
                                      END) ||
                              CASE
                                  WHEN crm.cpa_too_expensive IS TRUE THEN ' - CPA too expensive'::text
                                  ELSE '/'::text
                                  END) ||
                             CASE
                                 WHEN crm.min_cpc_too_expensive IS TRUE THEN ' - Min CPC too expensive'::text
                                 ELSE '/'::text
                                 END) ||
                            CASE
                                WHEN crm.recommended_cpc_too_expensive IS TRUE THEN ' - Recommended CPC too expensive'::text
                                ELSE '/'::text
                                END) ||
                           CASE
                               WHEN crm.gap_change_not_approved IS TRUE THEN ' - Gap change not approved'::text
                               ELSE '/'::text
                               END) ||
                          CASE
                              WHEN crm.no_additional_paid_jobs_available IS TRUE
                                  THEN ' - No additional paid jobs available'::text
                              ELSE '/'::text
                              END) ||
                         CASE
                             WHEN crm.not_enough_traffic_from_internal_sources IS TRUE
                                 THEN ' - Not enough traffic from internal sources'::text
                             ELSE '/'::text
                             END) ||
                        CASE
                            WHEN crm.affiliate_is_forbidden IS TRUE THEN ' - Affiliate is forbidden'::text
                            ELSE '/'::text
                            END) ||
                       CASE
                           WHEN crm.lack_of_tech_team_free_resources IS TRUE THEN ' - Lack of tech team free resources'::text
                           ELSE '/'::text
                           END) ||
                      CASE
                          WHEN crm.client_not_know_howtodo IS TRUE THEN ' - Client does not know how to do it'::text
                          ELSE '/'::text
                          END) ||
                     CASE
                         WHEN crm.tracking_is_prohibited_by_policy IS TRUE THEN ' - Tracking is prohibited by policy'::text
                         ELSE '/'::text
                         END) ||
                    CASE
                        WHEN crm.legal_gdpr IS TRUE THEN ' - Legal_GDPR'::text
                        ELSE '/'::text
                        END) ||
                   CASE
                       WHEN crm.blocker_competitor IS TRUE THEN ' - Client-competitor doesnt want to help us'::text
                       ELSE '/'::text
                       END) ||
                  CASE
                      WHEN crm.without_reason IS TRUE THEN ' - Without reason'::text
                      ELSE '/'::text
                      END, '/'::text, ''::text)), stlog.date_stg_start, stlog.date_stg_end, away_percent.aways_per_pr,
         away_percent.aways_cc, crm.lack_of_tech_team_free_resources, crm.client_not_know_howtodo,
         crm.tracking_is_prohibited_by_policy, crm.legal_gdpr, crm.blocker_competitor, crm.tracking_methods_comparison,
         crm.cs_team_help, crm.consultation_from_jooble, crm.communication_with_client_specialist,
         crm.tech_documentation, crm.client_switched_to_our_other_client, crm.refuses_to_pay, crm.paused_due_to_overdue_payments,
         crm.low_traffic_volumes, crm.no_marketing_budget, crm.no_budget_for_jooble, crm.no_client_for_jooble,
         crm.minimal_budget_is_too_high, crm.switched_to_pja, crm.policy_change, crm.change_in_the_company,
         crm.refuses_to_communicate_details, crm.joobles_decision, crm.technical_issues
         ;


