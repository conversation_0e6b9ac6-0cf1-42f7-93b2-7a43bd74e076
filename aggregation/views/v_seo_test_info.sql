create view aggregation.v_seo_test_info
            (test_num, iteration, test_country, start_date, end_date, test_status, running_days) as
SELECT gr.test_number AS test_num,
       gr.iteration,
       gr.country     AS test_country,
       gr.start_date,
       gr.end_date,
       CASE
           WHEN gr.end_date IS NULL AND gr.start_date <= CURRENT_DATE THEN 'is active'::text
           WHEN gr.end_date IS NULL AND gr.start_date IS NULL THEN 'under checking'::text
           ELSE 'is stopped'::text
           END        AS test_status,
       CASE
           WHEN gr.end_date IS NOT NULL THEN gr.end_date - gr.start_date
           WHEN gr.end_date IS NULL AND gr.start_date <= CURRENT_DATE THEN CURRENT_DATE - gr.start_date
           ELSE NULL::integer
           END        AS running_days
FROM aggregation.seo_test_groups gr
GROUP BY gr.test_number, gr.iteration, gr.country, gr.start_date, gr.end_date;

alter table aggregation.v_seo_test_info
    owner to ono;

grant select on aggregation.v_seo_test_info to ypr;

grant select on aggregation.v_seo_test_info to vnov;

