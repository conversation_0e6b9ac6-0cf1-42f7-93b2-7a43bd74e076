create or replace view aggregation.v_job_categories
            (country_name, date, job_project_id, job_project_name, child_category_name, parent_category_name,
             is_job_ad_exchange_project, away_type, revenue_usd, away_cnt, away_conversion_cnt, conversion_revenue_usd,
             conversion_cnt, paid_job_count, organic_job_count, avg_cpc_for_paid_job_count_usd)
as
SELECT countries.name_country_eng                                                                                  AS country_name,
       fn_get_timestamp_from_date_diff(jdp_away_clicks_agg.action_datediff)                                        AS date,
       jdp_away_clicks_agg.project_id                                                                              AS job_project_id,
       info_project.name                                                                                           AS job_project_name,
       COALESCE(job_kaiju_category.child_name, 'Other'::text)                                                      AS child_category_name,
       COALESCE(COALESCE(v_job_kaiju_category.parent_category_name, job_kaiju_category.child_name),
                'Other'::text)                                                                                     AS parent_category_name,
       CASE
           WHEN info_project.hide_in_search THEN 1
           ELSE 0
       END                                                                                                         AS is_job_ad_exchange_project,
       jdp_away_clicks_agg.away_type,
       jdp_away_clicks_agg.revenue_usd,
       jdp_away_clicks_agg.jdp_away_count                                                                          AS away_cnt,
       CASE
           WHEN jdp_away_clicks_agg.action_datediff >= start_conversion.start_conv_datediff
               THEN jdp_away_clicks_agg.jdp_away_count
           ELSE NULL::integer
       END                                                                                                         AS away_conversion_cnt,
       CASE
           WHEN jdp_away_clicks_agg.action_datediff >= start_conversion.start_conv_datediff
               THEN jdp_away_clicks_agg.revenue_usd
           ELSE NULL::numeric
       END                                                                                                         AS conversion_revenue_usd,
       jdp_away_clicks_agg.conversions                                                                             AS conversion_cnt,
       NULL::integer                                                                                               AS paid_job_count,
       NULL::integer                                                                                               AS organic_job_count,
       NULL::numeric                                                                                               AS avg_cpc_for_paid_job_count_usd
FROM aggregation.jdp_away_clicks_agg
         JOIN dimension.countries
              ON jdp_away_clicks_agg.country_id = countries.id
         LEFT JOIN dimension.info_project
                   ON jdp_away_clicks_agg.country_id = info_project.country AND
                      jdp_away_clicks_agg.project_id = info_project.id
         LEFT JOIN (SELECT jdp_away_clicks_agg_1.country_id,
                           jdp_away_clicks_agg_1.project_id,
                           MIN(jdp_away_clicks_agg_1.action_datediff) AS start_conv_datediff
                    FROM aggregation.jdp_away_clicks_agg jdp_away_clicks_agg_1
                    WHERE jdp_away_clicks_agg_1.conversions > 0
                      AND fn_get_timestamp_from_date_diff(jdp_away_clicks_agg_1.action_datediff) >= (CURRENT_DATE - 30)
                    GROUP BY jdp_away_clicks_agg_1.country_id, jdp_away_clicks_agg_1.project_id) start_conversion
                   ON jdp_away_clicks_agg.country_id = start_conversion.country_id AND
                      jdp_away_clicks_agg.project_id = start_conversion.project_id
         LEFT JOIN (SELECT job_kaiju_category_1.id_child,
                           job_kaiju_category_1.child_name
                    FROM dimension.job_kaiju_category job_kaiju_category_1
                    UNION
                    SELECT job_kaiju_category_1.id_parent,
                           job_kaiju_category_1.parent_name
                    FROM dimension.job_kaiju_category job_kaiju_category_1) job_kaiju_category
                   ON jdp_away_clicks_agg.job_category_id = job_kaiju_category.id_child
         LEFT JOIN aggregation.v_job_kaiju_category
                   ON job_kaiju_category.id_child = v_job_kaiju_category.child_category_id
WHERE fn_get_timestamp_from_date_diff(jdp_away_clicks_agg.action_datediff) >= (CURRENT_DATE - 30)
  AND (jdp_away_clicks_agg.country_id = ANY (ARRAY [1, 2, 3, 4, 5, 6, 9, 10, 11, 12, 13, 14, 16, 17, 19, 20, 51]))
UNION ALL
SELECT countries.name_country_eng                                                                                  AS country_name,
       jobs_stat_daily.date::date                                                                                  AS date,
       jobs_stat_daily.id_project                                                                                  AS job_project_id,
       info_project.name                                                                                           AS job_project_name,
       COALESCE(job_kaiju_category.child_name, 'Other'::text)                                                      AS child_category_name,
       COALESCE(COALESCE(v_job_kaiju_category.parent_category_name, job_kaiju_category.child_name),
                'Other'::text)                                                                                     AS parent_category_name,
       CASE
           WHEN info_project.hide_in_search THEN 1
           ELSE 0
       END                                                                                                         AS is_job_ad_exchange_project,
       NULL::character varying                                                                                     AS away_type,
       NULL::numeric                                                                                               AS revenue_usd,
       NULL::integer                                                                                               AS away_cnt,
       NULL::integer                                                                                               AS away_conversion_cnt,
       NULL::numeric                                                                                               AS conversion_revenue_usd,
       NULL::integer                                                                                               AS conversion_cnt,
       jobs_stat_daily.paid_job_count,
       jobs_stat_daily.organic_job_count,
       jobs_stat_daily.avg_cpc_for_paid_job_count_usd
FROM aggregation.jobs_stat_daily
         JOIN dimension.countries
              ON jobs_stat_daily.id_country = countries.id
         LEFT JOIN dimension.info_project
                   ON jobs_stat_daily.id_country = info_project.country AND jobs_stat_daily.id_project = info_project.id
         LEFT JOIN (SELECT job_kaiju_category_1.id_child,
                           job_kaiju_category_1.child_name
                    FROM dimension.job_kaiju_category job_kaiju_category_1
                    UNION
                    SELECT job_kaiju_category_1.id_parent,
                           job_kaiju_category_1.parent_name
                    FROM dimension.job_kaiju_category job_kaiju_category_1) job_kaiju_category
                   ON jobs_stat_daily.job_category_id = job_kaiju_category.id_child
         LEFT JOIN aggregation.v_job_kaiju_category
                   ON job_kaiju_category.id_child = v_job_kaiju_category.child_category_id
WHERE jobs_stat_daily.date::date >= (CURRENT_DATE - 30)
  AND (jobs_stat_daily.id_country = ANY (ARRAY [1, 2, 3, 4, 5, 6, 9, 10, 11, 12, 13, 14, 16, 17, 19, 20, 51]));

alter table aggregation.v_job_categories
    owner to ono;
