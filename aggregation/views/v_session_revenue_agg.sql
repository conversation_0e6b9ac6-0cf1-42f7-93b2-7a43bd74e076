create view aggregation.v_session_revenue_agg
            (country, action_date, metric_name, metric_value, first_traffic_source, first_traffic_channel,
             current_traffic_source, current_traffic_channel, is_returned, is_mobile, is_local,
             session_create_page_type)
as
SELECT countries.name_country_eng                                          AS country,
       fn_get_timestamp_from_date_diff(session_abtest_agg.action_datediff) AS action_date,
       'session_cnt'::text                                                 AS metric_name,
       session_abtest_agg.metric_value,
       u_traffic_source.name                                               AS first_traffic_source,
       u_traffic_source.channel                                            AS first_traffic_channel,
       current_u_traffic_source.name                                       AS current_traffic_source,
       current_u_traffic_source.channel                                    AS current_traffic_channel,
       session_abtest_agg.is_returned,
       session_abtest_agg.device_type_id                                   AS is_mobile,
       session_abtest_agg.is_local,
       session_abtest_agg.session_create_page_type
FROM aggregation.session_abtest_agg
         JOIN dimension.countries ON session_abtest_agg.country_id = countries.id
         LEFT JOIN dimension.u_traffic_source ON session_abtest_agg.country_id = u_traffic_source.country AND
                                                 session_abtest_agg.traffic_source_id = u_traffic_source.id
         LEFT JOIN dimension.u_traffic_source current_u_traffic_source
                   ON session_abtest_agg.country_id = current_u_traffic_source.country AND
                      session_abtest_agg.current_traffic_source_id = current_u_traffic_source.id
WHERE session_abtest_agg.metric_name::text = 'session_cnt'::text
  AND fn_get_timestamp_from_date_diff(session_abtest_agg.action_datediff) >=
      '2021-10-20 00:00:00'::timestamp without time zone
UNION ALL
SELECT countries.name_country_eng                                          AS country,
       fn_get_timestamp_from_date_diff(session_abtest_agg.action_datediff) AS action_date,
       'revenue'::text                                                     AS metric_name,
       session_abtest_agg.metric_value,
       u_traffic_source.name                                               AS first_traffic_source,
       u_traffic_source.channel                                            AS first_traffic_channel,
       current_u_traffic_source.name                                       AS current_traffic_source,
       current_u_traffic_source.channel                                    AS current_traffic_channel,
       session_abtest_agg.is_returned,
       session_abtest_agg.device_type_id                                   AS is_mobile,
       session_abtest_agg.is_local,
       session_abtest_agg.session_create_page_type
FROM aggregation.session_abtest_agg
         JOIN dimension.countries ON session_abtest_agg.country_id = countries.id
         LEFT JOIN dimension.u_traffic_source ON session_abtest_agg.country_id = u_traffic_source.country AND
                                                 session_abtest_agg.traffic_source_id = u_traffic_source.id
         LEFT JOIN dimension.u_traffic_source current_u_traffic_source
                   ON session_abtest_agg.country_id = current_u_traffic_source.country AND
                      session_abtest_agg.current_traffic_source_id = current_u_traffic_source.id
WHERE session_abtest_agg.metric_name::text = 'revenue'::text
  AND fn_get_timestamp_from_date_diff(session_abtest_agg.action_datediff) >=
      '2021-10-20 00:00:00'::timestamp without time zone
UNION ALL
SELECT dc.name_country_eng     AS country,
       vga.date                AS action_date,
       'ga_session_cnt'::text  AS metric_name,
       vga.session_cnt         AS metric_value,
       vga.channelgrouping     AS first_traffic_source,
       vga.sourcemedium        AS first_traffic_channel,
       NULL::character varying AS current_traffic_source,
       NULL::character varying AS current_traffic_channel,
       NULL::integer           AS is_returned,
       CASE
           WHEN vga.is_mobile = true THEN 1
           ELSE 0
           END                 AS is_mobile,
       CASE
           WHEN vga.is_local = true THEN 1
           ELSE 0
           END                 AS is_local,
       0                       AS session_create_page_type
FROM aggregation.v_ga_session_data vga
         JOIN dimension.countries dc ON lower(dc.alpha_2::text) = vga.country_code
WHERE vga.date >= '2021-10-20'::date;

alter table aggregation.v_session_revenue_agg
    owner to ono;

grant select on aggregation.v_session_revenue_agg to readonly;

grant delete, insert, select, update on aggregation.v_session_revenue_agg to write_ono;

grant select on aggregation.v_session_revenue_agg to readonly_aggregation;

grant select on aggregation.v_session_revenue_agg to "pavlo.kvasnii";

