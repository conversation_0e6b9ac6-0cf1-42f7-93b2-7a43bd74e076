create or replace view aggregation.v_budget_and_revenue
            (date, id_user, nonlocal_traffic_is, client_buying_model, is_force_jdp, country, id_project, site,
             is_unlim_cmp, session_cnt, click_cnt, organic_click_cnt, paid_click_cnt, revenue_usd, organic_revenue_usd,
             paid_revenue_usd, potential_revenue_usd, job_cnt, paid_overflow_cnt, user_budget_month_usd,
             campaign_budget_month_usd, revenue_budget_diff, potential_revenue_budget_diff, sale_manager, currency,
             currency_to_usd, organic_job_count, paid_job_count, test_click_cnt, test_click_revenue_usd,
             flag_32_jobs_count, contractor, is_job_ad_exchange_project, first_sale_manager, in_affiliate_ban,
             external_coefficient, dublicated_click_revenue_usd, is_daily_cmp_budget, is_price_per_job, campaign_cnt,
             campaign_with_budget_cnt, min_cpc_job_count, max_cpc_job_count, daily_budget_mark, affiliate_revenue,
             client_type, budget, avg_cpc_for_paid_job_count_usd, free_click_cnt, not_free_click_cnt)
as
WITH jobs_stat_daily as (

                   SELECT jobs_stat_daily_1.id_country,
                           cast(jobs_stat_daily_1.date as date) as date,
                           jobs_stat_daily_1.id_project,
                           sum(jobs_stat_daily_1.paid_job_count)         AS paid_job_count,
                           CASE
                               WHEN sum(jobs_stat_daily_1.paid_job_count) = 0 THEN 0::numeric
                               ELSE sum(jobs_stat_daily_1.paid_job_count::numeric *
                                        jobs_stat_daily_1.avg_cpc_for_paid_job_count_usd) /
                                    sum(jobs_stat_daily_1.paid_job_count)::numeric
                               END                                       AS avg_cpc_for_paid_job_count_usd,
                           sum(jobs_stat_daily_1.organic_job_count)      AS organic_job_count,
                           sum(jobs_stat_daily_1.flag_32_jobs_count)     AS flag_32_jobs_count,
                           max(
                                   CASE
                                       WHEN jobs_stat_daily_1.campaign_daily_budget IS NOT NULL THEN 1
                                       ELSE 0
                                       END)                              AS is_daily_cmp_budget,
                           max(jobs_stat_daily_1.is_price_per_job::text) AS is_price_per_job,
                           sum(jobs_stat_daily_1.min_cpc_job_count)      AS min_cpc_job_count,
                           sum(jobs_stat_daily_1.max_cpc_job_count)      AS max_cpc_job_count
                    FROM aggregation.jobs_stat_daily jobs_stat_daily_1
                    WHERE jobs_stat_daily_1.date::date <= '2023-11-26'
                    GROUP BY jobs_stat_daily_1.id_country, jobs_stat_daily_1.date,
                             jobs_stat_daily_1.id_project

union all

Select   id_country,
                            date,
                            id_project,
                            paid_job_count,
                            avg_cpc_for_paid_job_count_usd,
                            organic_job_count,
                            flag_32_jobs_count,
                            is_daily_cmp_budget,
                            is_price_per_job,
                            min_cpc_job_count,
                            max_cpc_job_count
                   from (Select *,
                                row_number()
                                over (partition by id_country, id_project, date order by paid_job_count desc) as num
                         from (SELECT jobs_stat_daily_1.id_country,
                                      jobs_stat_daily_1.date                        as date_time,
                                      cast(jobs_stat_daily_1.date as date)          as date,
                                      jobs_stat_daily_1.id_project,
                                      sum(jobs_stat_daily_1.paid_job_count)         AS paid_job_count,
                                      CASE
                                          WHEN sum(jobs_stat_daily_1.paid_job_count) = 0 THEN 0::numeric
                                          ELSE sum(jobs_stat_daily_1.paid_job_count::numeric *
                                                   jobs_stat_daily_1.avg_cpc_for_paid_job_count_usd) /
                                               sum(jobs_stat_daily_1.paid_job_count)::numeric
                                          END                                       AS avg_cpc_for_paid_job_count_usd,
                                      sum(jobs_stat_daily_1.organic_job_count)      AS organic_job_count,
                                      sum(jobs_stat_daily_1.flag_32_jobs_count)     AS flag_32_jobs_count,
                                      max(
                                              CASE
                                                  WHEN jobs_stat_daily_1.campaign_daily_budget IS NOT NULL THEN 1
                                                  ELSE 0
                                                  END)                              AS is_daily_cmp_budget,
                                      max(jobs_stat_daily_1.is_price_per_job::text) AS is_price_per_job,
                                      sum(jobs_stat_daily_1.min_cpc_job_count)      AS min_cpc_job_count,
                                      sum(jobs_stat_daily_1.max_cpc_job_count)      AS max_cpc_job_count
                               FROM aggregation.jobs_stat_hourly jobs_stat_daily_1
                               WHERE jobs_stat_daily_1.date::date <=
                                     ((SELECT max(budget_revenue_daily_agg_1.action_date) AS max
                                       FROM aggregation.budget_revenue_daily_agg budget_revenue_daily_agg_1))
                               GROUP BY jobs_stat_daily_1.id_country,
                                        jobs_stat_daily_1.date,
                                        jobs_stat_daily_1.id_project) hourly) row
                   where num = 1

                   ),

    budget_revenue_daily_agg AS (SELECT budget_revenue_daily_agg_1.country_id,
                                         budget_revenue_daily_agg_1.action_date,
                                         budget_revenue_daily_agg_1.user_id,
                                         budget_revenue_daily_agg_1.country_code,
                                         budget_revenue_daily_agg_1.project_id,
                                         budget_revenue_daily_agg_1.site_url,
                                         max(budget_revenue_daily_agg_1.is_unlim_cmp)                           AS is_unlim_cmp,
                                         sum(budget_revenue_daily_agg_1.session_cnt)                            AS session_cnt,
                                         sum(budget_revenue_daily_agg_1.click_cnt)                              AS click_cnt,
                                         sum(budget_revenue_daily_agg_1.organic_click_cnt)                      AS organic_click_cnt,
                                         sum(budget_revenue_daily_agg_1.paid_click_cnt)                         AS paid_click_cnt,
                                         sum(
                                                 CASE
                                                     WHEN budget_revenue_daily_agg_1.action_date < '2023-04-28'::date
                                                         THEN COALESCE(budget_revenue_daily_agg_1.revenue_usd,
                                                                       0::double precision) - COALESCE(
                                                                      budget_revenue_daily_agg_1.dublicated_click_revenue_usd,
                                                                      0::numeric)::double precision
                                                     ELSE budget_revenue_daily_agg_1.revenue_usd
                                                     END)                                                       AS revenue_usd,
                                         sum(budget_revenue_daily_agg_1.organic_revenue_usd -
                                             COALESCE(budget_revenue_daily_agg_1.organic_revenue_usd *
                                                      CASE
                                                          WHEN NULLIF(budget_revenue_daily_agg_1.revenue_usd, 0::double precision) =
                                                               0::double precision THEN 0::double precision
                                                          ELSE budget_revenue_daily_agg_1.dublicated_click_revenue_usd::double precision /
                                                               NULLIF(budget_revenue_daily_agg_1.revenue_usd, 0::double precision)
                                                          END,
                                                      0::double precision))                                     AS organic_revenue_usd,
                                         sum(budget_revenue_daily_agg_1.paid_revenue_usd -
                                             COALESCE(budget_revenue_daily_agg_1.paid_revenue_usd *
                                                      CASE
                                                          WHEN NULLIF(budget_revenue_daily_agg_1.revenue_usd, 0::double precision) =
                                                               0::double precision THEN 0::double precision
                                                          ELSE budget_revenue_daily_agg_1.dublicated_click_revenue_usd::double precision /
                                                               NULLIF(budget_revenue_daily_agg_1.revenue_usd, 0::double precision)
                                                          END,
                                                      0::double precision))                                     AS paid_revenue_usd,
                                         sum(budget_revenue_daily_agg_1.revenue_usd -
                                             CASE
                                                 WHEN budget_revenue_daily_agg_1.action_date < '2023-04-28'::date
                                                     THEN COALESCE(
                                                         budget_revenue_daily_agg_1.dublicated_click_revenue_usd,
                                                         0::numeric)::double precision
                                                 ELSE 0::double precision
                                                 END + (budget_revenue_daily_agg_1.revenue_usd -
                                                        CASE
                                                            WHEN budget_revenue_daily_agg_1.action_date < '2023-04-28'::date
                                                                THEN COALESCE(
                                                                    budget_revenue_daily_agg_1.dublicated_click_revenue_usd,
                                                                    0::numeric)::double precision
                                                            ELSE 0::double precision
                                                            END) / date_part('day'::text, CURRENT_DATE - 1) *
                                                       (make_date(
                                                                date_part('year'::text, CURRENT_DATE - 1 + '1 mon'::interval)::integer,
                                                                date_part('month'::text, CURRENT_DATE - 1 + '1 mon'::interval)::integer,
                                                                1) - (CURRENT_DATE - 1) -
                                                        1)::double precision)                                   AS potential_revenue_usd,
                                         sum(budget_revenue_daily_agg_1.job_cnt)                                AS job_cnt,
                                         sum(budget_revenue_daily_agg_1.paid_overflow_cnt)                      AS paid_overflow_cnt,
                                         max(budget_revenue_daily_agg_1.user_budget_month_usd)                  AS user_budget_month_usd,
                                         sum(COALESCE(budget_revenue_daily_agg_1.campaign_budget_month_usd,
                                                      0::double precision))                                     AS campaign_budget_month_usd,
                                         sum(budget_revenue_daily_agg_1.revenue_budget_diff)                    AS revenue_budget_diff,
                                         sum(budget_revenue_daily_agg_1.potential_revenue_budget_diff)          AS potential_revenue_budget_diff,
                                         sum(budget_revenue_daily_agg_1.test_click_cnt)                         AS test_click_cnt,
                                         sum(
                                                 CASE
                                                     WHEN budget_revenue_daily_agg_1.action_date <= '2023-09-01'::date
                                                         THEN
                                                             budget_revenue_daily_agg_1.test_click_revenue_usd::double precision -
                                                             budget_revenue_daily_agg_1.revenue_usd
                                                     ELSE budget_revenue_daily_agg_1.test_click_revenue_usd::double precision
                                                     END)::numeric                                              AS test_click_revenue_usd,
                                         max(budget_revenue_daily_agg_1.external_coefficient::text)             AS external_coefficient,
                                         sum(budget_revenue_daily_agg_1.dublicated_click_revenue_usd)           AS dublicated_click_revenue_usd,
                                         count(DISTINCT budget_revenue_daily_agg_1.id_campaign)                 AS campaign_cnt,
                                         count(DISTINCT
                                               CASE
                                                   WHEN COALESCE(budget_revenue_daily_agg_1.campaign_budget_month_usd,
                                                                 0::double precision) > 0::double precision
                                                       THEN budget_revenue_daily_agg_1.id_campaign
                                                   ELSE NULL::character varying
                                                   END)                                                         AS campaign_with_budget_cnt,
                                         max(
                                                 CASE
                                                     WHEN budget_revenue_daily_agg_1.is_cmp_with_daily_budget = 1 AND
                                                          budget_revenue_daily_agg_1.is_user_with_daily_budget = 1
                                                         THEN 3
                                                     WHEN budget_revenue_daily_agg_1.is_cmp_with_daily_budget = 1 THEN 1
                                                     WHEN budget_revenue_daily_agg_1.is_user_with_daily_budget = 1
                                                         THEN 2
                                                     ELSE 0
                                                     END)                                                       AS daily_budget_mark,
                                         sum(budget_revenue_daily_agg_1.affiliate_revenue)                      AS affiliate_revenue,
                                         sum(budget_revenue_daily_agg_1.free_clicks)                            AS free_click_cnt,
                                         sum(budget_revenue_daily_agg_1.paid_clicks)                            AS not_free_click_cnt
                                  FROM aggregation.budget_revenue_daily_agg budget_revenue_daily_agg_1
                                  WHERE budget_revenue_daily_agg_1.action_date >= '2022-01-01'::date
                                  GROUP BY budget_revenue_daily_agg_1.action_date, budget_revenue_daily_agg_1.user_id,
                                           budget_revenue_daily_agg_1.country_code,
                                           budget_revenue_daily_agg_1.project_id, budget_revenue_daily_agg_1.site_url,
                                           budget_revenue_daily_agg_1.country_id)
SELECT COALESCE(budget_revenue_daily_agg.action_date, jobs_stat_daily.date::date)        AS date,
       budget_revenue_daily_agg.user_id                                                  AS id_user,
       COALESCE(olpc.allow_nonlocal_traffic, 'allowed'::character varying::text)         AS nonlocal_traffic_is,
       CASE
           WHEN (auction_user.flags & 65536) = 65536 THEN 'external_CPA'::text
           WHEN (auction_user.flags & 2) = 2 THEN 'external_CPC'::text
           ELSE 'internal_CPC'::text
           END                                                                           AS client_buying_model,
       CASE
           WHEN ip.is_active = 1 AND (ip.jdp_flags::integer & 8) = 8 OR
                ip.is_active = 0 AND ip.is_daily_run = 1 AND (ip.jdp_flags::integer & 8) = 8 THEN 1
           ELSE 0
           END                                                                           AS is_force_jdp,
       COALESCE(budget_revenue_daily_agg.country_code, countries.alpha_2)                AS country,
       COALESCE(budget_revenue_daily_agg.project_id, jobs_stat_daily.id_project::bigint) AS id_project,
       budget_revenue_daily_agg.site_url                                                 AS site,
       budget_revenue_daily_agg.is_unlim_cmp,
       budget_revenue_daily_agg.session_cnt,
       budget_revenue_daily_agg.click_cnt,
       budget_revenue_daily_agg.organic_click_cnt,
       budget_revenue_daily_agg.paid_click_cnt,
       budget_revenue_daily_agg.revenue_usd,
       budget_revenue_daily_agg.organic_revenue_usd,
       budget_revenue_daily_agg.paid_revenue_usd,
       budget_revenue_daily_agg.potential_revenue_usd,
       budget_revenue_daily_agg.job_cnt,
       budget_revenue_daily_agg.paid_overflow_cnt,
       budget_revenue_daily_agg.user_budget_month_usd,
       budget_revenue_daily_agg.campaign_budget_month_usd,
       budget_revenue_daily_agg.revenue_budget_diff,
       budget_revenue_daily_agg.potential_revenue_budget_diff,
       v_sale_manager.sale_manager,
       info_currency.name                                                                AS currency,
       (SELECT info_currency_history.value_to_usd
        FROM dimension.info_currency_history
        WHERE info_currency_history.country = budget_revenue_daily_agg.country_id
          AND info_currency_history.date <= budget_revenue_daily_agg.action_date
          AND info_currency_history.id_currency = info_currency.id
        ORDER BY info_currency_history.date DESC
        LIMIT 1)                                                                         AS currency_to_usd,
       jobs_stat_daily.organic_job_count::bigint,
       jobs_stat_daily.paid_job_count::bigint,
       budget_revenue_daily_agg.test_click_cnt,
       budget_revenue_daily_agg.test_click_revenue_usd,
       jobs_stat_daily.flag_32_jobs_count::bigint,
       auction_user.company                                                              AS contractor,
       CASE
           WHEN ip.hide_in_search THEN 1
           ELSE 0
           END                                                                           AS is_job_ad_exchange_project,
       concat(fsm.manager_alias, ' ', fsm.manager_email)                                 AS first_sale_manager,
       CASE
           WHEN general_feed_ignored_projects.id_project IS NOT NULL THEN 1
           ELSE 0
           END                                                                           AS in_affiliate_ban,
       budget_revenue_daily_agg.external_coefficient,
       budget_revenue_daily_agg.dublicated_click_revenue_usd,
       COALESCE(jobs_stat_daily.is_daily_cmp_budget, 0)                                  AS is_daily_cmp_budget,
       COALESCE(jobs_stat_daily.is_price_per_job, 'false'::text)                         AS is_price_per_job,
       budget_revenue_daily_agg.campaign_cnt,
       budget_revenue_daily_agg.campaign_with_budget_cnt,
       jobs_stat_daily.min_cpc_job_count,
       jobs_stat_daily.max_cpc_job_count,
       CASE
           WHEN budget_revenue_daily_agg.daily_budget_mark = 3 THEN 'User and campaign daily budgets'::text
           WHEN budget_revenue_daily_agg.daily_budget_mark = 1 THEN 'Campaign daily budget'::text
           WHEN budget_revenue_daily_agg.daily_budget_mark = 2 THEN 'User daily budget'::text
           ELSE 'Without daily budget'::text
           END                                                                           AS daily_budget_mark,
       budget_revenue_daily_agg.affiliate_revenue,
       dic_info_project_source_type.name                                                 AS client_type,
       /*CASE
           WHEN budget_revenue_daily_agg.campaign_cnt = budget_revenue_daily_agg.campaign_with_budget_cnt AND
                budget_revenue_daily_agg.campaign_budget_month_usd < budget_revenue_daily_agg.user_budget_month_usd
               THEN budget_revenue_daily_agg.campaign_budget_month_usd
           WHEN budget_revenue_daily_agg.campaign_budget_month_usd > 0::double precision AND
                budget_revenue_daily_agg.campaign_budget_month_usd < budget_revenue_daily_agg.user_budget_month_usd
               THEN budget_revenue_daily_agg.campaign_budget_month_usd
           WHEN budget_revenue_daily_agg.is_unlim_cmp = 1 AND
                budget_revenue_daily_agg.user_budget_month_usd > budget_revenue_daily_agg.campaign_budget_month_usd
               THEN budget_revenue_daily_agg.user_budget_month_usd
           WHEN budget_revenue_daily_agg.user_budget_month_usd = 0::double precision
               THEN budget_revenue_daily_agg.campaign_budget_month_usd
           ELSE budget_revenue_daily_agg.user_budget_month_usd
           END                                                                           AS budget,*/
        case
                   when campaign_cnt = campaign_with_budget_cnt and campaign_budget_month_usd < user_budget_month_usd
                       then campaign_budget_month_usd
                   when is_unlim_cmp = 1 and user_budget_month_usd > campaign_budget_month_usd
                       then user_budget_month_usd
                   when user_budget_month_usd = 0 then campaign_budget_month_usd
                   else user_budget_month_usd end                                                            AS budget,    
       jobs_stat_daily.avg_cpc_for_paid_job_count_usd::numeric,
       budget_revenue_daily_agg.free_click_cnt,
       budget_revenue_daily_agg.not_free_click_cnt
FROM budget_revenue_daily_agg
         LEFT JOIN aggregation.v_sale_manager ON budget_revenue_daily_agg.country_id = v_sale_manager.country AND
                                                 budget_revenue_daily_agg.project_id = v_sale_manager.id_project
         LEFT JOIN imp.auction_user ON budget_revenue_daily_agg.country_id = auction_user.country AND
                                       budget_revenue_daily_agg.user_id = auction_user.id
         LEFT JOIN dimension.info_currency
                   ON auction_user.country = info_currency.country AND auction_user.currency = info_currency.id
         FULL JOIN  jobs_stat_daily
                   ON budget_revenue_daily_agg.action_date = jobs_stat_daily.date::date AND
                      budget_revenue_daily_agg.country_id = jobs_stat_daily.id_country AND
                      budget_revenue_daily_agg.project_id = jobs_stat_daily.id_project
         LEFT JOIN dimension.countries ON jobs_stat_daily.id_country = countries.id
         LEFT JOIN (SELECT cobra_info_projects_nonlocal.request_date,
                           cobra_info_projects_nonlocal.country_code,
                           cobra_info_projects_nonlocal.project_id,
                           min(cobra_info_projects_nonlocal.allow_nonlocal_traffic::text) AS allow_nonlocal_traffic
                    FROM imp_api.cobra_info_projects_nonlocal
                    GROUP BY cobra_info_projects_nonlocal.request_date, cobra_info_projects_nonlocal.country_code,
                             cobra_info_projects_nonlocal.project_id) olpc
                   ON olpc.request_date = COALESCE(budget_revenue_daily_agg.action_date, jobs_stat_daily.date::date) AND
                      olpc.country_code::text =
                      lower(COALESCE(budget_revenue_daily_agg.country_code, countries.alpha_2)::text) AND
                      olpc.project_id =
                      COALESCE(budget_revenue_daily_agg.project_id, jobs_stat_daily.id_project::bigint)
         LEFT JOIN dimension.info_project ip
                   ON ip.country = COALESCE(budget_revenue_daily_agg.country_id::integer, countries.id) AND
                      ip.id = COALESCE(budget_revenue_daily_agg.project_id, jobs_stat_daily.id_project::bigint)
         LEFT JOIN aggregation.first_site_manager fsm
                   ON fsm.country::text = budget_revenue_daily_agg.country_code::text AND
                      fsm.id_project = budget_revenue_daily_agg.project_id AND
                      fsm.id_user = budget_revenue_daily_agg.user_id
         LEFT JOIN imp.general_feed_ignored_projects ON general_feed_ignored_projects.country_id =
                                                        COALESCE(budget_revenue_daily_agg.country_id::integer,
                                                                 jobs_stat_daily.id_country) AND
                                                        general_feed_ignored_projects.id_project =
                                                        COALESCE(budget_revenue_daily_agg.project_id,
                                                                 jobs_stat_daily.id_project::bigint)
         LEFT JOIN aggregation.dic_info_project_source_type
                   ON ip.source_type = dic_info_project_source_type.search_type;

alter table aggregation.v_budget_and_revenue
    owner to ono;

grant select on aggregation.v_budget_and_revenue to readonly;

grant select on aggregation.v_budget_and_revenue to ypr;

grant select on aggregation.v_budget_and_revenue to writeonly_pyscripts;

grant select on aggregation.v_budget_and_revenue to user_agg_team;

grant select on aggregation.v_budget_and_revenue to vnov;

grant select on aggregation.v_budget_and_revenue to "pavlo.kvasnii";

grant select on aggregation.v_budget_and_revenue to oleksandra_yankovska;

