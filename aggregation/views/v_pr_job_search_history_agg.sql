create or replace view aggregation.v_pr_job_search_history_agg
            (country, year_month, id_region, region_name, region_type, order_value, q_kw, top_num, title, salary_val1,
             salary_val2, currency, id_salary_rate, metric, metric_type, jobs_cnt, value)
as
WITH union_1 AS (
    SELECT lower(cc.alpha_2::text) AS country,
           vjha.year_month,
           vjha.region_id          AS id_region,
           ir.name                 AS region_name,
           CASE
               WHEN vjha.region_id IS NOT NULL AND ir.is_city = 0 THEN 'region_district'::text
               WHEN vjha.region_id IS NOT NULL AND ir.is_city = 1 THEN 'city_town'::text
               ELSE NULL::text
               END                 AS region_type,
           CASE
               WHEN vjha.region_id IS NOT NULL THEN ir.order_value
               ELSE NULL::integer
               END                 AS order_value,
           NULL::text              AS q_kw,
           vjha.top_num,
           vjha.title,
           vjha.salary_val1,
           vjha.salary_val2,
           icu.name                AS currency,
           vjha.id_salary_rate,
           vjha.metric,
           vjha.metric_type,
           vjha.jobs_cnt,
           vjha.unique_jobs_cnt    AS value
    FROM aggregation.vacancy_job_history_agg_new vjha
             LEFT JOIN dimension.countries cc ON cc.id = vjha.country_code
             LEFT JOIN dimension.info_region_other ir ON cc.id = ir.country AND vjha.region_id = ir.id
             LEFT JOIN dimension.info_currency icu ON cc.id = icu.country AND vjha.currency_id = icu.id
    WHERE vjha.metric_type <> 'category_id'::text
    UNION ALL
    SELECT lower(cc.alpha_2::text)       AS country,
           vjha.year_month,
           NULL::integer                 AS id_region,
           NULL::character varying       AS region_name,
           NULL::text                    AS region_type,
           NULL::integer                 AS order_value,
           category.child_category_name  AS q_kw,
           vjha.top_num,
           category.parent_category_name AS title,
           vjha.salary_val1,
           vjha.salary_val2,
           icu.name                      AS currency,
           vjha.id_salary_rate,
           vjha.metric,
           vjha.metric_type,
           sum(vjha.jobs_cnt)::bigint    AS jobs_cnt,
           sum(vjha.unique_jobs_cnt)     AS value
    FROM aggregation.vacancy_job_history_agg_new vjha
             JOIN (SELECT v_job_kaiju_category.parent_category_id,
                          v_job_kaiju_category.parent_category_name,
                          v_job_kaiju_category.child_category_id,
                          v_job_kaiju_category.child_category_name
                   FROM aggregation.v_job_kaiju_category) category ON category.child_category_id = vjha.top_num
             LEFT JOIN dimension.countries cc ON cc.id = vjha.country_code
             LEFT JOIN dimension.info_region_other ir ON cc.id = ir.country AND vjha.region_id = ir.id
             LEFT JOIN dimension.info_currency icu ON cc.id = icu.country AND vjha.currency_id = icu.id
    WHERE vjha.metric_type = 'category_id'::text
    GROUP BY (lower(cc.alpha_2::text)), vjha.year_month, category.child_category_name, vjha.top_num,
             category.parent_category_name, vjha.salary_val1, vjha.salary_val2, icu.name, vjha.id_salary_rate,
             vjha.metric, vjha.metric_type
    UNION ALL
    SELECT lower(cc.alpha_2::text) AS country,
           vjspa.year_month,
           vjspa.id_region,
           CASE
               WHEN vjspa.region_name IS NULL AND vjspa.id_region IS NOT NULL THEN ir.name
               ELSE vjspa.region_name
               END                 AS region_name,
           CASE
               WHEN vjspa.region_type IS NULL AND vjspa.id_region IS NOT NULL AND ir.is_city = 0
                   THEN 'region_district'::character varying
               WHEN vjspa.region_type IS NULL AND vjspa.id_region IS NOT NULL AND ir.is_city = 1
                   THEN 'city_town'::character varying
               ELSE vjspa.region_type
               END                 AS region_type,
           CASE
               WHEN vjspa.id_region IS NOT NULL THEN ir.order_value
               ELSE NULL::integer
               END                 AS order_value,
           vjspa.q_kw,
           vjspa.topno             AS top_num,
           vjspa.title,
           vjspa.salary_val1,
           vjspa.salary_val2,
           icu.name                AS currency,
           vjspa.id_salary_rate,
           vjspa.metric,
           vjspa.metric_type,
           NULL::bigint            AS jobs_cnt,
           vjspa.value
    FROM aggregation.vacancy_job_search_prod_agg vjspa
             LEFT JOIN dimension.countries cc ON cc.id = vjspa.country_id
             LEFT JOIN dimension.info_calendar ic ON vjspa.date_diff = ic.date_diff
             LEFT JOIN dimension.info_region_other ir ON vjspa.country_id = ir.country AND vjspa.id_region = ir.id
             LEFT JOIN dimension.info_currency icu ON vjspa.country_id = icu.country AND vjspa.id_currency = icu.id
)
SELECT u.country,
       u.year_month,
       u.id_region,
       u.region_name,
       u.region_type,
       u.order_value,
       u.q_kw,
       u.top_num,
       u.title,
       u.salary_val1,
       u.salary_val2,
       u.currency,
       u.id_salary_rate,
       u.metric,
       u.metric_type,
       u.jobs_cnt,
       u.value
FROM union_1 u;

alter table aggregation.v_pr_job_search_history_agg
    owner to vnov;

grant select on aggregation.v_pr_job_search_history_agg to readonly;

grant select on aggregation.v_pr_job_search_history_agg to writeonly_pyscripts;

grant select on aggregation.v_pr_job_search_history_agg to ono;

