create view v_action_agg
            (date, country_code, country_name, region_name, groups, channel_1, channel_2, is_returned, is_mobile,
             session_create_page_type, action_type, user_dtd_lifetime, metric, entity_type, event_cnt, page_cnt,
             session_cnt)
as
select
    d.dt                   as date,
    lower(c.alpha_2::text) as country_code,
    c.name_country_eng     as country_name,
    rc.region_name_eng     as region_name,
    a.groups,
    a.channel_1,
    a.channel_2,
    a.is_returned,
    a.is_mobile,
    scp.name               as session_create_page_type,
    a.action_type,
    a.user_dtd_lifetime,
    a.metric,
    et.name                as entity_type,
    a.event_cnt,
    a.page_cnt,
    a.session_cnt
from
    aggregation.action_agg a
    join dimension.info_calendar d
         on d.date_diff = a.date_diff
    join dimension.countries c
         on c.id = a.country_id
    join dimension.region_continent rc
         on rc.id = c.region_continent_id
    left join aggregation.dic_session_create_page_type scp
              on scp.id = a.session_create_page_type
    left join aggregation.dic_action_entity_type et
              on et.metric::text = a.metric::text and et.id::numeric =
                                                      case
                                                          when a.metric::text = 'session_action'::text
                                                              then floor(0.01 * a.action_type::numeric)
                                                          else a.entity_type::numeric
                                                          end
where
     (a.metric::text <> 'jdp_action'::text or coalesce(a.action_type, 0) <> 41)
    and d.dt >= current_date - 30;

alter table v_action_agg
    owner to ono;

grant select on v_action_agg to ypr;

grant select on v_action_agg to user_agg_team;
