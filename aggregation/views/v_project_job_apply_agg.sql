create or replace view aggregation.v_project_job_apply_agg as
	SELECT c.alpha_2 AS country_code,
    c.name_country_eng AS country_name,
    pjaa.job_view_datediff,
    pjaa.id_project,
    ip.name AS project_name,
    pjaa.current_traf_source_id,
    uts.channel as traffic_channel,
    uts.name    as traffic_name,
    pjaa.is_local,
    pjaa.view_type,
    pjaa.job_type,
    pjaa.job_view_cnt,
    pjaa.apply_click_cnt,
    pjaa.apply_cnt,
    pjaa.apply_with_questionnaire_cnt,
    pjaa.new_cv_cnt
   FROM aggregation.project_job_apply_agg pjaa
     LEFT JOIN dimension.countries c ON c.id = pjaa.country_id
     LEFT JOIN dimension.info_project ip ON ip.country = pjaa.country_id AND ip.id = pjaa.id_project
     left join dimension.u_traffic_source uts
          on pjaa.current_traf_source_id = uts.id
;

alter table aggregation.v_project_job_apply_agg owner to dap;

grant select on aggregation.v_project_job_apply_agg to readonly;

