CREATE OR REPLACE VIEW aggregation.v_exclude_ppc_traffic_request
           as
as
WITH accepted AS
         (SELECT distinct upper(exclude_ppc_traffic_request.country) as country
          FROM imp.exclude_ppc_traffic_request
          WHERE exclude_ppc_traffic_request.is_accepted = true)
SELECT countries.alpha_2                           AS country,
       project_conversions_daily.country_id,
       project_conversions_daily.project_id,
       project_conversions_daily.project_name,
       project_conversions_daily.metric,
       project_conversions_daily.session_date,
       project_conversions_daily.away_type,
       sum(project_conversions_daily.away_revenue) AS away_revenue,
       sum(project_conversions_daily.aways)        AS aways_cnt,
       sum(project_conversions_daily.conversions)  AS conversions_cnt,
       project_conversions_daily.id_current_traf_source,
       u_traffic_source_current.name               AS current_traffic_source,
       exclude_ppc_traffic_request.is_accepted,
       exclude_ppc_traffic_request.date_decision
FROM aggregation.project_conversions_daily
         JOIN dimension.countries ON project_conversions_daily.country_id = countries.id
         JOIN accepted ON accepted.country = countries.alpha_2
         LEFT JOIN dimension.u_traffic_source u_traffic_source_current
                   ON project_conversions_daily.country_id = u_traffic_source_current.country AND
                      project_conversions_daily.id_current_traf_source = u_traffic_source_current.id
         LEFT JOIN imp.exclude_ppc_traffic_request
                   ON countries.alpha_2 = upper(exclude_ppc_traffic_request.country)
                       AND project_conversions_daily.project_id = exclude_ppc_traffic_request.id_project
                       AND u_traffic_source_current.name = exclude_ppc_traffic_request.traffic_source
WHERE project_conversions_daily.session_date >= ('2022-12-13'::date - '30 days'::interval)
  AND project_conversions_daily.away_type::text <> 'Jdp'::text
  AND project_conversions_daily.metric::text <> 'applies'::text
GROUP BY countries.alpha_2, project_conversions_daily.country_id, project_conversions_daily.project_id,
         project_conversions_daily.project_name,
         project_conversions_daily.metric, project_conversions_daily.session_date, project_conversions_daily.away_type,
         project_conversions_daily.id_current_traf_source, u_traffic_source_current.name,
         exclude_ppc_traffic_request.is_accepted, exclude_ppc_traffic_request.date_decision;
