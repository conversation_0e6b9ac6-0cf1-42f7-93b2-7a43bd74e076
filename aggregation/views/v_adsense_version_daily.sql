create view v_adsense_version_daily
            (country_id, action_datediff, adsense_version, session_cnt, searche_cnt, click_cnt, away_cnt, revenue_eur,
             account_cnt, conversion_cnt, account_revenue, email_aways, email_conversions, email_revenue, date, country,
             alpha_2, afc_estimated_earnings_euro, afs_estimated_earnings_euro, ga_sessions, avg_session_duration,
             bounce_rate)
as
WITH adsense AS (
    SELECT accr.action_date,
           datc.country_code,
           datc.test_version,
           sum(
                   CASE
                       WHEN accr.custom_channel_name::text ~~ 'afs_%'::text THEN accr.estimated_earnings_eur
                       ELSE 0::numeric
                       END) AS afs_estimated_earnings_euro,
           sum(
                   CASE
                       WHEN accr.custom_channel_name::text ~~ 'afc_%'::text THEN accr.estimated_earnings_eur
                       ELSE 0::numeric
                       END) AS afc_estimated_earnings_euro
    FROM imp_api.adsense_custom_channels_revenue accr
             JOIN (SELECT dic_adsence_test_channels."Date_add",
                          dic_adsence_test_channels.custom_channel_name,
                          dic_adsence_test_channels.custom_channel_id,
                          dic_adsence_test_channels.country_code,
                          dic_adsence_test_channels.test_version,
                          row_number()
                          OVER (PARTITION BY dic_adsence_test_channels.custom_channel_id ORDER BY dic_adsence_test_channels."Date_add" DESC) AS last_test
                   FROM aggregation.dic_adsence_test_channels) datc
                  ON btrim(substr(accr.custom_channel_id::text, strpos(accr.custom_channel_id::text, ':'::text) + 1,
                                  length(accr.custom_channel_id::text) -
                                  strpos(accr.custom_channel_id::text, ':'::text)))::numeric =
                     datc.custom_channel_id AND datc.last_test = 1
    GROUP BY accr.action_date, datc.country_code, datc.test_version
),
     bounce_rate AS (
         SELECT ga_adsense_test.action_date,
                ga_adsense_test.advert_version,
                ga_adsense_test.country_id,
                countries_1.alpha_2                    AS country,
                sum(ga_adsense_test.sessions)          AS sessions,
                sum(ga_adsense_test.sessions::numeric * ga_adsense_test.bounce_rate) /
                sum(ga_adsense_test.sessions)::numeric AS bounce_rate,
                sum(ga_adsense_test.sessions::numeric * ga_adsense_test.avg_session_duration) /
                sum(ga_adsense_test.sessions)::numeric AS avg_session_duration
         FROM imp_api.ga_adsense_test
                  JOIN dimension.countries countries_1 ON ga_adsense_test.country_id = countries_1.id
         GROUP BY ga_adsense_test.action_date, ga_adsense_test.advert_version, ga_adsense_test.country_id,
                  countries_1.alpha_2
     )
SELECT avd.country_id,
       avd.action_datediff,
       avd.adsense_version,
       avd.session_cnt,
       avd.searche_cnt,
       avd.click_cnt,
       avd.away_cnt,
       avd.revenue_eur,
       avd.account_cnt,
       avd.conversion_cnt,
       avd.account_revenue,
       avd.email_aways,
       avd.email_conversions,
       avd.email_revenue,
       COALESCE(fn_get_timestamp_from_date_diff(avd.action_datediff),
                adsense.action_date::timestamp without time zone)           AS date,
       COALESCE(countries.name_country_eng, countries_ads.name_country_eng) AS country,
       countries.alpha_2,
       adsense.afc_estimated_earnings_euro,
       adsense.afs_estimated_earnings_euro,
       bounce_rate.sessions                                                 AS ga_sessions,
       bounce_rate.avg_session_duration,
       bounce_rate.bounce_rate
FROM aggregation.adsense_version_daily avd
         LEFT JOIN dimension.countries ON avd.country_id = countries.id
         FULL JOIN adsense ON fn_get_timestamp_from_date_diff(avd.action_datediff) = adsense.action_date AND
                              lower(countries.alpha_2::text) = lower(adsense.country_code) AND
                              avd.adsense_version = adsense.test_version
         LEFT JOIN bounce_rate ON fn_get_timestamp_from_date_diff(avd.action_datediff) = bounce_rate.action_date AND
                                  avd.country_id = bounce_rate.country_id AND
                                  avd.adsense_version = bounce_rate.advert_version::integer
         LEFT JOIN dimension.countries countries_ads
                   ON lower(countries_ads.alpha_2::text) = lower(adsense.country_code);

alter table v_adsense_version_daily
    owner to ono;

