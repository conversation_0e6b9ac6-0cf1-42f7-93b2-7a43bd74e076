create view aggregation.v_scroll_click_position
            (session_date, country, placement, is_local, is_mobile, is_returned, page_position_encoded, clicks,
             impressions, impressions_on_screen, afc_clicks, afc_shows, afs_clicks, afs_shows)
as
SELECT date(fn_get_timestamp_from_date_diff(scroll_click_position_agg.date_diff)) AS session_date,
       countries.name_country_eng                                                 AS country,
       scroll_click_position_agg.placement,
       scroll_click_position_agg.is_local,
       scroll_click_position_agg.is_mobile,
       scroll_click_position_agg.is_returned,
       scroll_click_position_agg.page_position_encoded,
       sum(scroll_click_position_agg.click_cnt)                                   AS clicks,
       sum(scroll_click_position_agg.impression_cnt)                              AS impressions,
       sum(scroll_click_position_agg.impr_on_screen_cnt)                          AS impressions_on_screen,
       0                                                                          AS afc_clicks,
       0                                                                          AS afc_shows,
       0                                                                          AS afs_clicks,
       0                                                                          AS afs_shows
FROM aggregation.scroll_click_position_agg
         LEFT JOIN dimension.countries ON scroll_click_position_agg.country_id = countries.id
         LEFT JOIN dimension.u_traffic_source ON scroll_click_position_agg.id_traf_source = u_traffic_source.id AND
                                                 scroll_click_position_agg.country_id = u_traffic_source.country
         LEFT JOIN dimension.u_traffic_source uts_cur
                   ON scroll_click_position_agg.id_current_traf_sourse = uts_cur.id AND
                      scroll_click_position_agg.country_id = uts_cur.country
WHERE date(fn_get_timestamp_from_date_diff(scroll_click_position_agg.date_diff)) >= (CURRENT_DATE - 30)
GROUP BY (date(fn_get_timestamp_from_date_diff(scroll_click_position_agg.date_diff))), countries.name_country_eng,
         scroll_click_position_agg.placement, scroll_click_position_agg.is_local, scroll_click_position_agg.is_mobile,
         scroll_click_position_agg.is_returned, scroll_click_position_agg.page_position_encoded
UNION ALL
SELECT c.session_date,
       c.country,
       NULL::character varying AS placement,
       NULL::integer           AS is_local,
       NULL::integer           AS is_mobile,
       NULL::integer           AS is_returned,
       NULL::integer           AS page_position_encoded,
       0                       AS clicks,
       0                       AS impressions,
       0                       AS impressions_on_screen,
       c.clicks                AS afc_clicks,
       c.shows                 AS afc_shows,
       0                       AS afs_clicks,
       0                       AS afs_shows
FROM (SELECT adsense_afc.create_date    AS session_date,
             countries.name_country_eng AS country,
             sum(adsense_afc.show)      AS shows,
             sum(adsense_afc.click)     AS clicks
      FROM imp_statistic.adsense_afc
               LEFT JOIN dimension.countries
                         ON lower(countries.alpha_2::text) = "substring"(adsense_afc.site_address::text, 1, 2)
      WHERE adsense_afc.create_date >= (CURRENT_DATE - 30)
      GROUP BY adsense_afc.create_date, countries.name_country_eng) c
UNION ALL
SELECT s.session_date,
       s.country,
       NULL::character varying AS placement,
       NULL::integer           AS is_local,
       NULL::integer           AS is_mobile,
       NULL::integer           AS is_returned,
       NULL::integer           AS page_position_encoded,
       0                       AS clicks,
       0                       AS impressions,
       0                       AS impressions_on_screen,
       0                       AS afc_clicks,
       0                       AS afc_shows,
       s.clicks                AS afs_clicks,
       s.shows                 AS afs_shows
FROM (SELECT adsense_afs.create_date    AS session_date,
             countries.name_country_eng AS country,
             sum(adsense_afs.show)      AS shows,
             sum(adsense_afs.click)     AS clicks
      FROM imp_statistic.adsense_afs
               LEFT JOIN ono.dic_countries_adsense ON dic_countries_adsense.country_as_in_reports = adsense_afs.country::text
               LEFT JOIN dimension.countries ON lower(countries.alpha_2::text) = dic_countries_adsense.two_digits
      WHERE adsense_afs.create_date >= (CURRENT_DATE - 30)
      GROUP BY adsense_afs.create_date, countries.name_country_eng) s;

alter table aggregation.v_scroll_click_position
    owner to ono;

grant select on aggregation.v_scroll_click_position to readonly;

grant select on aggregation.v_scroll_click_position to tma;

grant select on aggregation.v_scroll_click_position to ypr;

grant select on aggregation.v_scroll_click_position to user_agg_team;

grant select on aggregation.v_scroll_click_position to vnov;

grant select on aggregation.v_scroll_click_position to "pavlo.kvasnii";

