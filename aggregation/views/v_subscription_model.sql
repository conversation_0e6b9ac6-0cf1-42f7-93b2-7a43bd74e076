create or replace view aggregation.v_subscription_model
            (employer_id, date_created_emp_account, company_name, company_industry, date_ordered, date_paid, price,
             packet, currency_iso_code, packet_id, payment_status, traffic_source_id, traffic_source, date_start_packet,
             date_end_packet, date_packet_is_active, rank_payment, rank_payment_by_packet, next_packet_start_date,
             next_packet_name)
as
WITH prep AS (SELECT s.employer_id,
                     s.date_created_emp_account,
                     s.company_name,
                     s.company_industry,
                     s.date_ordered,
                     s.date_paid,
                     s.price,
                     s.currency_iso_code,
                     s.packet_id,
                     s.payment_status,
                     s.traffic_source_id,
                     s.traffic_source,
                     CASE
                         WHEN s.company_name::text = 'Jooble'::text OR
                              s.company_name::text = 'Ocean Call'::text AND s.price = 13000::numeric AND
                              s.date_paid = '2023-07-27'::date OR s.employer_id = 607380 AND s.employer_id = 610207
                             THEN 1
                         ELSE 0
                         END AS exceptions
              FROM aggregation.subscription_model s),
     subscription_raw AS (SELECT s.employer_id,
                                 s.date_created_emp_account,
                                 s.company_name,
                                 s.company_industry,
                                 s.date_ordered,
                                 s.date_paid,
                                 s.price,
                                 CASE
                                     WHEN s.packet_id = 61 AND ff.moderation_status = 1 THEN 'trial packet'::text
                                     WHEN s.packet_id = 62 AND (ff.moderation_status = 1 OR s.payment_status = 2)
                                         THEN 'Simple Start'::text
                                     WHEN s.price < 10000::numeric AND s.price > 0::numeric THEN 'Used Balance Month'::text
                                     WHEN s.price >= 10000::numeric AND s.price < 13000::numeric THEN 'Simple Month'::text
                                     WHEN s.price = 27000::numeric THEN 'Simple 3 Month'::text
                                     WHEN s.price = 84000::numeric THEN 'Simple Year'::text
                                     WHEN s.price >= 13000::numeric AND s.price < 16000::numeric
                                         THEN 'Simple Charming Month'::text
                                     WHEN s.price = 35100::numeric THEN 'Simple Charming 3 Month'::text
                                     WHEN s.price = 109200::numeric THEN 'Simple Charming Year'::text
                                     ELSE 'n/a'::text
                                     END                                                                  AS packet,
                                 s.currency_iso_code,
                                 s.packet_id,
                                 s.payment_status,
                                 s.traffic_source_id,
                                 s.traffic_source,
                                 min(s.date_paid) OVER (PARTITION BY s.employer_id)                       AS date_start_packet,
                                 dense_rank() OVER (PARTITION BY s.employer_id ORDER BY s.date_paid)      AS rank_payment,
                                 lead(s.date_paid)
                                 OVER (PARTITION BY s.employer_id ORDER BY s.date_paid)                   AS next_packet_start_date
                          FROM prep s
                                   LEFT JOIN (SELECT ff_1.id                     AS employer_id,
                                                     max(ff_1.moderation_status) AS moderation_status
                                              FROM aggregation.subscription_model_funnel_full ff_1
                                              GROUP BY ff_1.id) ff ON s.employer_id = ff.employer_id
                          WHERE s.exceptions = 0),
     subscription_agg AS (SELECT s.employer_id,
                                 s.date_created_emp_account,
                                 s.company_name,
                                 s.company_industry,
                                 s.date_ordered,
                                 s.date_paid,
                                 s.price,
                                 s.packet,
                                 s.currency_iso_code,
                                 s.packet_id,
                                 s.payment_status,
                                 s.traffic_source_id,
                                 s.traffic_source,
                                 s.date_start_packet,
                                 s.rank_payment,
                                 s.next_packet_start_date,
                                 dense_rank()
                                 OVER (PARTITION BY s.employer_id, s.packet ORDER BY s.date_paid)      AS rank_payment_by_packet,
                                 lead(s.packet) OVER (PARTITION BY s.employer_id ORDER BY s.date_paid) AS next_packet_name
                          FROM subscription_raw s),
     cohort_raw AS (SELECT sr.employer_id,
                           sr.date_created_emp_account,
                           sr.company_name,
                           sr.company_industry,
                           sr.date_ordered,
                           sr.date_paid,
                           NULL::numeric(19, 5)                                  AS price,
                           sr.packet,
                           sr.currency_iso_code,
                           NULL::integer                                         AS packet_id,
                           NULL::smallint                                        AS payment_status,
                           sr.traffic_source_id,
                           sr.traffic_source,
                           sr.date_start_packet,
                           sr.rank_payment,
                           sr.next_packet_start_date,
                           sr.rank_payment_by_packet,
                           sr.next_packet_name,
                           (sr.date_paid +
                            CASE
                                WHEN sr.packet = 'trial packet'::text THEN 0.167
                                WHEN sr.packet = 'Simple Start'::text THEN 0.233
                                WHEN sr.packet ~~ '%3 Month%'::text THEN 3::numeric
                                WHEN sr.packet ~~ '%Year%'::text THEN 12::numeric
                                WHEN sr.packet ~~ '%Month%'::text THEN 1::numeric
                                ELSE NULL::integer::numeric
                                END::double precision * '1 mon'::interval)::date AS date_end_packet,
                           info_calendar.dt                                      AS date_packet_is_active
                    FROM subscription_agg sr
                             LEFT JOIN dimension.info_calendar ON sr.date_paid <= info_calendar.dt AND (sr.date_paid +
                                                                                                        CASE
                                                                                                            WHEN sr.packet = 'trial packet'::text
                                                                                                                THEN 0.167
                                                                                                            WHEN sr.packet = 'Simple Start'::text
                                                                                                                THEN 0.233
                                                                                                            WHEN sr.packet ~~ '%3 Month%'::text
                                                                                                                THEN 3::numeric
                                                                                                            WHEN sr.packet ~~ '%Year%'::text
                                                                                                                THEN 12::numeric
                                                                                                            WHEN sr.packet ~~ '%Month%'::text
                                                                                                                THEN 1::numeric
                                                                                                            ELSE NULL::integer::numeric
                                                                                                            END::double precision *
                                                                                                        '1 mon'::interval)::date >=
                                                                                                       info_calendar.dt),
     unions AS (SELECT s.employer_id,
                       s.date_created_emp_account,
                       s.company_name,
                       s.company_industry,
                       s.date_ordered,
                       s.date_paid,
                       s.price,
                       s.packet,
                       s.currency_iso_code,
                       s.packet_id,
                       s.payment_status,
                       s.traffic_source_id,
                       s.traffic_source,
                       s.date_start_packet,
                       NULL::date AS date_end_packet,
                       NULL::date AS date_packet_is_active,
                       s.rank_payment,
                       s.rank_payment_by_packet,
                       s.next_packet_start_date,
                       s.next_packet_name
                FROM subscription_agg s
                UNION ALL
                SELECT cr.employer_id,
                       cr.date_created_emp_account,
                       cr.company_name,
                       cr.company_industry,
                       cr.date_ordered,
                       cr.date_paid,
                       cr.price,
                       cr.packet,
                       cr.currency_iso_code,
                       cr.packet_id,
                       cr.payment_status,
                       cr.traffic_source_id,
                       cr.traffic_source,
                       cr.date_start_packet,
                       cr.date_end_packet,
                       cr.date_packet_is_active,
                       cr.rank_payment,
                       cr.rank_payment_by_packet,
                       cr.next_packet_start_date,
                       cr.next_packet_name
                FROM cohort_raw cr)
SELECT unions.employer_id,
       unions.date_created_emp_account,
       unions.company_name,
       unions.company_industry,
       unions.date_ordered,
       unions.date_paid,
       unions.price,
       unions.packet,
       unions.currency_iso_code,
       unions.packet_id,
       unions.payment_status,
       unions.traffic_source_id,
       unions.traffic_source,
       unions.date_start_packet,
       unions.date_end_packet,
       unions.date_packet_is_active,
       unions.rank_payment,
       unions.rank_payment_by_packet,
       unions.next_packet_start_date,
       unions.next_packet_name
FROM unions;

alter table aggregation.v_subscription_model
    owner to vnov;

grant select on aggregation.v_subscription_model to readonly;

grant select on aggregation.v_subscription_model to ono;

grant select on aggregation.v_subscription_model to writeonly_pyscripts;

grant select on aggregation.v_subscription_model to readonly_aggregation;

