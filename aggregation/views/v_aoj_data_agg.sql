WITH aoj_list_project AS (SELECT DISTINCT i.country               AS id_country,
                                          LOWER(dc.alpha_2::text) AS country_alpha_low,
                                          i.id                    AS id_project,
                                          i.name                  AS project_name,
                                          i.is_active,
                                          i.deactivate_organic_jobs
                          FROM dimension.info_project i
                                   LEFT JOIN dimension.countries dc
                                             ON dc.id = i.country
                                   LEFT JOIN imp.site ass
                                             ON ass.id_project = i.id AND ass.country = i.country
                                   LEFT JOIN imp.auction_user au
                                             ON ass.id_project = i.id AND au.country = i.country
                          WHERE (i.jdp_flags::integer & 15) = 15
                            AND (i.apply_flags & 240) = 240
                            AND i.apply_form = TRUE
                            AND ass.site_status = 0
                            AND (au.flags & 32) = 0)
SELECT NULL::bigint                             AS action_datediff,
       DATE_TRUNC('day'::text, ac.date_created) AS date,
       alp.id_country,
       ac.country                               AS country_alpha_low,
       dc.name_country_eng,
       ac.id_project,
       ic.name                                  AS currency,
       alp.project_name,
       ac.id_campaign,
       c.name                                   AS campaign_name,
       COUNT(DISTINCT
             CASE
                 WHEN ac.cost IS NOT NULL THEN ac.external_id_apply
                 ELSE NULL::character varying
             END)                               AS conversions,
       COUNT(DISTINCT
             CASE
                 WHEN ac.cost IS NULL THEN ac.external_id_apply
                 ELSE NULL::character varying
             END)                               AS null_conversions,
       SUM(ac.cost)                             AS sum_cost_revenue_client_currency,
       SUM(ac.cost * ich.value_to_usd)          AS apply_revenue_usd,
       MIN(ac.cost)                             AS min_day_cost,
       MAX(ac.cost)                             AS max_day_cost,
       AVG(ac.cost)                             AS avg_cost_cpa,
       alp.is_active,
       alp.deactivate_organic_jobs,
       NULL::text                               AS is_paid,
       NULL::bigint                             AS device,
       NULL::character varying                  AS current_traffic_source,
       NULL::boolean                            AS current_traffic_source_is_paid,
       NULL::character varying                  AS current_channel,
       NULL::character varying                  AS away_type,
       NULL::double precision                   AS clicks,
       NULL::numeric                            AS apply_finish,
       NULL::double precision                   AS revenue_usd_internal,
       NULL::double precision                   AS revenue_user_currency_internal
FROM imp_statistic.apply_conversion_service ac
         JOIN aoj_list_project alp
              ON alp.country_alpha_low = ac.country::text AND alp.id_project = ac.id_project
         LEFT JOIN dimension.countries dc
                   ON dc.id = alp.id_country
         LEFT JOIN imp.campaign c
                   ON c.country = alp.id_country AND c.id::double precision = ac.id_campaign::double precision
         LEFT JOIN dimension.info_currency_history ich
                   ON ich.country = alp.id_country AND ich.date::date = ac.date_created::date AND
                      ich.id_currency = c.currency
         LEFT JOIN dimension.info_currency ic
                   ON ic.country = alp.id_country AND ic.id = c.currency
GROUP BY (DATE_TRUNC('day'::text, ac.date_created)), alp.id_country, ac.country, dc.name_country_eng, ac.id_project,
         ic.name, alp.project_name, ac.id_campaign, c.name, alp.is_active, alp.deactivate_organic_jobs
UNION ALL
SELECT cd.action_datediff,
       MIN(fn_get_date_from_date_diff(cd.action_datediff::integer)) AS date,
       cd.country_id                                                AS id_country,
       LOWER(dc2.alpha_2::text)                                     AS country_alpha_low,
       dc2.name_country_eng,
       cd.id_project,
       ic2.name                                                     AS currency,
       alp2.project_name,
       cd.id_campaign,
       campaign.name                                                AS campaign_name,
       NULL::bigint                                                 AS conversions,
       NULL::bigint                                                 AS null_conversions,
       NULL::numeric                                                AS sum_cost_revenue_client_currency,
       NULL::numeric                                                AS apply_revenue_usd,
       NULL::numeric                                                AS min_day_cost,
       NULL::numeric                                                AS max_day_cost,
       NULL::numeric                                                AS avg_cost_cpa,
       alp2.is_active,
       alp2.deactivate_organic_jobs,
       cd.is_paid,
       cd.device,
       u_traffic_source_current.name                                AS current_traffic_source,
       u_traffic_source_current.is_paid                             AS current_traffic_source_is_paid,
       u_traffic_source_current.channel                             AS current_channel,
       CASE
           WHEN cd.job_destination = 4::double precision THEN 'Away from JDP'::text
           WHEN cd.job_destination = 1::double precision THEN 'Other Aways'::text
           WHEN cd.job_destination = ANY (ARRAY [2::double precision, 3::double precision]) THEN 'JDP'::text
           ELSE NULL::text
       END::character varying(100)                                  AS away_type,
       SUM(cd.jdp_away_count)                                       AS clicks,
       SUM(cd.jooble_apply_count)                                   AS apply_finish,
       SUM(cd.revenue_usd)                                          AS revenue_usd_internal,
       SUM(cd.revenue_user_currency)                                AS revenue_user_currency_internal
FROM aggregation.click_data_agg cd
         JOIN aoj_list_project alp2
              ON alp2.id_country = cd.country_id AND alp2.id_project = cd.id_project
         LEFT JOIN dimension.countries dc2
                   ON dc2.id = cd.country_id
         LEFT JOIN imp.campaign
                   ON campaign.country = cd.country_id AND campaign.id::double precision = cd.id_campaign
         LEFT JOIN dimension.u_traffic_source u_traffic_source_current
                   ON cd.country_id = u_traffic_source_current.country AND
                      cd.id_current_traf_source = u_traffic_source_current.id
         LEFT JOIN dimension.info_currency ic2
                   ON ic2.country = alp2.id_country AND ic2.id = campaign.currency
WHERE cd.action_datediff > 45516
  AND (cd.id_project IN (SELECT DISTINCT aoj_list_project.id_project
                         FROM aoj_list_project))
GROUP BY cd.action_datediff, cd.country_id, (LOWER(dc2.alpha_2::text)), dc2.name_country_eng, cd.id_project, ic2.name,
         alp2.project_name, cd.id_campaign, campaign.name, alp2.is_active, alp2.deactivate_organic_jobs, cd.is_paid,
         cd.device, u_traffic_source_current.name, u_traffic_source_current.is_paid, u_traffic_source_current.channel,
         (
             CASE
                 WHEN cd.job_destination = 4::double precision THEN 'Away from JDP'::text
                 WHEN cd.job_destination = 1::double precision THEN 'Other Aways'::text
                 WHEN cd.job_destination = ANY (ARRAY [2::double precision, 3::double precision]) THEN 'JDP'::text
                 ELSE NULL::text
             END::character varying(100))
