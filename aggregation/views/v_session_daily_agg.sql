create view aggregation.v_session_daily_agg
            (country_id, action_datediff, id_traf_source, id_current_traf_source, session_create_page_type,
             total_session_cnt, bot_session_cnt, local_session_nobot_cnt, mobile_session_nobot_cnt,
             returned_session_nobot_cnt, click_type, user_no_bot_cnt, click_nobot_cnt, id, country_name,
             country_short_name, traffic_source_name, current_traffic_source_name, channel, current_channel)
as
SELECT sda.country_id,
       sda.action_datediff,
       sda.id_traf_source,
       sda.id_current_traf_source,
       sda.session_create_page_type,
       sda.total_session_cnt,
       sda.bot_session_cnt,
       sda.local_session_nobot_cnt,
       sda.mobile_session_nobot_cnt,
       sda.returned_session_nobot_cnt,
       sda.click_type,
       sda.user_no_bot_cnt,
       sda.click_nobot_cnt,
       sda.id,
       c.name_country_eng AS country_name,
       c.alpha_2          AS country_short_name,
       ufs_0.name         AS traffic_source_name,
       ufs.name           AS current_traffic_source_name,
       ufs_0.channel,
       ufs.channel        AS current_channel
FROM aggregation.session_daily_agg sda
         JOIN dimension.countries c ON sda.country_id = c.id
         LEFT JOIN dimension.u_traffic_source ufs_0 ON sda.country_id = ufs_0.country AND sda.id_traf_source = ufs_0.id
         LEFT JOIN dimension.u_traffic_source ufs
                   ON sda.country_id = ufs.country AND sda.id_current_traf_source = ufs.id;

alter table aggregation.v_session_daily_agg
    owner to ono;

grant select on aggregation.v_session_daily_agg to readonly;

grant select on aggregation.v_session_daily_agg to ypi;

grant select on aggregation.v_session_daily_agg to readonly_aggregation;

grant select on aggregation.v_session_daily_agg to "pavlo.kvasnii";

