create view v_collar_cluster_job_agg
            (has_one_cluster_j, local_cluster_id_j, local_cluster_name_j, is_paid_uid, active_id_cnt, active_uid_cnt,
             active_simgroup_cnt, avg_uid_cpc, country_j, datediff_j, common_cluster_id_j, common_cluster_name_j,
             group_j)
as
SELECT vc.cluster_cnt         AS has_one_cluster_j,
       vc.local_cluster_id    AS local_cluster_id_j,
       vc.local_cluster_name  AS local_cluster_name_j,
       vc.is_paid_uid,
       vc.active_id_cnt,
       vc.active_uid_cnt,
       vc.active_simgroup_cnt,
       vc.avg_uid_cpc,
       lower(c.alpha_2::text) AS country_j,
       vc.action_datediff     AS datediff_j,
       cl.common_cluster_id   AS common_cluster_id_j,
       cl.common_cluster_name AS common_cluster_name_j,
       cl.collar_group        AS group_j
FROM aggregation.vacancy_collars vc
         JOIN dimension.countries c ON c.id = vc.country_id
         LEFT JOIN aggregation.dic_job_cluster_group cl
                   ON cl.country_id = vc.country_id AND cl.local_cluster_name = vc.local_cluster_name;

alter table v_collar_cluster_job_agg
    owner to ono;

grant select on v_collar_cluster_job_agg to readonly;

grant select on v_collar_cluster_job_agg to ypr;

