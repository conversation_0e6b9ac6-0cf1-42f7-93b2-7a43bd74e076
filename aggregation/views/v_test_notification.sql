create view aggregation.v_test_notification
            (id_test, start_date, stop_date, is_active, running_days, test_type, countries) as
WITH email_test AS (SELECT email_account_test_settings.id_test,
                           email_account_test_settings.start_date,
                           email_account_test_settings.stop_date,
                           max(email_account_test_settings.is_active::integer)    AS is_active,
                           CASE
                               WHEN email_account_test_settings.stop_date IS NOT NULL THEN
                                       email_account_test_settings.stop_date::date -
                                       email_account_test_settings.start_date::date
                               WHEN email_account_test_settings.stop_date IS NULL AND
                                    email_account_test_settings.is_active IS FALSE THEN NULL::integer
                               ELSE COALESCE(email_account_test_settings.stop_date::date, CURRENT_DATE) -
                                    email_account_test_settings.start_date::date
                               END                                                AS running_days,
                           'Email TEST'::text                                     AS test_type,
                           count(DISTINCT email_account_test_settings.country_id) AS countries
                    FROM imp.email_account_test_settings
                    WHERE email_account_test_settings.country_id <> 1
                      AND email_account_test_settings.country_id <> 1
                    GROUP BY email_account_test_settings.id_test, email_account_test_settings.start_date,
                             email_account_test_settings.stop_date,
                             (
                                 CASE
                                     WHEN email_account_test_settings.stop_date IS NOT NULL THEN
                                             email_account_test_settings.stop_date::date -
                                             email_account_test_settings.start_date::date
                                     WHEN email_account_test_settings.stop_date IS NULL AND
                                          email_account_test_settings.is_active IS FALSE THEN NULL::integer
                                     ELSE COALESCE(email_account_test_settings.stop_date::date, CURRENT_DATE) -
                                          email_account_test_settings.start_date::date
                                     END)),
     serp_test AS (SELECT rows.test,
                          count(DISTINCT rows.countries) AS countries,
                          min(rows.start_date)           AS start_date,
                          max(rows.start_date)           AS end_date
                   FROM (SELECT "left"(session_abtest_agg.group_num::text, 3) AS test,
                                session_abtest_agg.country_id                 AS countries,
                                session_abtest_agg.action_datediff            AS start_date
                         FROM aggregation.session_abtest_agg session_abtest_agg
                         WHERE session_abtest_agg.group_num IS NOT NULL
                           AND session_abtest_agg.device_type_id <> 2
                           AND session_abtest_agg.action_datediff >=
                               (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) - 35)
                           AND session_abtest_agg.country_id <> 1
                           AND session_abtest_agg.country_id <> 10
                         UNION ALL
                         SELECT substr(session_abtest_agg.group_num::text, 9, 3) AS test,
                                session_abtest_agg.country_id                    AS countries,
                                session_abtest_agg.action_datediff               AS start_date
                         FROM aggregation.session_abtest_agg session_abtest_agg
                         WHERE session_abtest_agg.group_num IS NOT NULL
                           AND session_abtest_agg.device_type_id <> 2
                           AND session_abtest_agg.action_datediff >=
                               (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) - 35)
                           AND session_abtest_agg.country_id <> 1
                           AND session_abtest_agg.country_id <> 10
                         UNION ALL
                         SELECT substr(session_abtest_agg.group_num::text, 17, 3) AS test,
                                session_abtest_agg.country_id                     AS countries,
                                session_abtest_agg.action_datediff                AS start_date
                         FROM aggregation.session_abtest_agg session_abtest_agg
                         WHERE session_abtest_agg.group_num IS NOT NULL
                           AND session_abtest_agg.device_type_id <> 2
                           AND session_abtest_agg.action_datediff >=
                               (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) - 35)
                           AND session_abtest_agg.country_id <> 1
                           AND session_abtest_agg.country_id <> 10
                         UNION ALL
                         SELECT substr(session_abtest_agg.group_num::text, 25, 3) AS test,
                                session_abtest_agg.country_id                     AS countries,
                                session_abtest_agg.action_datediff                AS start_date
                         FROM aggregation.session_abtest_agg session_abtest_agg
                         WHERE session_abtest_agg.group_num IS NOT NULL
                           AND session_abtest_agg.device_type_id <> 2
                           AND session_abtest_agg.action_datediff >=
                               (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) - 35)
                           AND session_abtest_agg.country_id <> 1
                           AND session_abtest_agg.country_id <> 10
                         UNION ALL
                         SELECT substr(session_abtest_agg.group_num::text, 33, 3) AS test,
                                session_abtest_agg.country_id                     AS countries,
                                session_abtest_agg.action_datediff                AS start_date
                         FROM aggregation.session_abtest_agg session_abtest_agg
                         WHERE session_abtest_agg.group_num IS NOT NULL
                           AND session_abtest_agg.device_type_id <> 2
                           AND session_abtest_agg.action_datediff >=
                               (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) - 35)
                           AND session_abtest_agg.country_id <> 1
                           AND session_abtest_agg.country_id <> 10) rows
                   GROUP BY rows.test)
SELECT email_test.id_test::character(5) AS id_test,
       email_test.start_date::date      AS start_date,
       email_test.stop_date::date       AS stop_date,
       email_test.is_active,
       email_test.running_days,
       email_test.test_type,
       email_test.countries
FROM email_test
UNION ALL
SELECT btrim(serp_test.test)                                 AS id_test,
       fn_get_timestamp_from_date_diff(serp_test.start_date) AS start_date,
       fn_get_timestamp_from_date_diff(serp_test.end_date)   AS stop_date,
       CASE
           WHEN fn_get_timestamp_from_date_diff(serp_test.end_date) = (CURRENT_DATE - 1) THEN 1
           ELSE 0
           END                                               AS is_active,
       serp_test.end_date -
       CASE
           WHEN btrim(serp_test.test) = '504'::text THEN 44556
           WHEN btrim(serp_test.test) = '529'::text THEN 44633
           WHEN btrim(serp_test.test) = '523'::text THEN 44639
           WHEN btrim(serp_test.test) = '525'::text THEN 44647
           ELSE serp_test.start_date
           END                                               AS running_days,
       'SERP TEST'::text                                     AS test_type,
       serp_test.countries
FROM serp_test
WHERE length(btrim(serp_test.test)) > 1;

alter table aggregation.v_test_notification
    owner to ono;

grant select on aggregation.v_test_notification to readonly;

grant delete, insert, select, update on aggregation.v_test_notification to write_ono;

grant select on aggregation.v_test_notification to readonly_aggregation;

grant select on aggregation.v_test_notification to "pavlo.kvasnii";

