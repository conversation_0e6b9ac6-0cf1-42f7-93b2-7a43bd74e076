create or replace view aggregation.v_alert_dwh_bigquery
            (table_name, scheme_name, metric_name, country_code, project_id, date, value) as

SELECT 'budget_revenue_daily_agg'::text                   AS table_name,
       'aggregation'::text                                AS scheme_name,
       'user_budget_month'::text                          AS metric_name,
        budget_revenue_daily_agg.country_code             AS country_code,
        budget_revenue_daily_agg.project_id               AS project_id,
        budget_revenue_daily_agg.action_date              AS date,
     -- sum (campaign_budget_month) as campaign_budget_month ,
      sum (budget_revenue_daily_agg.user_budget_month) as value
FROM aggregation.budget_revenue_daily_agg
WHERE budget_revenue_daily_agg.action_date >= CURRENT_DATE - INTERVAL '7 days'
    AND budget_revenue_daily_agg.action_date < CURRENT_DATE
GROUP BY budget_revenue_daily_agg.country_code,
         budget_revenue_daily_agg.project_id,
         budget_revenue_daily_agg.action_date

UNION ALL

SELECT   'project_conversions_daily'::text             AS table_name,
         'aggregation'::text                           AS scheme_name,
         'away_revenue'::text                          AS metric_name,
         c.alpha_2                                     AS country_code,
         pcd.project_id                                AS project_id,
         pcd.session_date        AS date,
         sum(pcd.away_revenue)   AS value
FROM aggregation.project_conversions_daily pcd
LEFT JOIN dimension.countries c
ON pcd.country_id = c.id
WHERE pcd.session_date >= CURRENT_DATE - INTERVAL '7 days'
      AND pcd.session_date < CURRENT_DATE
GROUP BY c.alpha_2,
         pcd.project_id,
         pcd.session_date

UNION ALL

SELECT  'jdp_away_clicks_agg'::text              AS table_name,
         'aggregation'::text                     AS scheme_name,
         'revenue_usd'::text                     AS metric_name,
         c.alpha_2                               AS country_code,
         jac.project_id                          AS project_id,
         CAST(ic.dt AS DATE)                     AS date,
         sum(jac.revenue_usd)                    AS value
FROM  aggregation.jdp_away_clicks_agg jac
LEFT JOIN dimension.info_calendar ic
ON jac.action_datediff = ic.date_diff
LEFT JOIN dimension.countries c
ON jac.country_id = c.id
WHERE  jac.action_datediff >= fn_get_date_diff(CURRENT_DATE) - 7
    AND  jac.action_datediff < fn_get_date_diff(CURRENT_DATE)
GROUP BY  c.alpha_2,
          jac.project_id,
          CAST(ic.dt AS DATE)

UNION ALL

SELECT 'jobs_stat_daily'::text                           AS table_name,
       'aggregation'::text                               AS scheme_name,
       'total_jobs'::text                                AS metric_name,
        c.alpha_2                                        AS country_code,
        jsd.id_project                                   AS project_id,
        jsd.date::date                                   AS date,
        sum (jsd.paid_job_count + jsd.organic_job_count) AS value
FROM aggregation.jobs_stat_daily jsd
inner join imp.campaign as ac
on ac.id_project = jsd.id_project and jsd.id_campaign = ac.id and jsd.id_country = ac.country
LEFT JOIN dimension.countries c
ON jsd.id_country = c.id
WHERE jsd.date::date >=  CURRENT_DATE - INTERVAL '7 days'
      and jsd.date::date < CURRENT_DATE
GROUP BY c.alpha_2,
         jsd.id_project,
         jsd.date::date;


alter table aggregation.v_alert_dwh_bigquery owner to daryna_buslenko;
grant select on aggregation.v_alert_dwh_bigquery to readonly;
grant select on aggregation.v_alert_dwh_bigquery to readonly_aggregation;


