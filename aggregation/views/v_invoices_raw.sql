create or replace view aggregation.v_invoices_raw
            (country_id, country, date, contractor, site, currency, type, min_rate, spent) as
WITH base_info AS (
    SELECT countries.id                                      AS country_id,
           invoice_row.country,
           make_date(invoice_row.year, invoice_row.month, 1) AS date,
           btrim(invoice_row.contractor::text)               AS contractor,
           invoice_row.site,
           invoice_row.currency,
           sum(invoice_row.min_rate)                         AS min_rate,
           sum(invoice_row.spent)                            AS spent
    FROM aggregation.invoice_row
             JOIN dimension.countries ON invoice_row.country::text = countries.alpha_2::text
    WHERE make_date(invoice_row.year, invoice_row.month, 1) >= '2022-01-01'::date
    GROUP BY countries.id, invoice_row.country, (make_date(invoice_row.year, invoice_row.month, 1)),
             (btrim(invoice_row.contractor::text)), invoice_row.site, invoice_row.currency
),
     base_for_union AS (
         SELECT countries.id                                      AS country_id,
                invoice_row.country,
                make_date(invoice_row.year, invoice_row.month, 1) AS date,
                btrim(invoice_row.contractor::text)               AS contractor,
                invoice_row.site,
                invoice_row.currency,
                invoice_row.type,
                invoice_row.min_rate,
                sum(invoice_row.spent)                            AS spent
         FROM aggregation.invoice_row
                  JOIN dimension.countries ON invoice_row.country::text = countries.alpha_2::text
         GROUP BY countries.id, invoice_row.country, (make_date(invoice_row.year, invoice_row.month, 1)),
                  (btrim(invoice_row.contractor::text)), invoice_row.site, invoice_row.currency, invoice_row.type,
                  invoice_row.min_rate
     ),
     correction_info AS (
         SELECT c.site,
                c.currency,
                (fn_get_date_from_date_diff(c.invoice_date) - '2 days'::interval)::date    AS invoice_date,
                (((date_part('year'::text, (fn_get_date_from_date_diff(c.invoice_date) - '2 days'::interval)::date) ||
                   '-'::text) ||
                  date_part('month'::text, (fn_get_date_from_date_diff(c.invoice_date) - '2 days'::interval)::date)) ||
                 '-01'::text)::date                                                        AS mm_yy_invoice,
                c.sales                                                                    AS invoice_spent,
                (fn_get_date_from_date_diff(c.adjustment_date) - '2 days'::interval)::date AS adjustment_date,
                c.adjustment_sum
         FROM aggregation.invoice_correction c
     )
SELECT u.country_id,
       u.country,
       u.date,
       u.contractor,
       u.site,
       u.currency,
       u.type,
       u.min_rate,
       u.spent
FROM base_for_union u
UNION ALL
SELECT b.country_id,
       b.country,
       b.date,
       b.contractor,
       b.site,
       b.currency,
       'Invoice correction'::character varying AS type,
       b.min_rate,
       ic.adjustment_sum                       AS spent
FROM base_info b
         JOIN correction_info ic
              ON b.site::text = ic.site AND b.currency::text = ic.currency AND ic.mm_yy_invoice = b.date AND
                 COALESCE(b.min_rate, b.spent) = ic.invoice_spent;

alter table aggregation.v_invoices_raw
    owner to vnov;

grant select on aggregation.v_invoices_raw to readonly;

grant select on aggregation.v_invoices_raw to write_ono;

grant select on aggregation.v_invoices_raw to readonly_aggregation;

