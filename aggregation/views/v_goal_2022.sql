create view aggregation.v_goal_2022
            (session_date, country, aways, conversions, current_traf_channel, click_type, user_no_bot_cnt) as
WITH conversion AS (SELECT project_conversions_daily.session_date,
                           countries.name_country_eng                 AS country,
                           sum(project_conversions_daily.aways)       AS aways,
                           sum(project_conversions_daily.conversions) AS conversions
                    FROM aggregation.project_conversions_daily
                             JOIN (SELECT project_conversions_daily_1.country_id,
                                          project_conversions_daily_1.project_id,
                                          min(project_conversions_daily_1.session_date) AS session_date,
                                          sum(project_conversions_daily_1.conversions)  AS conversions
                                   FROM aggregation.project_conversions_daily project_conversions_daily_1
                                   WHERE COALESCE(project_conversions_daily_1.conversions, 0) > 0
                                   GROUP BY project_conversions_daily_1.country_id,
                                            project_conversions_daily_1.project_id
                                   HAVING sum(project_conversions_daily_1.conversions) > 5) start_conversion
                                  ON project_conversions_daily.country_id = start_conversion.country_id AND
                                     project_conversions_daily.project_id = start_conversion.project_id
                             LEFT JOIN dimension.countries ON project_conversions_daily.country_id = countries.id
                    WHERE project_conversions_daily.metric::text = 'aways'::text
                      AND project_conversions_daily.session_date >= start_conversion.session_date
                    GROUP BY project_conversions_daily.session_date, countries.name_country_eng)
SELECT conversion.session_date,
       conversion.country,
       conversion.aways,
       conversion.conversions,
       NULL::character varying AS current_traf_channel,
       NULL::integer           AS click_type,
       0                       AS user_no_bot_cnt
FROM conversion
WHERE conversion.aways IS NOT NULL
UNION ALL
SELECT fn_get_timestamp_from_date_diff(session_daily_agg.action_datediff) AS session_date,
       countries.name_country_eng                                         AS country,
       0                                                                  AS aways,
       0                                                                  AS conversions,
       u_traffic_source.channel                                           AS current_traf_channel,
       session_daily_agg.click_type,
       sum(session_daily_agg.user_no_bot_cnt)                             AS user_no_bot_cnt
FROM aggregation.session_daily_agg
         JOIN dimension.countries ON session_daily_agg.country_id = countries.id
         LEFT JOIN dimension.u_traffic_source ON session_daily_agg.id_current_traf_source = u_traffic_source.id
GROUP BY (fn_get_timestamp_from_date_diff(session_daily_agg.action_datediff)), countries.name_country_eng,
         u_traffic_source.channel, session_daily_agg.click_type;

alter table aggregation.v_goal_2022
    owner to ono;

grant select on aggregation.v_goal_2022 to readonly;

grant select on aggregation.v_goal_2022 to readonly_aggregation;

grant select on aggregation.v_goal_2022 to "pavlo.kvasnii";

