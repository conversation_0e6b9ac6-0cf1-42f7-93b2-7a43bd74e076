create view aggregation.v_scan_flow
            (link, date, country, step_1, step_2, step_3, step_4, step_5, domain, potential_in_usd, cnt_domains,
             job_count, id_project, is_index, source_type, source_type_name, id_crm, crm_status)
as
select
    'link'::text           as link,
    scan_flow_high_potential.date,
    scan_flow_high_potential.country,
    'All domains'::text    as step_1,
    'High potential'::text as step_2,
    case
        when scan_flow_high_potential.is_index is null then 'Unknown'::text
        when scan_flow_high_potential.is_index::integer = 1 then 'Index'::text
        when scan_flow_high_potential.is_index::integer = 0 then 'No index'::text
        else null::text
        end                as step_3,
    case
        when scan_flow_high_potential.is_index::integer = 1 and scan_flow_high_potential.crm_status::text = 'Client'::text
            then 'Client'::character varying
        when scan_flow_high_potential.is_index::integer = 1 and scan_flow_high_potential.crm_status is null
            then 'Unknown'::character varying
        when scan_flow_high_potential.is_index::integer = 1 and scan_flow_high_potential.crm_status is not null
            then 'Not client'::character varying
        when scan_flow_high_potential.is_index::integer = 0 then scan_flow_high_potential.no_index_reason
        else null::character varying
        end                as step_4,
    case
        when scan_flow_high_potential.is_index::integer = 1 and scan_flow_high_potential.crm_status is not null
            then scan_flow_high_potential.crm_status
        else null::character varying
        end                as step_5,
    scan_flow_high_potential.domain,
    scan_flow_high_potential.potential_in_usd,
    1                      as cnt_domains,
    scan_flow_high_potential.job_count,
    scan_flow_high_potential.id_project,
    scan_flow_high_potential.is_index,
    scan_flow_high_potential.source_type,
    dic_source_type.name   as source_type_name,
    scan_flow_high_potential.id_crm,
    scan_flow_high_potential.crm_status
from
    aggregation.scan_flow_high_potential
    left join aggregation.dic_source_type
        on dic_source_type.id = scan_flow_high_potential.source_type
union all
select
    'link'::text                                     as link,
    scan_flow_low_potential.date,
    scan_flow_low_potential.country,
    'All domains'::text                              as step_1,
    'Low potential'::text                            as step_2,
    null::text                                       as step_3,
    null::character varying                          as step_4,
    null::character varying                          as step_5,
    null::character varying                          as domain,
    scan_flow_low_potential.overall_potential_in_usd as potential_in_usd,
    scan_flow_low_potential.domains_count            as cnt_domains,
    scan_flow_low_potential.overall_jobs_count       as job_count,
    null::integer                                    as id_project,
    null::boolean                                    as is_index,
    null::integer                                    as source_type,
    null::character varying                          as source_type_name,
    null::character varying                          as id_crm,
    null::character varying                          as crm_status
from
    aggregation.scan_flow_low_potential
union all
select
    'link'::text                       as link,
    null::date                         as date,
    null::character varying            as country,
    'All domains'::text                as step_1,
    'High potential'::text             as step_2,
    'No index'::text                   as step_3,
    'Tech problems'::character varying as step_4,
    null::character varying            as step_5,
    null::character varying            as domain,
    0                                  as potential_in_usd,
    null::integer                      as cnt_domains,
    null::integer                      as job_count,
    null::integer                      as id_project,
    null::boolean                      as is_index,
    null::integer                      as source_type,
    null::character varying            as source_type_name,
    null::character varying            as id_crm,
    null::character varying            as crm_status
union all
select
    'link'::text                 as link,
    null::date                   as date,
    null::character varying      as country,
    'All domains'::text          as step_1,
    'High potential'::text       as step_2,
    'No index'::text             as step_3,
    'No jobs'::character varying as step_4,
    null::character varying      as step_5,
    null::character varying      as domain,
    0                            as potential_in_usd,
    null::integer                as cnt_domains,
    null::integer                as job_count,
    null::integer                as id_project,
    null::boolean                as is_index,
    null::integer                as source_type,
    null::character varying      as source_type_name,
    null::character varying      as id_crm,
    null::character varying      as crm_status
union all
select
    'link'::text                    as link,
    null::date                      as date,
    null::character varying         as country,
    'All domains'::text             as step_1,
    'High potential'::text          as step_2,
    'Index'::text                   as step_3,
    'Not client'::character varying as step_4,
    'Not active'::character varying as step_5,
    null::character varying         as domain,
    0                               as potential_in_usd,
    null::integer                   as cnt_domains,
    null::integer                   as job_count,
    null::integer                   as id_project,
    null::boolean                   as is_index,
    null::integer                   as source_type,
    null::character varying         as source_type_name,
    null::character varying         as id_crm,
    null::character varying         as crm_status
union all
select
    'link'::text                    as link,
    null::date                      as date,
    null::character varying         as country,
    'All domains'::text             as step_1,
    'High potential'::text          as step_2,
    'Index'::text                   as step_3,
    'Not client'::character varying as step_4,
    'Ex-client'::character varying  as step_5,
    null::character varying         as domain,
    0                               as potential_in_usd,
    null::integer                   as cnt_domains,
    null::integer                   as job_count,
    null::integer                   as id_project,
    null::boolean                   as is_index,
    null::integer                   as source_type,
    null::character varying         as source_type_name,
    null::character varying         as id_crm,
    null::character varying         as crm_status
union all
select
    'link'::text                         as link,
    null::date                           as date,
    null::character varying              as country,
    'All domains'::text                  as step_1,
    'High potential'::text               as step_2,
    'Index'::text                        as step_3,
    'Not client'::character varying      as step_4,
    'Client on pause'::character varying as step_5,
    null::character varying              as domain,
    0                                    as potential_in_usd,
    null::integer                        as cnt_domains,
    null::integer                        as job_count,
    null::integer                        as id_project,
    null::boolean                        as is_index,
    null::integer                        as source_type,
    null::character varying              as source_type_name,
    null::character varying              as id_crm,
    null::character varying              as crm_status;

alter table aggregation.v_scan_flow
    owner to ono;

grant select on aggregation.v_scan_flow to ypr;

grant select on aggregation.v_scan_flow to user_agg_team;
