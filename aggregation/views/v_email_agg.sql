create view v_email_agg
            (account_date, country_name, letter_type, letter_name, sent_msg_cnt, open_msg_cnt, click_msg_cnt, aways,
             away_revenue) as
SELECT email_agg.account_date,
       email_agg.country_name,
       email_agg.letter_type,
       email_agg.letter_name,
       sum(email_agg.sent_msg_cnt) AS sent_msg_cnt,
       sum(email_agg.open_msg_cnt) AS open_msg_cnt,
       sum(email_agg.vst_msg_cnt)  AS click_msg_cnt,
       sum(email_agg.away)         AS aways,
       sum(email_agg.away_revenue) AS away_revenue
FROM email.email_agg
GROUP BY email_agg.account_date, email_agg.country_name, email_agg.letter_type, email_agg.letter_name;

alter table v_email_agg
    owner to ono;

