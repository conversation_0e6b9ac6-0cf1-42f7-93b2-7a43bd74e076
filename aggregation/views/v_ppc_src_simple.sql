create view aggregation.v_ppc_src_simple
            (date, country, country_code, data_type, source, label, is_test, is_test_filter, device,
             session_create_page, parent_category, project_id, project_name, impressions, clicks, cost_usd, revenue_usd,
             paid_overflow_revenue_usd, paid_overflow_cnt, away_cnt, away_revenue_usd, conversion_away_cnt,
             conversion_cnt, apply_revenue_usd, conversion_jdp_cnt, apply_cnt, retention_revenue_usd,
             revenue_usd_discount, budget, paid_revenue_usd, csa, csa_1, ea, ea_1, budget_wo_32)
as
WITH week_dates AS (SELECT info_calendar.date_diff,
                           info_calendar.dt,
                           date(date_trunc('week'::text, info_calendar.dt::timestamp with time zone)) AS week_start
                    FROM dimension.info_calendar
                    WHERE info_calendar.dt >= '2023-11-01'::date
                      AND info_calendar.dt <= ((SELECT max(info_calendar_1.dt) AS max
                                                FROM dimension.info_calendar info_calendar_1
                                                WHERE date_part('isodow'::text, info_calendar_1.dt) = 7::double precision
                                                  AND info_calendar_1.dt <= (CURRENT_DATE - 1)))
                    ORDER BY info_calendar.dt DESC),
     revenue_conversion AS (SELECT ic.dt                                                                        AS date,
                                   countries.country_name                                                       AS country,
                                   countries.alpha_2                                                            AS country_code,
                                   'revenue'::text                                                              AS data_type,
                                   CASE
                                       WHEN legend.type IS NULL AND
                                            ("left"(lower(first_traf.name::text), 2) = 'fb'::text OR
                                             "left"(lower(first_traf.name::text), 8) = 'facebook'::text)
                                           THEN 'facebook'::character varying
                                       WHEN legend.type IS NULL AND "left"(lower(first_traf.name::text), 6) = 'criteo'::text
                                           THEN 'criteo'::character varying
                                       WHEN legend.type IS NULL AND
                                            "left"(lower(first_traf.name::text), 11) = 'remote_paid'::text
                                           THEN 'remote'::character varying
                                       WHEN legend.type IS NULL THEN 'other'::character varying
                                       ELSE legend.type
                                       END                                                                      AS source,
                                   CASE
                                       WHEN legend.type::text = 'gdn'::text AND
                                            "left"(lower(first_traf.name::text), 3) <> 'gdn'::text
                                           THEN 'gdn_'::text || lower(first_traf.name::text)
                                       ELSE replace(lower(first_traf.name::text), 'facebook_'::text, 'fb_'::text)
                                       END                                                                      AS label,
                                   legend.device,
                                   rev.session_create_page_type,
                                   vjkc.parent_category_name                                                    AS parent_category,
                                   rev.project_id,
                                   rev.project_name,
                                   sum(
                                           CASE
                                               WHEN first_traf.channel::text = current_traf.channel::text
                                                   THEN rev.revenue_usd
                                               ELSE NULL::numeric
                                               END)                                                             AS revenue_usd,
                                   sum(
                                           CASE
                                               WHEN rev.is_paid_overflow = 1 THEN rev.revenue_usd
                                               ELSE NULL::numeric
                                               END)                                                             AS paid_overflow_revenue_usd,
                                   sum(
                                           CASE
                                               WHEN rev.is_paid_overflow = 1 THEN rev.jdp_away_count
                                               ELSE NULL::integer
                                               END)                                                             AS paid_overflow_cnt,
                                   sum(
                                           CASE
                                               WHEN rev.away_type::text <> 'JDP only'::text THEN rev.jdp_away_count
                                               ELSE NULL::integer
                                               END)                                                             AS away_cnt,
                                   0                                                                            AS away_revenue_usd,
                                   0                                                                            AS conversion_away_cnt,
                                   0                                                                            AS conversion_cnt,
                                   0                                                                            AS apply_revenue_usd,
                                   0                                                                            AS conversion_jdp_cnt,
                                   0                                                                            AS apply_cnt,
                                   sum(
                                           CASE
                                               WHEN first_traf.channel::text <> current_traf.channel::text
                                                   THEN rev.revenue_usd
                                               ELSE NULL::numeric
                                               END)                                                             AS retention_revenue_usd,
                                   sum(COALESCE(rev.revenue_usd, 0::numeric)) * COALESCE((SELECT d.discount
                                                                                          FROM aggregation.dic_info_project_discount d
                                                                                          WHERE d.country_id = rev.country_id
                                                                                            AND d.id_project = rev.project_id
                                                                                            AND to_char(d.date::timestamp with time zone, 'YYYY-MM'::text) <=
                                                                                                to_char(ic.dt::timestamp with time zone, 'YYYY-MM'::text)
                                                                                          ORDER BY d.date DESC
                                                                                          LIMIT 1),
                                                                                         0::numeric)            AS revenue_usd_discount
                            FROM aggregation.jdp_away_clicks_agg rev
                            LEFT JOIN dimension.v_country_info countries ON rev.country_id = countries.country_id
                            LEFT JOIN dimension.info_calendar ic ON rev.action_datediff = ic.date_diff
                            LEFT JOIN dimension.u_traffic_source first_traf
                                      ON rev.country_id = first_traf.country AND rev.id_traf_source = first_traf.id
                            LEFT JOIN dimension.u_traffic_source current_traf
                                      ON rev.country_id = current_traf.country AND
                                         rev.id_current_traf_source = current_traf.id
                            LEFT JOIN traffic.paid_legend_labels_channel legend
                                      ON rev.country_id = legend.country_id AND
                                         lower(first_traf.name::text) = legend.name::text
                            LEFT JOIN aggregation.v_job_kaiju_category vjkc
                                      ON rev.job_category_id = vjkc.child_category_id
                            WHERE rev.action_datediff >=
                                  fn_get_date_diff('2023-11-01 00:00:00'::timestamp without time zone)
                              AND first_traf.channel::text = 'Paid Search'::text
                            GROUP BY ic.dt, countries.country_name, countries.alpha_2, rev.country_id,
                                     (
                                         CASE
                                             WHEN legend.type IS NULL AND
                                                  ("left"(lower(first_traf.name::text), 2) = 'fb'::text OR
                                                   "left"(lower(first_traf.name::text), 8) = 'facebook'::text)
                                                 THEN 'facebook'::character varying
                                             WHEN legend.type IS NULL AND
                                                  "left"(lower(first_traf.name::text), 6) = 'criteo'::text
                                                 THEN 'criteo'::character varying
                                             WHEN legend.type IS NULL AND
                                                  "left"(lower(first_traf.name::text), 11) = 'remote_paid'::text
                                                 THEN 'remote'::character varying
                                             WHEN legend.type IS NULL THEN 'other'::character varying
                                             ELSE legend.type
                                             END),
                                     (
                                         CASE
                                             WHEN legend.type::text = 'gdn'::text AND
                                                  "left"(lower(first_traf.name::text), 3) <> 'gdn'::text
                                                 THEN 'gdn_'::text || lower(first_traf.name::text)
                                             ELSE replace(lower(first_traf.name::text), 'facebook_'::text, 'fb_'::text)
                                             END), legend.device, rev.session_create_page_type,
                                     vjkc.parent_category_name, rev.project_id, rev.project_name
                            UNION ALL
                            SELECT wd.dt                  AS date,
                                   countries.country_name AS country,
                                   countries.alpha_2      AS country_code,
                                   'conversions'::text    AS data_type,
                                   CASE
                                       WHEN legend.type IS NULL AND
                                            ("left"(lower(pcd.traffic_name::text), 2) = 'fb'::text OR
                                             "left"(lower(pcd.traffic_name::text), 8) = 'facebook'::text)
                                           THEN 'facebook'::character varying
                                       WHEN legend.type IS NULL AND "left"(lower(pcd.traffic_name::text), 6) = 'criteo'::text
                                           THEN 'criteo'::character varying
                                       WHEN legend.type IS NULL AND
                                            "left"(lower(pcd.traffic_name::text), 11) = 'remote_paid'::text
                                           THEN 'remote'::character varying
                                       WHEN legend.type IS NULL THEN 'other'::character varying
                                       ELSE legend.type
                                       END                AS source,
                                   CASE
                                       WHEN legend.type::text = 'gdn'::text AND
                                            "left"(lower(pcd.traffic_name::text), 3) <> 'gdn'::text
                                           THEN 'gdn_'::text || lower(pcd.traffic_name::text)
                                       ELSE replace(lower(pcd.traffic_name::text), 'facebook_'::text, 'fb_'::text)
                                       END                AS label,
                                   legend.device,
                                   pcd.session_create_page_type,
                                   CASE
                                       WHEN wd.dt >= (CURRENT_DATE - 91) THEN vjkc.parent_category_name
                                       ELSE NULL::text
                                       END                AS parent_category,
                                   pcd.project_id,
                                   pcd.project_name,
                                   0                      AS revenue_usd,
                                   0                      AS paid_overflow_revenue_usd,
                                   0                      AS paid_overflow_cnt,
                                   0                      AS away_cnt,
                                   sum(
                                           CASE
                                               WHEN pcd.metric::text = 'aways'::text THEN pcd.away_revenue
                                               ELSE NULL::numeric
                                               END)       AS away_revenue_usd,
                                   sum(
                                           CASE
                                               WHEN pcd.metric::text = 'aways'::text THEN pcd.aways
                                               ELSE NULL::integer
                                               END)       AS conversion_away_cnt,
                                   sum(
                                           CASE
                                               WHEN pcd.metric::text = 'aways'::text THEN pcd.conversions
                                               ELSE NULL::integer
                                               END)       AS conversion_cnt,
                                   sum(
                                           CASE
                                               WHEN pcd.metric::text = 'applies'::text THEN pcd.away_revenue
                                               ELSE NULL::numeric
                                               END)       AS apply_revenue_usd,
                                   sum(
                                           CASE
                                               WHEN pcd.metric::text = 'applies'::text THEN pcd.aways
                                               ELSE NULL::integer
                                               END)       AS conversion_jdp_cnt,
                                   sum(
                                           CASE
                                               WHEN pcd.metric::text = 'applies'::text THEN pcd.conversions
                                               ELSE NULL::integer
                                               END)       AS apply_cnt,
                                   0                      AS retention_revenue_usd,
                                   0                      AS revenue_usd_discount
                            FROM week_dates wd
                            JOIN aggregation.project_conversions_daily pcd ON pcd.session_date = wd.dt
                            JOIN (SELECT project_conversions_daily.country_id,
                                         project_conversions_daily.project_id,
                                         project_conversions_daily.metric,
                                         min(project_conversions_daily.session_date) AS conv_start,
                                         max(project_conversions_daily.session_date) AS conv_end
                                  FROM aggregation.project_conversions_daily
                                  WHERE project_conversions_daily.conversions > 0
                                    AND project_conversions_daily.session_date >= '2023-11-01'::date
                                  GROUP BY project_conversions_daily.country_id, project_conversions_daily.project_id,
                                           project_conversions_daily.metric) cs
                                 ON cs.project_id = pcd.project_id AND cs.country_id = pcd.country_id AND
                                    cs.metric::text = pcd.metric::text AND pcd.session_date >= cs.conv_start AND
                                    pcd.session_date <= (cs.conv_end + 30)
                            LEFT JOIN dimension.v_country_info countries ON pcd.country_id = countries.country_id
                            LEFT JOIN traffic.paid_legend_labels_channel legend
                                      ON pcd.country_id = legend.country_id AND
                                         lower(pcd.traffic_name::text) = legend.name::text
                            LEFT JOIN aggregation.v_job_kaiju_category vjkc
                                      ON vjkc.child_category_id = pcd.job_category_id
                            WHERE pcd.session_date >= '2023-11-01'::date
                              AND pcd.channel::text = 'Paid Search'::text
                            GROUP BY wd.dt, countries.country_name, countries.alpha_2,
                                     (
                                         CASE
                                             WHEN legend.type IS NULL AND
                                                  ("left"(lower(pcd.traffic_name::text), 2) = 'fb'::text OR
                                                   "left"(lower(pcd.traffic_name::text), 8) = 'facebook'::text)
                                                 THEN 'facebook'::character varying
                                             WHEN legend.type IS NULL AND
                                                  "left"(lower(pcd.traffic_name::text), 6) = 'criteo'::text
                                                 THEN 'criteo'::character varying
                                             WHEN legend.type IS NULL AND
                                                  "left"(lower(pcd.traffic_name::text), 11) = 'remote_paid'::text
                                                 THEN 'remote'::character varying
                                             WHEN legend.type IS NULL THEN 'other'::character varying
                                             ELSE legend.type
                                             END),
                                     (
                                         CASE
                                             WHEN legend.type::text = 'gdn'::text AND
                                                  "left"(lower(pcd.traffic_name::text), 3) <> 'gdn'::text
                                                 THEN 'gdn_'::text || lower(pcd.traffic_name::text)
                                             ELSE replace(lower(pcd.traffic_name::text), 'facebook_'::text, 'fb_'::text)
                                             END), legend.device, pcd.session_create_page_type,
                                     (
                                         CASE
                                             WHEN wd.dt >= (CURRENT_DATE - 91) THEN vjkc.parent_category_name
                                             ELSE NULL::text
                                             END), pcd.project_id, pcd.project_name),
     cost_raw AS (SELECT cs.date,
                         countries.country_name AS country,
                         upper(cs.country)      AS country_code,
                         'cost'::text           AS data_type,
                         cs.type                AS source,
                         CASE
                             WHEN cs.label ~ similar_escape('cba[0-9]*_dsa%'::text, NULL::text) THEN 'cba_dsa_'::text ||
                                                                                                     replace(
                                                                                                             "substring"(cs.label, '[0-9]+'::text),
                                                                                                             '_'::text,
                                                                                                             ''::text)
                             WHEN cs.label ~ similar_escape('cba[0-9]*_pmax%'::text, NULL::text) THEN
                                     'cba_pmax_'::text ||
                                     replace("substring"(cs.label, '[0-9]+'::text), '_'::text, ''::text)
                             WHEN cs.label = 'cba75[_dsa'::text THEN 'cba_dsa_75'::text
                             WHEN cs.type ~~ '%facebook%'::text AND cs.label = 'other_fb'::text THEN 'fb_other'::text
                             WHEN cs.type ~~ '%facebook%'::text AND cs.label <> 'other_fb'::text
                                 THEN 'fb_'::text || cs.label
                             WHEN cs.type = 'bing'::text THEN 'bing_'::text || cs.label
                             WHEN cs.type = 'smart'::text THEN 'smart_'::text || cs.label
                             WHEN cs.type = 'remarketing'::text THEN 'rem_'::text || cs.label
                             WHEN cs.type = 'gdn'::text THEN 'gdn_'::text || cs.label
                             ELSE cs.label
                             END                AS label,
                         cs.is_test,
                         CASE
                             WHEN cs.is_test IS NULL OR cs.is_test = ''::text THEN 'no'::text
                             ELSE 'yes'::text
                             END                AS is_test_filter,
                         legend.device,
                         cs.imp                 AS impressions,
                         cs.clicks,
                         cs.cost_usd,
                         0                      AS csa,
                         0                      AS csa_1,
                         0                      AS ea,
                         0                      AS ea_1
                  FROM traffic.paid_traf_cost_2023 cs
                  LEFT JOIN traffic.paid_legend_labels_channel legend
                            ON lower(cs.country) = lower(legend.country::text) AND
                               lower(cs.label) = lower(legend.name::text)
                  LEFT JOIN dimension.v_country_info countries ON lower(cs.country) = lower(countries.alpha_2::text)
                  WHERE cs.date >= '2023-11-01'::date
                  UNION ALL
                  SELECT cs.date,
                         countries.country_name AS country,
                         upper(cs.country)      AS country_code,
                         'csa'::text            AS data_type,
                         cs.source,
                         CASE
                             WHEN cs.label ~ similar_escape('cba[0-9]*_dsa%'::text, NULL::text) THEN 'cba_dsa_'::text ||
                                                                                                     replace(
                                                                                                             "substring"(cs.label, '[0-9]+'::text),
                                                                                                             '_'::text,
                                                                                                             ''::text)
                             WHEN cs.label ~ similar_escape('cba[0-9]*_pmax%'::text, NULL::text) THEN
                                     'cba_pmax_'::text ||
                                     replace("substring"(cs.label, '[0-9]+'::text), '_'::text, ''::text)
                             WHEN cs.label = 'cba75[_dsa'::text THEN 'cba_dsa_75'::text
                             WHEN cs.type ~~ '%facebook%'::text AND cs.label = 'other_fb'::text THEN 'fb_other'::text
                             WHEN cs.type ~~ '%facebook%'::text AND cs.label <> 'other_fb'::text
                                 THEN 'fb_'::text || cs.label
                             WHEN cs.type = 'bing'::text THEN 'bing_'::text || cs.label
                             WHEN cs.type = 'smart'::text THEN 'smart_'::text || cs.label
                             WHEN cs.type = 'remarketing'::text THEN 'rem_'::text || cs.label
                             WHEN cs.type = 'gdn'::text THEN 'gdn_'::text || cs.label
                             ELSE cs.label
                             END                AS label,
                         dic_test.is_test,
                         CASE
                             WHEN dic_test.is_test IS NULL OR dic_test.is_test = ''::text THEN 'no'::text
                             ELSE 'yes'::text
                             END                AS is_test_filter,
                         cs.device,
                         0                      AS impressions,
                         0                      AS clicks,
                         0                      AS cost_usd,
                         cs.csa::integer        AS csa,
                         cs.csa_1::integer      AS csa_1,
                         0                      AS ea,
                         0                      AS ea_1
                  FROM traffic.paid_csa cs
                  LEFT JOIN dimension.v_country_info countries ON lower(cs.country) = lower(countries.alpha_2::text)
                  LEFT JOIN traffic.paid_dic_test dic_test ON lower(cs.country) = lower(dic_test.country) AND
                                                              lower(cs.source) = lower(dic_test.source) AND
                                                              lower(cs.label) = lower(dic_test.label)
                  WHERE cs.date >= '2023-11-01'::date
                  UNION ALL
                  SELECT cs.date,
                         countries.country_name AS country,
                         upper(cs.country)      AS country_code,
                         'ea'::text             AS data_type,
                         cs.type                AS source,
                         CASE
                             WHEN cs.label ~ similar_escape('cba[0-9]*_dsa%'::text, NULL::text) THEN 'cba_dsa_'::text ||
                                                                                                     replace(
                                                                                                             "substring"(cs.label, '[0-9]+'::text),
                                                                                                             '_'::text,
                                                                                                             ''::text)
                             WHEN cs.label ~ similar_escape('cba[0-9]*_pmax%'::text, NULL::text) THEN
                                     'cba_pmax_'::text ||
                                     replace("substring"(cs.label, '[0-9]+'::text), '_'::text, ''::text)
                             WHEN cs.label = 'cba75[_dsa'::text THEN 'cba_dsa_75'::text
                             WHEN cs.type ~~ '%facebook%'::text AND cs.label = 'other_fb'::text THEN 'fb_other'::text
                             WHEN cs.type ~~ '%facebook%'::text AND cs.label <> 'other_fb'::text
                                 THEN 'fb_'::text || cs.label
                             WHEN cs.type = 'bing'::text THEN 'bing_'::text || cs.label
                             WHEN cs.type = 'smart'::text THEN 'smart_'::text || cs.label
                             WHEN cs.type = 'remarketing'::text THEN 'rem_'::text || cs.label
                             WHEN cs.type = 'gdn'::text THEN 'gdn_'::text || cs.label
                             ELSE cs.label
                             END                AS label,
                         dic_test.is_test,
                         CASE
                             WHEN dic_test.is_test IS NULL OR dic_test.is_test = ''::text THEN 'no'::text
                             ELSE 'yes'::text
                             END                AS is_test_filter,
                         cs.device,
                         0                      AS impressions,
                         0                      AS clicks,
                         0                      AS cost_usd,
                         0                      AS csa,
                         0                      AS csa_1,
                         cs.ea::integer         AS ea,
                         cs.ea_1::integer       AS ea_1
                  FROM traffic.paid_ea cs
                  LEFT JOIN dimension.v_country_info countries ON lower(cs.country) = lower(countries.alpha_2::text)
                  LEFT JOIN traffic.paid_dic_test dic_test ON lower(cs.country) = lower(dic_test.country) AND
                                                              lower(cs.type) = lower(dic_test.source) AND
                                                              lower(cs.label) = lower(dic_test.label)
                  WHERE cs.date >= '2023-11-01'::date),
     revenue_agg AS (SELECT to_char(rev.date::timestamp with time zone, 'YYYYMM'::text) AS month_year,
                            rev.date,
                            rev.country,
                            rev.country_code,
                            rev.data_type,
                            rev.source,
                            rev.label,
                            dic_test.is_test,
                            CASE
                                WHEN dic_test.is_test IS NULL OR dic_test.is_test = ''::text THEN 'no'::text
                                ELSE 'yes'::text
                                END                                                     AS is_test_filter,
                            rev.device,
                            dic.name                                                    AS session_create_page,
                            rev.parent_category,
                            rev.project_id,
                            rev.project_name,
                            rev.revenue_usd,
                            rev.paid_overflow_revenue_usd,
                            rev.paid_overflow_cnt,
                            rev.away_cnt,
                            rev.away_revenue_usd,
                            rev.conversion_away_cnt,
                            rev.conversion_cnt,
                            rev.apply_revenue_usd,
                            rev.conversion_jdp_cnt,
                            rev.apply_cnt,
                            rev.retention_revenue_usd,
                            rev.revenue_usd_discount
                     FROM revenue_conversion rev
                     LEFT JOIN traffic.paid_dic_test dic_test
                               ON lower(rev.country_code::text) = lower(dic_test.country) AND
                                  lower(rev.source::text) = lower(dic_test.source) AND
                                  lower(rev.label) = lower(dic_test.label)
                     LEFT JOIN aggregation.dic_session_create_page_type dic ON rev.session_create_page_type = dic.id),
     revenue_by_date AS (SELECT rev.month_year,
                                rev.date,
                                rev.country,
                                rev.country_code,
                                rev.is_test_filter,
                                rev.project_id,
                                rev.project_name,
                                sum(rev.revenue_usd)               AS revenue_usd,
                                sum(rev.away_cnt)                  AS away_cnt,
                                sum(rev.paid_overflow_revenue_usd) AS paid_overflow_revenue_usd,
                                sum(rev.retention_revenue_usd)     AS retention_revenue_usd,
                                sum(rev.revenue_usd_discount)      AS revenue_usd_discount
                         FROM revenue_agg rev
                         GROUP BY rev.date, rev.month_year, rev.country, rev.country_code, rev.is_test_filter,
                                  rev.project_id, rev.project_name),
     revenue_by_month AS (SELECT rev.month_year,
                                 rev.country,
                                 rev.country_code,
                                 rev.is_test_filter,
                                 rev.project_id,
                                 rev.project_name,
                                 sum(rev.revenue_usd)               AS revenue_usd,
                                 sum(rev.away_cnt)                  AS away_cnt,
                                 sum(rev.paid_overflow_revenue_usd) AS paid_overflow_revenue_usd,
                                 sum(rev.retention_revenue_usd)     AS retention_revenue_usd,
                                 sum(rev.revenue_usd_discount)      AS revenue_usd_discount
                          FROM revenue_agg rev
                          GROUP BY rev.month_year, rev.country, rev.country_code, rev.is_test_filter, rev.project_id,
                                   rev.project_name),
     revenue_proj_agg AS (SELECT rev.month_year,
                                 rev.country,
                                 rev.country_code,
                                 rev.is_test_filter,
                                 rev.project_id,
                                 rev.project_name,
                                 rev.revenue_usd,
                                 COALESCE(rev.away_cnt, 0::numeric)                                                AS away_cnt,
                                 sum(rev.revenue_usd)
                                 OVER (PARTITION BY rev.country, rev.month_year, rev.is_test_filter)               AS revenue_usd_month_total,
                                 sum(rev.away_cnt)
                                 OVER (PARTITION BY rev.country, rev.month_year, rev.is_test_filter)               AS away_cnt_month_total,
                                 round(COALESCE(rev.revenue_usd * 1::numeric / NULLIF(sum(rev.revenue_usd)
                                                                                      OVER (PARTITION BY rev.country, rev.month_year, rev.is_test_filter),
                                                                                      0::numeric), 0::numeric),
                                       5)                                                                          AS percent_revenue,
                                 round(COALESCE(COALESCE(rev.away_cnt, 0::numeric) * 1::numeric / NULLIF(
                                                 sum(rev.away_cnt)
                                                 OVER (PARTITION BY rev.country, rev.month_year, rev.is_test_filter),
                                                 0::numeric), 0::numeric),
                                       5)                                                                          AS percent_aways
                          FROM revenue_by_month rev
                          GROUP BY rev.month_year, rev.country, rev.country_code, rev.is_test_filter, rev.project_id,
                                   rev.project_name, rev.revenue_usd, rev.away_cnt),
     cost_grouped AS (SELECT to_char(cs.date::timestamp with time zone, 'YYYYMM'::text)                   AS month_year,
                             cs.date,
                             cs.country,
                             cs.country_code,
                             cs.is_test_filter,
                             r.project_id,
                             r.project_name,
                             sum(cs.cost_usd) * COALESCE(r.percent_revenue, 0::numeric)::double precision AS cost_usd,
                             sum(cs.csa)::numeric * COALESCE(r.percent_revenue, 0::numeric)               AS csa,
                             sum(cs.csa_1)::numeric * COALESCE(r.percent_revenue, 0::numeric)             AS csa_1,
                             sum(cs.ea)::numeric * COALESCE(r.percent_revenue, 0::numeric)                AS ea,
                             sum(cs.ea_1)::numeric * COALESCE(r.percent_revenue, 0::numeric)              AS ea_1
                      FROM cost_raw cs
                      LEFT JOIN revenue_proj_agg r
                                ON r.month_year = to_char(cs.date::timestamp with time zone, 'YYYYMM'::text) AND
                                   r.country::text = cs.country::text AND r.is_test_filter = cs.is_test_filter
                      GROUP BY (to_char(cs.date::timestamp with time zone, 'YYYYMM'::text)), cs.date, cs.country,
                               cs.country_code, cs.is_test_filter, r.project_id, r.project_name,
                               (COALESCE(r.percent_revenue, 0::numeric))),
     budget_accumulative AS (SELECT cs.date,
                                    cs.country,
                                    cs.country_code,
                                    'budget-project'::text                                                                                            AS data_type,
                                    cs.is_test_filter,
                                    cs.project_id,
                                    cs.project_name,
                                    sum(cs.cost_usd)
                                    OVER (PARTITION BY cs.country, cs.is_test_filter, cs.project_id, cs.project_name, cs.month_year ORDER BY cs.date) AS cost_usd,
                                    sum(cs.csa)
                                    OVER (PARTITION BY cs.country, cs.is_test_filter, cs.project_id, cs.project_name, cs.month_year ORDER BY cs.date) AS csa,
                                    sum(cs.csa_1)
                                    OVER (PARTITION BY cs.country, cs.is_test_filter, cs.project_id, cs.project_name, cs.month_year ORDER BY cs.date) AS csa_1,
                                    sum(cs.ea)
                                    OVER (PARTITION BY cs.country, cs.is_test_filter, cs.project_id, cs.project_name, cs.month_year ORDER BY cs.date) AS ea,
                                    sum(cs.ea_1)
                                    OVER (PARTITION BY cs.country, cs.is_test_filter, cs.project_id, cs.project_name, cs.month_year ORDER BY cs.date) AS ea_1,
                                    0                                                                                                                 AS revenue_usd,
                                    0                                                                                                                 AS paid_overflow_revenue_usd,
                                    0                                                                                                                 AS away_cnt,
                                    0                                                                                                                 AS retention_revenue_usd,
                                    0                                                                                                                 AS revenue_usd_discount,
                                    0                                                                                                                 AS budget,
                                    0                                                                                                                 AS paid_revenue_usd,
                                    0                                                                                                                 AS budget_wo_32
                             FROM cost_grouped cs
                             GROUP BY cs.date, cs.month_year, cs.country, cs.country_code, cs.is_test_filter,
                                      cs.project_id, cs.project_name, cs.cost_usd, cs.csa, cs.csa_1, cs.ea, cs.ea_1
                             UNION ALL
                             SELECT rev.date,
                                    rev.country,
                                    rev.country_code,
                                    'budget-project'::text                                                                                                  AS data_type,
                                    rev.is_test_filter,
                                    rev.project_id,
                                    rev.project_name,
                                    0                                                                                                                       AS cost_usd,
                                    0                                                                                                                       AS csa,
                                    0                                                                                                                       AS csa_1,
                                    0                                                                                                                       AS ea,
                                    0                                                                                                                       AS ea_1,
                                    sum(rev.revenue_usd)
                                    OVER (PARTITION BY rev.country, rev.month_year, rev.is_test_filter, rev.project_id, rev.project_name ORDER BY rev.date) AS revenue_usd,
                                    sum(rev.paid_overflow_revenue_usd)
                                    OVER (PARTITION BY rev.country, rev.month_year, rev.is_test_filter, rev.project_id, rev.project_name ORDER BY rev.date) AS paid_overflow_revenue_usd,
                                    sum(rev.away_cnt)
                                    OVER (PARTITION BY rev.country, rev.month_year, rev.is_test_filter, rev.project_id, rev.project_name ORDER BY rev.date) AS away_cnt,
                                    sum(rev.retention_revenue_usd)
                                    OVER (PARTITION BY rev.country, rev.month_year, rev.is_test_filter, rev.project_id, rev.project_name ORDER BY rev.date) AS retention_revenue_usd,
                                    sum(rev.revenue_usd_discount)
                                    OVER (PARTITION BY rev.country, rev.month_year, rev.is_test_filter, rev.project_id, rev.project_name ORDER BY rev.date) AS revenue_usd_discount,
                                    0                                                                                                                       AS budget,
                                    0                                                                                                                       AS paid_revenue_usd,
                                    0                                                                                                                       AS budget_wo_32
                             FROM revenue_by_date rev
                             GROUP BY rev.date, rev.month_year, rev.country, rev.country_code, rev.is_test_filter,
                                      rev.project_id, rev.project_name, rev.revenue_usd, rev.paid_overflow_revenue_usd,
                                      rev.revenue_usd_discount, rev.retention_revenue_usd, rev.away_cnt
                             UNION ALL
                             SELECT bgt.date,
                                    countries.country_name    AS country,
                                    bgt.country               AS country_code,
                                    'budget-project'::text    AS data_type,
                                    NULL::text                AS is_test_filter,
                                    bgt.id_project            AS project_id,
                                    bgt.site                  AS project_name,
                                    0                         AS cost_usd,
                                    0                         AS csa,
                                    0                         AS csa_1,
                                    0                         AS ea,
                                    0                         AS ea_1,
                                    0                         AS revenue_usd,
                                    0                         AS paid_overflow_revenue_usd,
                                    0                         AS away_cnt,
                                    0                         AS retention_revenue_usd,
                                    0                         AS revenue_usd_discount,
                                    sum(bgt.budget)           AS budget,
                                    sum(bgt.paid_revenue_usd) AS paid_revenue_usd,
                                    0                         AS budget_wo_32
                             FROM aggregation.v_budget_and_revenue bgt
                             LEFT JOIN dimension.v_country_info countries ON bgt.country::text = countries.alpha_2::text
                             WHERE bgt.date >= '2023-11-01'::date
                             GROUP BY bgt.date, countries.country_name, bgt.country, bgt.id_project, bgt.site)
SELECT cs.date,
       cs.country,
       cs.country_code,
       cs.data_type,
       cs.source,
       cs.label,
       cs.is_test,
       cs.is_test_filter,
       cs.device,
       NULL::character varying AS session_create_page,
       NULL::text              AS parent_category,
       NULL::integer           AS project_id,
       NULL::character varying AS project_name,
       cs.impressions,
       cs.clicks,
       cs.cost_usd,
       0                       AS revenue_usd,
       0                       AS paid_overflow_revenue_usd,
       0                       AS paid_overflow_cnt,
       0                       AS away_cnt,
       0                       AS away_revenue_usd,
       0                       AS conversion_away_cnt,
       0                       AS conversion_cnt,
       0                       AS apply_revenue_usd,
       0                       AS conversion_jdp_cnt,
       0                       AS apply_cnt,
       0                       AS retention_revenue_usd,
       0                       AS revenue_usd_discount,
       0                       AS budget,
       0                       AS paid_revenue_usd,
       cs.csa,
       cs.csa_1,
       cs.ea,
       cs.ea_1,
       0                       AS budget_wo_32
FROM cost_raw cs
UNION ALL
SELECT rev.date,
       rev.country,
       rev.country_code,
       rev.data_type,
       rev.source,
       rev.label,
       rev.is_test,
       rev.is_test_filter,
       rev.device,
       rev.session_create_page,
       rev.parent_category,
       rev.project_id,
       rev.project_name,
       0 AS impressions,
       0 AS clicks,
       0 AS cost_usd,
       rev.revenue_usd,
       rev.paid_overflow_revenue_usd,
       rev.paid_overflow_cnt,
       rev.away_cnt,
       rev.away_revenue_usd,
       rev.conversion_away_cnt,
       rev.conversion_cnt,
       rev.apply_revenue_usd,
       rev.conversion_jdp_cnt,
       rev.apply_cnt,
       rev.retention_revenue_usd,
       rev.revenue_usd_discount,
       0 AS budget,
       0 AS paid_revenue_usd,
       0 AS csa,
       0 AS csa_1,
       0 AS ea,
       0 AS ea_1,
       0 AS budget_wo_32
FROM revenue_agg rev
UNION ALL
SELECT bgt.date,
       bgt.country,
       bgt.country_code,
       bgt.data_type,
       NULL::text              AS source,
       NULL::text              AS label,
       NULL::text              AS is_test,
       bgt.is_test_filter,
       NULL::character varying AS device,
       NULL::character varying AS session_create_page,
       NULL::text              AS parent_category,
       bgt.project_id,
       bgt.project_name,
       0                       AS impressions,
       0                       AS clicks,
       bgt.cost_usd,
       bgt.revenue_usd,
       bgt.paid_overflow_revenue_usd,
       0                       AS paid_overflow_cnt,
       bgt.away_cnt,
       0                       AS away_revenue_usd,
       0                       AS conversion_away_cnt,
       0                       AS conversion_cnt,
       0                       AS apply_revenue_usd,
       0                       AS conversion_jdp_cnt,
       0                       AS apply_cnt,
       bgt.retention_revenue_usd,
       bgt.revenue_usd_discount,
       bgt.budget,
       bgt.paid_revenue_usd,
       bgt.csa,
       bgt.csa_1,
       bgt.ea,
       bgt.ea_1,
       bgt.budget_wo_32
FROM budget_accumulative bgt
union all
select date,
       country,
       country_code,
       data_type,
       source,
       label,
       is_test,
       is_test_filter,
       device,
       session_create_page,
       parent_category,
       project_id,
       project_name,
       impressions,
       clicks,
       cost_usd,
       revenue_usd,
       paid_overflow_revenue_usd,
       paid_overflow_cnt,
       away_cnt,
       away_revenue_usd,
       conversion_away_cnt,
       conversion_cnt,
       apply_revenue_usd,
       conversion_jdp_cnt,
       apply_cnt,
       retention_revenue_usd,
       revenue_usd_discount,
       budget,
       paid_revenue_usd,
       csa,
       csa_1,
       ea,
       ea_1,
       budget_wo_32,
       date,
       country,
       country_code,
       data_type,
       source,
       label,
       is_test,
       is_test_filter,
       device,
       session_create_page,
       parent_category,
       project_id,
       project_name,
       impressions,
       clicks,
       cost_usd,
       revenue_usd,
       paid_overflow_revenue_usd,
       paid_overflow_cnt,
       away_cnt,
       away_revenue_usd,
       conversion_away_cnt,
       conversion_cnt,
       apply_revenue_usd,
       conversion_jdp_cnt,
       apply_cnt,
       retention_revenue_usd,
       revenue_usd_discount,
       budget,
       paid_revenue_usd,
       csa,
       csa_1,
       ea,
       ea_1,
       budget_wo_32
from aggregation.ppc_src_simple_stat_data;
