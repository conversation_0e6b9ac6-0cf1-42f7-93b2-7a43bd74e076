create or replace view aggregation.v_paid_acquisition_prod_metrics
            (date, country, country_code, data_type, source, label, is_test, is_test_filter, device,
             session_create_page, parent_category, project_id, project_name, impressions, clicks, cost_usd, revenue_usd,
             paid_overflow_revenue_usd, paid_overflow_cnt, away_cnt, away_revenue_usd, conversion_away_cnt,
             conversion_cnt, apply_revenue_usd, conversion_jdp_cnt, apply_cnt, retention_revenue_usd,
             revenue_usd_discount, budget, paid_revenue_usd, csa, csa_1, ea, ea_1, budget_wo_32, paid_job_cnt,
             avg_cpc_for_paid_job_cnt, flag_32_jobs_cnt, paid_clicks, free_clicks, total_clicks, session_cnt_0,
             organic_job_cnt, last_paid_hour, session_cnt_1, new_account_cnt, account_revenue)
as
WITH sessions_agg AS (SELECT ic.dt            AS action_date,
                             cc.country_name  AS country,
                             cc.alpha_2       AS country_code,
                             CASE
                                 WHEN u_traffic_source.name::text ~~* '%_dsa%'::text THEN 'DSA'::character varying
                                 WHEN u_traffic_source.name::text ~~* '%_pmax_%'::text OR
                                      u_traffic_source.name::text ~~* '%_рмах_%'::text THEN 'Max_eff'::character varying
                                 WHEN u_traffic_source.name::text ~~* '%_srch_%'::text OR
                                      u_traffic_source.name::text ~~* '%_search_%'::text
                                     THEN 'Search'::character varying
                                 WHEN legend.type IS NULL AND
                                      ("left"(lower(u_traffic_source.name::text), 2) = 'fb'::text OR
                                       "left"(lower(u_traffic_source.name::text), 8) = 'facebook'::text)
                                     THEN 'facebook'::character varying
                                 WHEN legend.type IS NULL AND "left"(lower(u_traffic_source.name::text), 6) = 'criteo'::text
                                     THEN 'criteo'::character varying
                                 WHEN legend.type IS NULL AND
                                      "left"(lower(u_traffic_source.name::text), 11) = 'remote_paid'::text
                                     THEN 'remote'::character varying
                                 WHEN legend.type IS NULL THEN 'other'::character varying
                                 ELSE legend.type
                                 END          AS source,
                             CASE
                                 WHEN legend.type::text = 'gdn'::text AND
                                      "left"(lower(u_traffic_source.name::text), 3) <> 'gdn'::text
                                     THEN 'gdn_'::text || lower(u_traffic_source.name::text)
                                 ELSE replace(lower(u_traffic_source.name::text), 'facebook_'::text, 'fb_'::text)
                                 END          AS label,
                             sum(
                                     CASE
                                         WHEN u_traffic_source.channel::text = uts.channel::text THEN
                                             COALESCE(session_daily_agg.total_session_cnt, 0) -
                                             COALESCE(session_daily_agg.bot_session_cnt, 0)
                                         ELSE NULL::integer
                                         END) AS session_cnt_0,
                             sum(
                                     CASE
                                         WHEN u_traffic_source.channel::text <> uts.channel::text THEN
                                             COALESCE(session_daily_agg.total_session_cnt, 0) -
                                             COALESCE(session_daily_agg.bot_session_cnt, 0)
                                         ELSE NULL::integer
                                         END) AS session_cnt_1
                      FROM aggregation.session_daily_agg
                      LEFT JOIN dimension.info_calendar ic ON session_daily_agg.action_datediff = ic.date_diff
                      LEFT JOIN dimension.u_traffic_source
                                ON session_daily_agg.country_id = u_traffic_source.country AND
                                   session_daily_agg.id_traf_source = u_traffic_source.id
                      LEFT JOIN dimension.u_traffic_source uts ON session_daily_agg.country_id = uts.country AND
                                                                  session_daily_agg.id_current_traf_source = uts.id
                      JOIN dimension.v_country_info cc ON cc.country_id = session_daily_agg.country_id
                      LEFT JOIN traffic.paid_legend_labels_channel legend
                                ON session_daily_agg.country_id = legend.country_id AND
                                   lower(u_traffic_source.name::text) = legend.name::text
                      WHERE ic.dt >= '2024-01-01'::date
                        AND ic.dt <= (CURRENT_DATE - 1)
                        AND u_traffic_source.channel::text = 'Paid Search'::text
                      GROUP BY ic.dt, cc.country_name, cc.alpha_2,
                               (
                                   CASE
                                       WHEN u_traffic_source.name::text ~~* '%_dsa%'::text THEN 'DSA'::character varying
                                       WHEN u_traffic_source.name::text ~~* '%_pmax_%'::text OR
                                            u_traffic_source.name::text ~~* '%_рмах_%'::text
                                           THEN 'Max_eff'::character varying
                                       WHEN u_traffic_source.name::text ~~* '%_srch_%'::text OR
                                            u_traffic_source.name::text ~~* '%_search_%'::text
                                           THEN 'Search'::character varying
                                       WHEN legend.type IS NULL AND
                                            ("left"(lower(u_traffic_source.name::text), 2) = 'fb'::text OR
                                             "left"(lower(u_traffic_source.name::text), 8) = 'facebook'::text)
                                           THEN 'facebook'::character varying
                                       WHEN legend.type IS NULL AND
                                            "left"(lower(u_traffic_source.name::text), 6) = 'criteo'::text
                                           THEN 'criteo'::character varying
                                       WHEN legend.type IS NULL AND
                                            "left"(lower(u_traffic_source.name::text), 11) = 'remote_paid'::text
                                           THEN 'remote'::character varying
                                       WHEN legend.type IS NULL THEN 'other'::character varying
                                       ELSE legend.type
                                       END),
                               (
                                   CASE
                                       WHEN legend.type::text = 'gdn'::text AND
                                            "left"(lower(u_traffic_source.name::text), 3) <> 'gdn'::text
                                           THEN 'gdn_'::text || lower(u_traffic_source.name::text)
                                       ELSE replace(lower(u_traffic_source.name::text), 'facebook_'::text, 'fb_'::text)
                                       END)),
     account_revenue_agg AS (SELECT account_revenue.account_date         AS action_date,
                                    cc.country_name                      AS country,
                                    cc.alpha_2                           AS country_code,
                                    CASE
                                        WHEN u_traffic_source.name::text ~~* '%_dsa%'::text
                                            THEN 'DSA'::character varying
                                        WHEN u_traffic_source.name::text ~~* '%_pmax_%'::text OR
                                             u_traffic_source.name::text ~~* '%_рмах_%'::text
                                            THEN 'Max_eff'::character varying
                                        WHEN u_traffic_source.name::text ~~* '%_srch_%'::text OR
                                             u_traffic_source.name::text ~~* '%_search_%'::text
                                            THEN 'Search'::character varying
                                        WHEN legend.type IS NULL AND
                                             ("left"(lower(u_traffic_source.name::text), 2) = 'fb'::text OR
                                              "left"(lower(u_traffic_source.name::text), 8) = 'facebook'::text)
                                            THEN 'facebook'::character varying
                                        WHEN legend.type IS NULL AND
                                             "left"(lower(u_traffic_source.name::text), 6) = 'criteo'::text
                                            THEN 'criteo'::character varying
                                        WHEN legend.type IS NULL AND
                                             "left"(lower(u_traffic_source.name::text), 11) = 'remote_paid'::text
                                            THEN 'remote'::character varying
                                        WHEN legend.type IS NULL THEN 'other'::character varying
                                        ELSE legend.type
                                        END                              AS source,
                                    CASE
                                        WHEN legend.type::text = 'gdn'::text AND
                                             "left"(lower(u_traffic_source.name::text), 3) <> 'gdn'::text
                                            THEN 'gdn_'::text || lower(u_traffic_source.name::text)
                                        ELSE replace(lower(u_traffic_source.name::text), 'facebook_'::text, 'fb_'::text)
                                        END                              AS label,
                                    sum(account_revenue.new_account_cnt) AS new_account_cnt,
                                    sum(account_revenue.account_revenue) AS account_revenue
                             FROM aggregation.account_revenue
                             LEFT JOIN dimension.u_traffic_source
                                       ON account_revenue.country_id = u_traffic_source.country AND
                                          account_revenue.id_traf_src = u_traffic_source.id
                             JOIN dimension.v_country_info cc ON account_revenue.country_id = cc.country_id
                             LEFT JOIN traffic.paid_legend_labels_channel legend
                                       ON account_revenue.country_id = legend.country_id AND
                                          lower(u_traffic_source.name::text) = legend.name::text
                             WHERE account_revenue.account_date >= '2024-05-01'::date
                               AND account_revenue.account_date <= (CURRENT_DATE - 1)
                               AND (account_revenue.revenue_date IS NULL OR
                                    account_revenue.revenue_date >= account_revenue.account_date)
                               AND u_traffic_source.channel::text = 'Paid Search'::text
                             GROUP BY cc.country_name, cc.alpha_2, account_revenue.account_date,
                                      (
                                          CASE
                                              WHEN u_traffic_source.name::text ~~* '%_dsa%'::text
                                                  THEN 'DSA'::character varying
                                              WHEN u_traffic_source.name::text ~~* '%_pmax_%'::text OR
                                                   u_traffic_source.name::text ~~* '%_рмах_%'::text
                                                  THEN 'Max_eff'::character varying
                                              WHEN u_traffic_source.name::text ~~* '%_srch_%'::text OR
                                                   u_traffic_source.name::text ~~* '%_search_%'::text
                                                  THEN 'Search'::character varying
                                              WHEN legend.type IS NULL AND
                                                   ("left"(lower(u_traffic_source.name::text), 2) = 'fb'::text OR
                                                    "left"(lower(u_traffic_source.name::text), 8) = 'facebook'::text)
                                                  THEN 'facebook'::character varying
                                              WHEN legend.type IS NULL AND
                                                   "left"(lower(u_traffic_source.name::text), 6) = 'criteo'::text
                                                  THEN 'criteo'::character varying
                                              WHEN legend.type IS NULL AND
                                                   "left"(lower(u_traffic_source.name::text), 11) = 'remote_paid'::text
                                                  THEN 'remote'::character varying
                                              WHEN legend.type IS NULL THEN 'other'::character varying
                                              ELSE legend.type
                                              END),
                                      (
                                          CASE
                                              WHEN legend.type::text = 'gdn'::text AND
                                                   "left"(lower(u_traffic_source.name::text), 3) <> 'gdn'::text
                                                  THEN 'gdn_'::text || lower(u_traffic_source.name::text)
                                              ELSE replace(lower(u_traffic_source.name::text), 'facebook_'::text,
                                                           'fb_'::text)
                                              END))
SELECT pp.action_date AS date,
       pp.country,
       pp.country_code,
       pp.data_type,
       pp.source,
       pp.label,
       pp.is_test,
       pp.is_test_filter,
       pp.device,
       pp.session_create_page,
       pp.parent_category,
       pp.project_id,
       pp.project_name,
       pp.impressions,
       pp.clicks,
       pp.cost_usd,
       pp.revenue_usd,
       pp.paid_overflow_revenue_usd,
       pp.paid_overflow_cnt,
       pp.away_cnt,
       pp.away_revenue_usd,
       pp.conversion_away_cnt,
       pp.conversion_cnt,
       pp.apply_revenue_usd,
       pp.conversion_jdp_cnt,
       pp.apply_cnt,
       pp.retention_revenue_usd,
       pp.revenue_usd_discount,
       pp.budget,
       pp.paid_revenue_usd,
       pp.csa,
       pp.csa_1,
       pp.ea,
       pp.ea_1,
       pp.budget_wo_32,
       pp.paid_job_cnt,
       pp.avg_cpc_for_paid_job_cnt,
       pp.flag_32_jobs_cnt,
       pp.paid_clicks,
       pp.free_clicks,
       pp.total_clicks,
       0              AS session_cnt_0,
       pp.organic_job_cnt,
       pp.last_paid_hour,
       0              AS session_cnt_1,
       0              AS new_account_cnt,
       0::numeric     AS account_revenue
FROM aggregation.paid_acquisition_prod_metrics pp
UNION ALL
SELECT ss.action_date               AS date,
       ss.country,
       ss.country_code,
       'revenue'::character varying AS data_type,
       ss.source,
       ss.label,
       NULL::text                   AS is_test,
       NULL::character varying      AS is_test_filter,
       NULL::character varying      AS device,
       NULL::character varying      AS session_create_page,
       NULL::text                   AS parent_category,
       NULL::integer                AS project_id,
       NULL::character varying      AS project_name,
       0::bigint                    AS impressions,
       0::bigint                    AS clicks,
       0::double precision          AS cost_usd,
       0::numeric                   AS revenue_usd,
       0::numeric                   AS paid_overflow_revenue_usd,
       0::bigint                    AS paid_overflow_cnt,
       0::numeric                   AS away_cnt,
       0::numeric                   AS away_revenue_usd,
       0::bigint                    AS conversion_away_cnt,
       0::bigint                    AS conversion_cnt,
       0::numeric                   AS apply_revenue_usd,
       0::bigint                    AS conversion_jdp_cnt,
       0::bigint                    AS apply_cnt,
       0::numeric                   AS retention_revenue_usd,
       0::numeric                   AS revenue_usd_discount,
       0                            AS budget,
       0                            AS paid_revenue_usd,
       0::numeric                   AS csa,
       0::numeric                   AS csa_1,
       0::numeric                   AS ea,
       0::numeric                   AS ea_1,
       0::numeric                   AS budget_wo_32,
       0                            AS paid_job_cnt,
       0::numeric                   AS avg_cpc_for_paid_job_cnt,
       0                            AS flag_32_jobs_cnt,
       0::numeric                   AS paid_clicks,
       0::numeric                   AS free_clicks,
       0::numeric                   AS total_clicks,
       0                            AS session_cnt_0,
       0                            AS organic_job_cnt,
       0                            AS last_paid_hour,
       0                            AS session_cnt_1,
       ss.new_account_cnt,
       ss.account_revenue
FROM account_revenue_agg ss
UNION ALL
SELECT s.action_date                AS date,
       s.country,
       s.country_code,
       'revenue'::character varying AS data_type,
       s.source,
       s.label,
       NULL::text                   AS is_test,
       NULL::character varying      AS is_test_filter,
       NULL::character varying      AS device,
       NULL::character varying      AS session_create_page,
       NULL::text                   AS parent_category,
       NULL::integer                AS project_id,
       NULL::character varying      AS project_name,
       0::bigint                    AS impressions,
       0::bigint                    AS clicks,
       0::double precision          AS cost_usd,
       0::numeric                   AS revenue_usd,
       0::numeric                   AS paid_overflow_revenue_usd,
       0::bigint                    AS paid_overflow_cnt,
       0::numeric                   AS away_cnt,
       0::numeric                   AS away_revenue_usd,
       0::bigint                    AS conversion_away_cnt,
       0::bigint                    AS conversion_cnt,
       0::numeric                   AS apply_revenue_usd,
       0::bigint                    AS conversion_jdp_cnt,
       0::bigint                    AS apply_cnt,
       0::numeric                   AS retention_revenue_usd,
       0::numeric                   AS revenue_usd_discount,
       0                            AS budget,
       0                            AS paid_revenue_usd,
       0::numeric                   AS csa,
       0::numeric                   AS csa_1,
       0::numeric                   AS ea,
       0::numeric                   AS ea_1,
       0::numeric                   AS budget_wo_32,
       0                            AS paid_job_cnt,
       0::numeric                   AS avg_cpc_for_paid_job_cnt,
       0                            AS flag_32_jobs_cnt,
       0::numeric                   AS paid_clicks,
       0::numeric                   AS free_clicks,
       0::numeric                   AS total_clicks,
       s.session_cnt_0,
       0                            AS organic_job_cnt,
       0                            AS last_paid_hour,
       s.session_cnt_1,
       0                            AS new_account_cnt,
       0::numeric                   AS account_revenue
FROM sessions_agg s;

alter table aggregation.v_paid_acquisition_prod_metrics
    owner to vnov;
