create or replace view aggregation.v_sale_manager(country, id_project, sale_manager, project_name, id_user) as
select
    row_num.country,
    row_num.id_project,
    row_num.sale_manager,
    row_num.project_name,
    row_num.id_user
from
    (select
         site.country,
         site.id_project,
         info_project.name                                                                                                                                           as project_name,
         concat(btrim(coalesce(site_manager_fix."Real_name", site_manager.name::text)), ' ',
                btrim(coalesce(site_manager_fix."Real_email", site_manager.email::text)))::character varying(200)                                                    as sale_manager,
         row_number()
         over (partition by site.country, site.id_project order by site.site_status, case when auction_user.flags & 32 = 32 then 1 else 0 end, site_manager.id desc) as num,
         site_manager_fix."Real_name",
         site_manager_fix."Real_email",
         site.id_user
     from
         imp.site
         join imp.site_manager
              on site.country = site_manager.country and site.id = site_manager.id_site
         join dimension.info_project
              on site.country = info_project.country and site.id_project = info_project.id
         left join imp.auction_user
                   on site.country = auction_user.country and site.id_user = auction_user.id
         left join ono.site_manager_fix
                   on btrim(site_manager.email::text) = btrim(site_manager_fix.email) and
                      coalesce(site_manager.name, ''::character varying)::text =
                      coalesce(site_manager_fix.name, ''::text)) row_num
where
    row_num.num = 1;

alter table aggregation.v_sale_manager
    owner to ono;

grant select on aggregation.v_sale_manager to readonly;

grant delete, insert, select, update on aggregation.v_sale_manager to write_ono;

grant select on aggregation.v_sale_manager to tma;

grant select on aggregation.v_sale_manager to ypr;

grant select on aggregation.v_sale_manager to readonly_aggregation;

grant select on aggregation.v_sale_manager to "pavlo.kvasnii";

