create or replace view aggregation.v_email_and_account_metric
            (table_name, country_name, action_date, account_source, send_interval, unsub_type,
             account_unsub_date_period, source_name, source_is_paid, account_by_reg_cnt, unsub_account,
             varified_account_cnt, account_revenue, session_cnt, letter_type, letter_name, calc_result_count,
             account_alerts, account_send_interval, account_traffic_source, account_date, alertview, account_cnt,
             account_unsub_cnt, sent_msg_cnt, open_msg_cnt, vst_msg_cnt, undelivered_msg, open, click, open_jdp,
             away_revenue, away, message_with_open_away_cnt, message_with_alert_view_cnt, message_with_open_jdp_cnt,
             message_with_applies_cnt, apply_cnt, away_jdp_cnt)
as
WITH v_email_metric_agg_tmp AS (WITH union_table AS (SELECT countries.name_country_eng                                     AS country_name,
                                                            COALESCE(email_agg.sent_date::timestamp without time zone,
                                                                     fn_get_timestamp_from_date_diff(email_agg.date_diff)) AS sent_date,
                                                            email_agg.letter_type,
                                                            letter_type_name.letter_name,
                                                            email_agg.calc_result_count,
                                                            email_agg.account_alerts,
                                                            email_agg.account_send_interval,
                                                            email_agg.account_traffic_source,
                                                            email_agg.account_date,
                                                            sum(email_agg.alertview_cnt)                                   AS alertview,
                                                            sum(email_agg.account_cnt)                                     AS account_cnt,
                                                            sum(email_agg.account_unsub_cnt)                               AS account_unsub_cnt,
                                                            sum(email_agg.sent_msg)                                        AS sent_msg_cnt,
                                                            sum(email_agg.open_msg)                                        AS open_msg_cnt,
                                                            sum(email_agg.visit_msg)                                       AS vst_msg_cnt,
                                                            0                                                              AS undelivered_msg,
                                                            sum(email_agg.open_msg)                                        AS open,
                                                            sum(email_agg.visit_msg)                                       AS click,
                                                            sum(email_agg.jdp_cnt)                                         AS open_jdp,
                                                            0                                                              AS away_revenue,
                                                            sum(email_agg.away_cnt)                                        AS away,
                                                            sum(email_agg.message_with_away_cnt)                           AS message_with_open_away_cnt,
                                                            sum(email_agg.message_with_alertview_cnt)                      AS message_with_alert_view_cnt,
                                                            sum(email_agg.message_with_jdp_cnt)                            AS message_with_open_jdp_cnt,
                                                            sum(email_agg.message_with_jdp_away_cnt)                       AS message_with_applies_cnt,
                                                            sum(email_agg.apply_cnt)                                       AS apply_cnt,
                                                            sum(email_agg.away_jdp_cnt)                                    AS away_jdp_cnt
                                                     FROM aggregation.email_metric_daily email_agg
                                                              LEFT JOIN dimension.countries ON email_agg.country_id = countries.id
                                                              LEFT JOIN dimension.letter_type_name
                                                                        ON email_agg.letter_type = letter_type_name.letter_type
                                                     WHERE COALESCE(email_agg.sent_date::timestamp without time zone,
                                                                    fn_get_timestamp_from_date_diff(email_agg.date_diff)) >=
                                                           (CURRENT_DATE - 92)
                                                       AND email_agg.letter_type IS NOT NULL
                                                     GROUP BY countries.name_country_eng,
                                                              (COALESCE(
                                                                      email_agg.sent_date::timestamp without time zone,
                                                                      fn_get_timestamp_from_date_diff(email_agg.date_diff))),
                                                              email_agg.letter_type, letter_type_name.letter_name,
                                                              email_agg.calc_result_count, email_agg.account_alerts,
                                                              email_agg.account_send_interval,
                                                              email_agg.account_traffic_source, email_agg.account_date
                                                     UNION ALL
                                                     SELECT countries.name_country_eng    AS country_name,
                                                            undelivered_email.action_date AS sent_date,
                                                            NULL::integer                 AS letter_type,
                                                            NULL::character varying       AS letter_name,
                                                            NULL::integer                 AS calc_result_count,
                                                            NULL::character varying       AS account_alerts,
                                                            NULL::integer                 AS account_send_interval,
                                                            NULL::integer                 AS account_traffic_source,
                                                            NULL::date                    AS account_date,
                                                            NULL::bigint                  AS alertview,
                                                            NULL::bigint                  AS account_cnt,
                                                            NULL::bigint                  AS account_unsub_cnt,
                                                            NULL::bigint                  AS sent_msg_cnt,
                                                            NULL::bigint                  AS open_msg_cnt,
                                                            NULL::bigint                  AS vst_msg_cnt,
                                                            count(undelivered_email.id)   AS undelivered_msg,
                                                            NULL::bigint                  AS open,
                                                            NULL::bigint                  AS click,
                                                            NULL::bigint                  AS open_jdp,
                                                            NULL::integer                 AS away_revenue,
                                                            NULL::bigint                  AS away,
                                                            NULL::bigint                  AS message_with_open_away_cnt,
                                                            NULL::bigint                  AS message_with_alert_view_cnt,
                                                            NULL::bigint                  AS message_with_open_jdp_cnt,
                                                            NULL::bigint                  AS message_with_applies_cnt,
                                                            NULL::bigint                  AS apply_cnt,
                                                            NULL::bigint                  AS away_jdp_cnt
                                                     FROM imp.undelivered_email
                                                              JOIN dimension.countries ON undelivered_email.country_id = countries.id
                                                     WHERE undelivered_email.action_date >= (CURRENT_DATE - 92)
                                                     GROUP BY countries.name_country_eng, undelivered_email.action_date
                                                     UNION ALL
                                                     SELECT countries.name_country_eng                                                  AS country_name,
                                                            fn_get_timestamp_from_date_diff(adv_revenue_by_placement_and_src.date_diff) AS sent_date,
                                                            CASE
                                                                WHEN adv_revenue_by_placement_and_src.placement::text =
                                                                     'other letter types'::text THEN 999
                                                                ELSE replace(
                                                                        adv_revenue_by_placement_and_src.placement::text,
                                                                        'letter type '::text, ''::text)::integer
                                                                END                                                                     AS letter_type,
                                                            letter_type_name.letter_name,
                                                            NULL::integer                                                               AS calc_result_count,
                                                            NULL::character varying                                                     AS account_alerts,
                                                            NULL::integer                                                               AS account_send_interval,
                                                            NULL::integer                                                               AS account_traffic_source,
                                                            NULL::date                                                                  AS account_date,
                                                            NULL::bigint                                                                AS alertview,
                                                            NULL::bigint                                                                AS account_cnt,
                                                            NULL::bigint                                                                AS account_unsub_cnt,
                                                            NULL::bigint                                                                AS sent_msg_cnt,
                                                            NULL::bigint                                                                AS open_msg_cnt,
                                                            NULL::bigint                                                                AS vst_msg_cnt,
                                                            NULL::bigint                                                                AS undelivered_msg,
                                                            NULL::bigint                                                                AS open,
                                                            NULL::bigint                                                                AS click,
                                                            NULL::bigint                                                                AS open_jdp,
                                                            sum(adv_revenue_by_placement_and_src.revenue_usd)                           AS away_revenue,
                                                            NULL::bigint                                                                AS away,
                                                            NULL::bigint                                                                AS message_with_open_away_cnt,
                                                            NULL::bigint                                                                AS message_with_alert_view_cnt,
                                                            NULL::bigint                                                                AS message_with_open_jdp_cnt,
                                                            NULL::bigint                                                                AS message_with_applies_cnt,
                                                            NULL::bigint                                                                AS apply_cnt,
                                                            NULL::bigint                                                                AS away_jdp_cnt
                                                     FROM aggregation.adv_revenue_by_placement_and_src_analytics adv_revenue_by_placement_and_src
                                                              JOIN dimension.countries
                                                                   ON adv_revenue_by_placement_and_src.country_id = countries.id
                                                              LEFT JOIN dimension.letter_type_name ON replace(
                                                                                                              adv_revenue_by_placement_and_src.placement::text,
                                                                                                              'letter type '::text,
                                                                                                              ''::text)::integer =
                                                                                                      letter_type_name.letter_type
                                                     WHERE adv_revenue_by_placement_and_src.date_diff >=
                                                           (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) - 92)
                                                       AND adv_revenue_by_placement_and_src.placement::text ~~
                                                           'letter type%'::text
                                                     GROUP BY countries.name_country_eng,
                                                              (fn_get_timestamp_from_date_diff(adv_revenue_by_placement_and_src.date_diff)),
                                                              adv_revenue_by_placement_and_src.placement,
                                                              letter_type_name.letter_name)
                                SELECT union_table.country_name,
                                       union_table.sent_date,
                                       union_table.letter_type,
                                       union_table.letter_name,
                                       union_table.calc_result_count,
                                       union_table.account_alerts,
                                       union_table.account_send_interval,
                                       union_table.account_traffic_source,
                                       union_table.account_date,
                                       union_table.alertview,
                                       union_table.account_cnt,
                                       union_table.account_unsub_cnt,
                                       union_table.sent_msg_cnt,
                                       union_table.open_msg_cnt,
                                       union_table.vst_msg_cnt,
                                       union_table.undelivered_msg,
                                       union_table.open,
                                       union_table.click,
                                       union_table.open_jdp,
                                       union_table.away_revenue,
                                       union_table.away,
                                       union_table.message_with_open_away_cnt,
                                       union_table.message_with_alert_view_cnt,
                                       union_table.message_with_open_jdp_cnt,
                                       union_table.message_with_applies_cnt,
                                       union_table.apply_cnt,
                                       union_table.away_jdp_cnt
                                FROM union_table)
SELECT 'v_account_agg'::text     AS table_name,
       v_account_agg.country_name,
       v_account_agg.create_date AS action_date,
       v_account_agg.account_source,
       v_account_agg.send_interval,
       v_account_agg.unsub_type,
       v_account_agg.account_unsub_date_period,
       v_account_agg.source_name,
       v_account_agg.source_is_paid,
       v_account_agg.account_cnt AS account_by_reg_cnt,
       NULL::integer             AS unsub_account,
       v_account_agg.varified_account_cnt,
       v_account_agg.account_revenue,
       v_account_agg.session_cnt,
       NULL::integer             AS letter_type,
       NULL::character varying   AS letter_name,
       NULL::integer             AS calc_result_count,
       NULL::character varying   AS account_alerts,
       NULL::integer             AS account_send_interval,
       NULL::integer             AS account_traffic_source,
       NULL::date                AS account_date,
       NULL::bigint              AS alertview,
       NULL::bigint              AS account_cnt,
       NULL::bigint              AS account_unsub_cnt,
       NULL::bigint              AS sent_msg_cnt,
       NULL::bigint              AS open_msg_cnt,
       NULL::bigint              AS vst_msg_cnt,
       NULL::bigint              AS undelivered_msg,
       NULL::bigint              AS open,
       NULL::bigint              AS click,
       NULL::bigint              AS open_jdp,
       NULL::integer             AS away_revenue,
       NULL::bigint              AS away,
       NULL::bigint              AS message_with_open_away_cnt,
       NULL::bigint              AS message_with_alert_view_cnt,
       NULL::bigint              AS message_with_open_jdp_cnt,
       NULL::bigint              AS message_with_applies_cnt,
       NULL::bigint              AS apply_cnt,
       NULL::bigint              AS away_jdp_cnt
FROM kku.v_account_agg_edit v_account_agg
UNION ALL
SELECT 'v_account_agg_unsub'::text                                                                               AS table_name,
       v_account_agg.country_name,
       v_account_agg.create_date + v_account_agg.account_unsub_date_period::double precision *
                                   '1 day'::interval                                                             AS action_date,
       v_account_agg.account_source,
       v_account_agg.send_interval,
       v_account_agg.unsub_type,
       v_account_agg.account_unsub_date_period,
       v_account_agg.source_name,
       v_account_agg.source_is_paid,
       NULL::bigint                                                                                              AS account_by_reg_cnt,
       v_account_agg.account_cnt                                                                                 AS unsub_account,
       v_account_agg.varified_account_cnt,
       v_account_agg.account_revenue,
       v_account_agg.session_cnt,
       NULL::integer                                                                                             AS letter_type,
       NULL::character varying                                                                                   AS letter_name,
       NULL::integer                                                                                             AS calc_result_count,
       NULL::character varying                                                                                   AS account_alerts,
       NULL::integer                                                                                             AS account_send_interval,
       NULL::integer                                                                                             AS account_traffic_source,
       NULL::date                                                                                                AS account_date,
       NULL::bigint                                                                                              AS alertview,
       NULL::bigint                                                                                              AS account_cnt,
       NULL::bigint                                                                                              AS account_unsub_cnt,
       NULL::bigint                                                                                              AS sent_msg_cnt,
       NULL::bigint                                                                                              AS open_msg_cnt,
       NULL::bigint                                                                                              AS vst_msg_cnt,
       NULL::bigint                                                                                              AS undelivered_msg,
       NULL::bigint                                                                                              AS open,
       NULL::bigint                                                                                              AS click,
       NULL::bigint                                                                                              AS open_jdp,
       NULL::integer                                                                                             AS away_revenue,
       NULL::bigint                                                                                              AS away,
       NULL::bigint                                                                                              AS message_with_open_away_cnt,
       NULL::bigint                                                                                              AS message_with_alert_view_cnt,
       NULL::bigint                                                                                              AS message_with_open_jdp_cnt,
       NULL::bigint                                                                                              AS message_with_applies_cnt,
       NULL::bigint                                                                                              AS apply_cnt,
       NULL::bigint                                                                                              AS away_jdp_cnt
FROM kku.v_account_agg_edit v_account_agg
WHERE v_account_agg.unsub_type IS NOT NULL
  AND (v_account_agg.create_date + v_account_agg.account_unsub_date_period::double precision * '1 day'::interval) <
      CURRENT_DATE
UNION ALL
SELECT 'v_email_metric_agg'::text   AS table_name,
       v_email_metric_agg.country_name,
       v_email_metric_agg.sent_date AS action_date,
       NULL::text                   AS account_source,
       NULL::integer                AS send_interval,
       NULL::integer                AS unsub_type,
       NULL::integer                AS account_unsub_date_period,
       NULL::character varying      AS source_name,
       NULL::boolean                AS source_is_paid,
       NULL::bigint                 AS account_by_reg_cnt,
       NULL::bigint                 AS unsub_account,
       NULL::bigint                 AS varified_account_cnt,
       NULL::numeric                AS account_revenue,
       NULL::bigint                 AS session_cnt,
       v_email_metric_agg.letter_type,
       v_email_metric_agg.letter_name,
       v_email_metric_agg.calc_result_count,
       v_email_metric_agg.account_alerts,
       v_email_metric_agg.account_send_interval,
       v_email_metric_agg.account_traffic_source,
       v_email_metric_agg.account_date,
       v_email_metric_agg.alertview,
       v_email_metric_agg.account_cnt,
       v_email_metric_agg.account_unsub_cnt,
       v_email_metric_agg.sent_msg_cnt,
       v_email_metric_agg.open_msg_cnt,
       v_email_metric_agg.vst_msg_cnt,
       v_email_metric_agg.undelivered_msg,
       v_email_metric_agg.open,
       v_email_metric_agg.click,
       v_email_metric_agg.open_jdp,
       v_email_metric_agg.away_revenue,
       v_email_metric_agg.away,
       v_email_metric_agg.message_with_open_away_cnt,
       v_email_metric_agg.message_with_alert_view_cnt,
       v_email_metric_agg.message_with_open_jdp_cnt,
       v_email_metric_agg.message_with_applies_cnt,
       v_email_metric_agg.apply_cnt,
       v_email_metric_agg.away_jdp_cnt
FROM v_email_metric_agg_tmp v_email_metric_agg;
