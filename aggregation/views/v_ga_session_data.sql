create or replace view aggregation.v_ga_session_data
            (country_code, date, channelgrouping, sourcemedium, is_mobile, user_country, is_local, session_cnt,
             bounce_rate, avg_session_duration)
as
WITH raw AS (
    SELECT "substring"(replace(replace(replace(replace(google_analytics_general.name::text, 'http://'::text, ''::text),
                                               'https://'::text, ''::text), 'www.'::text, ''::text), 'ja'::text,
                               'jp'::text), 1, 2) AS country_code,
           google_analytics_general.date,
           google_analytics_general.channelgrouping,
           google_analytics_general.sourcemedium,
           CASE
               WHEN google_analytics_general.devicecategory::text = 'desktop'::text THEN false
               ELSE true
               END                                AS is_mobile,
           CASE
               WHEN google_analytics_general.country::text = 'Bosnia & Herzegovina'::text
                   THEN 'Bosnia and Herzegovina'::character varying
               WHEN google_analytics_general.country::text = 'Cuba'::text THEN 'Republic of Cuba'::character varying
               WHEN google_analytics_general.country::text = 'Czechia'::text THEN 'Czech Republic'::character varying
               WHEN google_analytics_general.country::text = 'South Korea'::text
                   THEN 'Republic of Korea'::character varying
               WHEN google_analytics_general.country::text = 'El Salvador'::text
                   THEN 'The Republic of El Salvad'::character varying
               WHEN google_analytics_general.country::text = 'South Africa'::text
                   THEN 'Republic of South Africa'::character varying
               WHEN google_analytics_general.country::text = 'Macao'::text THEN 'China'::character varying
               WHEN google_analytics_general.country::text = 'Türkiye'::text THEN 'Turkey'::character varying
               ELSE google_analytics_general.country
               END                                AS user_country,
           sum(google_analytics_general.sessions) AS session_cnt,
           NULL::numeric                          AS bounce_rate,
           NULL::numeric                          AS avg_session_duration
    FROM imp_statistic.google_analytics_general
    WHERE google_analytics_general.date >= '2020-01-01'::date
      AND google_analytics_general.date <= '2022-06-26'::date
    GROUP BY ("substring"(replace(replace(replace(replace(google_analytics_general.name::text, 'http://'::text,
                                                          ''::text), 'https://'::text, ''::text), 'www.'::text,
                                          ''::text), 'ja'::text, 'jp'::text), 1, 2)), google_analytics_general.date,
             google_analytics_general.channelgrouping, google_analytics_general.sourcemedium,
             (
                 CASE
                     WHEN google_analytics_general.devicecategory::text = 'desktop'::text THEN false
                     ELSE true
                     END),
             (
                 CASE
                     WHEN google_analytics_general.country::text = 'Bosnia & Herzegovina'::text
                         THEN 'Bosnia and Herzegovina'::character varying
                     WHEN google_analytics_general.country::text = 'Cuba'::text
                         THEN 'Republic of Cuba'::character varying
                     WHEN google_analytics_general.country::text = 'Czechia'::text
                         THEN 'Czech Republic'::character varying
                     WHEN google_analytics_general.country::text = 'South Korea'::text
                         THEN 'Republic of Korea'::character varying
                     WHEN google_analytics_general.country::text = 'El Salvador'::text
                         THEN 'The Republic of El Salvad'::character varying
                     WHEN google_analytics_general.country::text = 'South Africa'::text
                         THEN 'Republic of South Africa'::character varying
                     WHEN google_analytics_general.country::text = 'Macao'::text THEN 'China'::character varying
                     WHEN google_analytics_general.country::text = 'Türkiye'::text THEN 'Turkey'::character varying
                     ELSE google_analytics_general.country
                     END)
    UNION ALL
    SELECT lower(countries.alpha_2::text)                                                                          AS country_code,
           ga_general.action_date                                                                                  AS date,
           ga_general.channel_grouping                                                                             AS channelgrouping,
           ga_general.source_medium                                                                                AS sourcemedium,
           CASE
               WHEN ga_general.device_category::text = 'desktop'::text THEN false
               ELSE true
               END                                                                                                 AS is_mobile,
           CASE
               WHEN ga_general.country::text = 'Bosnia & Herzegovina'::text
                   THEN 'Bosnia and Herzegovina'::character varying
               WHEN ga_general.country::text = 'Cuba'::text THEN 'Republic of Cuba'::character varying
               WHEN ga_general.country::text = 'Czechia'::text THEN 'Czech Republic'::character varying
               WHEN ga_general.country::text = 'South Korea'::text THEN 'Republic of Korea'::character varying
               WHEN ga_general.country::text = 'El Salvador'::text THEN 'The Republic of El Salvad'::character varying
               WHEN ga_general.country::text = 'South Africa'::text THEN 'Republic of South Africa'::character varying
               WHEN ga_general.country::text = 'Macao'::text THEN 'China'::character varying
               WHEN ga_general.country::text = 'Türkiye'::text THEN 'Turkey'::character varying
               ELSE ga_general.country
               END                                                                                                 AS user_country,
           sum(ga_general.sessions)                                                                                AS session_cnt,
           sum(ga_general.sessions::numeric * ga_general.bounce_rate) /
           sum(ga_general.sessions)::numeric                                                                       AS bounce_rate,
           sum(ga_general.sessions::numeric * ga_general.avg_session_duration) /
           sum(ga_general.sessions)::numeric                                                                       AS avg_session_duration
    FROM imp_api.ga_general
             JOIN dimension.countries ON ga_general.country_id = countries.id
    WHERE ga_general.action_date <= '2023-07-18'::date
       OR ga_general.action_date > '2023-07-18'::date AND ga_general.action_date < '2023-08-01'::date AND
          (lower(countries.alpha_2::text) <> ALL
           (ARRAY ['ua'::text, 'sk'::text, 'es'::text, 'bg'::text, 'ec'::text, 'dk'::text, 'do'::text, 'be'::text, 'my'::text, 'cr'::text, 'pr'::text, 'sa'::text, 'sv'::text, 'bh'::text, 'kw'::text, 'ph'::text, 'ba'::text, 'qa'::text, 'za'::text, 'jp'::text, 'nz'::text, 'sg'::text, 'az'::text, 'eg'::text]))
    GROUP BY (lower(countries.alpha_2::text)), ga_general.action_date, ga_general.channel_grouping,
             ga_general.source_medium,
             (
                 CASE
                     WHEN ga_general.device_category::text = 'desktop'::text THEN false
                     ELSE true
                     END),
             (
                 CASE
                     WHEN ga_general.country::text = 'Bosnia & Herzegovina'::text
                         THEN 'Bosnia and Herzegovina'::character varying
                     WHEN ga_general.country::text = 'Cuba'::text THEN 'Republic of Cuba'::character varying
                     WHEN ga_general.country::text = 'Czechia'::text THEN 'Czech Republic'::character varying
                     WHEN ga_general.country::text = 'South Korea'::text THEN 'Republic of Korea'::character varying
                     WHEN ga_general.country::text = 'El Salvador'::text
                         THEN 'The Republic of El Salvad'::character varying
                     WHEN ga_general.country::text = 'South Africa'::text
                         THEN 'Republic of South Africa'::character varying
                     WHEN ga_general.country::text = 'Macao'::text THEN 'China'::character varying
                     WHEN ga_general.country::text = 'Türkiye'::text THEN 'Turkey'::character varying
                     ELSE ga_general.country
                     END)
    UNION ALL
    SELECT lower(countries.alpha_2::text)                                                                AS country_code,
           ga4.action_date                                                                               AS date,
           ga4.channel_grouping                                                                          AS channelgrouping,
           concat(ga4.session_source, ' / ', ga4.session_medium)                                         AS sourcemedium,
           CASE
               WHEN ga4.device_category::text = 'desktop'::text THEN false
               ELSE true
               END                                                                                       AS is_mobile,
           CASE
               WHEN ga4.country::text = 'Bosnia & Herzegovina'::text THEN 'Bosnia and Herzegovina'::character varying
               WHEN ga4.country::text = 'Cuba'::text THEN 'Republic of Cuba'::character varying
               WHEN ga4.country::text = 'Czechia'::text THEN 'Czech Republic'::character varying
               WHEN ga4.country::text = 'South Korea'::text THEN 'Republic of Korea'::character varying
               WHEN ga4.country::text = 'El Salvador'::text THEN 'The Republic of El Salvad'::character varying
               WHEN ga4.country::text = 'South Africa'::text THEN 'Republic of South Africa'::character varying
               WHEN ga4.country::text = 'Macao'::text THEN 'China'::character varying
               WHEN ga4.country::text = 'Türkiye'::text THEN 'Turkey'::character varying
               ELSE ga4.country
               END                                                                                       AS user_country,
           sum(ga4.sessions)                                                                             AS sessions_cnt,
           sum(ga4.sessions - ga4.engaged_sessions)::numeric / sum(ga4.sessions)::numeric * 100::numeric AS bounce_rate,
           sum(ga4.user_engagement_duration)::numeric /
           sum(ga4.sessions)::numeric                                                                    AS avg_session_duration
    FROM imp_api.ga4_general ga4
             JOIN dimension.countries ON ga4.country_id = countries.id
    WHERE ga4.sessions > 0
      AND (ga4.action_date > '2023-07-18'::date AND ga4.action_date < '2023-08-01'::date AND
           (lower(countries.alpha_2::text) = ANY
            (ARRAY ['ua'::text, 'sk'::text, 'es'::text, 'bg'::text, 'ec'::text, 'dk'::text, 'do'::text, 'be'::text, 'my'::text, 'cr'::text, 'pr'::text, 'sa'::text, 'sv'::text, 'bh'::text, 'kw'::text, 'ph'::text, 'ba'::text, 'qa'::text, 'za'::text, 'jp'::text, 'nz'::text, 'sg'::text, 'az'::text, 'eg'::text]))
            OR
           ga4.action_date >= '2023-08-01'::date)
    GROUP BY (lower(countries.alpha_2::text)), ga4.action_date, ga4.channel_grouping,
             (concat(ga4.session_source, ' / ', ga4.session_medium)),
             (
                 CASE
                     WHEN ga4.device_category::text = 'desktop'::text THEN false
                     ELSE true
                     END),
             (
                 CASE
                     WHEN ga4.country::text = 'Bosnia & Herzegovina'::text
                         THEN 'Bosnia and Herzegovina'::character varying
                     WHEN ga4.country::text = 'Cuba'::text THEN 'Republic of Cuba'::character varying
                     WHEN ga4.country::text = 'Czechia'::text THEN 'Czech Republic'::character varying
                     WHEN ga4.country::text = 'South Korea'::text THEN 'Republic of Korea'::character varying
                     WHEN ga4.country::text = 'El Salvador'::text THEN 'The Republic of El Salvad'::character varying
                     WHEN ga4.country::text = 'South Africa'::text THEN 'Republic of South Africa'::character varying
                     WHEN ga4.country::text = 'Macao'::text THEN 'China'::character varying
                     WHEN ga4.country::text = 'Türkiye'::text THEN 'Turkey'::character varying
                     ELSE ga4.country
                     END)
)
SELECT r.country_code,
       r.date,
       r.channelgrouping::character varying(255) AS channelgrouping,
       r.sourcemedium::character varying(512)    AS sourcemedium,
       r.is_mobile,
       r.user_country,
       CASE
           WHEN r.user_country::text = dc.name_country_eng::text THEN true
           ELSE false
           END                                   AS is_local,
       r.session_cnt::numeric                    AS session_cnt,
       r.bounce_rate,
       r.avg_session_duration
FROM raw r
         JOIN dimension.countries dc ON lower(dc.alpha_2::text) = lower(r.country_code)
WHERE r.country_code <> 'jo'::text;

alter table aggregation.v_ga_session_data
    owner to ono;

grant select on aggregation.v_ga_session_data to readonly;

grant delete, insert, select, update on aggregation.v_ga_session_data to write_ono;

grant select on aggregation.v_ga_session_data to ypr;

grant select on aggregation.v_ga_session_data to vnov;

grant select on aggregation.v_ga_session_data to readonly_aggregation;

grant select on aggregation.v_ga_session_data to "pavlo.kvasnii";

