create view aggregation.v_subscription_model_jobs
            (date_diff, date, country, id_employer, active, banned, stopped, moderation, deleted, deactivated,
             premium_active, premium_active_7, premium_stopped, have_add_question)
as
SELECT j.date_diff,
       dd.dt      AS date,
       'rs'::text AS country,
       j.id_employer,
       j.active,
       j.banned,
       j.stopped,
       j.moderation,
       j.deleted,
       j.deactivated,
       j.premium_active,
       j.premium_active_7,
       j.premium_stopped,
       j.have_add_question
FROM aggregation.subscription_model_jobs j
         JOIN dimension.info_calendar dd ON j.date_diff = dd.date_diff;

alter table aggregation.v_subscription_model_jobs
    owner to vnov;

grant select on aggregation.v_subscription_model_jobs to readonly;

