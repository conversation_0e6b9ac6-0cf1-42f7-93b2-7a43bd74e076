with main_metrics_raw as (select m.country,
                                 is_mobile                                     as device_type_id,
                                 unnest(string_to_array(
                                         split_part(m.group_num::text,
                                                    ','::text, 1), ' '::text)) as test_iteration_group_str,
                                 m.action_datediff,
                                 m.metric_name,
                                 m.metric_value
                          from aggregation.session_abtest_main_metrics_agg m
                          where m.union_group = 'union 1'::text),
     main_metrics_parsed
                      as (select split_part(m.test_iteration_group_str, '-'::text, 1)::integer as test_id,
                                 split_part(m.test_iteration_group_str, '_'::text, 2)::integer as iteration,
                                 m.country,
                                 m.device_type_id,
                                 m.action_datediff,
                                 m.metric_name,
                                 m.metric_value
                          from main_metrics_raw m
                          where "position"(m.test_iteration_group_str, '_'::text) > 0),
     test_sessions    as (select m.test_id,
                                 m.iteration,
                                 m.country,
                                 m.device_type_id,
                                 sum(m.metric_value) filter (where metric_name = 'session_cnt') as session_cnt,
                                 sum(m.metric_value) filter (where metric_name = 'revenue')     as revenue
                          from main_metrics_parsed m
                          where metric_name = 'revenue'
                          group by m.test_id, m.iteration,
                                   m.country, m.device_type_id)
select test_id,
       iteration,
       country,
       device_type_id,
       revenue,
       revenue / sum(revenue)
                 over (partition by test_id, iteration, country) as device_sessions_proportion,
       revenue / sum(revenue)
                 over (partition by test_id, iteration, country) as device_revenue_proportion
from test_sessions;
