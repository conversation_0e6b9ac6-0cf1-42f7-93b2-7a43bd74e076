create or replace view aggregation.v_email_metric_agg as
SELECT countries.name_country_eng                   country_name,
       email_agg.sent_date,
       email_agg.letter_type,
       letter_type_name.letter_name,
       email_agg.calc_result_count,
       email_agg.max_away_position,
       email_agg.alert_position,
       email_agg.account_alerts,
       email_agg.account_send_interval,
       email_agg.account_traffic_source,
       email_agg.account_date                    AS account_date,
       sum(email_agg.alertview_cnt)              AS alertview,
       sum(email_agg.account_cnt)                AS account_cnt,
       sum(email_agg.account_unsub_cnt)          AS account_unsub_cnt,
       sum(email_agg.sent_msg)                   AS sent_msg_cnt,
       sum(email_agg.open_msg)                   AS open_msg_cnt,
       sum(email_agg.visit_msg)                  AS vst_msg_cnt,
       0                                         AS undelivered_msg,
       sum(email_agg.open_msg)                   AS open,
       sum(email_agg.visit_msg)                  AS click,
       sum(email_agg.jdp_cnt)                    AS open_jdp,
       0                                         AS away_revenue,
       sum(email_agg.away_cnt)                   AS away,
       sum(email_agg.message_with_away_cnt)      AS message_with_open_away_cnt,
       sum(email_agg.message_with_alertview_cnt) AS message_with_alert_view_cnt,
       sum(email_agg.message_with_jdp_cnt)       AS message_with_open_jdp_cnt,
       sum(email_agg.message_with_jdp_away_cnt)  AS message_with_applies_cnt,
       sum(email_agg.apply_cnt)                  as apply_cnt,
       sum(email_agg.away_jdp_cnt)               as away_jdp_cnt
FROM aggregation.email_metric_daily email_agg
         left join dimension.countries
                   on email_agg.country_id = countries.id
         left join dimension.letter_type_name
                   on email_agg.letter_type = letter_type_name.letter_type
WHERE email_agg.sent_date >= (CURRENT_DATE - 90)
GROUP BY countries.name_country_eng,
         email_agg.sent_date,
         email_agg.letter_type,
         letter_type_name.letter_name,
         email_agg.calc_result_count,
         email_agg.max_away_position,
         email_agg.alert_position,
         email_agg.account_alerts,
         email_agg.account_send_interval,
         email_agg.account_traffic_source,
         email_agg.account_date

union all

Select countries.name_country_eng,
       undelivered_email.action_date,
       null,
       null,
       null,
       null,
       null,
       null,
       null,
       null,
       null,
       null,
       null,
       null,
       null,
       null,
       null,
       count(undelivered_email.id),
       null,
       null,
       null,
       null,
       null,
       null,
       null,
       null,
       null,
       null,
       null
from imp.undelivered_email
         join dimension.countries
              on undelivered_email.country_id = countries.id
where undelivered_email.action_date >= current_date - 90
group by countries.name_country_eng,
         undelivered_email.action_date

union all

Select countries.name_country_eng,
       public.fn_get_timestamp_from_date_diff(adv_revenue_by_placement_and_src.date_diff),
       case
           when adv_revenue_by_placement_and_src.placement = 'alertview' then 1
           when adv_revenue_by_placement_and_src.placement = 'letter type 8' then 8
           end,
       case
           when adv_revenue_by_placement_and_src.placement = 'alertview' then 'Default'
           when adv_revenue_by_placement_and_src.placement = 'letter type 8' then 'Paid_Job_List'
           end,
       null,
       null,
       null,
       null,
       null,
       null,
       null,
       null,
       null,
       null,
       null,
       null,
       null,
       null,
       null,
       null,
       null,
       sum(revenue_usd),
       null,
       null,
       null,
       null,
       null,
       null,
       null
from imp.adv_revenue_by_placement_and_src
         join dimension.countries
              on adv_revenue_by_placement_and_src.country = countries.id
where adv_revenue_by_placement_and_src.date_diff >= public.fn_get_date_diff(current_date) - 90
  and placement in ('alertview', 'letter type 8')
group by countries.name_country_eng,
         public.fn_get_timestamp_from_date_diff(adv_revenue_by_placement_and_src.date_diff),
         case
             when adv_revenue_by_placement_and_src.placement = 'alertview' then 1
             when adv_revenue_by_placement_and_src.placement = 'letter type 8' then 8
             end,
         case
             when adv_revenue_by_placement_and_src.placement = 'alertview' then 'Default'
             when adv_revenue_by_placement_and_src.placement = 'letter type 8' then 'Paid_Job_List'
             end;



alter table aggregation.v_email_metric_agg
    owner to ono;

grant select on aggregation.v_email_metric_agg to readonly;

grant select on aggregation.v_email_metric_agg to tma;

grant select on aggregation.v_email_metric_agg to ypr;

grant select on aggregation.v_email_metric_agg to vnov;

grant delete, insert, references, select, trigger, truncate, update on aggregation.v_email_metric_agg to ypr;

