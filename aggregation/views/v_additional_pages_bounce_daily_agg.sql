create view aggregation.v_additional_pages_bounce_daily_agg
            (country_name, action_date, salary_page_type_id, traffic_source_ga, device_category, avg_session_duration,
             bounce_cnt, session_cnt)
as
SELECT c.name_country_eng                                                        AS country_name,
       glpb.page_name,
       glpb.action_date,
       glpb.source                                                               AS traffic_source_ga,
       glpb.device_category,
       coalesce(sum(glpb.avg_session_duration) / sum(glpb.sessions)::numeric, 0) AS avg_session_duration,
       coalesce(sum(glpb.bounces), 0)                                            AS bounce_cnt,
       coalesce(sum(glpb.sessions), 0)                                           AS session_cnt
FROM imp_api.ga_landing_page_bounce glpb
         LEFT JOIN dimension.countries c ON glpb.country_id = c.id
WHERE (glpb.country_id = ANY
       (ARRAY [1, 2, 3, 11, 4, 7, 9, 18, 19, 20, 21, 26, 33, 35, 36, 39, 10, 51, 17, 55, 57, 59, 60, 61, 64, 66, 5, 6, 12, 13, 14, 15, 16]))
  AND glpb.action_date >= '2022-12-05'::date
  AND glpb.action_date <= '2023-06-30'::date
GROUP BY c.name_country_eng, glpb.action_date, glpb.page_name, glpb.source, glpb.device_category
UNION ALL
SELECT c.name_country_eng                                                                              AS country_name,
       ga4.page_name,
       ga4.action_date,
       ga4.session_source                                                                              AS traffic_source_ga,
       ga4.device_category,
       coalesce(sum(ga4.avg_session_duration * ga4.sessions::numeric) / sum(ga4.sessions)::numeric,
                0)                                                                                     AS avg_session_duration,
       coalesce(sum(ga4.bounce_rate * ga4.sessions::numeric), 0)                                       AS bounce_cnt,
       coalesce(sum(ga4.sessions), 0)                                                                  AS session_cnt
FROM imp_api.ga4_landing_page_bounce ga4
         LEFT JOIN dimension.countries c ON ga4.country_id = c.id
WHERE (ga4.country_id = ANY
       (ARRAY [1, 2, 3, 11, 4, 7, 9, 18, 19, 20, 21, 26, 33, 35, 36, 39, 10, 51, 17, 55, 57, 59, 60, 61, 64, 66, 5, 6, 12, 13, 14, 15, 16]))
  AND ga4.action_date >= '2023-07-01'::date
GROUP BY c.name_country_eng, ga4.action_date, ga4.page_name, ga4.session_source, ga4.device_category;

alter table aggregation.v_additional_pages_bounce_daily_agg
    owner to postgres;

grant select on aggregation.v_additional_pages_bounce_daily_agg to readonly;

grant select on aggregation.v_additional_pages_bounce_daily_agg to writeonly_pyscripts;