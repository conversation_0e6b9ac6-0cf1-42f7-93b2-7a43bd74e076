create view v_job_kaiju_category
            (parent_category_id, parent_category_name, child_category_id, child_category_name, level) as
WITH RECURSIVE
    categories AS (SELECT DISTINCT job_kaiju_category.id_parent   AS parent_category_id,
                                   job_kaiju_category.parent_name AS parent_category_name,
                                   job_kaiju_category.id_child    AS child_category_id,
                                   job_kaiju_category.child_name  AS child_category_name,
                                   1                              AS level
                   FROM dimension.job_kaiju_category
                   UNION ALL
                   SELECT c.parent_category_id,
                          c.parent_category_name,
                          child.id_child   AS child_category_id,
                          child.child_name AS child_category_name,
                          c.level + 1      AS level
                   FROM categories c
                   JOIN dimension.job_kaiju_category child ON c.child_category_id = child.id_parent),
    tree AS (SELECT categories.parent_category_id,
                    categories.parent_category_name,
                    categories.child_category_id,
                    categories.child_category_name
             FROM categories
             WHERE NOT (categories.parent_category_name IN (SELECT DISTINCT categories_1.child_category_name
                                                            FROM categories categories_1)))
SELECT tree.parent_category_id,
       tree.parent_category_name,
       tree.child_category_id,
       tree.child_category_name,
       1 AS level
FROM tree
UNION
SELECT job_kaiju_category.id_parent   AS parent_category_id,
       job_kaiju_category.parent_name AS parent_category_name,
       job_kaiju_category.id_parent   AS child_category_id,
       job_kaiju_category.parent_name AS child_category_name,
       1                              AS level
FROM dimension.job_kaiju_category
WHERE NOT (job_kaiju_category.id_parent IN (SELECT tree.child_category_id
                                            FROM tree));

alter table v_job_kaiju_category
    owner to ono;
