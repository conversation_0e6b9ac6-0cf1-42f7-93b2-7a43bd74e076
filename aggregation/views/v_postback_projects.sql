create view aggregation.v_postback_projects
            (id_country, project_id, conversion_start_date_diff, conversion_max_date_diff, conversion_cnt) as
select
    t.country_id,
    t.project_id,
    fn_get_date_diff(min(t.session_date)::timestamp without time zone) as conversion_start_date_diff,
    fn_get_date_diff(max(t.session_date)::timestamp without time zone) as conversion_max_date_diff,
    sum(t.conversions)                                                 as conversion_cnt
from
    aggregation.project_conversions_daily t
where
      t.metric::text = 'aways'::text
  and t.conversions > 0
group by
    t.country_id, t.project_id;


alter table aggregation.v_postback_projects
    owner to ono;

grant select on aggregation.v_postback_projects to ypr;

grant select on aggregation.v_postback_projects to user_agg_team;