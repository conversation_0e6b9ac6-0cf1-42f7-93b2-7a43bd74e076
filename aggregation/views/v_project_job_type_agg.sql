create view aggregation.v_project_job_type_agg
            (country_code, country_name, job_datediff, id_project, project_name, job_type, job_cnt, unique_job_cnt) as
SELECT c.alpha_2          AS country_code,
       c.name_country_eng AS country_name,
       pjta.job_datediff,
       pjta.id_project,
       ip.name            AS project_name,
       pjta.job_type,
       pjta.job_cnt,
       pjta.unique_job_cnt
FROM aggregation.project_job_type_agg pjta
         LEFT JOIN dimension.countries c ON c.id = pjta.country_id
         LEFT JOIN dimension.info_project ip ON ip.country = pjta.country_id AND ip.id = pjta.id_project;

alter table aggregation.v_project_job_type_agg
    owner to dap;

grant select on aggregation.v_project_job_type_agg to readonly;

