create view v_collar_cluster_click_agg
            (has_one_cluster, placement, is_paid_source, local_cluster_id, local_cluster_name, channel, revenue_usd,
             paid_clicks_cnt, away_clicks_cnt, jdp_clicks_cnt, country, datediff, common_cluster_id,
             common_cluster_name, "group")
as
SELECT cc.cluster_cnt         AS has_one_cluster,
       cc.placement,
       cc.is_paid_source,
       cc.local_cluster_id,
       cc.local_cluster_name,
       cc.channel,
       cc.revenue_usd,
       cc.paid_clicks_cnt,
       cc.away_clicks_cnt,
       cc.jdp_clicks_cnt,
       lower(c.alpha_2::text) AS country,
       cc.action_datediff     AS datediff,
       cl.common_cluster_id,
       cl.common_cluster_name,
       cl.collar_group        AS "group"
FROM aggregation.clicks_collars cc
         JOIN dimension.countries c ON c.id = cc.country_id
         LEFT JOIN aggregation.dic_job_cluster_group cl
                   ON cl.country_id = cc.country_id AND cl.local_cluster_name = cc.local_cluster_name;

alter table v_collar_cluster_click_agg
    owner to ono;

grant select on v_collar_cluster_click_agg to readonly;

grant select on v_collar_cluster_click_agg to ypr;

