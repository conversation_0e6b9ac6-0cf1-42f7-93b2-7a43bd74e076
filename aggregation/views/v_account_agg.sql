create view aggregation.v_account_agg
            (continent_name, region_name, country_name, create_date, account_source, send_interval, unsub_type,
             account_unsub_date_period, source_name, source_is_paid, account_cnt, varified_account_cnt, account_revenue,
             session_cnt)
as
SELECT v_country_info.continent_name,
       v_country_info.region_name,
       v_country_info.country_name,
       '2020-01-01'::date                 AS create_date,
       auth_source.source_name            AS account_source,
       accounts.account_send_interval     AS send_interval,
       accounts.account_unsub_type        AS unsub_type,
       accounts.account_unsub_date_period,
       accounts.account_source_name       AS source_name,
       accounts.account_source_is_paid    AS source_is_paid,
       sum(accounts.account_cnt)          AS account_cnt,
       sum(accounts.varified_account_cnt) AS varified_account_cnt,
       sum(accounts.account_revenue)      AS account_revenue,
       0                                  AS session_cnt
FROM email.account_agg accounts
         LEFT JOIN dimension.v_country_info ON accounts.country_id = v_country_info.country_id
         LEFT JOIN dimension.auth_source ON accounts.source = auth_source.id
WHERE accounts.account_date::text < '2021-01-01'::text
GROUP BY v_country_info.continent_name, v_country_info.region_name, v_country_info.country_name,
         auth_source.source_name, accounts.account_send_interval, accounts.account_unsub_type,
         accounts.account_unsub_date_period, accounts.account_source_name, accounts.account_source_is_paid
UNION ALL
SELECT v_country_info.continent_name,
       v_country_info.region_name,
       v_country_info.country_name,
       accounts.account_date::date     AS create_date,
       auth_source.source_name         AS account_source,
       accounts.account_send_interval  AS send_interval,
       accounts.account_unsub_type     AS unsub_type,
       accounts.account_unsub_date_period,
       accounts.account_source_name    AS source_name,
       accounts.account_source_is_paid AS source_is_paid,
       accounts.account_cnt,
       accounts.varified_account_cnt,
       accounts.account_revenue,
       0                               AS session_cnt
FROM email.account_agg accounts
         LEFT JOIN dimension.v_country_info ON accounts.country_id = v_country_info.country_id
         LEFT JOIN dimension.auth_source ON accounts.source = auth_source.id
WHERE accounts.account_date::text >= '2021-01-01'::text
UNION ALL
SELECT v_country_info.continent_name,
       v_country_info.region_name,
       v_country_info.country_name,
       fn_get_timestamp_from_date_diff(session_daily_agg.action_datediff)                                     AS create_date,
       NULL::text                                                                                             AS account_source,
       NULL::integer                                                                                          AS send_interval,
       NULL::integer                                                                                          AS unsub_type,
       NULL::integer                                                                                          AS account_unsub_date_period,
       NULL::character varying                                                                                AS source_name,
       NULL::boolean                                                                                          AS source_is_paid,
       NULL::bigint                                                                                           AS account_cnt,
       NULL::bigint                                                                                           AS varified_account_cnt,
       NULL::numeric                                                                                          AS account_revenue,
       sum(COALESCE(session_daily_agg.total_session_cnt, 0) -
           COALESCE(session_daily_agg.bot_session_cnt, 0))                                                    AS session_cnt
FROM aggregation.session_daily_agg
         LEFT JOIN dimension.v_country_info ON session_daily_agg.country_id = v_country_info.country_id
GROUP BY v_country_info.continent_name, v_country_info.region_name, v_country_info.country_name,
         (fn_get_timestamp_from_date_diff(session_daily_agg.action_datediff));

alter table aggregation.v_account_agg
    owner to ono;

grant select on aggregation.v_account_agg to readonly;

grant select on aggregation.v_account_agg to ypr;

grant select on aggregation.v_account_agg to writeonly_pyscripts;

grant select on aggregation.v_account_agg to user_agg_team;

