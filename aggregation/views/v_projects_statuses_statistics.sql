create view v_projects_statuses_statistics(country, date, status, manager, cnt_projects, revenue_usd) as
WITH distinct_dates AS (
    SELECT generate_series(date_trunc('month'::text, ((SELECT min(auction_click_statistic.date) AS min
                                                       FROM imp_statistic.auction_click_statistic))::timestamp with time zone),
                           CURRENT_DATE::timestamp without time zone::timestamp with time zone,
                           '1 mon'::interval)::date AS date
),
     manager_data AS (
         SELECT c.alpha_2                                                                                             AS country,
                sm.id_project,
                split_part(COALESCE(NULLIF(split_part(sm.sale_manager::text, ' '::text, 5), ''::text),
                                    NULLIF(split_part(sm.sale_manager::text, ' '::text, 4), ''::text),
                                    NULLIF(split_part(sm.sale_manager::text, ' '::text, 3), ''::text),
                                    NULLIF(split_part(sm.sale_manager::text, ' '::text, 2), ''::text),
                                    NULLIF(split_part(sm.sale_manager::text, ' '::text, 1), ''::text)), '@'::text,
                           1)                                                                                         AS manager
         FROM aggregation.v_sale_manager sm
                  JOIN dimension.countries c ON c.id = sm.country
         WHERE sm.id_project IS NOT NULL
     ),
     projects_data AS (
         SELECT acs.country,
                acs.id_project,
                date_trunc('month'::text, acs.date::timestamp with time zone) AS date,
                sum(acs.click_count - acs.test_count)                         AS cnt_clicks,
                sum(acs.total_value)                                          AS revenue_usd
         FROM imp_statistic.auction_click_statistic acs
         WHERE acs.click_price > 0::numeric
         GROUP BY acs.country, acs.id_project, (date_trunc('month'::text, acs.date::timestamp with time zone))
         HAVING sum(acs.click_count - acs.test_count) > 0
     ),
     project_dates AS (
         SELECT dp.country,
                dd.date,
                dp.id_project
         FROM distinct_dates dd
                  CROSS JOIN (SELECT DISTINCT projects_data.country,
                                              projects_data.id_project
                              FROM projects_data) dp
     ),
     current_data AS (
         SELECT pd.country,
                pd.date,
                pd.id_project,
                COALESCE(man.manager, 'unknown'::text) AS manager,
                p.cnt_clicks,
                p.revenue_usd
         FROM project_dates pd
                  LEFT JOIN projects_data p
                            ON pd.date = p.date AND pd.id_project = p.id_project AND pd.country::text = p.country::text
                  LEFT JOIN manager_data man ON man.country::text = pd.country::text AND man.id_project = pd.id_project
     ),
     projects_statuses AS (
         SELECT cd.country,
                cd.date,
                cd.id_project,
                cd.manager,
                COALESCE(cd.revenue_usd, 0::numeric) AS revenue_usd,
                COALESCE(cd.cnt_clicks, 0::bigint)   AS cnt_clicks,
                COALESCE(prev.cnt_clicks, 0::bigint) AS prev_clicks,
                COALESCE(next.cnt_clicks, 0::bigint) AS next_clicks,
                CASE
                    WHEN COALESCE(cd.cnt_clicks, 0::bigint) > 0 AND COALESCE(prev.cnt_clicks, 0::bigint) > 0 AND
                         COALESCE(next.cnt_clicks, 0::bigint) > 0 THEN 'active'::text
                    WHEN COALESCE(cd.cnt_clicks, 0::bigint) > 0 AND COALESCE(prev.cnt_clicks, 0::bigint) = 0 AND
                         COALESCE(next.cnt_clicks, 0::bigint) > 0 THEN 'new'::text
                    WHEN COALESCE(cd.cnt_clicks, 0::bigint) > 0 AND COALESCE(prev.cnt_clicks, 0::bigint) > 0 AND
                         COALESCE(next.cnt_clicks, 0::bigint) = 0 THEN 'ex'::text
                    WHEN COALESCE(cd.cnt_clicks, 0::bigint) > 0 AND COALESCE(prev.cnt_clicks, 0::bigint) = 0 AND
                         COALESCE(next.cnt_clicks, 0::bigint) = 0 THEN 'new-ex'::text
                    ELSE NULL::text
                    END                              AS status
         FROM current_data cd
                  JOIN current_data prev
                       ON cd.date = (prev.date + '1 mon'::interval) AND cd.id_project = prev.id_project AND
                          cd.country::text = prev.country::text
                  JOIN current_data next
                       ON cd.date = (next.date - '1 mon'::interval) AND cd.id_project = next.id_project AND
                          cd.country::text = next.country::text
     ),
     statuses_agg AS (
         SELECT ps.country,
                ps.date,
                ps.status,
                ps.manager,
                count(ps.id_project) AS cnt_projects,
                sum(ps.revenue_usd)  AS revenue_usd
         FROM projects_statuses ps
         WHERE ps.status IS NOT NULL
         GROUP BY ps.country, ps.date, ps.status, ps.manager
     )
SELECT f.country,
       f.date,
       f.status,
       f.manager,
       COALESCE(sa.cnt_projects, 0::bigint) AS cnt_projects,
       COALESCE(sa.revenue_usd, 0::numeric) AS revenue_usd
FROM (SELECT d.date,
             s.status,
             c.country,
             m.manager
      FROM (SELECT DISTINCT projects_statuses.date
            FROM projects_statuses
            WHERE projects_statuses.status IS NOT NULL) d
               CROSS JOIN (SELECT DISTINCT projects_statuses.status
                           FROM projects_statuses
                           WHERE projects_statuses.status IS NOT NULL) s
               CROSS JOIN (SELECT DISTINCT projects_statuses.country
                           FROM projects_statuses
                           WHERE projects_statuses.status IS NOT NULL) c
               CROSS JOIN (SELECT DISTINCT projects_statuses.manager
                           FROM projects_statuses
                           WHERE projects_statuses.manager IS NOT NULL) m) f
         LEFT JOIN statuses_agg sa
                   ON f.date = sa.date AND f.status = sa.status AND f.country::text = sa.country::text AND
                      f.manager = sa.manager;

alter table v_projects_statuses_statistics
    owner to ono;

grant select on v_projects_statuses_statistics to readonly;

grant select on v_projects_statuses_statistics to ypr;

