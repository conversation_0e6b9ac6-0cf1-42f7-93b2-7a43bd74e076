create or replace view aggregation.v_auction_click_statistic
            (country_id, country, date, action_date, id_project, site, contractor, id_campaign, currency, campaign,
             click_count, session_count, ip_count, to_jdp_count, from_jdp_count, total_value, id_user, test_count,
             budget, organic_count, paid_overflow_count, manager, impressions_count, user_currency_total_value,
             contractor_size, job_count, is_job_ad_exchange_project, duplicated_count, total_value_discount,
             external_click_cnt, external_revenue, away_count, user_budget, user_daily_budget, cmp_budget,
             cmp_daily_budget, crm_link, is_category_segmentation, paid_job_count, organic_job_count,
             avg_cpc_for_paid_job_count_usd, client_type)
as
WITH auction AS (SELECT auction_click_statistic_analytics.country_id,
                        countries.alpha_2                            AS country,
                        auction_click_statistic_analytics.date::date AS date,
                        auction_click_statistic_analytics.id_project,
                        auction_click_statistic_analytics.site,
                        auction_click_statistic_analytics.contractor,
                        auction_click_statistic_analytics.id_campaign,
                        auction_click_statistic_analytics.campaign,
                        auction_click_statistic_analytics.currency,
                        auction_click_statistic_analytics.click_count,
                        auction_click_statistic_analytics.session_count,
                        auction_click_statistic_analytics.ip_count,
                        CASE
                            WHEN auction_click_statistic_analytics.date::text < '2023-04-01'::text THEN
                                    auction_click_statistic_analytics.total_value + COALESCE(
                                        - (auction_click_statistic_analytics.total_value -
                                           (auction_click_statistic_analytics.click_count -
                                            COALESCE(auction_click_statistic_analytics.test_count, 0) - COALESCE(
                                                    CASE
                                                        WHEN auction_click_statistic_analytics.test_count > 0 THEN 0
                                                        ELSE auction_click_statistic_analytics.duplicated_count
                                                        END, 0))::numeric *
                                           (auction_click_statistic_analytics.total_value /
                                            COALESCE(auction_click_statistic_analytics.click_count, 1)::numeric)),
                                        0::numeric)
                            ELSE auction_click_statistic_analytics.total_value
                            END                                      AS total_value,
                        auction_click_statistic_analytics.id_user,
                        auction_click_statistic_analytics.test_count,
                        auction_click_statistic_analytics.organic_count,
                        auction_click_statistic_analytics.paid_overflow_count,
                        auction_click_statistic_analytics.duplicated_count,
                        auction_click_statistic_analytics.total_value_discount,
                        auction_click_statistic_analytics.away_count,
                        auction_click_statistic_analytics.user_budget,
                        auction_click_statistic_analytics.user_daily_budget,
                        auction_click_statistic_analytics.cmp_budget,
                        auction_click_statistic_analytics.cmp_daily_budget,
                        auction_click_statistic_analytics.is_category_segmentation
                 FROM aggregation.auction_click_statistic_analytics
                          JOIN dimension.countries ON auction_click_statistic_analytics.country_id = countries.id
                 WHERE auction_click_statistic_analytics.date::text >= '2023-03-01'::text
                   AND auction_click_statistic_analytics.click_count > 0),
     acs_1 AS (SELECT countries.id                            AS country_id,
                      acs_1.country,
                      acs_1.date::timestamp without time zone AS date,
                      acs_1.date::timestamp without time zone AS action_date,
                      acs_1.id_project,
                      acs_1.site,
                      acs_1.contractor,
                      acs_1.id_campaign,
                      acs_1.campaign,
                      acs_1.currency,
                      sum(
                              CASE
                                  WHEN acs_1.date < '2023-04-01'::date THEN acs_1.click_count - acs_1.duplicated_count
                                  ELSE acs_1.click_count
                                  END)                        AS click_count,
                      sum(acs_1.session_count)                AS session_count,
                      sum(acs_1.ip_count)                     AS ip_count,
                      0                                       AS to_jdp_count,
                      0                                       AS from_jdp_count,
                      sum(acs_1.total_value)                  AS total_value,
                      acs_1.id_user,
                      sum(acs_1.test_count)                   AS test_count,
                      0                                       AS budget,
                      sum(acs_1.organic_count)                AS organic_count,
                      sum(acs_1.paid_overflow_count)          AS paid_overflow_count,
                      v_sale_manager.sale_manager             AS manager,
                      0                                       AS job_count,
                      sum(acs_1.duplicated_count)             AS duplicated_count,
                      sum(acs_1.total_value_discount)         AS total_value_discount,
                      sum(acs_1.away_count)                   AS away_count,
                      max(acs_1.user_budget)                  AS user_budget,
                      max(acs_1.user_daily_budget)            AS user_daily_budget,
                      max(acs_1.cmp_budget)                   AS cmp_budget,
                      max(acs_1.cmp_daily_budget)             AS cmp_daily_budget,
                      acs_1.is_category_segmentation
               FROM auction acs_1
                        LEFT JOIN dimension.info_currency ic_1
                                  ON acs_1.country_id = ic_1.country AND acs_1.currency::text = ic_1.name::text
                        LEFT JOIN dimension.countries ON acs_1.country::text = countries.alpha_2::text
                        LEFT JOIN aggregation.v_sale_manager ON countries.id = v_sale_manager.country AND
                                                                acs_1.id_project = v_sale_manager.id_project
               GROUP BY acs_1.country, acs_1.date, acs_1.id_project, acs_1.site, acs_1.contractor, acs_1.id_campaign,
                        acs_1.campaign, acs_1.id_user, v_sale_manager.sale_manager, countries.id, acs_1.currency,
                        acs_1.is_category_segmentation),
     crm AS (SELECT rows.country_code,
                    rows.account_name,
                    rows.id_account,
                    concat('https://jooble.creatio.com/0/Nui/ViewModule.aspx#CardModuleV2/UsrAccountPageV21/edit/',
                           rows.id_account) AS crm_link
             FROM (SELECT "left"(crm_account_opportunity.account_country::text, 2) AS country_code,
                          lower(btrim(crm_account_opportunity.account_name::text)) AS account_name,
                          max(crm_account_opportunity.account_id::text)            AS id_account
                   FROM aggregation.crm_account_opportunity
                   GROUP BY ("left"(crm_account_opportunity.account_country::text, 2)),
                            (lower(btrim(crm_account_opportunity.account_name::text)))) rows
             WHERE length(COALESCE(rows.country_code, ''::text)) > 1),
     acs AS (SELECT acs_1.country_id,
                    acs_1.country,
                    acs_1.date,
                    acs_1.action_date,
                    acs_1.id_project,
                    acs_1.site,
                    acs_1.contractor,
                    acs_1.id_campaign,
                    acs_1.campaign,
                    acs_1.currency,
                    acs_1.click_count,
                    acs_1.session_count,
                    acs_1.ip_count,
                    acs_1.to_jdp_count,
                    acs_1.from_jdp_count,
                    acs_1.total_value,
                    acs_1.id_user,
                    acs_1.test_count,
                    acs_1.budget,
                    acs_1.organic_count,
                    acs_1.paid_overflow_count,
                    acs_1.manager,
                    first_value(acs_1.currency)
                    OVER (PARTITION BY acs_1.country, acs_1.id_user ORDER BY acs_1.date DESC)    AS user_currency,
                    row_number() OVER (PARTITION BY acs_1.country, acs_1.date, acs_1.id_project) AS campaign_number,
                    acs_1.job_count,
                    acs_1.duplicated_count,
                    acs_1.total_value_discount,
                    acs_1.away_count,
                    acs_1.user_budget,
                    acs_1.user_daily_budget,
                    acs_1.cmp_budget,
                    acs_1.cmp_daily_budget,
                    acs_1.is_category_segmentation
             FROM acs_1),
     external_stat AS (SELECT app.id_project,
                              app.country,
                              app.datetime::date AS date,
                              count(
                                      CASE
                                          WHEN app.action_type = 'click'::text THEN app.id_click
                                          ELSE NULL::numeric
                                          END)   AS external_click_cnt,
                              sum(app.price)     AS external_revenue,
                              'appcast'::text    AS type
                       FROM imp_statistic.conversions_appcast app
                       WHERE app.datetime >= '2023-03-01 00:00:00'::timestamp without time zone
                       GROUP BY app.id_project, (app.datetime::date), app.country
                       UNION ALL
                       SELECT ind.id_project,
                              ind.country,
                              ind.date         AS dates,
                              sum(ind.clicks)  AS count_clic,
                              sum(ind.revenue) AS external_revenue,
                              'indeed'::text   AS type
                       FROM imp_statistic.conversions_indeed ind
                       WHERE ind.date >= '2023-03-01'::date
                       GROUP BY ind.id_project, ind.country, ind.date
                       UNION ALL
                       SELECT jov.id_project,
                              jov.country,
                              jov.date        AS dates,
                              sum(jov.clicks) AS count_clic,
                              sum(jov.spent)  AS external_revenue,
                              'joveo'::text   AS type
                       FROM imp_statistic.conversions_joveo jov
                       WHERE jov.date >= '2023-03-01'::date
                       GROUP BY jov.id_project, jov.country, jov.date),
     external_stat_additional AS (SELECT conversions_project_case.id_project,
                                         acs_1.country,
                                         acs_1.date,
                                         acs_1.total_value                                        AS total_value_jooble,
                                         sum(acs_1.total_value)
                                         OVER (PARTITION BY acs_1.date, conversions_case.case_id) AS total_value_by_case_jooble,
                                         (sum(acs_1.total_value) / COALESCE(sum(acs_1.total_value)
                                                                            OVER (PARTITION BY acs_1.date, conversions_case.case_id),
                                                                            1::numeric))::double precision *
                                         conversions_case.revenue                                 AS revenue_external,
                                         sum(acs_1.total_value) / COALESCE(sum(acs_1.total_value)
                                                                           OVER (PARTITION BY acs_1.date, conversions_case.case_id),
                                                                           1::numeric) *
                                         conversions_case.click::numeric                          AS clicks
                                  FROM acs acs_1
                                           JOIN imp_statistic.conversions_project_case
                                                ON acs_1.country::bpchar = conversions_project_case.country AND
                                                   acs_1.id_project = conversions_project_case.id_project
                                           JOIN imp_statistic.conversions_case ON conversions_case.date = acs_1.date AND
                                                                                  conversions_case.case_id =
                                                                                  conversions_project_case.case_id
                                  GROUP BY conversions_project_case.id_project, acs_1.country, acs_1.date,
                                           acs_1.total_value, conversions_case.revenue, conversions_case.case_id,
                                           conversions_case.click),
     external_group AS (SELECT external_stat_additional.id_project,
                               external_stat_additional.country,
                               external_stat_additional.date,
                               round(sum(external_stat_additional.clicks), 0) AS external_clicks,
                               sum(external_stat_additional.revenue_external) AS external_revenue
                        FROM external_stat_additional
                        GROUP BY external_stat_additional.id_project, external_stat_additional.country,
                                 external_stat_additional.date),
    external_stat_2 AS ( SELECT
                              conversions.id_project,
                              conversions.country,
                              conversions.date,
                              sum(
                                      CASE
                                          WHEN conversions.id_conversion_type = 2 THEN conversions.conversions
                                          ELSE NULL::numeric
                                          END)        AS external_click_cnt,
                              NULL::int               AS external_revenue,
                              'stepstone'::text       AS type
                       FROM imp_statistic.conversions
                       WHERE conversions.date >= '2023-03-01'::date
                       and ((country = 'AT' and id_project = 3) or (country = 'DE' and id_project = 36) or (country = 'BE' and id_project = 4))
                       GROUP BY conversions.id_project, conversions.country, conversions.date
                       UNION ALL
                       SELECT conversions.id_project,
                              conversions.country,
                              conversions.date,
                              sum(
                                      CASE
                                          WHEN conversions.id_conversion_type = 2 THEN conversions.conversions
                                          ELSE NULL::numeric
                                          END)        AS external_click_cnt,
                              NULL::int               AS external_revenue,
                              'totaljobs'::text       AS type
                       FROM imp_statistic.conversions
                       WHERE conversions.date >= '2023-03-01'::date
                       and (country = 'UK' and id_project = 4)
                       GROUP BY conversions.id_project, conversions.country, conversions.date),
     jobs AS (SELECT jobs_stat_daily.date,
                     jobs_stat_daily.id_country,
                     jobs_stat_daily.id_project,
                     jobs_stat_daily.id_campaign,
                     sum(jobs_stat_daily.paid_job_count)                 AS paid_job_count,
                     sum(jobs_stat_daily.organic_job_count)              AS organic_job_count,
                     max(jobs_stat_daily.avg_cpc_for_paid_job_count_usd) AS avg_cpc_for_paid_job_count_usd
              FROM aggregation.jobs_stat_daily
              WHERE jobs_stat_daily.date::text >= '2023-03-01'::text
              GROUP BY jobs_stat_daily.date, jobs_stat_daily.id_country, jobs_stat_daily.id_project,
                       jobs_stat_daily.id_campaign)
SELECT acs.country_id,
       acs.country::character varying(10)                              AS country,
       acs.date,
       acs.action_date,
       acs.id_project,
       acs.site,
       acs.contractor,
       acs.id_campaign,
       acs.user_currency                                               AS currency,
       acs.campaign,
       acs.click_count,
       acs.session_count,
       acs.ip_count,
       acs.to_jdp_count::bigint                                        AS to_jdp_count,
       acs.from_jdp_count::bigint                                      AS from_jdp_count,
       acs.total_value,
       acs.id_user,
       acs.test_count,
       acs.budget::numeric                                             AS budget,
       acs.organic_count,
       acs.paid_overflow_count,
       acs.manager,
       ims.impressions_count,
       acs.total_value / COALESCE(infh.value_to_usd, 1::numeric)       AS user_currency_total_value,
       '0'::text                                                       AS contractor_size,
       acs.job_count::bigint                                           AS job_count,
       CASE
           WHEN ip.hide_in_search THEN 1
           ELSE 0
           END                                                         AS is_job_ad_exchange_project,
       acs.duplicated_count,
       acs.total_value_discount,
       CASE
           WHEN acs.campaign_number = 1 THEN COALESCE(external_stat.external_click_cnt::numeric,
                                                      external_group.external_clicks,
                                                      external_stat_2.external_click_cnt)
           ELSE NULL::numeric
           END                                                         AS external_click_cnt,
       CASE
           WHEN acs.campaign_number = 1 THEN COALESCE(external_stat.external_revenue, external_group.external_revenue)
           ELSE NULL::numeric
           END                                                         AS external_revenue,
       acs.away_count,
       acs.user_budget * COALESCE(infh.value_to_usd, 1::numeric)       AS user_budget,
       acs.user_daily_budget * COALESCE(infh.value_to_usd, 1::numeric) AS user_daily_budget,
       acs.cmp_budget * COALESCE(infh.value_to_usd, 1::numeric)        AS cmp_budget,
       acs.cmp_daily_budget * COALESCE(infh.value_to_usd, 1::numeric)  AS cmp_daily_budget,
       crm.crm_link,
       acs.is_category_segmentation,
       jobs.paid_job_count,
       jobs.organic_job_count,
       jobs.avg_cpc_for_paid_job_count_usd,
       dic_info_project_source_type.name                               AS client_type
FROM acs
         LEFT JOIN imp_statistic.impression_statistic ims
                   ON ims.country = acs.country_id AND ims.id_campaign = acs.id_campaign AND
                      fn_get_date_diff(acs.date::date::timestamp without time zone) = ims.date_diff
         LEFT JOIN dimension.info_currency ic ON acs.country_id = ic.country AND acs.user_currency::text = ic.name::text
         LEFT JOIN LATERAL ( SELECT ich.value_to_usd
                             FROM dimension.info_currency_history ich
                             WHERE acs.country_id = ich.country
                               AND ich.date <= acs.date
                               AND ich.id_currency = ic.id
                               AND ich.value_to_usd IS NOT NULL
                               AND ich.value_to_usd <> 0::numeric
                             ORDER BY ich.date DESC
                             LIMIT 1) infh ON true
         LEFT JOIN dimension.info_project ip ON ip.country = acs.country_id AND ip.id = acs.id_project
         LEFT JOIN external_stat
                   ON acs.country::text = external_stat.country::text AND acs.id_project = external_stat.id_project AND
                      acs.date = external_stat.date
         LEFT JOIN crm ON acs.country::text = crm.country_code AND crm.account_name = acs.site::text
         LEFT JOIN jobs ON acs.date = jobs.date::date AND acs.country_id = jobs.id_country AND
                           acs.id_project = jobs.id_project AND acs.id_campaign = jobs.id_campaign
         LEFT JOIN aggregation.dic_info_project_source_type ON ip.source_type = dic_info_project_source_type.search_type
         LEFT JOIN external_group ON acs.id_project = external_group.id_project AND
                                     acs.country::text = external_group.country::text AND
                                     acs.date = external_group.date
         LEFT JOIN external_stat_2 ON acs.id_project = external_stat_2.id_project AND
                                      acs.country = external_stat_2.country AND
                                      acs.date = external_stat_2.date;

alter table aggregation.v_auction_click_statistic
    owner to ono;

grant select on aggregation.v_auction_click_statistic to readonly;

grant select on aggregation.v_auction_click_statistic to write_ono;

grant select on aggregation.v_auction_click_statistic to writeonly_pyscripts;

grant select on aggregation.v_auction_click_statistic to readonly_aggregation;
