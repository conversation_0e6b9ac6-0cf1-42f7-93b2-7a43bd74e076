create view aggregation.v_sentinel_statistic
            (country_id, country_name, action_date, sentinel_type, sentinel_name, revenue_usd, job_cnt,
             job_with_away_cnt, job_with_revenue_cnt, away_cnt, away_conversion_cnt, conversion_cnt, median_cpc_usd,
             parent_sentinel, parent_sentinel_type, job_project_id, job_project_name, avg_cpc_for_paid_job_count_usd,
             conversion_revenue_usd)
as
SELECT sentinel_statistic.country_id,
       sentinel_statistic.country_name,
       sentinel_statistic.action_date,
       sentinel_statistic.sentinel_type,
       sentinel_statistic.sentinel_name,
       sentinel_statistic.revenue_usd,
       sentinel_statistic.job_cnt,
       sentinel_statistic.job_with_away_cnt,
       sentinel_statistic.job_with_revenue_cnt,
       sentinel_statistic.away_cnt,
       sentinel_statistic.away_conversion_cnt,
       sentinel_statistic.conversion_cnt,
       sentinel_statistic.median_cpc_usd,
       sentinel_statistic.parent_sentinel,
       sentinel_statistic.parent_sentinel_type,
       sentinel_statistic.job_project_id,
       info_project.name AS job_project_name,
       sentinel_statistic.avg_cpc_for_paid_job_count_usd,
       sentinel_statistic.conversion_revenue_usd
FROM aggregation.sentinel_statistic
         LEFT JOIN dimension.info_project ON sentinel_statistic.country_id = info_project.country AND
                                             sentinel_statistic.job_project_id = info_project.id
WHERE sentinel_statistic.action_date >= (CURRENT_DATE - 30);

alter table aggregation.v_sentinel_statistic
    owner to ono;

