create or replace view aggregation.v_ppc_goals
            (date, country_code, revenue_goal, cost_goal, clicks_goal, net_revenue_goal) as
WITH targets_month AS (
    SELECT tg.date::date                                                                                             AS date,
           date_part('day'::text, date_trunc('MONTH'::text, tg.date::date::timestamp with time zone) +
                                  '1 mon -1 days'::interval)                                                         AS days_in_month,
           tg.country_code,
           tg.clicks_goal::double precision / date_part('day'::text,
                                                        date_trunc('MONTH'::text, tg.date::date::timestamp with time zone) +
                                                        '1 mon -1 days'::interval)                                   AS clicks_goal_daily,
           tg.net_revenue_goal::double precision / date_part('day'::text,
                                                             date_trunc('MONTH'::text, tg.date::date::timestamp with time zone) +
                                                             '1 mon -1 days'::interval)                              AS net_revenue_goal_daily,
           max(date_trunc('MONTH'::text, tg.date::date::timestamp with time zone) +
               '1 mon -1 days'::interval)::date                                                                      AS max_date
    FROM traffic.ppc_targets tg
    WHERE tg.date::date >= '2024-01-01'::date
    GROUP BY (tg.date::date),
             (date_part('day'::text, date_trunc('MONTH'::text, tg.date::date::timestamp with time zone) +
                                     '1 mon -1 days'::interval)), tg.country_code, tg.clicks_goal, tg.net_revenue_goal
)
SELECT ic.dt                                         AS date,
       cc.country_code::character varying            AS country_code,
       0                                             AS revenue_goal,
       0                                             AS cost_goal,
       targets_month.clicks_goal_daily::numeric      AS clicks_goal,
       targets_month.net_revenue_goal_daily::numeric AS net_revenue_goal
FROM dimension.info_calendar ic
         JOIN (SELECT min(tg_1.date::date)                 AS min_date,
                      max(date_trunc('MONTH'::text, tg_1.date::date::timestamp with time zone) +
                          '1 mon -1 days'::interval)::date AS max_date
               FROM vnov.ppc_targets tg_1
               WHERE tg_1.date::date >= '2024-01-01'::date) tg ON ic.dt >= tg.min_date AND ic.dt <= tg.max_date
         CROSS JOIN (SELECT DISTINCT c.alpha_2 AS country_code
                     FROM dimension.v_country_info c
                              JOIN vnov.ppc_targets tg_1 ON tg_1.country_code = c.alpha_2::text) cc
         LEFT JOIN targets_month ON targets_month.date = date_trunc('month'::text, ic.dt::timestamp with time zone) AND
                                    targets_month.country_code = cc.country_code::text
UNION ALL
SELECT de_goal_metrics.date::date AS date,
       countries.alpha_2          AS country_code,
       de_goal_metrics.revenue    AS revenue_goal,
       de_goal_metrics.cost       AS cost_goal,
       0                          AS clicks_goal,
       0                          AS net_revenue_goal
FROM ono."2024_goals" de_goal_metrics
         JOIN dimension.countries ON de_goal_metrics.country = countries.id
WHERE de_goal_metrics.channel = 'Paid Search'::text
  AND de_goal_metrics.date::date >= '2024-01-01'::date;

alter table aggregation.v_ppc_goals
    owner to vnov;