create or replace view aggregation.v_job_categories_underutilised_budget
            (date, country, id_country, id_job_category, id_project, project_name, child_category_name, parent_category_name,
             underutilised_budget) as
with
    recursive
    dates_raw as (
       select
            '2023-07-01'::date as date
       union all
       select
             (date + '1 month'::interval)::date
       from
             dates_raw
       where
             date::date < date_trunc('month', now() - '1 month'::interval)::date
       ),
    dates_clean as (
       select
             (date + '1 month'::interval - '1 day'::interval)::date as date
       from
             dates_raw
       ),
    paid_jobs_by_category as (
       select
            jobs_stat_daily.date::date                                        as date,
            jobs_stat_daily.id_country,
            countries.alpha_2                                                 as country,
            coalesce(jobs_stat_daily.job_category_id::integer, '-1'::integer) as id_job_category,
            jobs_stat_daily.id_project,
            sum(jobs_stat_daily.paid_job_count)                               as paid_jobs
        from
            aggregation.jobs_stat_daily
            left join dimension.countries
                      on jobs_stat_daily.id_country = countries.id
            JOIN dates_clean dc ON date_part('month'::text, jobs_stat_daily.date::date) =
                                                              date_part('month'::text, dc.date) AND
                                                              date_part('year'::text, jobs_stat_daily.date::date) =
                                                              date_part('year'::text, dc.date)
        group by
            (jobs_stat_daily.date::date), jobs_stat_daily.id_country, countries.alpha_2,
            (coalesce(jobs_stat_daily.job_category_id::integer, '-1'::integer)), jobs_stat_daily.id_project
        having
            sum(jobs_stat_daily.paid_job_count) > 0
    ),
    paid_jobs_by_project as (
        select
            paid_jobs_by_category.date,
            paid_jobs_by_category.id_country,
            paid_jobs_by_category.country,
            paid_jobs_by_category.id_project,
            sum(paid_jobs_by_category.paid_jobs) as total
        from
            paid_jobs_by_category
        group by
            paid_jobs_by_category.date, paid_jobs_by_category.id_project, paid_jobs_by_category.id_country,
            paid_jobs_by_category.country
    ),
    percentage as (
        select
            paid_jobs_by_category.paid_jobs::numeric / paid_jobs_by_project.total as percent,
            paid_jobs_by_category.country,
            paid_jobs_by_category.id_country,
            paid_jobs_by_category.id_project,
            paid_jobs_by_category.date,
            paid_jobs_by_category.id_job_category
        from
            paid_jobs_by_category
            join paid_jobs_by_project
                 on paid_jobs_by_category.id_project = paid_jobs_by_project.id_project and
                    paid_jobs_by_category.date = paid_jobs_by_project.date and
                    paid_jobs_by_category.id_country = paid_jobs_by_project.id_country
    ),
    underutilised as (
        select
            v_budget_and_revenue.date,
            v_budget_and_revenue.country,
            countries.id                                                        as id_country,
            v_budget_and_revenue.id_project,
            sum(v_budget_and_revenue.budget - v_budget_and_revenue.revenue_usd) as underutilised_budget
        from
            aggregation.v_budget_and_revenue
            left join dimension.countries
                      on lower(v_budget_and_revenue.country::text) = lower(countries.alpha_2::text)
            join dates_clean as dc
                      on v_budget_and_revenue.date = dc.date
        group by v_budget_and_revenue.date, v_budget_and_revenue.country, countries.id, v_budget_and_revenue.id_project
        having
                sum(v_budget_and_revenue.budget - v_budget_and_revenue.revenue_usd) > 0::double precision
    )
select
    underutilised.date,
    underutilised.country,
    underutilised.id_country,
    percentage.id_job_category,
    underutilised.id_project,
    info_project.name                                                                                           as project_name,
    CASE
           WHEN info_project.hide_in_search THEN 1
           ELSE 0
           END                                                                                                     AS is_job_ad_exchange_project,
    coalesce(job_kaiju_category.child_name, 'Other'::text)                                                      as child_category_name,
    coalesce(coalesce(v_job_kaiju_category.parent_category_name, job_kaiju_category.child_name),
             'Other'::text)                                                                                     as parent_category_name,
    sum(underutilised.underutilised_budget *
        percentage.percent::double precision)::numeric(19, 4)                                                   as underutilised_budget
from
    underutilised
    join percentage
         on underutilised.id_project = percentage.id_project and underutilised.date = percentage.date and
            percentage.id_country = underutilised.id_country
    left join (select
                   job_kaiju_category_1.id_child,
                   job_kaiju_category_1.child_name
               from
                   dimension.job_kaiju_category job_kaiju_category_1
               union
               select
                   job_kaiju_category_1.id_parent,
                   job_kaiju_category_1.parent_name
               from
                   dimension.job_kaiju_category job_kaiju_category_1) job_kaiju_category
              on percentage.id_job_category = job_kaiju_category.id_child
    left join aggregation.v_job_kaiju_category
              on job_kaiju_category.id_child = v_job_kaiju_category.child_category_id
    left join dimension.info_project
              ON underutilised.id_country = info_project.country AND underutilised.id_project = info_project.id
group by
    underutilised.date, underutilised.country, underutilised.id_country, percentage.id_job_category,
    (coalesce(job_kaiju_category.child_name, 'Other'::text)),
    (coalesce(coalesce(v_job_kaiju_category.parent_category_name, job_kaiju_category.child_name), 'Other'::text)),
    underutilised.id_project, info_project.name, info_project.hide_in_search;
