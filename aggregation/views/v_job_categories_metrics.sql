CREATE OR REPLACE VIEW aggregation.v_job_categories_metrics
AS
SELECT session_date as date,
       country_id,
       COALESCE(project_conversions_daily.job_category_id, '-1'::integer) as id_job_category,
       countries.name_country_eng                                         as country,
       COALESCE(job_kaiju_category.child_name, 'Other'::text)             as child_category_name,
       COALESCE(COALESCE(v_job_kaiju_category.parent_category_name, job_kaiju_category.child_name),
                'Other'::text)                                            as parent_category_name,
       SUM(away_revenue) as revenue_usd,
       SUM(aways) as aways,
       SUM(conversions) as conversions,
       SUM(case when conversions is not null or conversions <> 0 then aways else null end)    as aways_with_conversion
FROM aggregation.project_conversions_daily
         LEFT JOIN dimension.countries
                   ON project_conversions_daily.country_id = countries.id
         LEFT JOIN (SELECT job_kaiju_category_1.id_child,
                           job_kaiju_category_1.child_name
                    FROM dimension.job_kaiju_category job_kaiju_category_1
                    UNION
                    SELECT job_kaiju_category_1.id_parent,
                           job_kaiju_category_1.parent_name
                    FROM dimension.job_kaiju_category job_kaiju_category_1) job_kaiju_category
                   ON project_conversions_daily.job_category_id = job_kaiju_category.id_child
         LEFT JOIN aggregation.v_job_kaiju_category
                   ON job_kaiju_category.id_child = v_job_kaiju_category.child_category_id
WHERE project_conversions_daily.session_date > '2023-07-01'
GROUP BY session_date,
         country_id,
         COALESCE(project_conversions_daily.job_category_id, '-1'::integer),
         countries.name_country_eng,
         COALESCE(job_kaiju_category.child_name, 'Other'::text),
         COALESCE(COALESCE(v_job_kaiju_category.parent_category_name, job_kaiju_category.child_name),
                  'Other'::text);
