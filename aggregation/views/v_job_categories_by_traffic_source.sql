create or replace view aggregation.v_job_categories_by_traffic_source
as

   SELECT fn_get_timestamp_from_date_diff(adv_revenue_by_placement_and_src_analytics.date_diff) AS date,
       COALESCE(adv_revenue_by_placement_and_src_analytics.id_job_category, '-1'::integer)   AS id_job_category,
       COALESCE(NULLIF(uts.channel::text, ''::text), 'Undefined'::text)                      AS traffic_source,
       adv_revenue_by_placement_and_src_analytics.country_id,
       countries.name_country_eng                                                            as country,
       COALESCE(job_kaiju_category.child_name, 'Other'::text)                                AS child_category_name,
       COALESCE(COALESCE(v_job_kaiju_category.parent_category_name, job_kaiju_category.child_name),
                'Other'::text)                                                               AS parent_category_name,
       SUM(adv_revenue_by_placement_and_src_analytics.revenue_usd)                           AS revenue
FROM aggregation.adv_revenue_by_placement_and_src_analytics
         LEFT JOIN dimension.u_traffic_source uts
                   ON adv_revenue_by_placement_and_src_analytics.id_current_traf_source = uts.id AND
                      adv_revenue_by_placement_and_src_analytics.country_id = uts.country
         LEFT JOIN dimension.countries
                   ON adv_revenue_by_placement_and_src_analytics.country_id = countries.id
         LEFT JOIN (SELECT job_kaiju_category_1.id_child,
                           job_kaiju_category_1.child_name
                    FROM dimension.job_kaiju_category job_kaiju_category_1
                    UNION
                    SELECT job_kaiju_category_1.id_parent,
                           job_kaiju_category_1.parent_name
                    FROM dimension.job_kaiju_category job_kaiju_category_1) job_kaiju_category
                   ON adv_revenue_by_placement_and_src_analytics.id_job_category = job_kaiju_category.id_child
         LEFT JOIN aggregation.v_job_kaiju_category
                   ON job_kaiju_category.id_child = v_job_kaiju_category.child_category_id
WHERE adv_revenue_by_placement_and_src_analytics.date_diff > 45106 -- 2023-07-01
GROUP BY (fn_get_timestamp_from_date_diff(adv_revenue_by_placement_and_src_analytics.date_diff)),
         (COALESCE(adv_revenue_by_placement_and_src_analytics.id_job_category, '-1'::integer)),
         (COALESCE(NULLIF(uts.channel::text, ''::text), 'Undefined'::text)),
         adv_revenue_by_placement_and_src_analytics.country_id,
         countries.name_country_eng,
         COALESCE(job_kaiju_category.child_name, 'Other'::text),
         COALESCE(COALESCE(v_job_kaiju_category.parent_category_name, job_kaiju_category.child_name),
                  'Other'::text)
