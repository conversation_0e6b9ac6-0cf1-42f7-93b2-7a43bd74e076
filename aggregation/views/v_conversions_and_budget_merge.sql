 SELECT pcd.country_id,
    pcd.session_date,
    pcd.project_id,
        CASE
            WHEN bud_rev.campaign_bud_mont_usd = 0::numeric OR bud_rev.campaign_bud_mont_usd IS NULL THEN NULL::integer
            ELSE pcd.campaign_id
        END AS campaign_id,
    countries.name_country_eng AS country,
    countries.alpha_2 AS alpha2,
    crm.benchmark_cpa,
    crm.benchmark_cr,
    crm.target_action,
    bud_rev.user_id,
    pcd.project_name,
    pcd.name,
    sum(pcd.away_revenue) AS away_revenue,
    sum(pcd.away_revenue_origin_currency) AS away_revenue_origin_currency,
    sum(pcd.aways) AS aways,
    sum(pcd.conversions) AS conversions,
    sum(bud_rev.revenue_usd::double precision - COALESCE(bud_rev.dublicated_click_revenue_usd, 0::numeric)::double precision) AS revenue_usd,
    sum(bud_rev.revenue_budget_diff) AS revenue_budget_diff,
        CASE
            WHEN bud_rev.campaign_bud_mont_usd = 0::numeric OR bud_rev.campaign_bud_mont_usd IS NULL THEN
            CASE
                WHEN row_number() OVER (PARTITION BY pcd.session_date, pcd.project_id, pcd.country_id) = 1 THEN avg(bud_rev.user_budget_month_usd) OVER (PARTITION BY pcd.session_date, pcd.project_id, pcd.country_id)
                ELSE 0::numeric
            END
            ELSE sum(bud_rev.user_budget_month_usd)
        END AS user_budget_month_usd,
    sum(bud_rev.campaign_bud_mont_usd) AS campaign_budget_month_usd,
    sum(bud_rev.potent_rev_budget_diff) AS potent_rev_budget_diff,
    sum(bud_rev.potent_rev_usd) AS potent_rev_usd,
    count(DISTINCT
        CASE
            WHEN COALESCE(bud_rev.campaign_budget_month_usd::double precision, 0::double precision) > 0::double precision THEN bud_rev.id_campaign
            ELSE NULL::character varying
        END) AS campaign_with_budget_cnt,
    count(DISTINCT bud_rev.id_campaign) AS campaign_cnt,
    max(bud_rev.unlim_cmp) AS unlim_cmp,
    sum(bud_rev.click_cnt) AS click_cnt,
    sum(bud_rev.paid_click_cnt) AS paid_click_cnt,
    sum(bud_rev.divided_click_cnt) AS divided_click_cnt,
    sum(bud_rev.divided_paid_click_cnt) AS divided_paid_click_cnt,
        CASE
            WHEN bud_rev.campaign_bud_mont_usd = 0::numeric OR bud_rev.campaign_bud_mont_usd IS NULL THEN
            CASE
                WHEN row_number() OVER (PARTITION BY pcd.session_date, pcd.project_id, pcd.country_id) = 1 THEN max(view_bud_rev.currency_to_usd) OVER (PARTITION BY pcd.session_date, pcd.project_id, pcd.country_id)
                ELSE 0::numeric
            END
            ELSE view_bud_rev.currency_to_usd
        END AS currency_to_usd,
    view_bud_rev.sale_manager
   FROM ( SELECT pcd_1.session_date,
            pcd_1.campaign_id,
            pcd_1.project_id,
            pcd_1.country_id,
            sum(pcd_1.away_revenue_origin_currency) AS away_revenue_origin_currency,
            sum(pcd_1.away_revenue) AS away_revenue,
            sum(pcd_1.aways) AS aways,
            sum(pcd_1.conversions) AS conversions,
            pcd_1.name,
            pcd_1.project_name
           FROM aggregation.project_conversions_daily pcd_1
          WHERE pcd_1.session_date >= '2023-03-01'::date
          GROUP BY pcd_1.session_date, pcd_1.campaign_id, pcd_1.project_id, pcd_1.country_id, pcd_1.name, pcd_1.project_name) pcd
     LEFT JOIN dimension.countries ON pcd.country_id = countries.id
     LEFT JOIN ( SELECT crm_client_account.soska_project_link,
            "left"(crm_client_account.soska_project_link::text, 2) AS country,
            substr(crm_client_account.soska_project_link::text, 3, length(crm_client_account.soska_project_link::text) - 2) AS id_project,
            crm_client_account.benchmark_cr,
            crm_client_account.benchmark_cpa,
            crm_client_account.target_action,
            row_number() OVER (PARTITION BY crm_client_account.soska_project_link ORDER BY crm_client_account.created_on DESC) AS num
           FROM aggregation.crm_client_account
          WHERE (crm_client_account.benchmark_cr > 0::numeric OR crm_client_account.benchmark_cpa > 0::numeric) AND crm_client_account.soska_project_link IS NOT NULL) crm ON countries.alpha_2::text = crm.country AND pcd.project_id = NULLIF(crm.id_project, ''::text)::integer AND crm.num = 1
     LEFT JOIN ( SELECT budget_revenue_daily_agg.country_id,
            budget_revenue_daily_agg.action_date,
            budget_revenue_daily_agg.project_id,
            budget_revenue_daily_agg.id_campaign,
            budget_revenue_daily_agg.click_cnt,
            budget_revenue_daily_agg.user_id,
            budget_revenue_daily_agg.revenue_usd::numeric AS revenue_usd,
            budget_revenue_daily_agg.user_budget_month_usd::numeric AS user_budget_month_usd,
            budget_revenue_daily_agg.campaign_budget_month_usd::numeric AS campaign_budget_month_usd,
            budget_revenue_daily_agg.revenue_budget_diff::numeric AS revenue_budget_diff,
            budget_revenue_daily_agg.is_unlim_cmp,
            budget_revenue_daily_agg.campaign_budget_month_usd::numeric AS campaign_bud_mont_usd,
            budget_revenue_daily_agg.site_url,
            budget_revenue_daily_agg.potential_revenue_budget_diff::numeric AS potent_rev_budget_diff,
            budget_revenue_daily_agg.potential_revenue_usd::numeric AS potent_rev_usd,
            budget_revenue_daily_agg.country_code,
            budget_revenue_daily_agg.is_unlim_cmp AS unlim_cmp,
            budget_revenue_daily_agg.paid_click_cnt,
                CASE
                    WHEN (budget_revenue_daily_agg.paid_click_cnt - lag(budget_revenue_daily_agg.paid_click_cnt) OVER (PARTITION BY (date_part('year'::text, budget_revenue_daily_agg.action_date)), (date_part('month'::text, budget_revenue_daily_agg.action_date)), budget_revenue_daily_agg.project_id, budget_revenue_daily_agg.country_id, (budget_revenue_daily_agg.id_campaign::integer) ORDER BY budget_revenue_daily_agg.action_date)) IS NULL THEN budget_revenue_daily_agg.paid_click_cnt
                    ELSE budget_revenue_daily_agg.paid_click_cnt - lag(budget_revenue_daily_agg.paid_click_cnt) OVER (PARTITION BY (date_part('year'::text, budget_revenue_daily_agg.action_date)), (date_part('month'::text, budget_revenue_daily_agg.action_date)), budget_revenue_daily_agg.project_id, budget_revenue_daily_agg.country_id, (budget_revenue_daily_agg.id_campaign::integer) ORDER BY budget_revenue_daily_agg.action_date)
                END AS divided_paid_click_cnt,
                CASE
                    WHEN (budget_revenue_daily_agg.click_cnt - lag(budget_revenue_daily_agg.click_cnt) OVER (PARTITION BY (date_part('year'::text, budget_revenue_daily_agg.action_date)), (date_part('month'::text, budget_revenue_daily_agg.action_date)), budget_revenue_daily_agg.project_id, budget_revenue_daily_agg.country_id, (budget_revenue_daily_agg.id_campaign::integer) ORDER BY budget_revenue_daily_agg.action_date)) IS NULL THEN budget_revenue_daily_agg.click_cnt
                    ELSE budget_revenue_daily_agg.click_cnt - lag(budget_revenue_daily_agg.click_cnt) OVER (PARTITION BY (date_part('year'::text, budget_revenue_daily_agg.action_date)), (date_part('month'::text, budget_revenue_daily_agg.action_date)), budget_revenue_daily_agg.project_id, budget_revenue_daily_agg.country_id, (budget_revenue_daily_agg.id_campaign::integer) ORDER BY budget_revenue_daily_agg.action_date)
                END AS divided_click_cnt,
            budget_revenue_daily_agg.dublicated_click_revenue_usd
           FROM aggregation.budget_revenue_daily_agg) bud_rev ON pcd.country_id = bud_rev.country_id AND pcd.session_date = bud_rev.action_date AND pcd.project_id = bud_rev.project_id AND pcd.campaign_id = bud_rev.id_campaign::integer
     LEFT JOIN ( SELECT budget_and_revenue.country,
            budget_and_revenue.date,
            budget_and_revenue.id_project,
            budget_and_revenue.currency_to_usd,
            budget_and_revenue.currency,
            budget_and_revenue.sale_manager
           FROM ono.budget_and_revenue) view_bud_rev ON bud_rev.country_code::text = view_bud_rev.country::text AND bud_rev.action_date = view_bud_rev.date AND bud_rev.project_id = view_bud_rev.id_project
  WHERE pcd.session_date >= '2023-03-01'::date
  GROUP BY pcd.country_id, pcd.session_date, pcd.project_id, pcd.campaign_id, countries.name_country_eng, countries.alpha_2, crm.benchmark_cpa, crm.benchmark_cr, crm.target_action, bud_rev.user_id, bud_rev.campaign_bud_mont_usd, bud_rev.user_budget_month_usd, view_bud_rev.currency_to_usd, pcd.project_name, pcd.name, view_bud_rev.sale_manager;
