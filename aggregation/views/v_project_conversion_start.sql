create view v_project_conversion_start
            (id_country, country, id_project, conversion_start_date, conversion_start_date_diff) as
SELECT con.country            AS id_country,
       lower(c.alpha_2::text) AS country,
       sa.id_project,
       min(d.dt)              AS conversion_start_date,
       min(con.date_diff)     AS conversion_start_date_diff
FROM imp.conversion_away_connection con
         JOIN imp.session_away sa
              ON sa.country = con.country AND sa.date_diff = con.date_diff AND sa.id = con.id_session_away
         JOIN dimension.countries c ON c.id = con.country
         JOIN dimension.info_calendar d ON con.date_diff = d.date_diff
GROUP BY con.country, (lower(c.alpha_2::text)), sa.id_project;

alter table v_project_conversion_start
    owner to ono;

