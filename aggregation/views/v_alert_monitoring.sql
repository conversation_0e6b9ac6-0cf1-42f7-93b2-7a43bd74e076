create or replace view aggregation.v_alert_monitoring
            (table_name, scheme_name, metric_name, country_id, country_name_eng, yesterday_metric, avg_prev7_metric,
             is_data) as
SELECT source.table_name,
       source.scheme_name,
       source.metric_name,
       source.country_id,
       COALESCE(countries.name_country_eng, 'Other'::character varying) AS country_name_eng,
       source.yesterday_metric,
       source.avg_prev7_metric::numeric                                 AS avg_prev7_metric,
       CASE
           WHEN source.yesterday_metric > 0::double precision THEN 1
           ELSE 0
           END                                                          AS is_data
FROM (WITH startdate AS (SELECT fn_get_date_diff((CURRENT_DATE - 1)::timestamp without time zone) AS date_d,
                                fn_get_date_diff((CURRENT_DATE - 8)::timestamp without time zone) AS date_d8,
                                CURRENT_DATE - 1                                                  AS date,
                                CURRENT_DATE - 8                                                  AS date8)
      SELECT 'adv_revenue_by_placement_and_src'::text AS table_name,
             'imp'::text                              AS scheme_name,
             'revenue_usd'::text                      AS metric_name,
             eaa.country                              AS country_id,
             sum(
                     CASE
                         WHEN (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) - eaa.date_diff) = 1
                             THEN eaa.revenue_usd
                         ELSE NULL::integer::numeric
                         END)                         AS yesterday_metric,
             sum(
                     CASE
                         WHEN (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) - eaa.date_diff) >= 2 AND
                              (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) - eaa.date_diff) <= 8
                             THEN eaa.revenue_usd
                         ELSE NULL::integer::numeric
                         END) / 7::numeric            AS avg_prev7_metric
      FROM imp.adv_revenue_by_placement_and_src eaa
      WHERE eaa.date_diff >= ((SELECT startdate.date_d8
                               FROM startdate))
      GROUP BY eaa.country
      UNION ALL
      SELECT 'adv_revenue_by_placement_and_src_analytics'::text AS table_name,
             'aggregation'::text                                AS scheme_name,
             'revenue_usd'::text                                AS metric_name,
             eaa.country_id,
             sum(
                     CASE
                         WHEN (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) - eaa.date_diff) = 1
                             THEN eaa.revenue_usd
                         ELSE NULL::integer::numeric
                         END)                                   AS yesterday_metric,
             sum(
                     CASE
                         WHEN (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) - eaa.date_diff) >= 2 AND
                              (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) - eaa.date_diff) <= 8
                             THEN eaa.revenue_usd
                         ELSE NULL::integer::numeric
                         END) / 7::numeric                      AS avg_prev7_metric
      FROM aggregation.adv_revenue_by_placement_and_src_analytics eaa
      WHERE eaa.date_diff >= ((SELECT startdate.date_d8
                               FROM startdate))
      GROUP BY eaa.country_id
      UNION ALL
      SELECT 'auction_click_statistic_analytics'::text AS table_name,
             'aggregation'::text                       AS scheme_name,
             'revenue_usd'::text                       AS metric_name,
             acs_1.country_id,
             sum(
                     CASE
                         WHEN (CURRENT_DATE - acs_1.date::date) = 1 THEN acs_1.total_value
                         ELSE NULL::integer::numeric
                         END)                          AS yesterday_metric,
             sum(
                     CASE
                         WHEN (CURRENT_DATE - acs_1.date::date) >= 2 AND (CURRENT_DATE - acs_1.date::date) <= 8
                             THEN acs_1.total_value
                         ELSE NULL::integer::numeric
                         END) / 7::numeric             AS avg_prev7_metric
      FROM aggregation.auction_click_statistic_analytics acs_1
      WHERE acs_1.date::date >= ((SELECT startdate.date8
                                  FROM startdate))
      GROUP BY acs_1.country_id
      UNION ALL
      SELECT 'funnel_agg'::text            AS table_name,
             'aggregation'::text           AS scheme_name,
             'away_revenue_usd'::text      AS metric_name,
             funnel_agg.country_id,
             sum(
                     CASE
                         WHEN (CURRENT_DATE - funnel_agg.action_date::date) = 1 THEN funnel_agg.away_revenue_usd
                         ELSE NULL::integer::numeric
                         END)              AS yesterday_metric,
             sum(
                     CASE
                         WHEN (CURRENT_DATE - funnel_agg.action_date::date) >= 2 AND
                              (CURRENT_DATE - funnel_agg.action_date::date) <= 8 THEN funnel_agg.away_revenue_usd
                         ELSE NULL::integer::numeric
                         END) / 7::numeric AS avg_prev7_metric
      FROM aggregation.funnel_agg
      WHERE funnel_agg.action_date >= ((SELECT startdate.date8
                                        FROM startdate))
      GROUP BY funnel_agg.country_id
      UNION ALL
      SELECT 'email_abtest_agg'::text      AS table_name,
             'aggregation'::text           AS scheme_name,
             'revenue_total'::text         AS metric_name,
             email_abtest_agg.country_id,
             sum(
                     CASE
                         WHEN (CURRENT_DATE - email_abtest_agg.action_date) = 1 AND
                              btrim(email_abtest_agg.metric_name::text) = 'Total Revenue'::text
                             THEN email_abtest_agg.metric_cnt
                         ELSE NULL::integer::bigint::numeric
                         END)              AS yesterday_metric,
             sum(
                     CASE
                         WHEN (CURRENT_DATE - email_abtest_agg.action_date) >= 2 AND
                              (CURRENT_DATE - email_abtest_agg.action_date) <= 8 AND
                              btrim(email_abtest_agg.metric_name::text) = 'Total Revenue'::text
                             THEN email_abtest_agg.metric_cnt
                         ELSE NULL::integer::numeric
                         END) / 7::numeric AS avg_prev7_metric
      FROM aggregation.email_abtest_agg
      WHERE email_abtest_agg.action_date >= ((SELECT startdate.date8
                                              FROM startdate))
      GROUP BY email_abtest_agg.country_id
      UNION ALL
      SELECT 'budget_revenue_daily_agg'::text AS table_name,
             'aggregation'::text              AS scheme_name,
             'revenue_accum'::text            AS metric_name,
             budget_revenue_daily_agg.country_id,
             CASE
                 WHEN (CURRENT_DATE - 1) = date_trunc('month'::text, now()) THEN sum(
                         CASE
                             WHEN (CURRENT_DATE - budget_revenue_daily_agg.action_date) = 1
                                 THEN budget_revenue_daily_agg.revenue_usd
                             ELSE NULL::integer::double precision
                             END)
                 ELSE sum(
                              CASE
                                  WHEN (CURRENT_DATE - budget_revenue_daily_agg.action_date) = 1
                                      THEN budget_revenue_daily_agg.revenue_usd
                                  ELSE NULL::integer::double precision
                                  END) - sum(
                              CASE
                                  WHEN (CURRENT_DATE - budget_revenue_daily_agg.action_date) = 2
                                      THEN budget_revenue_daily_agg.revenue_usd
                                  ELSE NULL::integer::double precision
                                  END)
                 END                          AS yesterday_metric,
             sum(
                     CASE
                         WHEN (CURRENT_DATE - budget_revenue_daily_agg.action_date) = 1
                             THEN budget_revenue_daily_agg.revenue_usd
                         ELSE NULL::integer::double precision
                         END)                 AS avg_prev7_metric
      FROM aggregation.budget_revenue_daily_agg
      WHERE budget_revenue_daily_agg.action_date >= ((SELECT startdate.date8
                                                      FROM startdate))
      GROUP BY budget_revenue_daily_agg.country_id
      UNION ALL
      SELECT 'adsense_version_daily'::text AS table_name,
             'aggregation'::text           AS scheme_name,
             'revenue_eur'::text           AS metric_name,
             adsense_version_daily.country_id,
             sum(
                     CASE
                         WHEN (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) -
                               adsense_version_daily.action_datediff) = 1 THEN adsense_version_daily.revenue_eur
                         ELSE NULL::integer::numeric
                         END)              AS yesterday_metric,
             sum(
                     CASE
                         WHEN (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) -
                               adsense_version_daily.action_datediff) >= 2 AND
                              (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) -
                               adsense_version_daily.action_datediff) <= 8 THEN adsense_version_daily.revenue_eur
                         ELSE NULL::integer::numeric
                         END) / 7::numeric AS avg_prev7_metric
      FROM aggregation.adsense_version_daily
      WHERE adsense_version_daily.action_datediff >= ((SELECT startdate.date_d8
                                                       FROM startdate))
      GROUP BY adsense_version_daily.country_id
      UNION ALL
      SELECT 'facebook_2018'::text         AS table_name,
             'imp_statistic'::text         AS scheme_name,
             'impressions'::text           AS metric_name,
             0                             AS country_id,
             sum(
                     CASE
                         WHEN (CURRENT_DATE - facebook_2018.date_start) = 1 THEN facebook_2018.impressions
                         ELSE NULL::integer
                         END)              AS yesterday_metric,
             sum(
                     CASE
                         WHEN (CURRENT_DATE - facebook_2018.date_start) >= 2 AND
                              (CURRENT_DATE - facebook_2018.date_start) <= 8 THEN facebook_2018.impressions::numeric
                         ELSE NULL::integer::numeric
                         END) / 7::numeric AS avg_prev7_metric
      FROM imp_statistic.facebook_2018
      WHERE facebook_2018.date_start >= ((SELECT startdate.date8
                                          FROM startdate))
      GROUP BY 0::integer
      UNION ALL
      SELECT 'adwords'::text               AS table_name,
             'imp_statistic'::text         AS scheme_name,
             'impressions'::text           AS metric_name,
             0                             AS country_id,
             sum(
                     CASE
                         WHEN (CURRENT_DATE - adwords.day) = 1 THEN adwords.impressions
                         ELSE NULL::integer
                         END)              AS yesterday_metric,
             sum(
                     CASE
                         WHEN (CURRENT_DATE - adwords.day) >= 2 AND (CURRENT_DATE - adwords.day) <= 8
                             THEN adwords.impressions::numeric
                         ELSE NULL::integer::numeric
                         END) / 7::numeric AS avg_prev7_metric
      FROM imp_statistic.adwords
      WHERE adwords.day >= ((SELECT startdate.date8
                             FROM startdate))
      GROUP BY 0::integer
      UNION ALL
      SELECT 'session_abtest_agg'::text    AS table_name,
             'aggregation'::text           AS scheme_name,
             'revenue_usd'::text           AS metric_name,
             session_abtest_agg.country_id,
             sum(
                     CASE
                         WHEN (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) -
                               session_abtest_agg.action_datediff) = 1 AND
                              session_abtest_agg.metric_name::text = 'revenue'::text AND
                              session_abtest_agg.attribute_value = 7 THEN session_abtest_agg.metric_value
                         ELSE NULL::integer::numeric
                         END)              AS yesterday_metric,
             sum(
                     CASE
                         WHEN (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) -
                               session_abtest_agg.action_datediff) >= 2 AND
                              (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) -
                               session_abtest_agg.action_datediff) <= 8 AND
                              session_abtest_agg.metric_name::text = 'revenue'::text AND
                              session_abtest_agg.attribute_value = 7 THEN session_abtest_agg.metric_value
                         ELSE NULL::integer::numeric
                         END) / 7::numeric AS avg_prev7_metric
      FROM aggregation.session_abtest_agg
      WHERE session_abtest_agg.action_datediff >= ((SELECT startdate.date_d8
                                                    FROM startdate))
      GROUP BY session_abtest_agg.country_id
      UNION ALL
      SELECT 'jobs_stat_daily'::text       AS table_name,
             'aggregation'::text           AS scheme_name,
             'organic_job_count'::text     AS metric_name,
             jobs_stat_daily.id_country    AS country_id,
             sum(
                     CASE
                         WHEN (CURRENT_DATE - jobs_stat_daily.date::date) = 1 THEN jobs_stat_daily.organic_job_count
                         ELSE NULL::integer
                         END)              AS yesterday_metric,
             sum(
                     CASE
                         WHEN (CURRENT_DATE - jobs_stat_daily.date::date) >= 2 AND
                              (CURRENT_DATE - jobs_stat_daily.date::date) <= 8 THEN jobs_stat_daily.organic_job_count::numeric
                         ELSE NULL::integer::numeric
                         END) / 7::numeric AS avg_prev7_metric
      FROM aggregation.jobs_stat_daily
      WHERE jobs_stat_daily.date::date >= ((SELECT startdate.date8
                                            FROM startdate))
      GROUP BY jobs_stat_daily.id_country
      UNION ALL
      SELECT 'action_agg'::text            AS table_name,
             'aggregation'::text           AS scheme_name,
             'session_cnt'::text           AS metric_name,
             action_agg.country_id,
             sum(
                     CASE
                         WHEN (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) - action_agg.date_diff) = 1
                             THEN action_agg.session_cnt
                         ELSE NULL::integer
                         END)              AS yesterday_metric,
             sum(
                     CASE
                         WHEN (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) - action_agg.date_diff) >=
                              2 AND
                              (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) - action_agg.date_diff) <= 8
                             THEN action_agg.session_cnt::numeric
                         ELSE NULL::integer::numeric
                         END) / 7::numeric AS avg_prev7_metric
      FROM aggregation.action_agg
      WHERE action_agg.date_diff >= ((SELECT startdate.date_d8
                                      FROM startdate))
      GROUP BY action_agg.country_id
      UNION ALL
      SELECT 'email_metric_daily'::text    AS table_name,
             'aggregation'::text           AS scheme_name,
             'sent_msg'::text              AS metric_name,
             email_metric_daily.country_id,
             sum(
                     CASE
                         WHEN (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) -
                               email_metric_daily.date_diff) = 1 THEN email_metric_daily.sent_msg::numeric
                         ELSE NULL::integer::numeric
                         END)              AS yesterday_metric,
             sum(
                     CASE
                         WHEN (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) -
                               email_metric_daily.date_diff) >= 2 AND
                              (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) -
                               email_metric_daily.date_diff) <= 8 THEN email_metric_daily.sent_msg::numeric
                         ELSE NULL::integer::numeric
                         END) / 7::numeric AS avg_prev7_metric
      FROM aggregation.email_metric_daily
      WHERE email_metric_daily.date_diff >= ((SELECT startdate.date_d8
                                              FROM startdate))
      GROUP BY email_metric_daily.country_id
      UNION ALL
      SELECT 'finance_report'::text        AS table_name,
             'aggregation'::text           AS scheme_name,
             'revenue_usd'::text           AS metric_name,
             countries_1.id                AS country_id,
             sum(
                     CASE
                         WHEN (CURRENT_DATE - finance_report.date) = 1 THEN finance_report.revenue_usd
                         ELSE NULL::integer::double precision
                         END)              AS yesterday_metric,
             sum(
                     CASE
                         WHEN (CURRENT_DATE - finance_report.date) >= 2 AND (CURRENT_DATE - finance_report.date) <= 8
                             THEN finance_report.revenue_usd::numeric
                         ELSE NULL::integer::numeric
                         END) / 7::numeric AS avg_prev7_metric
      FROM aggregation.finance_report
               LEFT JOIN dimension.countries countries_1 ON finance_report.country::text = countries_1.name_country_eng::text
      WHERE finance_report.date >= ((SELECT startdate.date8
                                     FROM startdate))
        AND COALESCE(lower(finance_report.project_site::text), ''::text) !~~ 'j-vers.%'::text
        AND finance_report.revenue_type = 'Auction'::bpchar
      GROUP BY countries_1.id
      UNION ALL
      SELECT 'session_daily_agg'::text     AS table_name,
             'aggregation'::text           AS scheme_name,
             'total_session_cnt'::text     AS metric_name,
             session_daily_agg.country_id,
             sum(
                     CASE
                         WHEN (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) -
                               session_daily_agg.action_datediff) = 1 THEN session_daily_agg.total_session_cnt::numeric
                         ELSE NULL::integer::numeric
                         END)              AS yesterday_metric,
             sum(
                     CASE
                         WHEN (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) -
                               session_daily_agg.action_datediff) >= 2 AND
                              (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) -
                               session_daily_agg.action_datediff) <= 8 THEN session_daily_agg.total_session_cnt::numeric
                         ELSE NULL::integer::numeric
                         END) / 7::numeric AS avg_prev7_metric
      FROM aggregation.session_daily_agg
      WHERE session_daily_agg.action_datediff >= ((SELECT startdate.date_d8
                                                   FROM startdate))
      GROUP BY session_daily_agg.country_id
      UNION ALL
      SELECT 'adsense_afc'::text            AS table_name,
             'imp_api'::text                AS scheme_name,
             'estimated_earnings_usd'::text AS metric_name,
             countries_1.id                 AS country_id,
             sum(
                     CASE
                         WHEN (CURRENT_DATE - adsense_afc.action_date) = 1 THEN adsense_afc.estimated_earnings_usd
                         ELSE NULL::numeric
                         END)               AS yesterday_metric,
             sum(
                     CASE
                         WHEN (CURRENT_DATE - adsense_afc.action_date) >= 2 AND
                              (CURRENT_DATE - adsense_afc.action_date) <= 8 THEN adsense_afc.estimated_earnings_usd
                         ELSE NULL::integer::numeric
                         END) / 7::numeric  AS avg_prev7_metric
      FROM imp_api.adsense_afc
               LEFT JOIN ono.dic_countries_adsense ON "left"(
                                                              CASE
                                                                  WHEN adsense_afc.domain_code::text = 'jooble.org'::text
                                                                      THEN 'us.jooble.org'::character varying
                                                                  WHEN adsense_afc.domain_code::text = 'ja.jooble.org'::text
                                                                      THEN 'jp.jooble.org'::character varying
                                                                  ELSE adsense_afc.domain_code
                                                                  END::text, 2) =
                                                      dic_countries_adsense.country_as_in_reports
               LEFT JOIN dimension.countries countries_1
                         ON lower(dic_countries_adsense.two_digits) = lower(countries_1.alpha_2::text)
      WHERE adsense_afc.action_date >= ((SELECT startdate.date8
                                         FROM startdate))
      GROUP BY countries_1.id
      UNION ALL
      SELECT 'adsense_afs'::text            AS table_name,
             'imp_api'::text                AS scheme_name,
             'estimated_earnings_usd'::text AS metric_name,
             countries_1.id                 AS country_id,
             sum(
                     CASE
                         WHEN (CURRENT_DATE - adsense_afs.action_date) = 1 THEN adsense_afs.estimated_earnings_usd
                         ELSE NULL::numeric
                         END)               AS yesterday_metric,
             sum(
                     CASE
                         WHEN (CURRENT_DATE - adsense_afs.action_date) >= 2 AND
                              (CURRENT_DATE - adsense_afs.action_date) <= 8 THEN adsense_afs.estimated_earnings_usd
                         ELSE NULL::integer::numeric
                         END) / 7::numeric  AS avg_prev7_metric
      FROM imp_api.adsense_afs
               LEFT JOIN dimension.countries countries_1
                         ON adsense_afs.country_name::text = countries_1.name_country_eng::text
      WHERE adsense_afs.action_date >= ((SELECT startdate.date8
                                         FROM startdate))
      GROUP BY countries_1.id
      UNION ALL
      SELECT 'adsense_custom_channels_revenue'::text AS table_name,
             'imp_api'::text                         AS scheme_name,
             'estimated_earnings_usd'::text          AS metric_name,
             countries_1.id                          AS country_id,
             sum(
                     CASE
                         WHEN (CURRENT_DATE - adsense_custom_channels_revenue.action_date) = 1
                             THEN adsense_custom_channels_revenue.estimated_earnings_usd
                         ELSE NULL::numeric
                         END)                        AS yesterday_metric,
             sum(
                     CASE
                         WHEN (CURRENT_DATE - adsense_custom_channels_revenue.action_date) >= 2 AND
                              (CURRENT_DATE - adsense_custom_channels_revenue.action_date) <= 8
                             THEN adsense_custom_channels_revenue.estimated_earnings_usd
                         ELSE NULL::integer::numeric
                         END) / 7::numeric           AS avg_prev7_metric
      FROM imp_api.adsense_custom_channels_revenue
               LEFT JOIN dimension.countries countries_1
                         ON adsense_custom_channels_revenue.country_name::text = countries_1.name_country_eng::text
      WHERE adsense_custom_channels_revenue.action_date >= ((SELECT startdate.date8
                                                             FROM startdate))
      GROUP BY countries_1.id
      UNION ALL
      SELECT 'adsense_product_revenue'::text AS table_name,
             'imp_api'::text                 AS scheme_name,
             'estimated_earnings_usd'::text  AS metric_name,
             countries_1.id                  AS country_id,
             sum(
                     CASE
                         WHEN (CURRENT_DATE - adsense_product_revenue.action_date) = 1
                             THEN adsense_product_revenue.estimated_earnings_usd
                         ELSE NULL::numeric
                         END)                AS yesterday_metric,
             sum(
                     CASE
                         WHEN (CURRENT_DATE - adsense_product_revenue.action_date) >= 2 AND
                              (CURRENT_DATE - adsense_product_revenue.action_date) <= 8
                             THEN adsense_product_revenue.estimated_earnings_usd
                         ELSE NULL::integer::numeric
                         END) / 7::numeric   AS avg_prev7_metric
      FROM imp_api.adsense_product_revenue
               LEFT JOIN dimension.countries countries_1
                         ON adsense_product_revenue.country_name::text = countries_1.name_country_eng::text
      WHERE adsense_product_revenue.action_date >= ((SELECT startdate.date8
                                                     FROM startdate))
      GROUP BY countries_1.id
      UNION ALL
      SELECT 'account_revenue'::text       AS table_name,
             'aggregation'::text           AS scheme_name,
             'account_revenue'::text       AS metric_name,
             account_revenue.country_id,
             sum(
                     CASE
                         WHEN (CURRENT_DATE - account_revenue.revenue_date) = 1 THEN account_revenue.account_revenue
                         ELSE NULL::numeric
                         END)              AS yesterday_metric,
             sum(
                     CASE
                         WHEN (CURRENT_DATE - account_revenue.revenue_date) >= 2 AND
                              (CURRENT_DATE - account_revenue.revenue_date) <= 8 THEN account_revenue.account_revenue
                         ELSE NULL::integer::numeric
                         END) / 7::numeric AS avg_prev7_metric
      FROM aggregation.account_revenue
      WHERE account_revenue.revenue_date >= ((SELECT startdate.date8
                                              FROM startdate))
      GROUP BY account_revenue.country_id
      UNION ALL
      SELECT 'conversions'::text                             AS table_name,
             'imp_statistic'::text                           AS scheme_name,
             'conversions'::text                             AS metric_name,
             countries_1.id                                  AS country_id,
             sum(
                     CASE
                         WHEN (CURRENT_DATE - conversions.date) = 1 THEN conversions.conversions
                         ELSE NULL::numeric::double precision
                         END)                                AS yesterday_metric,
             sum(
                     CASE
                         WHEN (CURRENT_DATE - conversions.date) >= 2 AND (CURRENT_DATE - conversions.date) <= 8
                             THEN conversions.conversions
                         ELSE NULL::integer::numeric::double precision
                         END) / 7::numeric::double precision AS avg_prev7_metric
      FROM imp_statistic.conversions
               LEFT JOIN dimension.countries countries_1 ON conversions.country::text = countries_1.alpha_2::text
      WHERE conversions.date >= ((SELECT startdate.date8
                                  FROM startdate))
      GROUP BY countries_1.id
      UNION ALL
      SELECT 'webmaster_statistic'::text                     AS table_name,
             'traffic'::text                                 AS scheme_name,
             'impressions'::text                             AS metric_name,
             webmaster_statistic.country_id,
             sum(
                     CASE
                         WHEN (CURRENT_DATE - webmaster_statistic.date) = 4
                             THEN webmaster_statistic.impressions::double precision
                         ELSE NULL::numeric::double precision
                         END)                                AS yesterday_metric,
             sum(
                     CASE
                         WHEN (CURRENT_DATE - webmaster_statistic.date) >= 2 AND
                              (CURRENT_DATE - webmaster_statistic.date) <= 8
                             THEN webmaster_statistic.impressions::double precision
                         ELSE NULL::integer::numeric::double precision
                         END) / 7::numeric::double precision AS avg_prev7_metric
      FROM traffic.webmaster_statistic
      WHERE webmaster_statistic.date >= ((SELECT startdate.date8
                                          FROM startdate))
      GROUP BY webmaster_statistic.country_id
      UNION ALL
      SELECT 'seo_ab_prod_metrics'::text   AS table_name,
             'traffic'::text               AS scheme_name,
             'revenue'::text               AS metric_name,
             seo_ab_prod_metrics.country_id,
             sum(
                     CASE
                         WHEN (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) -
                               seo_ab_prod_metrics.date_diff) = 1 AND seo_ab_prod_metrics.metric::text = 'revenue'::text
                             THEN seo_ab_prod_metrics.value::numeric
                         ELSE NULL::integer::numeric
                         END)              AS yesterday_metric,
             sum(
                     CASE
                         WHEN (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) -
                               seo_ab_prod_metrics.date_diff) >= 2 AND
                              (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) -
                               seo_ab_prod_metrics.date_diff) <= 8 AND seo_ab_prod_metrics.metric::text = 'revenue'::text
                             THEN seo_ab_prod_metrics.value::numeric
                         ELSE NULL::integer::numeric
                         END) / 7::numeric AS avg_prev7_metric
      FROM traffic.seo_ab_prod_metrics
      WHERE seo_ab_prod_metrics.date_diff >= ((SELECT startdate.date_d8
                                               FROM startdate))
      GROUP BY seo_ab_prod_metrics.country_id
      UNION ALL
      SELECT 'clicks_collars'::text        AS table_name,
             'aggregation'::text           AS scheme_name,
             'aways'::text                 AS metric_name,
             clicks_collars.country_id,
             sum(
                     CASE
                         WHEN (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) -
                               clicks_collars.action_datediff) = 1 THEN clicks_collars.away_clicks_cnt::numeric
                         ELSE NULL::integer::numeric
                         END)              AS yesterday_metric,
             sum(
                     CASE
                         WHEN (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) -
                               clicks_collars.action_datediff) >= 2 AND
                              (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) -
                               clicks_collars.action_datediff) <= 8 THEN clicks_collars.away_clicks_cnt::numeric
                         ELSE NULL::integer::numeric
                         END) / 7::numeric AS avg_prev7_metric
      FROM aggregation.clicks_collars
      WHERE clicks_collars.action_datediff >= ((SELECT startdate.date_d8
                                                FROM startdate))
        AND clicks_collars.local_cluster_name::text = 'total'::text
      GROUP BY clicks_collars.country_id
      UNION ALL
      SELECT 'vacancy_collars'::text       AS table_name,
             'aggregation'::text           AS scheme_name,
             'uid_count'::text             AS metric_name,
             vacancy_collars.country_id,
             sum(
                     CASE
                         WHEN (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) -
                               vacancy_collars.action_datediff) = 1 THEN vacancy_collars.active_uid_cnt::numeric
                         ELSE NULL::integer::numeric
                         END)              AS yesterday_metric,
             sum(
                     CASE
                         WHEN (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) -
                               vacancy_collars.action_datediff) >= 2 AND
                              (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) -
                               vacancy_collars.action_datediff) <= 8 THEN vacancy_collars.active_uid_cnt::numeric
                         ELSE NULL::integer::numeric
                         END) / 7::numeric AS avg_prev7_metric
      FROM aggregation.vacancy_collars
      WHERE vacancy_collars.action_datediff >= ((SELECT startdate.date_d8
                                                 FROM startdate))
        AND vacancy_collars.local_cluster_name::text = 'total'::text
      GROUP BY vacancy_collars.country_id
      UNION ALL
      SELECT 'paid_traffic_product_metrics'::text AS table_name,
             'aggregation'::text                  AS scheme_name,
             'new_account_cnt'::text              AS metric_name,
             countries_2.id                       AS country_id,
             sum(
                     CASE
                         WHEN (CURRENT_DATE - paid_traffic_product_metrics.action_date) <= 13 AND
                              (CURRENT_DATE - paid_traffic_product_metrics.action_date) >= 0
                             THEN paid_traffic_product_metrics.metric_value
                         ELSE NULL::numeric
                         END)                     AS yesterday_metric,
             sum(
                     CASE
                         WHEN (CURRENT_DATE - paid_traffic_product_metrics.action_date) > 13 AND
                              (CURRENT_DATE - paid_traffic_product_metrics.action_date) < 21
                             THEN paid_traffic_product_metrics.metric_value
                         ELSE NULL::numeric
                         END)                     AS avg_prev7_metric
      FROM aggregation.paid_traffic_product_metrics
               LEFT JOIN dimension.countries countries_2
                         ON paid_traffic_product_metrics.country_code::text = lower(countries_2.alpha_2::text)
      WHERE paid_traffic_product_metrics.action_date >= (CURRENT_DATE - 22)
        AND paid_traffic_product_metrics.metric_name::text = 'new_account_cnt'::text
      GROUP BY countries_2.id
      UNION ALL
      SELECT 'ga_organic_agg'::text AS table_name,
             'aggregation'::text    AS scheme_name,
             'session_cnt'::text    AS metric_name,
             countries_2.id         AS country_id,
             sum(
                     CASE
                         WHEN (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) -
                               fn_get_date_diff(ga_organic_agg.date::timestamp without time zone)) = 1
                             THEN ga_organic_agg.session_cnt
                         ELSE NULL::numeric
                         END)       AS yesterday_metric,
             sum(
                     CASE
                         WHEN (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) -
                               fn_get_date_diff(ga_organic_agg.date::timestamp without time zone)) = 2
                             THEN ga_organic_agg.session_cnt
                         ELSE NULL::numeric
                         END)       AS day_minus2_metric
      FROM aggregation.ga_organic_agg
               LEFT JOIN dimension.countries countries_2 ON ga_organic_agg.country_cc = lower(countries_2.alpha_2::text)
      WHERE ga_organic_agg.date >= ((SELECT startdate.date8
                                     FROM startdate))
      GROUP BY countries_2.id
      UNION ALL
      SELECT 'banner_stat_history'::text   AS table_name,
             'traffic'::text               AS scheme_name,
             'show_cnt'::text              AS metric_name,
             banner.country_id,
             sum(
                     CASE
                         WHEN (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) - banner.date_diff) = 1
                             THEN banner.shows::numeric
                         ELSE NULL::numeric
                         END)              AS yesterday_metric,
             sum(
                     CASE
                         WHEN (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) - banner.date_diff) >= 2 AND
                              (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) - banner.date_diff) <= 8
                             THEN banner.shows::numeric
                         ELSE NULL::numeric
                         END) / 7::numeric AS avg_prev7_metric
      FROM traffic.banner_stat_history banner
      WHERE banner.date_diff >= ((SELECT startdate.date_d8
                                  FROM startdate))
      GROUP BY banner.country_id
      UNION ALL
      SELECT 'outreach_link_agg'::text AS table_name,
             'aggregation'::text       AS scheme_name,
             'link_id_cnt'::text       AS metric_name,
             6                         AS country_id,
             count(DISTINCT
                   CASE
                       WHEN (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) -
                             fn_get_date_diff(outreach.report_update_time)) = 0 THEN outreach.link_id::numeric
                       ELSE NULL::numeric
                       END)            AS yesterday_metric,
             NULL::numeric             AS avg_prev7_metric
      FROM aggregation.outreach_link_agg outreach
      WHERE fn_get_date_diff(outreach.report_update_time) = fn_get_date_diff(CURRENT_DATE::timestamp without time zone)
      UNION ALL
      SELECT 'outreach_link_current'::text AS table_name,
             'aggregation'::text           AS scheme_name,
             'link_total'::text            AS metric_name,
             6                             AS country_id,
             sum(
                     CASE
                         WHEN (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) -
                               fn_get_date_diff(outreach.report_update_time)) = 0 THEN outreach.link_total::numeric
                         ELSE NULL::numeric
                         END)              AS yesterday_metric,
             NULL::numeric                 AS avg_prev7_metric
      FROM aggregation.outreach_link_current outreach
      WHERE fn_get_date_diff(outreach.report_update_time) = fn_get_date_diff(CURRENT_DATE::timestamp without time zone)
      UNION ALL
      SELECT 'outreach_link_cost'::text AS table_name,
             'aggregation'::text        AS scheme_name,
             'link_id_cnt'::text        AS metric_name,
             6                          AS country_id,
             count(DISTINCT
                   CASE
                       WHEN (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) -
                             fn_get_date_diff(outreach.report_update_time)) = 0 THEN outreach.id_link::numeric
                       ELSE NULL::numeric
                       END)             AS yesterday_metric,
             NULL::numeric              AS avg_prev7_metric
      FROM aggregation.outreach_link_cost outreach
      WHERE fn_get_date_diff(outreach.report_update_time) = fn_get_date_diff(CURRENT_DATE::timestamp without time zone)
      UNION ALL
      SELECT 'outreach_funnel_agg'::text AS table_name,
             'aggregation'::text         AS scheme_name,
             'account_id_cnt'::text      AS metric_name,
             6                           AS country_id,
             count(DISTINCT
                   CASE
                       WHEN (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) -
                             fn_get_date_diff(outreach.report_update_time)) = 0 THEN outreach.account_id::numeric
                       ELSE NULL::numeric
                       END)              AS yesterday_metric,
             NULL::numeric               AS avg_prev7_metric
      FROM aggregation.outreach_funnel_agg outreach
      WHERE fn_get_date_diff(outreach.report_update_time) = fn_get_date_diff(CURRENT_DATE::timestamp without time zone)
      UNION ALL
      SELECT 'outreach_kpi_current'::text AS table_name,
             'aggregation'::text          AS scheme_name,
             'team_id_cnt'::text          AS metric_name,
             6                            AS country_id,
             count(DISTINCT
                   CASE
                       WHEN (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) -
                             fn_get_date_diff(outreach.report_update_time)) = 0 THEN outreach.id_team::numeric
                       ELSE NULL::numeric
                       END)               AS yesterday_metric,
             NULL::numeric                AS avg_prev7_metric
      FROM aggregation.outreach_kpi_current outreach
      WHERE fn_get_date_diff(outreach.report_update_time) = fn_get_date_diff(CURRENT_DATE::timestamp without time zone)
      UNION ALL
      SELECT 'ga4_general'::text             AS table_name,
             'imp_api'::text                 AS scheme_name,
             'session_cnt_total'::text       AS metric_name,
             ga4_general.country_id::integer AS country_id,
             sum(
                     CASE
                         WHEN (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) -
                               fn_get_date_diff(ga4_general.action_date::timestamp without time zone)) = 1
                             THEN ga4_general.sessions::numeric
                         ELSE NULL::numeric
                         END)                AS yesterday_metric,
             sum(
                     CASE
                         WHEN (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) -
                               fn_get_date_diff(ga4_general.action_date::timestamp without time zone)) >= 2 AND
                              (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) -
                               fn_get_date_diff(ga4_general.action_date::timestamp without time zone)) <= 8
                             THEN ga4_general.sessions::numeric
                         ELSE NULL::numeric
                         END) / 7::numeric   AS avg_prev7_metric
      FROM imp_api.ga4_general
      WHERE ga4_general.action_date >= ((SELECT startdate.date8
                                         FROM startdate))
      GROUP BY ga4_general.country_id
      UNION ALL
      SELECT 'ga4_general'::text             AS table_name,
             'imp_api'::text                 AS scheme_name,
             'organic_session_cnt'::text     AS metric_name,
             ga4_general.country_id::integer AS country_id,
             sum(
                     CASE
                         WHEN (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) -
                               fn_get_date_diff(ga4_general.action_date::timestamp without time zone)) = 1
                             THEN ga4_general.sessions::numeric
                         ELSE NULL::numeric
                         END)                AS yesterday_metric,
             sum(
                     CASE
                         WHEN (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) -
                               fn_get_date_diff(ga4_general.action_date::timestamp without time zone)) = 2
                             THEN ga4_general.sessions::numeric
                         ELSE NULL::numeric
                         END)                AS day_minus2_metric
      FROM imp_api.ga4_general
      WHERE ga4_general.action_date >= ((SELECT startdate.date8
                                         FROM startdate))
        AND ga4_general.channel_grouping::text = 'Organic Search'::text
      GROUP BY ga4_general.country_id
      UNION ALL
      SELECT 'session_daily_agg'::text            AS table_name,
             'aggregation'::text                  AS scheme_name,
             'internal_organic_session_cnt'::text AS metric_name,
             session_daily_agg.country_id,
             sum(
                     CASE
                         WHEN (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) -
                               session_daily_agg.action_datediff) = 1 THEN
                                 session_daily_agg.total_session_cnt::numeric -
                                 COALESCE(session_daily_agg.bot_session_cnt::numeric, 0::numeric)
                         ELSE NULL::integer::numeric
                         END)                     AS yesterday_metric,
             sum(
                     CASE
                         WHEN (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) -
                               session_daily_agg.action_datediff) >= 2 AND
                              (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) -
                               session_daily_agg.action_datediff) <= 8 THEN
                                 session_daily_agg.total_session_cnt::numeric -
                                 COALESCE(session_daily_agg.bot_session_cnt::numeric, 0::numeric)
                         ELSE NULL::integer::numeric
                         END) / 7::numeric        AS avg_prev7_metric
      FROM aggregation.session_daily_agg
               LEFT JOIN dimension.u_traffic_source ufs ON session_daily_agg.country_id = ufs.country AND
                                                           session_daily_agg.id_current_traf_source = ufs.id
      WHERE session_daily_agg.action_datediff >= fn_get_date_diff((CURRENT_DATE - 8)::timestamp without time zone)
        AND ufs.channel::text = 'Organic Search'::text
      GROUP BY session_daily_agg.country_id
      UNION ALL
      SELECT 'session_abtest_main_metrics_agg'::text AS table_name,
             'aggregation'::text                     AS scheme_name,
             'revenue'::text                         AS metric_name,
             samma.country_id,
             sum(
                     CASE
                         WHEN (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) - samma.action_datediff) = 1
                             THEN samma.metric_value
                         ELSE NULL::integer::numeric
                         END)                        AS yesterday_metric,
             sum(
                     CASE
                         WHEN (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) - samma.action_datediff) >=
                              2 AND
                              (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) - samma.action_datediff) <= 8
                             THEN samma.metric_value
                         ELSE NULL::integer::numeric
                         END) / 7::numeric           AS avg_prev7_metric
      FROM aggregation.session_abtest_main_metrics_agg samma
      WHERE fn_get_date_from_date_diff(samma.action_datediff) >= ((SELECT startdate.date8
                                                                   FROM startdate))
        AND samma.metric_name::text = 'revenue'::text
        AND samma.union_group = 'union 1'::text
      GROUP BY samma.country_id
      UNION ALL
      SELECT 'account_revenue_abtest_agg'::text AS table_name,
             'aggregation'::text                AS scheme_name,
             'new_account_revenue'::text        AS metric_name,
             account_revenue_abtest_agg.country_id,
             sum(
                     CASE
                         WHEN (CURRENT_DATE - fn_get_date_from_date_diff(account_revenue_abtest_agg.date_diff)) = 1
                             THEN account_revenue_abtest_agg.new_account_revenue
                         ELSE NULL::numeric
                         END)                   AS yesterday_metric,
             sum(
                     CASE
                         WHEN (CURRENT_DATE - fn_get_date_from_date_diff(account_revenue_abtest_agg.date_diff)) >= 2 AND
                              (CURRENT_DATE - fn_get_date_from_date_diff(account_revenue_abtest_agg.date_diff)) <= 8
                             THEN account_revenue_abtest_agg.new_account_revenue
                         ELSE NULL::integer::numeric
                         END) / 7::numeric      AS avg_prev7_metric
      FROM aggregation.account_revenue_abtest_agg
      WHERE fn_get_date_from_date_diff(account_revenue_abtest_agg.date_diff) >= ((SELECT startdate.date8
                                                                                  FROM startdate))
      GROUP BY account_revenue_abtest_agg.country_id
      UNION ALL
      SELECT 'serp_rivals_agg'::text       AS table_name,
             'aggregation'::text           AS scheme_name,
             'click_cnt'::text             AS metric_name,
             sra.country_id,
             sum(
                     CASE
                         WHEN (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) - sra.date_diff) = 1
                             THEN sra.click_cnt::numeric
                         ELSE NULL::integer::numeric
                         END)              AS yesterday_metric,
             sum(
                     CASE
                         WHEN (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) - sra.date_diff) >= 2 AND
                              (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) - sra.date_diff) <= 8
                             THEN sra.click_cnt::numeric
                         ELSE NULL::integer::numeric
                         END) / 7::numeric AS avg_prev7_metric
      FROM aggregation.serp_rivals_agg sra
      WHERE sra.date_diff >= ((SELECT startdate.date_d8
                               FROM startdate))
      GROUP BY sra.country_id
      UNION ALL
      SELECT 'serp_campaign_rivals_agg'::text AS table_name,
             'aggregation'::text              AS scheme_name,
             'click_cnt'::text                AS metric_name,
             scra.country_id,
             sum(
                     CASE
                         WHEN (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) - scra.date_diff) = 1
                             THEN scra.click_cnt::numeric
                         ELSE NULL::integer::numeric
                         END)                 AS yesterday_metric,
             sum(
                     CASE
                         WHEN (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) - scra.date_diff) >= 2 AND
                              (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) - scra.date_diff) <= 8
                             THEN scra.click_cnt::numeric
                         ELSE NULL::integer::numeric
                         END) / 7::numeric    AS avg_prev7_metric
      FROM aggregation.serp_campaign_rivals_agg scra
      WHERE scra.date_diff >= ((SELECT startdate.date_d8
                                FROM startdate))
      GROUP BY scra.country_id
      UNION ALL
      SELECT 'session_abtest_agg_active'::text AS table_name,
             'aggregation'::text               AS scheme_name,
             'revenue_usd'::text               AS metric_name,
             session_abtest_agg.country_id,
             sum(
                     CASE
                         WHEN (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) -
                               session_abtest_agg.action_datediff) = 1 AND
                              session_abtest_agg.metric_name::text = 'revenue'::text AND
                              session_abtest_agg.attribute_value = 7 THEN session_abtest_agg.metric_value
                         ELSE NULL::integer::numeric
                         END)                  AS yesterday_metric,
             sum(
                     CASE
                         WHEN (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) -
                               session_abtest_agg.action_datediff) >= 2 AND
                              (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) -
                               session_abtest_agg.action_datediff) <= 8 AND
                              session_abtest_agg.metric_name::text = 'revenue'::text AND
                              session_abtest_agg.attribute_value = 7 THEN session_abtest_agg.metric_value
                         ELSE NULL::integer::numeric
                         END) / 7::numeric     AS avg_prev7_metric
      FROM aggregation.session_abtest_agg
      WHERE session_abtest_agg.action_datediff >= ((SELECT startdate.date_d8
                                                    FROM startdate))
        AND session_abtest_agg.group_num IS NOT NULL
      GROUP BY session_abtest_agg.country_id
      UNION ALL
      SELECT 'adv_revenue_by_placement_and_src_analytics_for_mobile_app'::text AS table_name,
             'aggregation'::text                                               AS scheme_name,
             'revenue_usd'::text                                               AS metric_name,
             eaa.country_id,
             sum(
                     CASE
                         WHEN (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) - eaa.date_diff) = 1
                             THEN eaa.revenue_usd
                         ELSE NULL::integer::numeric
                         END)                                                  AS yesterday_metric,
             sum(
                     CASE
                         WHEN (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) - eaa.date_diff) >= 2 AND
                              (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) - eaa.date_diff) <= 8
                             THEN eaa.revenue_usd
                         ELSE NULL::integer::numeric
                         END) / 7::numeric                                     AS avg_prev7_metric
      FROM aggregation.adv_revenue_by_placement_and_src_analytics eaa
      WHERE eaa.date_diff >= ((SELECT startdate.date_d8
                               FROM startdate))
        AND eaa.placement::text = 'mobile app'::text
      GROUP BY eaa.country_id
      UNION ALL
      SELECT 'mobile_app_session_revenue'::text AS table_name,
             'mobile_app'::text                 AS scheme_name,
             'revenue_usd'::text                AS metric_name,
             session_revenue.country_id,
             sum(
                     CASE
                         WHEN (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) -
                               session_revenue.session_datediff) = 1 THEN session_revenue.revenue_usd
                         ELSE NULL::integer::numeric
                         END)                   AS yesterday_metric,
             sum(
                     CASE
                         WHEN (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) -
                               session_revenue.session_datediff) >= 2 AND
                              (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) -
                               session_revenue.session_datediff) <= 8 THEN session_revenue.revenue_usd
                         ELSE NULL::integer::numeric
                         END) / 7::numeric      AS avg_prev7_metric
      FROM mobile_app.session_revenue
      WHERE session_revenue.session_datediff >= ((SELECT startdate.date_d8
                                                  FROM startdate))
      GROUP BY session_revenue.country_id
      UNION ALL
      SELECT 'email_abtest_agg_revenue'::text AS table_name,
             'aggregation'::text              AS scheme_name,
             'revenue_usd'::text              AS metric_name,
             email_abtest_agg.country_id,
             sum(
                     CASE
                         WHEN (CURRENT_DATE - email_abtest_agg.action_date) = 1 THEN email_abtest_agg.metric_cnt
                         ELSE NULL::integer::bigint::numeric
                         END)                 AS yesterday_metric,
             sum(
                     CASE
                         WHEN (CURRENT_DATE - email_abtest_agg.action_date) >= 2 AND
                              (CURRENT_DATE - email_abtest_agg.action_date) <= 8 THEN email_abtest_agg.metric_cnt
                         ELSE NULL::integer::bigint::numeric
                         END) / 7::numeric    AS avg_prev7_metric
      FROM aggregation.email_abtest_agg
      WHERE email_abtest_agg.action_date >= ((SELECT startdate.date8
                                              FROM startdate))
        AND (btrim(email_abtest_agg.metric_name::text) = ANY
             (ARRAY ['Letter Revenue'::text, 'SERP Revenue'::text, 'JDP Revenue'::text]))
      GROUP BY email_abtest_agg.country_id
      UNION ALL
      SELECT 'adv_revenue_by_placement_and_src_analytics_for_email'::text AS table_name,
             'aggregation'::text                                          AS scheme_name,
             'revenue_usd'::text                                          AS metric_name,
             adv_revenue_by_placement_and_src_analytics.country_id,
             sum(
                     CASE
                         WHEN (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) -
                               adv_revenue_by_placement_and_src_analytics.date_diff) = 1
                             THEN adv_revenue_by_placement_and_src_analytics.revenue_usd
                         ELSE NULL::integer::numeric
                         END)                                             AS yesterday_metric,
             sum(
                     CASE
                         WHEN (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) -
                               adv_revenue_by_placement_and_src_analytics.date_diff) >= 2 AND
                              (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) -
                               adv_revenue_by_placement_and_src_analytics.date_diff) <= 8
                             THEN adv_revenue_by_placement_and_src_analytics.revenue_usd
                         ELSE NULL::integer::numeric
                         END) / 7::numeric                                AS avg_prev7_metric
      FROM (SELECT DISTINCT email_account_test_settings.country_id
            FROM imp.email_account_test_settings
            WHERE email_account_test_settings.stop_date IS NULL AND email_account_test_settings.is_active = true
               OR email_account_test_settings.stop_date::date >= (CURRENT_DATE - 1)) email
               LEFT JOIN aggregation.adv_revenue_by_placement_and_src_analytics
                         ON email.country_id = adv_revenue_by_placement_and_src_analytics.country_id
      WHERE adv_revenue_by_placement_and_src_analytics.date_diff >= ((SELECT startdate.date_d8
                                                                      FROM startdate))
        AND adv_revenue_by_placement_and_src_analytics.placement::text ~~ '%letter%'::text
      GROUP BY adv_revenue_by_placement_and_src_analytics.country_id
      UNION ALL
      SELECT 'product_metrics'::text       AS table_name,
             'aggregation'::text           AS scheme_name,
             'user_cnt'::text              AS metric_name,
             product_metrics.country_id,
             sum(
                     CASE
                         WHEN (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) -
                               product_metrics.first_visit_date_diff) = 1 THEN product_metrics.user_cnt::numeric
                         ELSE NULL::integer::numeric
                         END)              AS yesterday_metric,
             sum(
                     CASE
                         WHEN (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) -
                               product_metrics.first_visit_date_diff) >= 2 AND
                              (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) -
                               product_metrics.first_visit_date_diff) <= 8 THEN product_metrics.user_cnt::numeric
                         ELSE NULL::integer::numeric
                         END) / 7::numeric AS avg_prev7_metric
      FROM aggregation.product_metrics
      WHERE product_metrics.first_visit_date_diff >= ((SELECT startdate.date_d8
                                                       FROM startdate))
      GROUP BY product_metrics.country_id
      UNION ALL
      SELECT 'bing'::text                  AS table_name,
             'imp_statistic'::text         AS scheme_name,
             'impressions'::text           AS metric_name,
             0                             AS country_id,
             sum(
                     CASE
                         WHEN (CURRENT_DATE - bing.date) = 1 THEN bing.impressions
                         ELSE NULL::integer
                         END)              AS yesterday_metric,
             sum(
                     CASE
                         WHEN (CURRENT_DATE - bing.date) >= 2 AND (CURRENT_DATE - bing.date) <= 8 THEN bing.impressions::numeric
                         ELSE NULL::integer::numeric
                         END) / 7::numeric AS avg_prev7_metric
      FROM imp_statistic.bing
      WHERE bing.date >= ((SELECT startdate.date8
                           FROM startdate))
      GROUP BY 0::integer
      UNION ALL
      SELECT 'paid_info_currency'::text AS table_name,
             'traffic'::text            AS scheme_name,
             'cop_usd'::text            AS metric_name,
             0                          AS country_id,
             sum(
                     CASE
                         WHEN (CURRENT_DATE - cost.date) = 1 THEN cost.cop_usd
                         ELSE NULL::integer::double precision
                         END)           AS yesterday_metric,
             sum(
                     CASE
                         WHEN (CURRENT_DATE - cost.date) = 2 THEN cost.cop_usd
                         ELSE NULL::integer::double precision
                         END)           AS day_minus2_metric
      FROM traffic.paid_info_currency cost
      WHERE cost.date >= ((SELECT startdate.date8
                           FROM startdate))
      GROUP BY 0::integer
      UNION ALL
      SELECT 'search_agg'::text  AS table_name,
             'aggregation'::text AS scheme_name,
             'revenue_usd'::text AS metric_name,
             search_agg.country_id,
             sum(
                     CASE
                         WHEN (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) - search_agg.date_diff) = 1
                             THEN search_agg.revenue_usd::double precision
                         ELSE NULL::integer::double precision
                         END)    AS yesterday_metric,
             sum(
                     CASE
                         WHEN (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) - search_agg.date_diff) = 2
                             THEN search_agg.revenue_usd::double precision
                         ELSE NULL::integer::double precision
                         END)    AS day_minus2_metric
      FROM aggregation.search_agg
      WHERE search_agg.date_diff >= ((SELECT startdate.date_d8
                                      FROM startdate))
      GROUP BY search_agg.country_id
      UNION ALL
      SELECT 'account_revenue'::text       AS table_name,
             'aggregation'::text           AS scheme_name,
             'email_revenue'::text         AS metric_name,
             account_revenue.country_id,
             sum(
                     CASE
                         WHEN (CURRENT_DATE - account_revenue.revenue_date) = 1 THEN account_revenue.email_revenue
                         ELSE NULL::numeric
                         END)              AS yesterday_metric,
             sum(
                     CASE
                         WHEN (CURRENT_DATE - account_revenue.revenue_date) >= 2 AND
                              (CURRENT_DATE - account_revenue.revenue_date) <= 8 THEN account_revenue.email_revenue
                         ELSE NULL::integer::numeric
                         END) / 7::numeric AS avg_prev7_metric
      FROM (SELECT DISTINCT email_account_test_settings.country_id
            FROM imp.email_account_test_settings
            WHERE email_account_test_settings.stop_date IS NULL AND email_account_test_settings.is_active = true
               OR email_account_test_settings.stop_date::date >= (CURRENT_DATE - 1)) email
               LEFT JOIN aggregation.account_revenue ON email.country_id = account_revenue.country_id
      WHERE account_revenue.revenue_date >= ((SELECT startdate.date8
                                              FROM startdate))
      GROUP BY account_revenue.country_id
      UNION ALL
      SELECT 'cv_information_agg'::text    AS table_name,
             'aggregation'::text           AS scheme_name,
             'cv_count'::text              AS metric_name,
             cv_information_agg.country_id,
             sum(
                     CASE
                         WHEN (CURRENT_DATE - cv_information_agg.action_date) = 1 THEN cv_information_agg.cv_cnt::numeric
                         ELSE NULL::integer::numeric
                         END)              AS yesterday_metric,
             sum(
                     CASE
                         WHEN (CURRENT_DATE - cv_information_agg.action_date) >= 2 AND
                              (CURRENT_DATE - cv_information_agg.action_date) <= 8 THEN cv_information_agg.cv_cnt::numeric
                         ELSE NULL::integer::numeric
                         END) / 7::numeric AS avg_prev7_metric
      FROM aggregation.cv_information_agg
      WHERE cv_information_agg.action_date >= ((SELECT startdate.date8
                                                FROM startdate))
      GROUP BY cv_information_agg.country_id
      UNION ALL
      SELECT 'ahrefs_organic_v3'::text AS table_name,
             'traffic'::text           AS chema_name,
             'org_traffic'::text       AS metric_name,
             0                         AS country_id,
             sum(aov3.org_traffic)     AS org_traffic,
             0                         AS avg_prev7_metric
      FROM traffic.ahrefs_organic_v3 aov3
      WHERE aov3.date_add = make_date(date_part('year'::text, CURRENT_DATE - 1)::integer,
                                      date_part('month'::text, CURRENT_DATE - 1)::integer,
                                      CASE
                                          WHEN date_part('day'::text, CURRENT_DATE - 1) >= 1::double precision AND
                                               date_part('day'::text, CURRENT_DATE - 1) <= 6::double precision THEN 1
                                          WHEN date_part('day'::text, CURRENT_DATE - 1) >= 7::double precision AND
                                               date_part('day'::text, CURRENT_DATE - 1) <= 13::double precision THEN 7
                                          WHEN date_part('day'::text, CURRENT_DATE - 1) >= 14::double precision AND
                                               date_part('day'::text, CURRENT_DATE - 1) <= 20::double precision THEN 14
                                          WHEN date_part('day'::text, CURRENT_DATE - 1) >= 21::double precision THEN 21
                                          ELSE NULL::integer
                                          END)
        AND aov3.date_add >= ((SELECT startdate.date8
                               FROM startdate))
      GROUP BY 0::integer
      UNION ALL
      SELECT 'ahrefs_metrics_extended_v3'::text AS table_name,
             'traffic'::text                    AS chema_name,
             'backlinks'::text                  AS metric_name,
             0                                  AS country_id,
             sum(amev3.backlinks)               AS org_traffic,
             0                                  AS avg_prev7_metric
      FROM traffic.ahrefs_metrics_extended_v3 amev3
      WHERE amev3.date_add = make_date(date_part('year'::text, CURRENT_DATE - 1)::integer,
                                       date_part('month'::text, CURRENT_DATE - 1)::integer,
                                       CASE
                                           WHEN date_part('day'::text, CURRENT_DATE - 1) >= 1::double precision AND
                                                date_part('day'::text, CURRENT_DATE - 1) <= 6::double precision THEN 1
                                           WHEN date_part('day'::text, CURRENT_DATE - 1) >= 7::double precision AND
                                                date_part('day'::text, CURRENT_DATE - 1) <= 13::double precision THEN 7
                                           WHEN date_part('day'::text, CURRENT_DATE - 1) >= 14::double precision AND
                                                date_part('day'::text, CURRENT_DATE - 1) <= 20::double precision THEN 14
                                           WHEN date_part('day'::text, CURRENT_DATE - 1) >= 21::double precision THEN 21
                                           ELSE NULL::integer
                                           END)
        AND amev3.date_add >= ((SELECT startdate.date8
                                FROM startdate))
      GROUP BY 0::integer
      UNION ALL
      SELECT 'impression_statistic'::text AS table_name,
             'aggregation'::text          AS scheme_name,
             'impression_cnt'::text       AS metric_name,
             impression_statistic.country_id,
             sum(
                     CASE
                         WHEN (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) -
                               impression_statistic.action_datediff) = 1
                             THEN impression_statistic.impression_cnt::double precision
                         ELSE NULL::integer::double precision
                         END)             AS yesterday_metric,
             sum(
                     CASE
                         WHEN (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) -
                               impression_statistic.action_datediff) = 2
                             THEN impression_statistic.impression_cnt::double precision
                         ELSE NULL::integer::double precision
                         END)             AS day_minus2_metric
      FROM aggregation.impression_statistic
      WHERE impression_statistic.action_datediff >= ((SELECT startdate.date_d8
                                                      FROM startdate))
      GROUP BY impression_statistic.country_id
      UNION ALL
      SELECT 'recommendations_agg'::text AS table_name,
             'aggregation'::text         AS scheme_name,
             'click_price_usd'::text     AS metric_name,
             recommendations_agg.country_id,
             sum(
                     CASE
                         WHEN (CURRENT_DATE - recommendations_agg.action_date) = 1
                             THEN recommendations_agg.click_price_usd::double precision
                         ELSE NULL::integer::double precision
                         END)            AS yesterday_metric,
             sum(
                     CASE
                         WHEN (CURRENT_DATE - recommendations_agg.action_date) = 2
                             THEN recommendations_agg.click_price_usd::double precision
                         ELSE NULL::integer::double precision
                         END)            AS day_minus2_metric
      FROM aggregation.recommendations_agg
      WHERE recommendations_agg.action_date >= ((SELECT startdate.date
                                                 FROM startdate))
      GROUP BY recommendations_agg.country_id
      UNION ALL
      SELECT 'click_data_agg'::text                 AS table_name,
             'aggregation'::text                    AS scheme_name,
             'revenue_usd'::text                    AS metric_name,
             aggregation.click_data_agg.country_id,
             sum(
                     CASE
                         WHEN (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) -
                               aggregation.click_data_agg.action_datediff) = 1 THEN aggregation.click_data_agg.revenue_usd
                         ELSE NULL::integer::numeric::double precision
                         END)                       AS yesterday_metric,
             sum(
                     CASE
                         WHEN (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) -
                               aggregation.click_data_agg.action_datediff) >= 2 AND
                              (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) -
                               aggregation.click_data_agg.action_datediff) <= 8 THEN aggregation.click_data_agg.revenue_usd
                         ELSE NULL::integer::numeric::double precision
                         END) / 7::numeric::double precision AS avg_prev7_metric
      FROM postgres.aggregation.click_data_agg
      WHERE aggregation.click_data_agg.action_datediff >= ((SELECT startdate.date_d8
                                                              FROM startdate))
      GROUP BY aggregation.click_data_agg.country_id) source
         LEFT JOIN dimension.countries ON source.country_id = countries.id
WHERE COALESCE(countries.name_country_eng, 'Other'::character varying)::text <> 'Russia'::text
  AND COALESCE(countries.name_country_eng, 'Other'::character varying)::text <> 'Belarus'::text;

alter table aggregation.v_alert_monitoring
    owner to postgres;

grant select on aggregation.v_alert_monitoring to readonly;

grant delete, insert, references, select, trigger, truncate, update on aggregation.v_alert_monitoring to math;

grant select on aggregation.v_alert_monitoring to pbi;

grant select on aggregation.v_alert_monitoring to writeonly_pyscripts;

grant select on aggregation.v_alert_monitoring to readonly_aggregation;

grant select on aggregation.v_alert_monitoring to "pavlo.kvasnii";

grant select on aggregation.v_alert_monitoring to bdu;
