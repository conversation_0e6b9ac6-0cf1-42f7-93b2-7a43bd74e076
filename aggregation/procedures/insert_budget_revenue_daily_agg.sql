create procedure insert_budget_revenue_daily_agg()
    language plpgsql
as
$$begin



    insert into aggregation.budget_revenue_daily_agg(country_id, action_date, user_id, country_code, project_id, site_url,
                                        is_unlim_cmp, session_cnt, cpc_usd, click_cnt, organic_click_cnt,
                                        paid_click_cnt, revenue_usd, organic_revenue_usd, paid_revenue_usd,
                                        potential_revenue_usd, job_cnt, paid_overflow_cnt, user_budget_month_usd,
                                        campaign_budget_month_usd, revenue_budget_diff, potential_revenue_budget_diff,
                                        test_click_cnt, test_click_revenue_usd, external_coefficient, dublicated_click_revenue_usd,
                                        id_campaign, campaign)
    with date_range as
             (
                 Select make_date( cast(EXTRACT( year from  current_date - 1) as int), cast(EXTRACT(month from current_date - 1) as int), 1)   as start_date,
                        make_date( cast(EXTRACT( year from  current_date - 1) as int), cast(EXTRACT(month from current_date - 1) as int), cast(EXTRACT(day from current_date - 1) as int))as end_date
             ) ,

        click_data as (
             select acs.country_id as country,
                    acs.id_project,
                    acs.site,
                    acs.id_user,
                    acs.id_campaign,
                    acs.campaign,
                    sum(session_count)                                             as session_cnt,
                    sum(total_value) / sum(click_count)                            as cpc_usd,
                    sum(click_count)                                               as click_cnt,
                    sum(organic_count)                                             as organic_click_cnt,
                    sum(click_count) - sum(organic_count)                          as paid_click_cnt,
                    sum(total_value)                                               as revenue_usd,
                    sum(  total_value +
                    total_value / extract(day from current_date - 1) *
                    (make_date(
            cast(extract(year from current_date - 1 + INTERVAL '1 month' ) as int) ,cast( extract(month from current_date - 1 + INTERVAL '1 month' ) as int), 1)
    - cast( current_date - 1  as date) -1))    as potential_revenue_usd,
                    sum(total_value/click_count*organic_count)                     as organic_revenue_usd,
                    sum(total_value/click_count*(click_count-organic_count))       as paid_revenue_usd,
                    sum(job_count)                                                 as job_cnt,
                    sum(paid_overflow_count)                                       as paid_overflow_cnt,
                    sum(test_count)                                                as test_click_cnt,
                    sum(total_value/click_count*test_count)                        as test_click_revenue_usd,
                    sum(total_value/click_count*duplicated_count)                  as dublicated_click_revenue_usd
             from aggregation.auction_click_statistic_analytics acs
             where cast(date  as date) BETWEEN (select start_date from date_range) and (select end_date from date_range)
               and acs.click_price > 0
             group by acs.country_id,
                      acs.id_project,
                      acs.site,
                      acs.id_user,
                      acs.id_campaign,
                      acs.campaign
         ),

         budgets as (
             select cd.country,
                    cd.id_user,
                    cd.id_project,
                    cd.id_campaign,
                    coalesce(au.budget * ic.value_to_usd, 0)  as user_budget_month_usd,
                    coalesce(ac.budget * ic1.value_to_usd, 0) as campaign_budget_month_usd,
                    case when ac.budget = 0 and ac.click_price >0
                    or ac.budget = 0 and campaign_status = 0 and is_price_per_job = TRUE  then 1 else 0 end as is_unlim_cmp
             from click_data cd
                      join imp.auction_user au
                           on  au.country = cd.country
                            and   au.id = cd.id_user
                      join dimension.info_currency ic
                           on  au.country = ic.country
                            and   au.currency = ic.id
                      join imp.auction_campaign ac
                           on  ac.country_id = cd.country
                           and    ac.id = cd.id_campaign
                      join dimension.info_currency ic1
                           on  ac.country_id = ic1.country
                           and    ac.currency = ic1.id
         ),
        coefficient as
         (
                        SELECT campaign.country_id,
                               campaign.id_project,
                               avg(campaign.conversion_rate) AS external_coefficient
                        FROM imp.auction_campaign campaign
                        WHERE campaign.conversion_rate IS NOT NULL
                          AND campaign.campaign_status = 0
                        GROUP BY  campaign.id_project,campaign.country_id
         )

    select cd.country as country_id,
           current_date - 1   as action_date,
           cd.id_user,
           countries.alpha_2 as country,
           cd.id_project,
           cd.site,
           max(b.is_unlim_cmp) as is_unlim_cmp,
           sum(session_cnt)                            as session_cnt,
           sum(cd.revenue_usd) / sum(click_cnt)        as cpc_usd,
           sum(click_cnt)                              as click_cnt,
           sum(organic_click_cnt)                      as organic_click_cnt,
           sum(paid_click_cnt)                         as paid_click_cnt,
           sum(revenue_usd)                            as revenue_usd,
           sum(organic_revenue_usd)                    as organic_revenue_usd,
           sum(paid_revenue_usd)                       as paid_revenue_usd,
           sum(potential_revenue_usd)                  as potential_revenue_usd,
           sum(job_cnt)                                as job_cnt,
           sum(paid_overflow_cnt)                      as paid_overflow_cnt,
           max(user_budget_month_usd)                  as user_budget_month_usd,
           sum(campaign_budget_month_usd)              as campaign_budget_month_usd,
           case
               when
                   max(user_budget_month_usd) > 0
               then max(user_budget_month_usd) - sum(revenue_usd)
               when
                   max(user_budget_month_usd) = 0 and sum(campaign_budget_month_usd) > 0
               then sum(campaign_budget_month_usd) - sum(revenue_usd)
               else 0 end                                as revenue_budget_diff,
           case
               when
                   max(user_budget_month_usd) > 0
               then max(user_budget_month_usd) - sum(potential_revenue_usd)
               when
                   max(user_budget_month_usd) = 0 and sum(campaign_budget_month_usd) > 0
               then sum(campaign_budget_month_usd) - sum(potential_revenue_usd)
               else 0 end                                as potential_revenue_budget_diff,
           sum(test_click_cnt)                         as test_click_cnt,
           sum(test_click_revenue_usd)                 as test_click_revenue_usd,
           c.external_coefficient,
           sum(dublicated_click_revenue_usd)          as dublicated_click_revenue_usd,
           cd.id_campaign,
           cd.campaign
    from click_data cd
             left join budgets b
                       on b.country = cd.country and
                           b.id_project = cd.id_project and b.id_campaign = cd.id_campaign
                           and b.id_user = cd.id_user
              left join coefficient c
                        on  cd.country = c.country_id and
                            cd.id_project = c.id_project
               left join dimension.countries
                        on cd.country = countries.id

    group by
             cd.id_user,
             cd.country,
             cd.id_project,
             cd.site,
             c.external_coefficient,
             countries.alpha_2,
             cd.id_campaign,
             cd.campaign ;





end$$;

alter procedure insert_budget_revenue_daily_agg() owner to yiv;

