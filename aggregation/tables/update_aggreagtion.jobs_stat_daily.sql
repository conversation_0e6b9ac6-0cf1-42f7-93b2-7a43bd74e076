-- jobs_stat_daily (updated sql) --


declare @start_datediff int = ${dt_begin},
		@dt_end int = ${dt_end},
		@country_id int = ${country_id}


Select @country_id as id_country,
       cast(getdate() as date) as date,
       job.id_project,
       job_region.id_campaign,
sum(case when campaign.click_price > 0 or coalesce(job_region_billing_info.click_price, 0) > 0 then 1 else 0 end)     as paid_job_count,
sum(case when campaign.click_price = 0 and coalesce(job_region_billing_info.click_price, 0) = 0 then 1 else 0 end)     as organic_job_count,
coalesce(sum(case when campaign.click_price = 0 and campaign.is_price_per_job = 1 then coalesce(job_region_billing_info.click_price, 0) else campaign.click_price end) / nullif(sum(case when campaign.click_price > 0 or coalesce(job_region_billing_info.click_price, 0) > 0 then 1 else 0 end),0),0)  as avg_cpc_for_paid_job_count,
sum(case when campaign.flags = 32 then 1 else 0 end) as flag_32_jobs_count
from dbo.job with (nolock)
         join dbo.job_region with (nolock)
              on job.id = job_region.id_job
         join auction.campaign with (nolock)
              on job_region.id_campaign = campaign.id
         left join dbo.job_region_billing_info with (nolock)
                   on job_region.uid = job_region_billing_info.uid_job
where (job.date_expired is null or cast(job.date_expired as date) > getdate())
  and job_region.inactive = 0
  and (campaign.date_end is null or cast(campaign.date_end as date) > getdate())
  and campaign.campaign_status = 0
group by job.id_project,
         job_region.id_campaign;


