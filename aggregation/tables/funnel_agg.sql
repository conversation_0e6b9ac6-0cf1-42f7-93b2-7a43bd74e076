SET NOCOUNT ON;

<PERSON><PERSON><PERSON><PERSON> @start_date_diff int = :to_sqlcode_date_or_datediff_start;


WITH Session_cte AS (SELECT CAST(session.start_date AS date)                                   AS date,
                            CASE WHEN session.flags & 16 = 16 THEN 'Mobile' ELSE 'Desktop' END AS device,
                            session.date_diff,
                            session.id                                                         AS id_session,
                            u_traffic_source.name                                              AS traffic_source,
                            u_traffic_source.is_paid                                           AS traffic_source_is_paid,
                            session.session_create_page_type
                     FROM dbo.session WITH (NOLOCK)
                              LEFT JOIN dbo.u_traffic_source WITH (NOLOCK)
                                        ON COALESCE(session.id_current_traf_source, session.id_traf_source) =
                                           u_traffic_source.id
                     WHERE session.flags & 1 = 0
                       AND session.date_diff = @start_date_diff),


     Search AS (SELECT CAST(session_search.date AS date) AS date,
                       Session_cte.device,
                       Session_cte.traffic_source,
                       Session_cte.traffic_source_is_paid,
                       session_search.id                 AS id_search,
                       session_search.id_session,
                       Session_cte.session_create_page_type
                FROM dbo.session_search WITH (NOLOCK)
                         JOIN Session_cte
                              ON session_search.date_diff = Session_cte.date_diff
                                  AND session_search.id_session = Session_cte.id_session
                WHERE session_search.date_diff = @start_date_diff),


     AlertView AS (SELECT CAST(session_alertview.date AS date) AS date,
                          Session_cte.device,
                          Session_cte.traffic_source,
                          Session_cte.traffic_source_is_paid,
                          session_alertview.id                 AS id_alertview,
                          session_alertview.id_session,
                          Session_cte.session_create_page_type
                   FROM dbo.session_alertview WITH (NOLOCK)
                            JOIN Session_cte
                                 ON session_alertview.date_diff = Session_cte.date_diff
                                     AND session_alertview.id_session = Session_cte.id_session
                   WHERE session_alertview.date_diff = @start_date_diff),

     JDP AS
         (SELECT CAST(session_jdp.date AS date)                                                        AS date,
                 Session_cte.device,
                 Session_cte.traffic_source,
                 Session_cte.traffic_source_is_paid,
                 session_jdp.id                                                                        AS id_jdp,
                 session_jdp.id_session,
                 Session_cte.session_create_page_type,
                 COALESCE(COALESCE(session_click.id_search, session_click.id_alertview),
                          session_recommend.id_search)                                                 AS id_search,
                 info_project.name                                                                     AS project_name,
                 session_jdp.letter_type,
                 CASE
                     WHEN session_click.id_alertview IS NOT NULL AND session_jdp.letter_type IS NOT NULL
                         THEN CONCAT('Letter type ', session_jdp.letter_type, '->AlertView->JDP')
                     WHEN session_click.id_alertview IS NOT NULL AND session_jdp.letter_type IS NULL
                         THEN 'AlertView->JDP'
                     WHEN session_jdp.letter_type IS NOT NULL AND session_click.id_alertview IS NULL
                         THEN CONCAT('Letter type ', session_jdp.letter_type, '->JDP')
                     WHEN session_click.id_search IS NOT NULL THEN 'Serp->JDP'
                     ELSE 'Other->JDP'
                     END                                                                               AS jdp_type
          FROM dbo.session_jdp WITH (NOLOCK)
                   LEFT JOIN dbo.info_project WITH (NOLOCK)
                             ON session_jdp.job_id_project = info_project.id
                   JOIN Session_cte WITH (NOLOCK)
                        ON session_jdp.date_diff = Session_cte.date_diff
                            AND session_jdp.id_session = Session_cte.id_session
                   LEFT JOIN dbo.session_click
                             ON session_jdp.date_diff = session_click.date_diff
                                 AND session_jdp.id_click = session_click.id
                   LEFT JOIN dbo.session_recommend
                             ON session_click.date_diff = session_recommend.date_diff
                                 AND session_click.id_recommend = session_recommend.id
          WHERE session_jdp.date_diff = @start_date_diff),


     Aways AS (SELECT CAST(session_away.date AS date)                                                  AS date,
                      session_away.id_session,
                      session_away.date_diff,
                      info_project.name                                                                AS project_name,
                      CASE
                          WHEN session_click_jdp.id_alertview IS NOT NULL AND session_jdp.letter_type IS NOT NULL AND
                               session_away.id_jdp IS NOT NULL THEN CONCAT('Letter type', session_jdp.letter_type,
                                                                           '->AlertView->JDP->Away')
                          WHEN session_click.id_alertview IS NOT NULL AND session_away.letter_type IS NOT NULL
                              THEN CONCAT('Letter type', session_jdp.letter_type, '->AlertView->Away')
                          WHEN session_away.id_jdp IS NOT NULL AND session_click_jdp.id_alertview IS NOT NULL
                              THEN 'AlertView->JDP->Away'
                          WHEN session_away.id_jdp IS NOT NULL AND session_jdp.letter_type IS NOT NULL
                              THEN CONCAT('Letter type', session_jdp.letter_type, '->JDP->Away')
                          WHEN session_away.id_jdp IS NOT NULL THEN 'Serp->JDP->Away'
                          WHEN session_click.id_alertview IS NOT NULL THEN 'AlertView->Away'
                          WHEN session_click.id_alertview IS NULL AND session_click.id_search IS NOT NULL
                              THEN 'Serp->Away'
                          WHEN session_away.letter_type IS NOT NULL
                              THEN CONCAT('Letter type ', session_away.letter_type, '->Away')
                          WHEN session_away.id_click_no_serp IS NOT NULL THEN 'No Serp->Away'
                          WHEN session_away.id_cdp IS NOT NULL THEN 'CDP->Away'
                          ELSE 'Other->Away' END                                                       AS away_type,
                      session_away.click_price * info_currency.value_to_usd                            AS away_revenue_usd,
                      session_away.id                                                                  AS id_away,
                      session_away.id_jdp,
                      COALESCE(COALESCE(COALESCE(COALESCE(COALESCE(session_click.id_search, session_click.id_alertview),
                                                          session_click_jdp.id_search), session_click_jdp.id_alertview),
                                        session_recommend.id_search), session_recommend_jdp.id_search) AS id_search,
                      conversion_away_connection.id_session_away                                       AS id_conversion
               FROM dbo.session_away WITH (NOLOCK)
                        LEFT JOIN dbo.session_click WITH (NOLOCK)
                                  ON session_away.date_diff = session_click.date_diff
                                      AND session_away.id_click = session_click.id
                        LEFT JOIN dbo.session_jdp WITH (NOLOCK)
                                  ON session_away.date_diff = session_jdp.date_diff
                                      AND session_away.id_jdp = session_jdp.id
                        LEFT JOIN dbo.session_click AS session_click_jdp WITH (NOLOCK)
                                  ON session_jdp.date_diff = session_click_jdp.date_diff
                                      AND session_jdp.id_click = session_click_jdp.id
                        LEFT JOIN dbo.session_recommend
                                  ON session_click.date_diff = session_recommend.date_diff
                                      AND session_click.id_recommend = session_recommend.id
                        LEFT JOIN dbo.session_recommend session_recommend_jdp
                                  ON session_click_jdp.date_diff = session_recommend_jdp.date_diff
                                      AND session_click_jdp.id_recommend = session_recommend_jdp.id
                        LEFT JOIN dbo.info_currency WITH (NOLOCK)
                                  ON session_away.id_currency = info_currency.id
                        LEFT JOIN dbo.info_project WITH (NOLOCK)
                                  ON session_away.id_project = info_project.id
                        LEFT JOIN (SELECT DISTINCT date_diff, id_session_away
                                   FROM auction.conversion_away_connection WITH (NOLOCK)) conversion_away_connection
                                  ON session_away.date_diff = conversion_away_connection.date_diff
                                      AND session_away.id = conversion_away_connection.id_session_away
               WHERE session_away.flags & 2 = 0
                 AND session_away.date_diff = @start_date_diff),

     Apply AS (SELECT CAST(session_apply.apply_date AS date) AS date,
                      JDP.device,
                      JDP.traffic_source,
                      JDP.traffic_source_is_paid,
                      JDP.session_create_page_type,
                      JDP.project_name,
                      session_apply.id                       AS id_click,
                      JDP.id_session,
                      JDP.id_search                          AS id_search,
                      CONCAT(JDP.jdp_type, '->Apply')        AS apply_type
               FROM dbo.session_apply WITH (NOLOCK)
                        JOIN dbo.session_jdp_action WITH (NOLOCK)
                             ON session_apply.date_diff = session_jdp_action.date_diff
                                 AND session_apply.id_src_jdp_action = session_jdp_action.id
                        JOIN JDP
                             ON session_jdp_action.id_jdp = JDP.id_jdp
               WHERE session_apply.date_diff = @start_date_diff),

     Group_Sessions AS (SELECT date,
                               device,
                               traffic_source,
                               traffic_source_is_paid,
                               session_create_page_type,
                               COUNT(Session_cte.id_session) AS sessions
                        FROM Session_cte
                        GROUP BY date,
                                 device,
                                 traffic_source,
                                 traffic_source_is_paid,
                                 session_create_page_type),
     Group_Searches AS (SELECT date,
                               device,
                               traffic_source,
                               traffic_source_is_paid,
                               session_create_page_type,
                               COUNT(DISTINCT Search.id_session) AS sessions,
                               COUNT(Search.id_search)           AS searches
                        FROM Search
                        GROUP BY date,
                                 device,
                                 traffic_source,
                                 traffic_source_is_paid,
                                 session_create_page_type),
     Group_Impressions AS (SELECT DATEADD(DAY, impression_statistic.date_diff, '1900-01-01') AS date,
                                  info_project.name                                          AS project_name,
                                  SUM(impressions_count)                                     AS impressions_count
                           FROM auction.impression_statistic WITH (NOLOCK)
                                    LEFT JOIN auction.campaign WITH (NOLOCK)
                                              ON impression_statistic.id_campaign = campaign.id
                                    LEFT JOIN dbo.info_project WITH (NOLOCK)
                                              ON campaign.id_project = info_project.id
                           WHERE impression_statistic.date_diff = @start_date_diff
                           GROUP BY DATEADD(DAY, impression_statistic.date_diff, '1900-01-01'),
                                    info_project.name),
     Group_Alertviews AS (SELECT date,
                                 device,
                                 traffic_source,
                                 traffic_source_is_paid,
                                 session_create_page_type,
                                 COUNT(DISTINCT Alertview.id_session) AS sessions,
                                 COUNT(Alertview.id_alertview)        AS alertViews
                          FROM Alertview
                          GROUP BY date,
                                   device,
                                   traffic_source,
                                   traffic_source_is_paid,
                                   session_create_page_type),

     Group_JDPs AS (SELECT date,
                           device,
                           traffic_source,
                           traffic_source_is_paid,
                           session_create_page_type,
                           project_name,
                           jdp_type,
                           COUNT(DISTINCT JDP.id_session) AS sessions,
                           COUNT(DISTINCT JDP.id_search)  AS searches,
                           COUNT(id_jdp)                  AS jdps
                    FROM JDP
                    GROUP BY date,
                             device,
                             traffic_source,
                             traffic_source_is_paid,
                             project_name,
                             jdp_type,
                             session_create_page_type),
     Group_Aways AS (SELECT Aways.date,
                            Session_cte.device,
                            Session_cte.traffic_source,
                            Session_cte.traffic_source_is_paid,
                            Session_cte.session_create_page_type,
                            project_name,
                            Aways.away_type,
                            COUNT(DISTINCT Aways.id_session) AS sessions,
                            COUNT(DISTINCT Aways.id_search)  AS searches,
                            COUNT(DISTINCT Aways.id_jdp)     AS jdps,
                            COUNT(DISTINCT id_away)          AS aways,
                            COUNT(DISTINCT id_conversion)    AS conversions,
                            SUM(Aways.away_revenue_usd)      AS away_revenue_usd
                     FROM Aways
                              JOIN Session_cte
                                   ON Aways.date_diff = Session_cte.date_diff
                                       AND Aways.id_session = Session_cte.id_session
                     GROUP BY Aways.date,
                              Session_cte.device,
                              Session_cte.traffic_source,
                              Session_cte.traffic_source_is_paid,
                              Session_cte.session_create_page_type,
                              project_name,
                              Aways.away_type),
     Group_Applies AS (SELECT date,
                              device,
                              traffic_source,
                              traffic_source_is_paid,
                              session_create_page_type,
                              project_name,
                              apply_type,
                              COUNT(DISTINCT id_session) AS sessions,
                              COUNT(DISTINCT id_search)  AS searches,
                              COUNT(id_click)            AS applies
                       FROM Apply
                       GROUP BY date,
                                device,
                                traffic_source,
                                traffic_source_is_paid,
                                session_create_page_type,
                                project_name,
                                apply_type),
     Group_Total_Aways AS (SELECT Aways.date,
                                  Session_cte.device,
                                  Session_cte.traffic_source,
                                  Session_cte.traffic_source_is_paid,
                                  Session_cte.session_create_page_type,
                                  project_name,
                                  CASE
                                      WHEN Aways.away_type LIKE 'Serp->%' THEN 'Serp Away Total'
                                      WHEN Aways.away_type LIKE 'AlertView->%' THEN 'AlertView Away Total'
                                      ELSE 'Other Away Total' END  AS away_type,
                                  COUNT(DISTINCT Aways.id_session) AS sessions,
                                  COUNT(DISTINCT Aways.id_search)  AS searches,
                                  COUNT(DISTINCT Aways.id_jdp)     AS jdps,
                                  COUNT(id_away)                   AS aways,
                                  COUNT(id_conversion)             AS conversions,
                                  SUM(Aways.away_revenue_usd)      AS away_revenue_usd
                           FROM Aways
                                    JOIN Session_cte
                                         ON Aways.date_diff = Session_cte.date_diff
                                             AND Aways.id_session = Session_cte.id_session
                           GROUP BY Aways.date,
                                    Session_cte.device,
                                    Session_cte.traffic_source,
                                    Session_cte.traffic_source_is_paid,
                                    Session_cte.session_create_page_type,
                                    project_name,
                                    Aways.away_type),
     Group_JDPs_Aways AS (SELECT date,
                                 device,
                                 traffic_source,
                                 traffic_source_is_paid,
                                 session_create_page_type,
                                 project_name,
                                 CASE
                                     WHEN jdp_type IN ('Serp->Away', 'Serp->JDP') THEN 'Serp->JDP/Away'
                                     WHEN jdp_type IN ('AlertView->Away', 'AlertView->JDP') THEN 'AlertView->JDP/Away'
                                     ELSE 'Other->JDP/Away' END AS away_type,
                                 COUNT(DISTINCT id_session)     AS sessions,
                                 COUNT(DISTINCT id_search)      AS searches
                          FROM (SELECT date,
                                       device,
                                       traffic_source,
                                       traffic_source_is_paid,
                                       session_create_page_type,
                                       project_name,
                                       jdp_type,
                                       JDP.id_session,
                                       JDP.id_search

                                FROM JDP
                                UNION ALL
                                SELECT Aways.date,
                                       Session_cte.device,
                                       Session_cte.traffic_source,
                                       Session_cte.traffic_source_is_paid,
                                       session_create_page_type,
                                       project_name,
                                       Aways.away_type,
                                       Aways.id_session,
                                       Aways.id_search
                                FROM Aways
                                         JOIN Session_cte
                                              ON Aways.date_diff = Session_cte.date_diff
                                                  AND Aways.id_session = Session_cte.id_session) JdpAways
                          GROUP BY date,
                                   device,
                                   traffic_source,
                                   traffic_source_is_paid,
                                   session_create_page_type,
                                   project_name,
                                   CASE
                                       WHEN jdp_type IN ('Serp->Away', 'Serp->JDP') THEN 'Serp->JDP/Away'
                                       WHEN jdp_type IN ('AlertView->Away', 'AlertView->JDP') THEN 'AlertView->JDP/Away'
                                       ELSE 'Other->JDP/Away' END),
     Unions AS (SELECT date,
                       device,
                       traffic_source,
                       traffic_source_is_paid,
                       session_create_page_type,
                       NULL        AS project_name,
                       'Session->' AS step,
                       Sessions    AS value,
                       0           AS searches,
                       Sessions    AS events,
                       0           AS away_revenue_usd
                FROM Group_Sessions
                UNION ALL
                SELECT date,
                       device,
                       traffic_source,
                       traffic_source_is_paid,
                       session_create_page_type,
                       NULL     AS project_name,
                       'Serp->' AS step,
                       Sessions AS value,
                       searches AS searches,
                       searches AS events,
                       0        AS away_revenue_usd
                FROM Group_Searches
                UNION ALL
                SELECT date,
                       device,
                       traffic_source,
                       traffic_source_is_paid,
                       session_create_page_type,
                       NULL           AS project_name,
                       'Alert View->' AS step,
                       Sessions       AS value,
                       alertViews     AS searches,
                       alertViews     AS events,
                       0              AS away_revenue_usd
                FROM Group_Alertviews
                UNION ALL
                SELECT date,
                       device,
                       traffic_source,
                       traffic_source_is_paid,
                       session_create_page_type,
                       project_name,
                       jdp_type AS step,
                       sessions AS value,
                       searches AS searches,
                       jdps     AS events,
                       0        AS away_revenue_usd
                FROM Group_JDPs
                UNION ALL
                SELECT date,
                       device,
                       traffic_source,
                       traffic_source_is_paid,
                       session_create_page_type,
                       project_name,
                       away_type                                                    AS step,
                       Sessions                                                     AS value,
                       CASE WHEN away_type LIKE '%JDP%' THEN jdps ELSE searches END AS searches,
                       aways                                                        AS events,
                       away_revenue_usd
                FROM Group_Aways
                UNION ALL
                SELECT date,
                       device,
                       traffic_source,
                       traffic_source_is_paid,
                       session_create_page_type,
                       project_name,
                       away_type                                                    AS step,
                       Sessions                                                     AS value,
                       CASE WHEN away_type LIKE '%JDP%' THEN jdps ELSE searches END AS searches,
                       aways                                                        AS events,
                       away_revenue_usd
                FROM Group_Total_Aways
                UNION ALL
                SELECT date,
                       device,
                       traffic_source,
                       traffic_source_is_paid,
                       session_create_page_type,
                       project_name,
                       away_type AS step,
                       Sessions  AS value,
                       searches,
                       0         AS events,
                       0         AS away_revenue_usd
                FROM Group_JDPs_Aways
                UNION ALL
                SELECT date,
                       device,
                       traffic_source,
                       traffic_source_is_paid,
                       session_create_page_type,
                       project_name,
                       CONCAT(away_type, '->Conversion') AS step,
                       sessions                          AS value,
                       searches                          AS searches,
                       conversions                       AS events,
                       0                                 AS away_revenue_usd
                FROM Group_Aways
                WHERE conversions > 0
                  AND conversions IS NOT NULL
                UNION ALL
                SELECT date,
                       NULL              AS device,
                       NULL              AS traffic_source,
                       NULL              AS traffic_source_is_paid,
                       NULL              AS session_create_page_type,
                       project_name,
                       'Impressions->'   AS step,
                       0                 AS value,
                       0                 AS searches,
                       impressions_count AS events,
                       0                 AS away_revenue_usd
                FROM Group_Impressions
                UNION ALL
                SELECT date,
                       device,
                       traffic_source,
                       traffic_source_is_paid,
                       session_create_page_type,
                       project_name,
                       apply_type AS step,
                       sessions   AS value,
                       searches   AS searches,
                       applies    AS events,
                       0          AS away_revenue_usd
                FROM Group_Applies),
     Final AS (SELECT date,
                      device,
                      traffic_source,
                      traffic_source_is_paid,
                      session_create_page_type,
                      project_name,
                      step,
                      SUM(value)            AS value,
                      SUM(searches)         AS searches,
                      SUM(events)           AS events,
                      SUM(away_revenue_usd) AS away_revenue_usd
               FROM Unions
               GROUP BY date,
                        device,
                        traffic_source,
                        traffic_source_is_paid,
                        session_create_page_type,
                        project_name,
                        step)
SELECT CAST([date] AS datetime) AS action_date,
       device                   AS device_name,
       traffic_source           AS traffic_source_name,
       traffic_source_is_paid   AS is_paid_traffic,
       session_create_page_type,
       project_name,
       step,
       value                    AS session_cnt,
       searches                 AS searches_cnt,
       events                   AS event_cnt,
       away_revenue_usd
FROM Final
;
