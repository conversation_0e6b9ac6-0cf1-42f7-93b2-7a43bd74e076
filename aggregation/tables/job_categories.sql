    CREATE TEMP TABLE temp_info_currency AS
    SELECT id, value_to_usd
    FROM link_dbo.info_currency;

    CREATE TEMP TABLE temp_link_auction_campaign AS
    SELECT id, click_price, currency, is_price_per_job, campaign_status, date_end
    FROM link_auction.campaign;
    CREATE INDEX idx_temp_link_auction_campaign_id ON temp_link_auction_campaign (id);
    CREATE INDEX idx_temp_link_auction_campaign_currency ON temp_link_auction_campaign (currency);

    CREATE TEMP TABLE temp_link_auction_conversion_away_connection AS
    SELECT DISTINCT id_session_away, date_diff
    FROM link_auction.conversion_away_connection;
    CREATE INDEX idx_temp_link_auction_conversion_away_connection
        ON temp_link_auction_conversion_away_connection (id_session_away);

    CREATE TEMP TABLE temp_session_away AS
    SELECT id, date_diff, uid_job, click_price, id_session, id_currency, id_project, flags
    FROM public.session_away
    WHERE date_diff = _date_diff_start
      AND flags & 512 = 0
      AND flags & 2 = 0;
    ANALYZE temp_session_away;

    CREATE TEMP TABLE temp_session AS
    SELECT id, date_diff, flags
    FROM public.session
    WHERE date_diff = _date_diff_start;
    CREATE INDEX idx_temp_session_id ON temp_session (id);

    CREATE TEMP TABLE temp_conversion_start_p1 AS
    SELECT date_diff, id_session_away
    FROM link_auction.conversion_away_connection
    WHERE date_diff = _date_diff_start;
    ANALYZE temp_conversion_start_p1;

    CREATE TEMP TABLE temp_conversion_start_p2 AS
    SELECT sa.id_project,
           MIN(cs.date_diff) AS conversion_start
    FROM temp_conversion_start_p1 AS cs
             JOIN temp_session_away AS sa
                  ON cs.id_session_away = sa.id
    GROUP BY sa.id_project;


    ANALYZE temp_session;
    ANALYZE temp_info_currency;
    ANALYZE temp_link_auction_conversion_away_connection;
    ANALYZE temp_link_auction_campaign;
    ANALYZE temp_conversion_start_p2;


    CREATE TEMP TABLE temp_sa AS
    SELECT sa.uid_job,
           SUM(sa.click_price * CAST(ic.value_to_usd * 1.0 AS numeric))                         AS revenue_usd,
           COUNT(DISTINCT sa.id)                                                                AS away_cnt,
           COUNT(DISTINCT CASE WHEN sa.date_diff >= cs.conversion_start THEN sa.id END)         AS away_conversion_cnt,
           COUNT(cac.id_session_away)                                                           AS conversion_cnt,
           CAST(SUM(sa.click_price * ic.value_to_usd) / COUNT(DISTINCT sa.id) * 1.0 AS numeric) AS cpc_usd,
           SUM(CASE
                   WHEN sa.date_diff >= cs.conversion_start
                       THEN sa.click_price * CAST(ic.value_to_usd * 1.0 AS numeric)
               END)                                                                             AS conversion_revenue_usd
    FROM temp_session_away sa
             LEFT JOIN temp_session s
                       ON sa.id_session = s.id AND COALESCE(s.flags, 0) & 1 = 0
             LEFT JOIN temp_info_currency ic
                       ON ic.id = sa.id_currency
             LEFT JOIN temp_conversion_start_p2 AS cs
                       ON cs.id_project = sa.id_project
             LEFT JOIN temp_link_auction_conversion_away_connection AS cac
                       ON cac.id_session_away = sa.id
    WHERE sa.flags & 512 = 0
      AND sa.flags & 2 = 0
    GROUP BY sa.uid_job;

    CREATE INDEX temp_sa_idx ON temp_sa (uid_job);
    ANALYZE temp_sa;


    DROP TABLE IF EXISTS temp_link_auction_conversion_away_connection;
    DROP TABLE IF EXISTS tmp_session_away;
    DROP TABLE IF EXISTS tmp_session;
    DROP TABLE IF EXISTS temp_conversion_start_p1;
    DROP TABLE IF EXISTS temp_conversion_start_p2;


    CREATE TEMP TABLE temp_snap_job_region AS
    SELECT uid, id_job, id_campaign, inactive
    FROM an.snap_job_region
    WHERE inactive = 0;
    CREATE INDEX INDEX_temp_snap_job_region_01 ON temp_snap_job_region (id_job);
    CREATE INDEX INDEX_temp_snap_job_region_02 ON temp_snap_job_region (uid);
    ANALYZE temp_snap_job_region;

    CREATE TEMP TABLE temp_link_dbo_job_region_billing_info AS
    SELECT uid_job, click_price
    FROM link_dbo.job_region_billing_info;
    CREATE INDEX INDEX_temp_link_dbo_job_region_billing_info ON temp_link_dbo_job_region_billing_info (uid_job);
    ANALYZE temp_link_dbo_job_region_billing_info;


-- truncate desctination table
    DELETE
    FROM an.rpl_job_categories
    WHERE date = _date_diff_start;
--


    CREATE TEMP TABLE temp_result AS
    SELECT _country_id                                                                AS country_id,
           _country_name                                                              AS country_name,
           CAST(_date_diff_start AS INTEGER)                                          AS date,
           CAST(j.id_category AS INTEGER)                                             AS id_category,
           CAST(j.id_project AS INTEGER)                                              AS id_project,
           SUM(sa.revenue_usd)::numeric                                               AS revenue_usd,
           CAST(COUNT(DISTINCT j.id) AS BIGINT)                                       AS job_cnt,
           CAST(COUNT(DISTINCT sa.uid_job) AS BIGINT)                                 AS job_with_away_cnt,
           CAST(SUM(sa.away_cnt) AS BIGINT)                                           AS away_cnt,
           CAST(SUM(sa.away_conversion_cnt) AS BIGINT)                                AS away_conversion_cnt,
           CAST(SUM(sa.conversion_cnt) AS BIGINT)                                     AS conversion_cnt,
           CAST(COUNT(DISTINCT CASE WHEN sa.revenue_usd > 0 THEN j.id END) AS BIGINT) AS job_with_revenue_cnt,
           PERCENTILE_CONT(0.5)
           WITHIN GROUP (ORDER BY CASE WHEN cpc_usd = 0 THEN NULL ELSE cpc_usd END)   AS median_cpc_usd,
           CAST(COALESCE(SUM(CASE
                                 WHEN c.click_price = 0 AND c.is_price_per_job = 1
                                     THEN COALESCE(jrbi.click_price, 0) * ic.value_to_usd
                                 ELSE c.click_price * ic.value_to_usd
                             END) / NULLIF(SUM(CASE
                                                   WHEN c.click_price > 0 OR COALESCE(jrbi.click_price, 0) > 0
                                                       THEN 1
                                                   ELSE 0
                                               END), 0),
                         0) AS numeric)                                               AS avg_cpc_for_paid_job_count_usd,
           SUM(sa.conversion_revenue_usd)                                             AS conversion_revenue_usd
    FROM an.snap_job j
             LEFT JOIN temp_snap_job_region jr
                       ON j.id = jr.id_job
             LEFT JOIN temp_link_dbo_job_region_billing_info jrbi
                       ON jr.uid = jrbi.uid_job
             LEFT JOIN temp_sa sa
                       ON jr.uid = sa.uid_job
             LEFT JOIN temp_link_auction_campaign c
                       ON jr.id_campaign = c.id
             LEFT JOIN temp_info_currency ic
                       ON ic.id = c.currency
    WHERE (j.date_expired IS NULL OR CAST(j.date_expired AS date) > CURRENT_DATE)
      AND jr.inactive = 0
      AND (c.date_end IS NULL OR CAST(c.date_end AS date) > CURRENT_DATE)
      AND c.campaign_status = 0
    GROUP BY j.id_category,
             j.id_project;


    INSERT INTO an.rpl_job_categories(country_id, country_name, date, id_category, id_project, revenue_usd, job_cnt,
                                      job_with_away_cnt, away_cnt, away_conversion_cnt, conversion_cnt,
                                      job_with_revenue_cnt, median_cpc_usd, avg_cpc_for_paid_job_count_usd,
                                      conversion_revenue_usd)
    SELECT *
    FROM temp_result;
