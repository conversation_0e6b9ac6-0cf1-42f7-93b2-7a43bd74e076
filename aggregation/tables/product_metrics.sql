create temp table temp_session_test_agg as
    select  id_session,
            session_test.date_diff,
            array_to_string(array_agg(id_test),' ') as id_tests,
            array_to_string(array_agg(concat((id_test),'-',session_test."group",'_',(iteration))), ' ') as groups
    from public.session_test
            where date_diff between _date_diff - 31 and _date_diff
                  and iteration >= 0
     group by id_session,
              session_test.date_diff;


create temporary table temp_sessions as
select *
from (
    select _country_id                                as country_id,
           s.cookie_label,
           s.id,
           s.date_diff,
           s.start_date,
           s.first_visit_date_diff                    as first_visit_date_diff,
           st.groups                                  as groups,
           case when s.flags & 16 = 16 then 1 else 0  end   as is_mobile,
           case when s.ip_cc = lower(substring(current_database(), 5, 2)) or s.ip_cc = 'gb' and lower(substring(current_database(), 5, 2)) = 'uk'
           then 1 else 0 end                as is_local,
           coalesce(s.session_create_page_type, 0) as session_create_page_type,
           s.id_traf_source                        as id_traffic_source,
           s.id_current_traf_source                as id_current_traffic_source,
           row_number() over(partition by s.cookie_label, s.date_diff  order by s.start_date asc) AS rn
    from public.session as s
    left join temp_session_test_agg st  on st.date_diff = s.date_diff and s.id = st.id_session
    where s.flags & 1 = 0 and s.first_visit_date_diff between _date_diff - 8 and _date_diff - 1) as t
where t.rn = 1 ;

create temporary table session_clicks as
    select  date_diff, 
            id as id_click, 
            id_session
    from public.session_click
    where date_diff between _date_diff_start and _date_diff_end
    union all
    select  date_diff, 
            id as id_click, 
            id_session
    from public.session_click_no_serp
    where date_diff between _date_diff_start and _date_diff_end
    ;

create temporary table temp_activation as
    --first session click
select fs.*
from (
    select
        s.cookie_label,
        s.id,
        s.start_date,
        s.date_diff,
        s.first_visit_date_diff,
        row_number() over(partition by s.cookie_label order by s.start_date asc) AS rn
    from temp_sessions s) as fs
inner join session_clicks sc on fs.date_diff = sc.date_diff and fs.id = sc.id_session
where fs.rn = 1;

create temporary table temp_retention as
select sc.*
from (
    select s.cookie_label,
           s.id,
           sc.date,
           s.first_visit_date_diff
    from temp_sessions s
    inner join session_clicks sc  on s.date_diff = sc.date_diff and s.id = sc.id_session
    where sc.date_diff between s.first_visit_date_diff+1 and s.first_visit_date_diff+8 ) as sc
inner join temp_activation as a on a.cookie_label = sc.cookie_label;

select s.country_id,
       s.first_visit_date_diff,
       s.groups,
       s.is_mobile,
       s.is_local,
       s.session_create_page_type,
       s.id_traffic_source,
       s.id_current_traffic_source,
       count(s.id )                            as session_cnt,
       count( distinct s.cookie_label )        as user_cnt,
       count( distinct a.cookie_label)         as activation,
       count( distinct r.cookie_label)         as retention
from temp_sessions as s
left join temp_activation as a on a.first_visit_date_diff = s.first_visit_date_diff and a.cookie_label = s.cookie_label
left join temp_retention as r on r.first_visit_date_diff = s.first_visit_date_diff and r.cookie_label = s.cookie_label
group by s.country_id,
         s.first_visit_date_diff,
         s.groups,
         s.is_mobile,
         s.is_local,
         s.session_create_page_type,
         s.id_traffic_source,
         s.id_current_traffic_source;
