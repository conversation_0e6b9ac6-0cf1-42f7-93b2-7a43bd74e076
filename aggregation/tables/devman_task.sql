select t.id                                    as task_id,
       t.date_create                           as date_created,
       dateadd(day, t.duration, t.date_create) as deadline_date,
       tr.lang                                 as language_name,
       max(tr.date_updated)                    as date_submitted
from dbo.task t
         join dbo.translate tr on tr.id_task = t.id
group by t.id,
         t.date_create,
         tr.lang,
         dateadd(day, t.duration, t.date_create)
