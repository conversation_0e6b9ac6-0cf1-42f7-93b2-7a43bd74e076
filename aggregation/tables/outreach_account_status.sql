Select log2.edited_id_account,
       max(log2.dt_created) as account_delete_date
into #log_delete
FROM dbo.account_log log2 with (nolock)
Where log2.action = 'delete'
Group by log2.edited_id_account
;

Select log_3.edited_id_account,
       log_3.team_change_date     as last_team_change_date,
       log2.val_old,
       log2.val_new
into #log_team
FROM (
       Select acc.edited_id_account,
              max(acc.dt_created) as team_change_date
       FROM dbo.account_log acc with (nolock)
       Where acc.action = 'id_team'
               and acc.val_old is not null
       Group by acc.edited_id_account
                 ) log_3
left join dbo.account_log log2 with (nolock) on log_3.edited_id_account=log2.edited_id_account and log_3.team_change_date= log2.dt_created
;


Select acc.id               as account_id,
       acc.name             as account_name,
       acr.name             as account_role,
       acc.is_deleted       as account_is_deleted,
       acc.dt_created       as account_create_date,
       case when acc.is_deleted='true' then log_delete.account_delete_date end as account_delete_date_log,
       log_team.last_team_change_date      as account_team_change_date_log,
       log_team.val_old     as old_team_id,
       act3.name            as old_team_name,
       log_team.val_new     as new_team_id,
       act4.name            as new_team_name,
       acc.id_region        as account_region_id,
       c.cc                 as account_country,
       pt.name              as account_pay_type,
       acc.id_team          as team_id,
       act.name             as team_name,
       pt2.name             as team_pay_type,
       acc2.name            as team_lead_name

FROM dbo.account acc with (nolock)
left join #log_delete log_delete on log_delete.edited_id_account = acc.id
left join #log_team log_team on log_team.edited_id_account = acc.id
left join dbo.account_team act with (nolock) on acc.id_team = act.id
left join dbo.account_team act3 with (nolock) on log_team.val_old = act3.id
left join dbo.account_team act4 with (nolock) on log_team.val_new = act4.id
left join dbo.account_role acr with (nolock) on acc.id_role = acr.id
left join dbo.country c with (nolock) on c.id = acc.id_region
left join dbo.pay_type pt with (nolock) on acc.pay_type = pt.id
left join dbo.pay_type pt2 with (nolock) on act.pay_type = pt2.id
left join dbo.account acc2 with (nolock) on act.id_lead = acc2.id
where  acc.id_team is not null
;