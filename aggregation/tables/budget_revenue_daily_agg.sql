CREATE OR REPLACE PROCEDURE aggregation.insert_budget_revenue_daily_agg(IN _start_date date)
    LANGUAGE plpgsql
AS
$$

BEGIN

    --===========
    DELETE
    FROM aggregation.budget_revenue_daily_agg
    WHERE action_date = _start_date;
    --===========

    INSERT INTO aggregation.budget_revenue_daily_agg(country_id, action_date, user_id, country_code, project_id,
                                                     site_url,
                                                     is_unlim_cmp, session_cnt, cpc_usd, click_cnt, organic_click_cnt,
                                                     paid_click_cnt, revenue_usd, organic_revenue_usd, paid_revenue_usd,
                                                     potential_revenue_usd, job_cnt, paid_overflow_cnt,
                                                     user_budget_month_usd, campaign_budget_month_usd,
                                                     user_budget_month,
                                                     campaign_budget_month, revenue_budget_diff,
                                                     potential_revenue_budget_diff,
                                                     test_click_cnt, test_click_revenue_usd, external_coefficient,
                                                     dublicated_click_revenue_usd, id_campaign, campaign,
                                                     is_cmp_with_daily_budget,
                                                     is_user_with_daily_budget, is_paid_affiliate, affiliate_revenue,
                                                     free_clicks, paid_clicks)
    WITH date_range            AS (SELECT MAKE_DATE(CAST(EXTRACT(YEAR FROM CURRENT_DATE - 1) AS int),
                                                    CAST(EXTRACT(MONTH FROM CURRENT_DATE - 1) AS int), 1) AS start_date,
                                          MAKE_DATE(CAST(EXTRACT(YEAR FROM CURRENT_DATE - 1) AS int),
                                                    CAST(EXTRACT(MONTH FROM CURRENT_DATE - 1) AS int),
                                                    CAST(EXTRACT(DAY FROM CURRENT_DATE - 1) AS int))      AS end_date),

         click_data            AS (SELECT acs.country_id                                                              AS country,
                                          acs.id_project,
                                          info_project.name                                                           AS site,
                                          acs.user_id                                                                 AS id_user,
                                          acs.id_campaign,
                                          MAX(campaign.name)                                                          AS campaign,
                                          COALESCE(SUM(revenue_usd) / NULLIF(SUM(jdp_away_count), 0), 0)              AS cpc_usd,
                                          SUM(jdp_away_count)                                                         AS click_cnt,
                                          SUM(CASE WHEN u_traffic_source.is_paid IS NOT TRUE THEN jdp_away_count END) AS organic_click_cnt,
                                          SUM(jdp_away_count) -
                                          SUM(CASE WHEN u_traffic_source.is_paid IS NOT TRUE THEN jdp_away_count END) AS paid_click_cnt,
                                          SUM(revenue_usd)                                                            AS revenue_usd,
                                          SUM(revenue_usd +
                                              revenue_usd / EXTRACT(DAY FROM CURRENT_DATE - 1) *
                                              (MAKE_DATE(
                                                       CAST(EXTRACT(YEAR FROM CURRENT_DATE - 1 + INTERVAL '1 month') AS int),
                                                       CAST(EXTRACT(MONTH FROM CURRENT_DATE - 1 + INTERVAL '1 month') AS int),
                                                       1)
                                                  - CAST(CURRENT_DATE - 1 AS date) -
                                               1))                                                                    AS potential_revenue_usd,
                                          COALESCE(SUM(revenue_usd / NULLIF(jdp_away_count, 0) *
                                                       (CASE WHEN u_traffic_source.is_paid IS NOT TRUE THEN jdp_away_count END)),
                                                   0)                                                                 AS organic_revenue_usd,
                                          COALESCE(SUM(revenue_usd / NULLIF(jdp_away_count, 0) *
                                                       (jdp_away_count -
                                                        (CASE WHEN u_traffic_source.is_paid IS NOT TRUE THEN jdp_away_count END))),
                                                   0)                                                                 AS paid_revenue_usd,
                                          SUM(CASE WHEN is_paid_overflow = 1 THEN jdp_away_count END)                 AS paid_overflow_cnt,
                                          SUM(test_count)                                                             AS test_click_cnt,
                                          SUM(test_revenue_usd)                                                       AS test_click_revenue_usd,
                                          COALESCE(SUM(revenue_usd / NULLIF(jdp_away_count * duplicated_count, 0)),
                                                   0)                                                                 AS dublicated_click_revenue_usd,
                                          SUM(CASE
                                                  WHEN u_traffic_source.channel = 'Affiliate'
                                                      AND u_traffic_source.is_paid IS TRUE THEN jdp_away_count
                                              END)                                                                    AS is_paid_affiliate,
                                          SUM(CASE WHEN u_traffic_source.channel = 'Affiliate' THEN revenue_usd END)  AS affiliate_revenue,

                                          SUM(
                                                  CASE
                                                      WHEN (acs.revenue_usd = 0::numeric::double precision OR
                                                            acs.is_paid = 'free'::text) AND
                                                           acs.job_destination <> 4::double precision
                                                          THEN acs.jdp_away_count
                                                      ELSE 0::double precision
                                                  END)                                                                AS free_clicks,
                                          SUM(
                                                  CASE
                                                      WHEN (acs.revenue_usd > 0::numeric::double precision OR
                                                            acs.is_paid = 'paid'::text) AND
                                                           acs.job_destination <> 4::double precision
                                                          THEN acs.jdp_away_count
                                                      ELSE 0::double precision
                                                  END)                                                                AS paid_clicks
                                   FROM aggregation.click_data_agg acs
                                            LEFT JOIN dimension.u_traffic_source
                                                      ON acs.country_id = u_traffic_source.country
                                                          AND u_traffic_source.id = acs.id_current_traf_source
                                            LEFT JOIN imp.campaign
                                                      ON acs.country_id = campaign.country
                                                          AND campaign.id = acs.id_campaign
                                            LEFT JOIN dimension.info_project
                                                      ON acs.country_id = info_project.country
                                                          AND acs.id_project = info_project.id

                                   WHERE public.fn_get_timestamp_from_date_diff(acs.action_datediff::int)
                                             BETWEEN (SELECT start_date FROM date_range) AND (SELECT end_date FROM date_range)
                                   GROUP BY acs.country_id,
                                            acs.id_project,
                                            info_project.name,
                                            acs.user_id,
                                            acs.id_campaign),

         user_budget_log       AS (SELECT *
                                   FROM (SELECT t1.date,
                                                t1.country_id,
                                                t1.id,
                                                t1.id_user,
                                                t1.budget,
                                                t1.daily_budget,
                                                t1.flags,
                                                au.currency,
                                                ROW_NUMBER() OVER (PARTITION BY country_id, id_user ORDER BY date DESC) AS row_number
                                         FROM auction.user_budget_log t1
                                                  JOIN imp.auction_user au
                                                       ON au.country = t1.country_id
                                                           AND au.id = t1.id_user
                                         WHERE DATE(date) <= (SELECT end_date FROM date_range)) t2
                                   WHERE row_number = 1),

         auction_campaign_log  AS (SELECT *
                                   FROM (SELECT t1.*,
                                                ac.id_project,
                                                ac.is_price_per_job,
                                                ac.currency,
                                                ROW_NUMBER()
                                                OVER (PARTITION BY t1.id_campaign, t1.country_id ORDER BY date DESC) AS row_number
                                         FROM auction.campaign_log t1
                                                  LEFT JOIN imp.auction_campaign ac
                                                            ON ac.country_id = t1.country_id
                                                                AND ac.id = t1.id_campaign
                                         WHERE DATE(date) <= (SELECT end_date FROM date_range)
                                         and ( t1.flags & 8 != 8 or (t1.flags & 8 = 8 and t1.budget is not null))
                                         ) t2
                                   WHERE row_number = 1),

         info_currency_history AS (SELECT *
                                   FROM (SELECT ich.*,
                                                ROW_NUMBER()
                                                OVER (PARTITION BY ich.country, ich.id_currency ORDER BY date DESC) AS row_number
                                         FROM dimension.info_currency_history ich
                                         WHERE date(ich.date) <= (SELECT end_date FROM date_range)) t2
                                   WHERE row_number = 1),

         budgets               AS (SELECT DISTINCT cd.country,
                                                   cd.id_user,
                                                   cd.id_project,
                                                   cd.id_campaign,
                                                   COALESCE(ubl.budget * ic.value_to_usd, 0) AS user_budget_month_usd,
                                                   COALESCE(ac.budget * ic1.value_to_usd, 0) AS campaign_budget_month_usd,
                                                   COALESCE(ubl.budget, 0)                   AS user_budget_month,
                                                   COALESCE(ac.budget, 0)                    AS campaign_budget_month,
                                                   CASE
                                                       WHEN (ac.budget = 0 AND ac.click_price > 0)
                                                           OR
                                                            (ac.budget = 0 AND ac.campaign_status = 0 AND ac.is_price_per_job = TRUE)
                                                           THEN 1
                                                       ELSE 0
                                                   END                                       AS is_unlim_cmp,
                                                   CASE
                                                       WHEN ac.daily_budget > 0 AND ac.flags & 256 = 256 AND
                                                            ac.daily_budget IS NOT NULL THEN 1
                                                       ELSE 0
                                                   END                                       AS is_cmp_with_daily_budget,
                                                   CASE
                                                       WHEN ubl.daily_budget > 0 AND ubl.flags & 16384 = 16384 AND
                                                            ubl.daily_budget IS NOT NULL THEN 1
                                                       ELSE 0
                                                   END                                       AS is_user_with_daily_budget
                                   FROM click_data cd
                                            LEFT JOIN user_budget_log ubl
                                                      ON ubl.country_id = cd.country
                                                          AND ubl.id_user = cd.id_user
                                            LEFT JOIN info_currency_history ic
                                                      ON ubl.country_id = ic.country
                                                          AND ubl.currency = ic.id_currency
                                            LEFT JOIN auction_campaign_log ac
                                                      ON ac.country_id = cd.country
                                                          AND ac.id_campaign = cd.id_campaign
                                            LEFT JOIN info_currency_history ic1
                                                      ON ac.country_id = ic1.country
                                                          AND ac.currency = ic1.id_currency),
         coefficient           AS (SELECT campaign.country_id,
                                          campaign.id_project,
                                          AVG(campaign.conversion_rate) AS external_coefficient
                                   FROM auction_campaign_log campaign
                                   WHERE campaign.conversion_rate IS NOT NULL
                                     AND campaign.campaign_status = 0
                                   GROUP BY campaign.id_project, campaign.country_id)

    SELECT cd.country                                                   AS country_id,
           CURRENT_DATE - 1                                             AS action_date,
           cd.id_user,
           countries.alpha_2                                            AS country,
           cd.id_project,
           cd.site,
           MAX(b.is_unlim_cmp)                                          AS is_unlim_cmp,
           NULL                                                         AS session_cnt,
           COALESCE(SUM(cd.revenue_usd) / NULLIF(SUM(click_cnt), 0), 0) AS cpc_usd,
           SUM(click_cnt)                                               AS click_cnt,
           SUM(organic_click_cnt)                                       AS organic_click_cnt,
           SUM(paid_click_cnt)                                          AS paid_click_cnt,
           SUM(revenue_usd)                                             AS revenue_usd,
           SUM(organic_revenue_usd)                                     AS organic_revenue_usd,
           SUM(paid_revenue_usd)                                        AS paid_revenue_usd,
           SUM(potential_revenue_usd)                                   AS potential_revenue_usd,
           NULL                                                         AS job_cnt,
           SUM(paid_overflow_cnt)                                       AS paid_overflow_cnt,
           MAX(user_budget_month_usd)                                   AS user_budget_month_usd,
           SUM(campaign_budget_month_usd)                               AS campaign_budget_month_usd,
           MAX(user_budget_month)                                       AS user_budget_month,
           SUM(campaign_budget_month)                                   AS campaign_budget_month,
           CASE
               WHEN
                   MAX(user_budget_month_usd) > 0
                   THEN MAX(user_budget_month_usd) - SUM(revenue_usd)
               WHEN
                   MAX(user_budget_month_usd) = 0 AND SUM(campaign_budget_month_usd) > 0
                   THEN SUM(campaign_budget_month_usd) - SUM(revenue_usd)
               ELSE 0
           END                                                          AS revenue_budget_diff,
           CASE
               WHEN
                   MAX(user_budget_month_usd) > 0
                   THEN MAX(user_budget_month_usd) - SUM(potential_revenue_usd)
               WHEN
                   MAX(user_budget_month_usd) = 0 AND SUM(campaign_budget_month_usd) > 0
                   THEN SUM(campaign_budget_month_usd) - SUM(potential_revenue_usd)
               ELSE 0
           END                                                          AS potential_revenue_budget_diff,
           SUM(test_click_cnt)                                          AS test_click_cnt,
           SUM(test_click_revenue_usd)                                  AS test_click_revenue_usd,
           c.external_coefficient,
           SUM(dublicated_click_revenue_usd)                            AS dublicated_click_revenue_usd,
           cd.id_campaign,
           cd.campaign,
           MAX(b.is_cmp_with_daily_budget)                              AS is_cmp_with_daily_budget,
           MAX(b.is_user_with_daily_budget)                             AS is_user_with_daily_budget,
           SUM(is_paid_affiliate)                                       AS is_paid_affiliate,
           SUM(affiliate_revenue)                                       AS affiliate_revenue,
           SUM(free_clicks)                                             AS free_clicks,
           SUM(paid_clicks)                                             AS paid_cicks
    FROM click_data cd
             LEFT JOIN budgets b
                       ON b.country = cd.country AND
                          b.id_project = cd.id_project AND b.id_campaign = cd.id_campaign
                           AND b.id_user = cd.id_user
             LEFT JOIN coefficient c
                       ON cd.country = c.country_id AND
                          cd.id_project = c.id_project
             LEFT JOIN dimension.countries
                       ON cd.country = countries.id
    GROUP BY cd.id_user,
             cd.country,
             cd.id_project,
             cd.site,
             c.external_coefficient,
             countries.alpha_2,
             cd.id_campaign,
             cd.campaign;

END
$$;
