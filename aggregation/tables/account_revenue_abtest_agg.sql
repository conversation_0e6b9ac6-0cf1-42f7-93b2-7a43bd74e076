    create temp table temp_session_test_agg as
    select id_session,
           session_test.date_diff,
           array_to_string(array_agg(id_test), ' ')                                                        as id_tests,
           array_to_string(array_agg(concat((id_test), '-', session_test."group", '_', (iteration))), ' ') as groups
    from public.session_test
    where date_diff between _dd_start - 31 and _dd_start
      and iteration >= 0
    group by id_session,
             session_test.date_diff;


    create temp table temp_test_sessions as
    select st.groups   as groups,
           s.id        as id_session,
           s.date_diff as date_diff,
              case
                   when s.flags & 64 = 64 then 2 /*mobile app*/
                   when s.flags & 16 = 16 then 1 /*mobile web*/
                   else 0 /*desktop web*/
                   end                                 as device_type_id,
           s.start_date
    from public.session s
             left join temp_session_test_agg st on st.date_diff = s.date_diff and s.id = st.id_session
    where s.flags & 1 = 0
      and s.date_diff between _dd_start - 31 and _dd_start;


    create temp table temp_session_acc_info as
    select s.groups,
           s.date_diff,
           s.device_type_id,
           sac.id_account,
           account_info.id_traf_src,
           row_number() over (partition by new_acc.id order by s.start_date - new_acc.date_add) as new_account_rank,
           new_acc.flags,
           new_acc.source
    from temp_test_sessions s
             join public.session_account sac on sac.date_diff = s.date_diff and sac.id_session = s.id_session
             join link_dbo.account new_acc on s.date_diff = extract(day from new_acc.date_add - date('1900-01-01'))
                                                  and new_acc.id = sac.id_account and s.start_date <= new_acc.date_add
             join link_dbo.account_contact on account_contact.id_account = new_acc.id
             join link_dbo.account_info on new_acc.id = account_info.id_account
    ;


    create temp table temp_session_acc_agg as
    select temp_session_acc_info.groups,
           account_revenue.date_diff,
           temp_session_acc_info.flags,
           temp_session_acc_info.source,
           temp_session_acc_info.id_traf_src,
           temp_session_acc_info.device_type_id,
           sum(account_revenue.total_revenue) as new_account_revenue,
           sum(account_revenue.serp_revenue)  as new_serp_account_revenue,
           sum(account_revenue.email_revenue) as new_email_account_revenue
    from temp_session_acc_info
             join an.account_revenue on temp_session_acc_info.id_account = account_revenue.id_account
    where account_revenue.date_diff = _dd_start
      and new_account_rank = 1
    group by temp_session_acc_info.groups,
             account_revenue.date_diff,
             temp_session_acc_info.flags,
             temp_session_acc_info.source,
             temp_session_acc_info.id_traf_src,
             temp_session_acc_info.device_type_id
    ;


    --truncate an.rpl_account_revenue_abtest_agg;
    delete from an.rpl_account_revenue_abtest_agg
    where revenue_datediff = _dd_start;


    insert into an.rpl_account_revenue_abtest_agg(country_id, groups, revenue_datediff,device_type_id, new_account_revenue,
                                                  new_serp_account_revenue, new_email_account_revenue, account_flags,
                                                  new_account, source, id_traf_src)
    Select _country_id,
           sag.groups,
           sag.date_diff,
           sag.device_type_id,
           sag.new_account_revenue,
           sag.new_serp_account_revenue,
           sag.new_email_account_revenue,
           sag.flags as account_flags,
           null      as new_account,
           sag.source,
           id_traf_src
    from temp_session_acc_agg sag
    union all
    Select _country_id,
           sai.groups,
           sai.date_diff,
           sai.device_type_id,
           null,
           null,
           null,
           sai.flags                      as account_flags,
           count(distinct sai.id_account) as new_account,
           sai.source,
           id_traf_src
    from temp_session_acc_info sai
    where sai.date_diff = _dd_start
      and sai.new_account_rank = 1
    group by sai.groups,
             sai.date_diff,
             sai.flags,
             sai.source,
             id_traf_src,
             sai.device_type_id
             ;


    drop table temp_session_test_agg;
    drop table temp_test_sessions;
    drop table temp_session_acc_info;
    drop table temp_session_acc_agg;
