SET NOCOUNT ON;

DECLARE @date_start int = :to_sqlcode_date_or_datediff_start;

SELECT DATEADD(DAY, SJ.date_diff, '1900-01-01')                                                       AS session_date,
       SJ.job_id_project                                                                              AS project_id,
       info_project.name                                                                              AS project_name,
       ISNULL(campaign.name, campaign_2.name)                                                         AS campaign_name,
       ISNULL(sc.id_campaign, scns.id_campaign)                                                       AS campaign_id,
       session.id_current_traf_source,
       session.session_create_page_type,
       u_traffic_source.name                                                                          AS traffic_name,
       u_traffic_source.is_paid                                                                       AS traffic_is_paid,
       u_traffic_source.channel,
       ISNULL(SC.uid_job, scns.uid_job)                                                               AS uid_job,
       ISNULL(jr.id_job, jr2.id_job)                                                                  AS job_id,
       ISNULL(j.title, j2.title)                                                                      AS job_title,
       ISNULL(jr.id_region, jr2.id_region)                                                            AS region_id,
       ISNULL(ir.name, ir2.name)                                                                      AS region_name,
       ISNULL(ir.is_city, ir2.is_city)                                                                AS is_city,
       session.ip_cc,
       CASE
           WHEN session.flags & 16 = 16 THEN 1
           WHEN session.flags & 64 = 64 OR session.flags & 128 = 128 THEN 2
           ELSE 0 END                                                                                 AS is_mobile,
       CASE
           WHEN session.flags & 2 = 2 THEN 1
           ELSE 0 END                                                                                 AS is_returned,
       ISNULL(info_currency.name, ic.name)                                                            AS currency_name,
       ISNULL(sc.click_price, scns.click_price) * ISNULL(info_currency.value_to_usd, ic.value_to_usd) AS cpc_usd,
       ISNULL(sc.click_price, scns.click_price)                                                       AS cpc_origin,
       ISNULL(conversion_apply.conversion_rate, 0)                                                    AS convertions_to_apply,
       COUNT(DISTINCT SJ.id)                                                                          AS jdp_cnt,
       COUNT(DISTINCT SA.id)                                                                          AS apply_cnt

FROM dbo.session_jdp SJ WITH (NOLOCK)
         LEFT JOIN dbo.session_jdp_action SJA WITH (NOLOCK)
                   ON SJ.date_diff = SJA.date_diff
                       AND SJ.id = SJA.id_jdp
         LEFT JOIN dbo.session_apply SA WITH (NOLOCK)
                   ON SA.date_diff = SJA.date_diff
                       AND SA.id_src_jdp_action = SJA.id
         JOIN dbo.session session WITH (NOLOCK)
              ON SJ.date_diff = session.date_diff
                  AND SJ.id_session = session.id
         LEFT JOIN dbo.session_click SC WITH (NOLOCK)
                   ON SJ.date_diff = SC.date_diff
                       AND SJ.id_click = SC.id
         LEFT JOIN dbo.session_click_no_serp scns (NOLOCK)
                   ON sj.date_diff = scns.date_diff
                       AND sj.id_click_no_serp = scns.id
         LEFT JOIN dbo.u_traffic_source WITH (NOLOCK)
                   ON session.id_current_traf_source = u_traffic_source.id
         LEFT JOIN dbo.info_project WITH (NOLOCK)
                   ON SJ.job_id_project = info_project.id
         LEFT JOIN auction.campaign WITH (NOLOCK)
                   ON SC.id_campaign = campaign.id
         LEFT JOIN auction.campaign campaign_2 WITH (NOLOCK)
                   ON scns.id_campaign = campaign_2.id
         LEFT JOIN dbo.info_currency WITH (NOLOCK)
                   ON SC.id_currency = info_currency.id
         LEFT JOIN dbo.info_currency ic WITH (NOLOCK)
                   ON scns.id_currency = ic.id
         LEFT JOIN dbo.job_region jr WITH (NOLOCK)
                   ON jr.uid = SC.uid_job
         LEFT JOIN dbo.info_region ir WITH (NOLOCK)
                   ON jr.id_region = ir.id
         LEFT JOIN dbo.job j WITH (NOLOCK)
                   ON jr.id_job = j.id
         LEFT JOIN dbo.job_region jr2 WITH (NOLOCK)
                   ON jr2.uid = scns.uid_job
         LEFT JOIN dbo.info_region ir2 WITH (NOLOCK)
                   ON jr2.id_region = ir2.id
         LEFT JOIN dbo.job j2 WITH (NOLOCK)
                   ON jr2.id_job = j2.id
         CROSS APPLY (SELECT TOP 1 acl.conversion_rate
                      FROM auction.campaign_log acl
                      WHERE date <= SJ.date
                        AND acl.id_campaign = ISNULL(sc.id_campaign, scns.id_campaign)
                      ORDER BY date DESC) conversion_apply

WHERE SJ.date_diff = @date_start
  AND session.flags & 1 != 1 /* remove bot sessions */
  AND sj.flags & 4 = 4 /* easy apply form is available */
  AND SJ.job_id_project = 16902 /* appcast only */
  AND (u_traffic_source.channel = 'Paid Search' OR u_traffic_source.channel = 'Paid Innovation Search')

GROUP BY DATEADD(DAY, SJ.date_diff, '1900-01-01'),
         SJ.job_id_project,
         info_project.name,
         ISNULL(campaign.name, campaign_2.name),
         ISNULL(sc.id_campaign, scns.id_campaign),
         session.id_current_traf_source,
         session.session_create_page_type,
         u_traffic_source.name,
         u_traffic_source.is_paid,
         u_traffic_source.channel,
         ISNULL(SC.uid_job, scns.uid_job),
         ISNULL(jr.id_job, jr2.id_job),
         ISNULL(j.title, j2.title),
         ISNULL(jr.id_region, jr2.id_region),
         ISNULL(ir.name, ir2.name),
         ISNULL(ir.is_city, ir2.is_city),
         session.ip_cc,
         CASE
             WHEN session.flags & 16 = 16 THEN 1
             WHEN session.flags & 64 = 64 OR session.flags & 128 = 128 THEN 2
             ELSE 0 END,
         CASE WHEN session.flags & 2 = 2 THEN 1 ELSE 0 END,
         ISNULL(info_currency.name, ic.name),
         ISNULL(sc.click_price, scns.click_price) * ISNULL(info_currency.value_to_usd, ic.value_to_usd),
         ISNULL(sc.click_price, scns.click_price),
         ISNULL(conversion_apply.conversion_rate, 0)
;
