-- Step 1: Create a temporary table for the unnested data, filtering by id_test_input
CREATE TEMP TABLE tmp_unnested AS
SELECT id_account,
       country_id,
       action_date,
       account_test_info,
       id_test,
       id_group,
       iteration,
       account_age,
       account_active,
       alert_cnt,
       letter_type,
       id_project,
       metric_name,
       metric_cnt
FROM (SELECT id_account,
             country_id,
             action_date,
             UNNEST(STRING_TO_ARRAY(account_test_num, ','))                                              AS account_test_info,
             SPLIT_PART(UNNEST(STRING_TO_ARRAY(account_test_num, ',')), ':', 1)::INT                     AS id_test,
             SPLIT_PART(SPLIT_PART(UNNEST(STRING_TO_ARRAY(account_test_num, ',')), ':', 2), '_', 1)::INT AS id_group,
             SPLIT_PART(SPLIT_PART(UNNEST(STRING_TO_ARRAY(account_test_num, ',')), ':', 2), '_', 2)::INT AS iteration,
             account_age,
             account_active,
             alert_cnt,
             letter_type,
             id_project,
             metric_name,
             metric_cnt
      FROM an.email_abtest_agg_by_account) AS subquery
WHERE EXISTS
          (SELECT 1
           FROM an.email_account_test_settings ea
           WHERE ea.id_test = subquery.id_test
             AND ea.iteration = subquery.iteration
             AND ea.start_date::date >= CURRENT_DATE - 90
             AND (ea.is_active = 1 OR ea.stop_date::date = CURRENT_DATE - 1)
             AND ea.weights NOT LIKE '0;0;%');

CREATE INDEX idx_tmp_unnested ON tmp_unnested (id_account, id_test, iteration);
ANALYZE tmp_unnested;


-- Step 2: Create a temporary table for max action dates
CREATE TEMP TABLE tmp_account_test_max_action_date AS
SELECT id_account, id_test, iteration, MAX(action_date) AS max_action_date
FROM tmp_unnested
GROUP BY id_account, id_test, iteration;

CREATE INDEX idx_tmp_account_test_max_action_date ON tmp_account_test_max_action_date (id_account, id_test, iteration);
ANALYZE tmp_account_test_max_action_date;


-- Step 3: Create a temporary table for account info with the most recent data
CREATE TEMP TABLE tmp_account_info AS
SELECT unnested.id_account,
       unnested.id_test,
       unnested.iteration,
       MAX(unnested.action_date)    AS action_date,
       MAX(unnested.account_age)    AS account_age,
       MAX(unnested.account_active) AS account_active,
       MAX(unnested.alert_cnt)      AS alert_cnt
FROM tmp_unnested unnested
         INNER JOIN tmp_account_test_max_action_date max_action
                    ON unnested.id_account = max_action.id_account
                        AND unnested.id_test = max_action.id_test
                        AND unnested.iteration = max_action.iteration
                        AND unnested.action_date = max_action.max_action_date
GROUP BY unnested.id_account,
         unnested.id_test,
         unnested.iteration;

ANALYZE tmp_account_info;


-- Step 4: Final query using the temporary tables
SELECT unnested.country_id,
       upper(current_database())                                                                                                AS country_code,
       unnested.account_test_info,
       unnested.id_test,
       unnested.id_group,
       unnested.iteration,
       unnested.id_account,
       unnested.letter_type,
       account_info.account_age,
       account_info.account_active,
       account_info.alert_cnt,
       COALESCE(SUM(unnested.metric_cnt) FILTER (WHERE unnested.metric_name = 'Alertviews'), 0)::integer                        AS alertviews,
       COALESCE(SUM(unnested.metric_cnt) FILTER (WHERE unnested.metric_name = 'Applies'), 0)::integer                           AS applies,
       COALESCE(SUM(unnested.metric_cnt) FILTER (WHERE unnested.metric_name = 'Clicks'), 0)::integer                            AS clicks,
       COALESCE(SUM(unnested.metric_cnt) FILTER (WHERE unnested.metric_name = 'Conversion Aways'),
                0)::integer                                                                                                     AS conversion_aways,
       COALESCE(SUM(unnested.metric_cnt) FILTER (WHERE unnested.metric_name = 'Conversion Revenue'),
                0)::numeric                                                                                                     AS conversion_revenue,
       COALESCE(SUM(unnested.metric_cnt) FILTER (WHERE unnested.metric_name = 'Conversions'), 0)::integer                       AS conversions,
       COALESCE(SUM(unnested.metric_cnt) FILTER (WHERE unnested.metric_name = 'JDP Applies'), 0)::integer                       AS jdp_applies,
       COALESCE(SUM(unnested.metric_cnt) FILTER (WHERE unnested.metric_name = 'JDP Aways'), 0)::integer                         AS jdp_aways,
       COALESCE(SUM(unnested.metric_cnt) FILTER (WHERE unnested.metric_name = 'JDP Revenue'), 0)::numeric                       AS jdp_revenue,
       COALESCE(SUM(unnested.metric_cnt) FILTER (WHERE unnested.metric_name = 'JDPs'), 0)::integer                              AS jdps,
       COALESCE(SUM(unnested.metric_cnt) FILTER (WHERE unnested.metric_name = 'Letter Aways'), 0)::integer                      AS letter_aways,
       COALESCE(SUM(unnested.metric_cnt) FILTER (WHERE unnested.metric_name = 'Letter Revenue'),
                0)::numeric                                                                                                     AS letter_revenue,
       COALESCE(SUM(unnested.metric_cnt) FILTER (WHERE unnested.metric_name = 'Letters Clicked'),
                0)::integer                                                                                                     AS letters_clicked,
       COALESCE(SUM(unnested.metric_cnt) FILTER (WHERE unnested.metric_name = 'Letters Opened'),
                0)::integer                                                                                                     AS letters_opened,
       COALESCE(SUM(unnested.metric_cnt) FILTER (WHERE unnested.metric_name = 'Letters Sent'), 0)::integer                      AS letters_sent,
       COALESCE(SUM(unnested.metric_cnt) FILTER (WHERE unnested.metric_name = 'SERP Aways'), 0)::integer                        AS serp_aways,
       COALESCE(SUM(unnested.metric_cnt) FILTER (WHERE unnested.metric_name = 'SERP Revenue'), 0)::numeric                      AS serp_revenue,
       COALESCE(SUM(unnested.metric_cnt) FILTER (WHERE unnested.metric_name = 'Total Aways'), 0)::integer                       AS total_aways,
       COALESCE(SUM(unnested.metric_cnt) FILTER (WHERE unnested.metric_name = 'Total Revenue'), 0)::numeric                     AS total_revenue,
       COALESCE(SUM(unnested.metric_cnt) FILTER (WHERE unnested.metric_name = 'Unsubscribed Account'), 0)::integer              AS unsubscribed,
       COALESCE(SUM(unnested.metric_cnt) FILTER (WHERE unnested.metric_name IN ('Letter Revenue', 'SERP Revenue')), 0)::numeric AS email_revenue
FROM tmp_unnested unnested
         INNER JOIN tmp_account_info account_info
                    ON unnested.id_account = account_info.id_account
                        AND unnested.id_test = account_info.id_test
                        AND unnested.iteration = account_info.iteration
GROUP BY unnested.country_id,
         unnested.account_test_info,
         unnested.id_test,
         unnested.id_group,
         unnested.iteration,
         unnested.id_account,
         unnested.letter_type,
         account_info.account_age,
         account_info.account_active,
         account_info.alert_cnt;
