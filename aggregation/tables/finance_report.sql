create or replace procedure aggregation.insert_finance_report(_datediff integer)
    language plpgsql
as
$$

declare
    _date_diff_minus_thirty_days bigint := fn_get_date_diff(current_date - 30)::bigint;
    _date_diff_yesterday bigint := fn_get_date_diff(current_date - 1)::bigint;
begin

    -- create temp table to speed up
    create temp table tmp_click_data_agg as
    select action_datediff, revenue_usd, test_revenue_usd, country_id, id_project, user_id, jdp_away_count,
           (fn_get_date_from_date_diff(cda.action_datediff::int))::date as date
    from aggregation.click_data_agg cda
    where action_datediff between _date_diff_minus_thirty_days and _date_diff_yesterday;

    create index idx_tmp_click_data_agg on tmp_click_data_agg(country_id, id_project, user_id);
    analyse tmp_click_data_agg;


    -- delete old data
    delete from aggregation.finance_report
    where date between current_date - 30 and current_date - 1;


    -- insert new data
    insert into aggregation.finance_report(date, year_date, quarter_date, month_date, week_date, country, country_code,
                                           revenue_type, revenue_usd, test_revenue_usd, adv_impression, adv_view, adv_click,
                                           project_site, manager_name, cost_type, ga_channel, cost_usd, ga_users, ga_session,
                                           project_site_budget_usd, affiliate_partner)
    With ea_sales_usd as (
        SELECT  CAST(employer_balance_history.date_created AS date)     AS date,
                countries.name_country_eng                              AS country,
                countries.alpha_2 as country_code,
                ((-employer_balance_history.value-employer_balance_history.bonuses_spent) / currency_source.to_usd) /
                (employer_balance.vat / 100 + 1)                        AS value_usd,
                employer_balance_history.change_type,
                -employer_balance_history.value-employer_balance_history.bonuses_spent  AS in_local_currency,
                info_currency.iso_code                                  as currency,
                employer_balance.vat,
                (-employer_balance_history.value-employer_balance_history.bonuses_spent) / currency_source.to_usd AS in_usd_with_vat,
                employer_balance_history.sources                        as server,
                employer_balance_history.id_employer
        FROM imp_employer.employer_balance_history
                    LEFT OUTER JOIN
                imp_employer.employer_balance
                ON employer_balance.sources = employer_balance_history.sources
                    AND employer_balance.id_employer = employer_balance_history.id_employer
                    LEFT OUTER JOIN
                imp_employer.info_currency
                on employer_balance.sources = info_currency.sources
                    and employer_balance.id_currency = info_currency.id
                    left join imp_statistic.currency_source
                            ON currency_source.date = CAST(employer_balance_history.date_created AS date)
                                AND currency_source.currency = info_currency.iso_code

                    LEFT OUTER JOIN
                imp_employer.employer ON employer.id = employer_balance_history.id_employer
                    AND employer.sources = employer_balance_history.sources
                    left join dimension.countries
                            on lower(employer.country_code) = lower(countries.alpha_2)
        where employer_balance_history.change_type = 0 and CAST(employer_balance_history.date_created AS date) between current_date - 30 and current_date - 1
        ),


            Revenue as (
                SELECT create_date                                   as date,
                    coalesce(countries.name_country_eng, 'Other') as country,
                    coalesce(countries.alpha_2, 'Other')          as country_code,
                    'AdSense for Search'                          as Revenue_type,
                    estimated_income_usd                          as revenue,
                    0                                             as total_test_value,
                    show                                          as impressions,
                    views_page                                    as views,
                    click                                         as clicks,
                    null                                          as manager_name,
                    null                                          as project_site,
                    0                                             as user_budget_usd
                FROM (
                         select
                               create_date,
                               country,
                               estimated_income_usd,
                               show,
                               views_page,
                               click
                         from
                         imp_statistic.adsense_afs
                    where create_date <  '2022-06-01'
                    union all
                     select
                              adsense_afs.action_date,
                              adsense_afs.country_name,
                              adsense_afs.estimated_earnings_usd,
                              adsense_afs.impressions ,
                              adsense_afs.page_views,
                              adsense_afs.clicks
                         from
                         imp_api.adsense_afs
                    where action_date >=  '2022-06-01'
                         )adsense_afs
                        left join dimension.info_country_adsense
                                on adsense_afs.country = info_country_adsense.country_report_name
                        left join
                    dimension.countries
                    on lower(info_country_adsense.country_code) = lower(countries.alpha_2)
                    where create_date between current_date - 30 and current_date - 1
                union all
                Select create_date,
                    case
                        when site_address = 'jooble.org' then 'United States'
                        else coalesce(countries.name_country_eng, 'Other') end,
                    case
                        when site_address = 'jooble.org' then 'US'
                        else coalesce(countries.alpha_2, 'Other') end,
                    'AdSense for Content',
                    estimated_income_usd,
                    0 as total_test_value,
                    show      as impressions,
                    view_page as views,
                    click     as clicks,
                    null      as manager_name,
                    null      as project_site,
                    0         as user_budget_usd
                from
                     (
                                     SELECT adsense_afc_1.create_date,
                                            adsense_afc_1.estimated_income_usd,
                                            adsense_afc_1.site_address,
                                            show,
                                            view_page,
                                            click
                                     FROM imp_statistic.adsense_afc adsense_afc_1
                                     WHERE adsense_afc_1.create_date < '2022-06-01'::date
                                     UNION
                                     SELECT adsense_afc_1.action_date,
                                            adsense_afc_1.estimated_earnings_usd,
                                            adsense_afc_1.domain_code,
                                            adsense_afc_1.impressions ,
                                            adsense_afc_1.page_views ,
                                            adsense_afc_1.clicks
                                     FROM imp_api.adsense_afc adsense_afc_1
                                     WHERE adsense_afc_1.action_date >= '2022-06-01'::date
                         )
                     adsense_afc
                        left join dimension.countries
                                on left(adsense_afc.site_address, 2) = lower(countries.alpha_2)
                 where create_date between current_date - 30 and current_date - 1
                union all

                -- changed by yiv
                select
                    acsa.date as date,
                    name_country_eng,
                    countries.alpha_2,
                    'Auction',
                    sum(acsa.revenue_usd) as total_value,
                    sum(acsa.test_revenue_usd) as total_test_value,
                    0                                          as impressions,
                    0                                          as views,
                    sum(jdp_away_count)                        as clicks,
                    crm_manageranddomain.manager_name          as manager_name,
                    info_project.name                          as project_site,
                    max(budget_and_revenue.budget)           as user_budget_usd
                from tmp_click_data_agg acsa
                        left join dimension.countries on acsa.country_id = countries.id
                        left join dimension.info_project on acsa.country_id = info_project.country
                                                                and acsa.id_project = info_project.id
                        left join   (
                                    Select
                                        trim(crm_manageranddomain.domain_name) as domain_name,
                                        max(manager_name) as manager_name
                                    from imp_statistic.crm_manageranddomain
                                        group by trim(crm_manageranddomain.domain_name)
                                     ) crm_manageranddomain
                                on info_project.name = trim(crm_manageranddomain.domain_name)
                        left join 
                                 (
                                     Select 
                                            v_budget_and_revenue.country,
                                            v_budget_and_revenue.id_user,
                                            v_budget_and_revenue.date,
                                            sum(v_budget_and_revenue.budget) as budget
                                     from aggregation.v_budget_and_revenue
                                     where v_budget_and_revenue.date between current_date - 30 and current_date - 1
                                     group by 
                                            v_budget_and_revenue.country,
                                            v_budget_and_revenue.id_user,
                                            v_budget_and_revenue.date
                                 ) budget_and_revenue
                                on countries.alpha_2 = budget_and_revenue.country
                                    and acsa.date::date = budget_and_revenue.date
                                    and acsa.user_id =  budget_and_revenue.id_user
                 where acsa.date::date between current_date - 30 and current_date - 1
                 group by
                 acsa.date,
                 name_country_eng,
                 countries.alpha_2,
                 crm_manageranddomain.manager_name,
                 info_project.name
                -- /changed by yiv

                union all
                Select date,
                    country,
                    country_code,
                    'Employer Account' as revenue_type,
                    sum(value_usd)     as revenue,
                    0                  as total_test_value,
                    0                  as impressions,
                    0                  as views,
                    0                  as clicks,
                    null               as manager_name,
                    null               as project_site,
                    0                  as user_budget_usd
                from ea_sales_usd

                group by date,
                        country,
                        country_code
                union all
                SELECT fn_get_timestamp_from_date_diff(jcoin_model_revenue_pnl.revenue_datediff)     as date,
                       case when e.country_code = 'ua' then 'Ukraine'
                            when e.country_code = 'hu' then 'Hungary'
                            else '-'
                       end,
                       --'UA',
                       upper(e.country_code),
                       'DTE 2',
                       sum(coalesce(revenue_without_vat_uah, 0) *
                           coalesce(info_currency_history.value_to_usd, info_currency.value_to_usd)) as revenue,
                       0                                                                             as total_test_value,
                       0                                                                             as impressions,
                       0                                                                             as views,
                       0                                                                             as clicks,
                       null                                                                          as manager_name,
                       null                                                                          as project_site,
                       0                                                                             as user_budget_usd
                FROM employer.jcoin_model_revenue_pnl
                left join dimension.info_currency_history on cast(jcoin_model_revenue_pnl.payment_datetime as date) = cast(info_currency_history.date as date)
                                                          and info_currency_history.country = 1 and info_currency_history.id_currency = 0
                left join dimension.info_currency on info_currency.country = 1 and info_currency.id = 0
                left join imp_employer.employer e on e.sources = jcoin_model_revenue_pnl.database_source_id and e.id = jcoin_model_revenue_pnl.employer_id
                where fn_get_timestamp_from_date_diff(jcoin_model_revenue_pnl.revenue_datediff) < current_date
                  and fn_get_timestamp_from_date_diff(jcoin_model_revenue_pnl.revenue_datediff) between current_date - 30 and current_date - 1
                group by fn_get_timestamp_from_date_diff(jcoin_model_revenue_pnl.revenue_datediff), e.country_code
            ),
            Cost as (
                SELECT cost.date,
                       cost.country_name                                          as country,
                       upper(cost.country_cc)                                     as country_code,
                       null                                                       as revenue_type,
                       CASE WHEN cost.supplier = 'Google' THEN 'AdWords'
                            WHEN cost.supplier = 'Meta' THEN 'FaceBook'
                            WHEN cost.supplier = 'Microsoft' THEN 'Bing' ELSE 'NA' END as cost_type,
                       0                                                          as revenue,
                       sum(cost.impressions)                                      as impressions,
                       0                                                          as views,
                       sum(cost.clicks)                                           as clicks,
                       sum(cost.cost_usd)                                         as cost,
                       null                                                       as affiliate_partner
                FROM aggregation.v_paid_cost_finance cost
                WHERE cost.date between current_date - 30 and current_date - 1
                GROUP BY cost.date,
                       cost.country_name,
                       upper(cost.country_cc),
                       CASE WHEN cost.supplier = 'Google' THEN 'AdWords'
                            WHEN cost.supplier = 'Meta' THEN 'FaceBook'
                            WHEN cost.supplier = 'Microsoft' THEN 'Bing' ELSE 'NA' END
                union all
                select
                    v_main_metrics_agg.click_date                                                             as date,
                    countries.name_country_eng,
                    v_main_metrics_agg.country                                                                as country_code,
                    null                                                                                      as revenue_type,
                    case when coalesce(lower(ip.name), '') like 'j-vers.%' then 'J-Vers' else 'Affiliate' end as cost_type,
                    0                                                                                         as revenue,
                    0                                                                                         as impressions,
                    0                                                                                         as views,
                    sum(v_main_metrics_agg.certified_click_cnt)                                               as clicks,
                    sum(v_main_metrics_agg.certified_cost_usd)                                                as cost,
                    v_main_metrics_agg.publisher                                                              as affiliate_partner
                from
                    affiliate.v_main_metrics_agg
                    left join dimension.countries
                              on v_main_metrics_agg.id_country = countries.id
                    left join dimension.info_project ip
                              on ip.country = v_main_metrics_agg.id_country and ip.id = v_main_metrics_agg.id_project
                where
                    v_main_metrics_agg.click_date between current_date - 30 and current_date - 1
                group by
                    v_main_metrics_agg.click_date,
                    v_main_metrics_agg.country,
                    countries.name_country_eng,
                    v_main_metrics_agg.publisher,
                    case when coalesce(lower(ip.name), '') like 'j-vers.%' then 'J-Vers' else 'Affiliate' end
            ),

            Google_Analytics as
                (
                    Select date,
                        countries.name_country_eng as country,
                        countries.alpha_2          as country_code,
                        channel,
                        users,
                        sessions

                    from (
                            SELECT date,
                                    channelgrouping as channel,
                                    case
                                        when name = 'https://jooble.org' then 'US'
                                        else
                                            upper(replace(replace(replace(replace(replace(name, 'https://', ''), '.jooble.org', ''),
                                                                    '.jooble.com', ''), 'http://', ''),'www.',''))  end as country,
                                    sum(users)                                                            As users,
                                    sum(sessions)                                                         as sessions
                            FROM imp_statistic.google_analytics_general
                            where table_num = 6 and date < '2022-06-27'
                            group by date,
                                    case
                                        when name = 'https://jooble.org' then 'US'
                                        else
                                            upper(replace(replace(replace(replace(replace(name, 'https://', ''), '.jooble.org', ''),
                                                                    '.jooble.com', ''), 'http://', ''),'www.','')) end,
                                    channelgrouping
                              union all
                                    SELECT
                                        ga_general.action_date       as date,
                                        ga_general.channel_grouping  as channelgrouping,
                                        countries.alpha_2           as country_code,
                                        sum(ga_general.users)       as users,
                                        sum(ga_general.sessions)    as session_cnt
                                    FROM imp_api.ga_general
                                            JOIN dimension.countries ON ga_general.country_id = countries.id
                                    Where  ga_general.action_date  < '2023-07-01'
                                    GROUP BY ga_general.action_date,
                                         ga_general.channel_grouping,
                                         countries.alpha_2
                              union all
                                    SELECT
                                        ga4_general.action_date       as date,
                                        ga4_general.channel_grouping  as channelgrouping,
                                        countries.alpha_2             as country_code,
                                        sum(ga4_general.total_users)  as users,
                                        sum(ga4_general.sessions)     as session_cnt
                                    FROM imp_api.ga4_general
                                            JOIN dimension.countries ON ga4_general.country_id = countries.id
                                    Where ga4_general.action_date  >= '2023-07-01'
                                    GROUP BY ga4_general.action_date,
                                         ga4_general.channel_grouping,
                                         countries.alpha_2
                        ) GA
                            left join dimension.countries
                                    on GA.country = countries.alpha_2
                    where date between current_date - 30 and current_date - 1
                ),
            All_Unions as
                (
                    Select date,
                        country,
                        country_code,
                        Revenue_type as revenue_type,
                        abs(revenue)      as revenue_usd,
                        total_test_value as total_test_value ,
                        impressions as adv_impressions,
                        views       as adv_views,
                        clicks       as adv_clicks,
                        project_site,
                        manager_name,
                        null         as cost_type,
                        0            as cost_usd,
                        null         as channel,
                        0            as ga_users,
                        0            as ga_sessions,
                        user_budget_usd as user_budget_usd,
                        null         as affiliate_partner
                    from Revenue
                    union all
                    SELECT date,
                        country,
                        country_code,
                        null,
                        0,
                        0,
                        impressions,
                        views,
                        clicks,
                        null,
                        null,
                        cost_type,
                        cost as cost_usd,
                        null as channel,
                        0    as ga_users,
                        0    as ga_sessions,
                        0    as user_budget_usd,
                        affiliate_partner
                    from Cost
                    union all
                    Select date,
                        country,
                        country_code,
                        null,
                        0,
                        0,
                        0,
                        0,
                        0,
                        null,
                        null,
                        null,
                        0,
                        channel,
                        users    as ga_users,
                        sessions as ga_sessions,
                        0        as user_budget_usd,
                        null     as affiliate_partner
                    from Google_Analytics
                )
        SELECT
            date,
            extract(year from date) as year_date,
            extract(quarter from date) as quarter_date,
            extract(month from date) as month_date,
            extract(week from date) as week_date,
            country,
            country_code,
            revenue_type,
            sum(revenue_usd)     as revenue_usd,
            sum(total_test_value) as test_revenue_usd,
            sum(adv_impressions) as adv_impression,
            sum(adv_views)       as adv_view,
            sum(adv_clicks)      as adv_click,
            project_site,
            manager_name,
            cost_type,
            channel              as ga_channel,
            sum(cost_usd)        as cost_usd,
            sum(ga_users)        as ga_users,
            sum(ga_sessions)     as ga_session,
            max(user_budget_usd) as project_site_budget_usd,
            affiliate_partner
        from All_Unions
        group by date,
                extract(year from date),
                extract(quarter from date),
                extract(month from date),
                extract(week from date),
                country,
                country_code,
                revenue_type,
                project_site,
                manager_name,
                cost_type,
                channel,
                affiliate_partner;



end;

$$;
