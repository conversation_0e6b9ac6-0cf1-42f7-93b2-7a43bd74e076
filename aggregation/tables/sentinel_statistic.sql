drop table if exists temp_info_currency;
create temp table temp_info_currency as
            select *
            from link_dbo.info_currency;

            drop table if exists temp_sa;
            create temp table temp_sa as
            select sa.uid_job,
                   sum(sa.click_price * cast(ic.value_to_usd *1.0 as numeric))                                        as revenue_usd,
                   count(distinct sa.id)                                                        as away_cnt,
                   count(distinct case when sa.date_diff >= cs.conversion_start then sa.id end) as away_conversion_cnt,
                   count(conversion_away_connection.id_session_away)                            as conversion_cnt,
                   cast(sum(sa.click_price * ic.value_to_usd)/count(distinct sa.id)*1.0 as numeric)   as cpc_usd,
                   sum(case when sa.date_diff >= cs.conversion_start then sa.click_price * cast(ic.value_to_usd *1.0 as numeric) end) as conversion_revenue_usd,
            from public.session_away sa
            left join public.session s on sa.date_diff = s.date_diff and sa.id_session = s.id and coalesce(s.flags, 0) & 1 = 0
            left join temp_info_currency ic on ic.id = sa.id_currency
            left join (select sa.id_project,
                                        min(cac.date_diff) as conversion_start
                                from link_auction.conversion_away_connection as cac
                                join session_away as sa on cac.date_diff = sa.date_diff and cac.id_session_away = sa.id
                                group by sa.id_project) as cs on cs.id_project = sa.id_project
            left join (select distinct id_session_away
                        from link_auction.conversion_away_connection) as conversion_away_connection on conversion_away_connection.id_session_away = sa.id
            where sa.date_diff = _dd_start /*потрібна функція для визначення попердньої дати, типу current_date - 1, замість точної дати*/
              and sa.flags & 512 = 0
              and sa.flags & 2 = 0
            group by sa.uid_job;

            create index temp_sa_idx on temp_sa (uid_job);

WITH RECURSIVE sentinel AS (
	SELECT
	   parent.id_parent,
	   parent.id_child,
	   1 as level
	FROM
		an.hashtag_relation parent
	UNION all
		SELECT
			child.id_parent,
	        child.id_child,
		    s.level + 1 AS level
		FROM
			an.hashtag_relation child
		 JOIN sentinel s ON s.id_child = child.id_parent
),
 sentinels as
(
       Select id_parent,
              id_child
       from (
                Select  *,
                           row_number() over (partition by id_child order by level desc, id_parent asc) as top_sentinel
                from sentinel
            )top_sentinel
where top_sentinel = 1
)

            --return query
            select _country_id as country_id,
                   _country_name as country_name,
                   current_date - 1           as date,
                   hashtag.name as parent_sentinel,
                   hashtag.type as parent_sentinel_type,
                   job.id_project,
                   sent.sentinel as sentinel_name,
                   sent.type as sentinel_type,
                   sum(sa.revenue_usd)::numeric            as revenue_usd,
                   count(distinct sent.id_job) as job_cnt,
                   count(distinct sa.uid_job) as job_with_away_cnt,
                   sum(sa.away_cnt)              as away_cnt,
                   sum(sa.away_conversion_cnt)   as away_conversion_cnt,
                   sum(sa.conversion_cnt)        as conversion_cnt,
                   count(distinct case when sa.revenue_usd>0 then sent.id_job end) as job_with_revenue_cnt,
                   PERCENTILE_CONT(0.5) WITHIN GROUP(ORDER BY case when cpc_usd=0 then null else cpc_usd end) as median_cpc_usd,
                   coalesce(sum(case when campaign.click_price = 0 and campaign.is_price_per_job = 1 then coalesce(job_region_billing_info.click_price, 0)* info_currency.value_to_usd else campaign.click_price* info_currency.value_to_usd end) / nullif(sum(case when campaign.click_price > 0 or coalesce(job_region_billing_info.click_price, 0) > 0 then 1 else 0 end),0),0) as avg_cpc_for_paid_job_count_usd,
                   sum(conversion_revenue_usd)  as conversion_revenue_usd,
                   count(distinct case when campaign.click_price > 0 or coalesce(job_region_billing_info.click_price, 0) > 0 then sent.id_job end) as paid_job_count
            from an.job_sentinel as sent
            left join temp_sa sa on sent.id_job = sa.uid_job
            left join link_dbo.job_region
            on sent.id_job = job_region.uid
            left join link_dbo.job
            on job.id = job_region.id_job
            left join sentinels on
            sent.id_sentinel  = sentinels.id_child
            left join an.hashtag
            on sentinels.id_parent = hashtag.id
            left join link_auction.campaign
              on job_region.id_campaign = campaign.id
            left join link_dbo.info_currency
               on info_currency.id = campaign.currency
            left join link_dbo.job_region_billing_info
               on job_region.uid = job_region_billing_info.uid_job
            where (job.date_expired is null or cast(job.date_expired as date) > current_date)
                  and job_region.inactive = 0
                  and (campaign.date_end is null or cast(campaign.date_end as date) > current_date)
                  and campaign.campaign_status = 0
   
            group by hashtag.name ,
                   hashtag.type ,
                   job.id_project,
                   sent.sentinel ,
                   sent.type
