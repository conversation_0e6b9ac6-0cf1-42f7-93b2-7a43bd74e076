SET NOCOUNT ON;

DECLARE @datediff_start int = :to_sqlcode_date_or_datediff_start,
        @country_code varchar(2) = LOWER(SUBSTRING(DB_NAME(), 5, 2));


SELECT id_project,
       MIN(cac.date_diff) AS conversion_start_datediff
INTO #conversion_away_start
FROM auction.conversion_away_connection cac WITH (NOLOCK)
         JOIN dbo.session_away sa WITH (NOLOCK)
              ON cac.date_diff = sa.date_diff
                  AND cac.id_session_away = sa.id
GROUP BY id_project


SELECT DISTINCT sa.date_diff             AS job_view_datediff,
                s.id_current_traf_source,
                CASE
                    WHEN s.ip_cc = @country_code OR s.ip_cc = 'gb' AND @country_code = 'uk'
                        THEN 1
                    ELSE 0
                    END                  AS is_local,
                sa.uid_job,
                sa.id_project,
                1 /*jdp on client site*/ AS view_type,
                CASE
                    WHEN sa.date_diff >= cas.conversion_start_datediff THEN 2 /*away with conversions*/
                    ELSE 1 /*away without conversions*/
                    END                  AS job_type,
                sa.id_session,
                NULL                     AS id_jdp_with_apply_click,
                NULL                     AS id_apply,
                NULL                     AS id_apply_with_questionnaire,
                NULL                     AS id_new_cv,
                NULL                     AS id_jdp_with_questionnaire
INTO #view_job
FROM session_away sa WITH (NOLOCK)
         JOIN dbo.session s WITH (NOLOCK)
              ON s.date_diff = sa.date_diff AND
                 s.id = sa.id_session
         LEFT JOIN #conversion_away_start cas WITH (NOLOCK)
                   ON cas.id_project = sa.id_project
WHERE s.flags & 1 = 0 /*not bots*/
  AND sa.id_jdp IS NULL
  AND sa.date_diff = @datediff_start

UNION ALL


SELECT DISTINCT sj.date_diff                                  AS job_view_datediff,
                s.id_current_traf_source,
                CASE
                    WHEN s.ip_cc = @country_code OR s.ip_cc = 'gb' AND @country_code = 'uk'
                        THEN 1
                    ELSE 0
                    END                                       AS is_local,
                sj.uid_job,
                sj.job_id_project                             AS id_project,
                2 /*jdp on Jooble*/                           AS view_type,
                CASE
                    WHEN sj.flags & 4 = 4 AND sj.flags & 524288 = 524288 THEN 12/*new apply*/
                    WHEN sj.flags & 4 = 4 AND sj.flags & 524288 = 0 THEN 11/*easy apply default*/
                    WHEN sj.date_diff >= cas.conversion_start_datediff THEN 2 /*away with conversions*/
                    ELSE 1 /*away without conversions*/
                    END                                       AS job_type,
                sj.id_session                                 AS id_session,
                sja.id_jdp                                    AS id_jdp_with_apply_click,
                sa.id                                         AS id_apply,
                CASE WHEN sa.flags & 128 = 128 THEN sa.id END AS id_apply_with_questionnaire,
                ac.id                                         AS id_new_cv,
                sqa.id_jdp                                    AS id_jdp_with_questionnaire
FROM session_jdp sj WITH (NOLOCK)
         JOIN dbo.session s WITH (NOLOCK)
              ON s.date_diff = sj.date_diff AND
                 s.id = sj.id_session
         LEFT JOIN #conversion_away_start cas WITH (NOLOCK)
                   ON cas.id_project = sj.job_id_project
         LEFT JOIN session_jdp_action sja WITH (NOLOCK)
                   ON sja.id_jdp = sj.id AND
                      sja.flags & 2 = 2 /*easy apply*/ AND
                      sja.type = 1 /*apply click*/
         LEFT JOIN session_apply sa WITH (NOLOCK)
                   ON sa.id_src_jdp_action = sja.id
         LEFT JOIN account_cv ac WITH (NOLOCK)
                   ON sa.id_cv = ac.id AND
                      sa.date_diff = DATEDIFF(DAY, 0, ac.date_created)
         LEFT JOIN session_questionnaire_action sqa WITH (NOLOCK)
                   ON sqa.date_diff = sj.date_diff AND
                      sqa.id_jdp = sj.id
WHERE s.flags & 1 = 0 /*not bots*/
  AND sj.date_diff = @datediff_start
;

SELECT job_view_datediff,
       uid_job,
       id_project,
       view_type,
       job_type,
       id_current_traf_source,
       is_local,
       COUNT(DISTINCT id_session)                  AS job_view_cnt,
       COUNT(DISTINCT id_jdp_with_apply_click)     AS apply_click_cnt,
       COUNT(DISTINCT id_jdp_with_questionnaire)   AS questionnaire_start_cnt,
       COUNT(DISTINCT id_apply)                    AS apply_cnt,
       COUNT(DISTINCT id_apply_with_questionnaire) AS apply_with_questionnaire_cnt
INTO #view_job_agg
FROM #view_job
GROUP BY job_view_datediff,
         uid_job,
         id_project,
         view_type,
         job_type,
         id_current_traf_source,
         is_local

SELECT job_view_datediff,
       id_project,
       view_type,
       job_type,
       id_current_traf_source,
       is_local,
       SUM(job_view_cnt)                 AS job_view_cnt,
       SUM(apply_cnt)                    AS apply_cnt,
       SUM(apply_with_questionnaire_cnt) AS apply_with_questionnaire_cnt,
       SUM(apply_click_cnt)              AS apply_click_cnt,
       SUM(questionnaire_start_cnt)      AS questionnaire_start_cnt
INTO #view_and_apply
FROM #view_job_agg
GROUP BY job_view_datediff,
         id_project,
         view_type,
         job_type,
         id_current_traf_source,
         is_local;


SELECT job_view_datediff,
       id_project,
       view_type,
       job_type,
       id_current_traf_source,
       is_local,
       COUNT(DISTINCT id_new_cv) AS new_cv_cnt
INTO #cv
FROM #view_job
GROUP BY job_view_datediff,
         id_project,
         view_type,
         job_type,
         id_current_traf_source,
         is_local;

SELECT vaa.job_view_datediff,
       vaa.id_project,
       vaa.view_type,
       vaa.job_type,
       vaa.id_current_traf_source AS current_traf_source_id,
       vaa.is_local,
       vaa.job_view_cnt,
       vaa.apply_cnt,
       vaa.apply_with_questionnaire_cnt,
       vaa.apply_click_cnt,
       vaa.questionnaire_start_cnt,
       c.new_cv_cnt
FROM #view_and_apply vaa
         LEFT JOIN #cv c
                   ON vaa.job_view_datediff = c.job_view_datediff AND
                      vaa.id_project = c.id_project AND
                      vaa.job_type = c.job_type AND
                      vaa.view_type = c.view_type AND
                      vaa.id_current_traf_source = c.id_current_traf_source AND
                      vaa.is_local = c.is_local
;
