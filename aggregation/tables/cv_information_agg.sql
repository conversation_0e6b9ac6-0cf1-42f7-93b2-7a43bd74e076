create temp table temp_cv_created as
select id,
       date_diff,
       date,
       id_account,
       id_session,
       id_bytea,
       replace(json, '\u0000', '')                                                    as json,
       id_jdp,
       (json::json ->> 'source')::int                                                 as source,
       id                                                                             as cv_id,
       (json::json -> 'personalInfo' ->> 'encrypted_fullName'::text)                  as fullname,
       (json::json -> 'experience' ->> 'hasExperience')::boolean                      as has_experience,
       (json::json -> 'experience' -> 'workPlaces'):: text                            as workPlaces,
       json_array_length(json::json -> 'experience' -> 'workPlaces')                  as number_of_workplaces,
       (json::json -> 'experience' -> 'workPlaces' -> 0 ->> 'responsibilities')::text as responsibilities_1,
       (json::json -> 'experience' -> 'workPlaces' -> 1 ->> 'responsibilities')::text as responsibilities_2,
       (json::json -> 'experience' -> 'workPlaces' -> 2 ->> 'responsibilities')::text as responsibilities_3,
       (json::json -> 'experience' -> 'workPlaces' -> 3 ->> 'responsibilities')::text as responsibilities_4,
       (json::json -> 'experience' -> 'workPlaces' -> 0 ->> 'position')::text         as position_1,
       (json::json -> 'experience' -> 'workPlaces' -> 1 ->> 'position')::text         as position_2,
       (json::json -> 'experience' -> 'workPlaces' -> 2 ->> 'position')::text         as position_3,
       (json::json -> 'experience' -> 'workPlaces' -> 3 ->> 'position')::text         as position_4,
       (json::json -> 'experience' -> 'hasEducation')::text::boolean                  as has_education,
       (json::json -> 'education' -> 'educationPlaces')::text                         as education_places,
       json_array_length(json::json -> 'education' -> 'educationPlaces')              as number_of_educations,
       coalesce((json::json -> 'experience' ->> 'professionalSkills'),
                (json::json -> 'careerObjective' ->> 'skills'))                       as skills,
       (json::json ->> 'summary')::text                                               as summary
from public.cv_created
where json not like '%\\u0000%';

/*truncate table an.rpl_cv_information_agg;

insert into an.rpl_cv_information_agg(country_id, type_name, action_date, action_type, flags, current_traf_source_id,
                                      device, deleted, action_cnt, cv_cnt, uniqe_account_cnt, session_cnt,
                                      session_build_cnt, has_name, has_experience, has_workplace, has_education,
                                      has_skill, has_summary)*/
-- cv_build_action
select _country_id,
       'cv_build_action'                                                              as type,
       cv_build_action.date :: date,
       cv_build_action.type                                                           as action_type,
       cv_build_action.flags,
       session.id_current_traf_source,
       case
           when session.flags & 16 = 16 then 1
           when session.flags & 64 = 64 or session.flags & 128 = 128 then 2
           else 0 end
                                                                                      as device,
       null                                                                           as deleted,
       count(cv_build_action.id)                                                      as cnt_action,
       count(distinct case when cv_build_action.type = 1 then cv_build_action.id end) as cnt_cv,
       count(distinct cv_build_action.id_account)                                     as cnt_uniqe_account,
       count(distinct cv_build_action.id_session)                                     as cnt_session,
       count(distinct cv_build_action.id_session_build)                               as cnt_session_build,
       null::boolean                                                                  as has_name,
       null::boolean                                                                  as has_experience,
       null::boolean                                                                  as has_workplace,
       null::boolean                                                                  as has_education,
       null::boolean                                                                  as has_skill,
       null::boolean                                                                  as has_summary,
       null::integer                                                                  as cv_400_plus_symbols_cnt
from public.cv_build_action
join public.session
     on cv_build_action.date_diff = session.date_diff
         and cv_build_action.id_session = session.id
where session.flags & 1 = 0
--and cv_build_action.date_diff >= 45317
group by cv_build_action.date :: date,
         cv_build_action.type,
         cv_build_action.flags,
         cv_build_action.step,
         session.id_current_traf_source,
         case
             when session.flags & 16 = 16 then 1
             when session.flags & 64 = 64 or session.flags & 128 = 128 then 2
             else 0 end
union all
select _country_id,
       'cv_build_action_uniqe'                                                        as type,
       cv_build_action.date :: date,
       null                                                                           as action_type,
       null                                                                           as flags,
       session.id_current_traf_source,
       case
           when session.flags & 16 = 16 then 1
           when session.flags & 64 = 64 or session.flags & 128 = 128 then 2
           else 0 end
                                                                                      as device,
       null                                                                           as deleted,
       count(cv_build_action.id)                                                      as cnt_action,
       count(distinct case when cv_build_action.type = 1 then cv_build_action.id end) as cnt_cv,
       count(distinct cv_build_action.id_account)                                     as cnt_uniqe_account,
       count(distinct cv_build_action.id_session)                                     as cnt_session,
       count(distinct cv_build_action.id_session_build)                               as cnt_session_build,
       null::boolean                                                                  as has_name,
       null::boolean                                                                  as has_experience,
       null::boolean                                                                  as has_workplace,
       null::boolean                                                                  as has_education,
       null::boolean                                                                  as has_skill,
       null::boolean                                                                  as has_summary,
       null::integer                                                                  as cv_400_plus_symbols_cnt
from public.cv_build_action
join public.session
     on cv_build_action.date_diff = session.date_diff
         and cv_build_action.id_session = session.id
where session.flags & 1 = 0
--and cv_build_action.date_diff >= 45317
group by cv_build_action.date :: date,
         session.id_current_traf_source,
         case
             when session.flags & 16 = 16 then 1
             when session.flags & 64 = 64 or session.flags & 128 = 128 then 2
             else 0 end
union all
-- accounts with cv
select _country_id,
       'account_cv'                                  as type,
       acv.date_created :: date,
       acv.source,
       acv.flags,
       account_info.id_traf_src,
       case when acv.flags & 1 = 1 then 1 else 0 end as device,
       is_deleted                                    as deleted,
       count(distinct acv.id_file)                   as cnt_action,
       count(distinct case when json is not null then acv.id_file end ) as cnt_cv,
       count(distinct acv.id_account)                as cnt_uniqe_account,
       null                                          as cnt_session,
       null                                          as cnt_session_build,
       null                                          as has_name,
       null                                          as has_experience,
       null                                          as has_workplace,
       null                                          as has_education,
       null                                          as has_skill,
       null                                          as has_summary,
       null                                          as cv_400_plus_symbols_cnt
from link_dbo.account_cv acv
join link_dbo.account_info
     on acv.id_account = account_info.id_account
group by acv.date_created :: date, acv.source, acv.flags, is_deleted,
         account_info.id_traf_src
union all
-- show banners
select _country_id,
       'session_banner_action'                                                   as type,
       an.fn_get_timestamp_from_date_diff(session_banner_action.date_diff)::date as date,
       session_banner_action.banner_type,
       session_banner_action.flags,
       session.id_current_traf_source,
       case
           when session.flags & 16 = 16 then 1
           when session.flags & 64 = 64 or session.flags & 128 = 128 then 2
           else 0 end
                                                                                 as device,
       null                                                                      as deleted,
       count(session_banner_action.id)                                           as cnt_action,
       null                                                                      as cnt_cv,
       count(distinct id_account)                                                as cnt_uniqe_account,
       count(distinct id_session)                                                as cnt_session,
       null                                                                      as cnt_session_build,
       null                                                                      as has_name,
       null                                                                      as has_experience,
       null                                                                      as has_workplace,
       null                                                                      as has_education,
       null                                                                      as has_skill,
       null                                                                      as has_summary,
       null                                                                      as cv_400_plus_symbols_cnt
from public.session_banner_action
join public.session
     on session_banner_action.date_diff = session.date_diff
         and session_banner_action.id_session = session.id
where session.flags & 1 = 0
--and session_banner_action.date_diff >= 45317
group by an.fn_get_timestamp_from_date_diff(session_banner_action.date_diff)::date,
         session_banner_action.banner_type,
         session_banner_action.flags,
         session.id_current_traf_source,
         case
             when session.flags & 16 = 16 then 1
             when session.flags & 64 = 64 or session.flags & 128 = 128 then 2
             else 0 end
union all
-- cv fields
select _country_id,
       'cv_created'                                                                                              as type,
       an.fn_get_timestamp_from_date_diff(cv_created.date_diff)::date                                            as date,
       source                                                                                                    as type,
       null                                                                                                      as flags,
       session.id_current_traf_source,
       case
           when session.flags & 16 = 16 then 1
           when session.flags & 64 = 64 or session.flags & 128 = 128 then 2
           else 0 end
                                                                                                                 as device,
       null                                                                                                      as deleted,
       count(cv_created.id)                                                                                      as cnt_action,
       null                                                                                                      as cnt_cv,
       count(distinct cv_created.id_account)                                                                     as cnt_uniqe_account,
       count(distinct cv_created.id_session)                                                                     as cnt_session,
       count(distinct json::jsonb ->> 'buildCvId')                                                               as cnt_session_build,

       case when length(fullname) > 5 then true else false end                                                   as has_name,
       coalesce(has_experience, false)                                                                           as has_experience,
       case when length(workPlaces) > 2 then true else false end                                                 as has_workplace,
       case
           when coalesce(has_education, false) is true or length(education_places) > 2 then true
           else false end                                                                                        as has_education,
       case when length(skills) > 5 then true else false end                                                     as has_skill,
       case when length(summary) > 5 then true else false end                                                    as has_summary,
       count(distinct case
                          when
                              coalesce(length(responsibilities_1), 0) + coalesce(length(responsibilities_2), 0) +
                              coalesce(length(responsibilities_3), 0) + coalesce(length(responsibilities_4), 0) +
                              coalesce(length(position_1), 0) + coalesce(length(position_2), 0) +
                              coalesce(length(position_3), 0) + coalesce(length(position_4), 0) +
                              coalesce(length(case when number_of_educations > 0 then education_places end) - 91, 0) +
                              coalesce(length(skills), 0) +
                              coalesce(length(summary), 0)
                                  > 400
                              then cv_id end)                                                                    as cv_400_plus_symbols_cnt

from temp_cv_created as cv_created
join public.session
     on cv_created.date_diff = session.date_diff
         and cv_created.id_session = session.id
where session.flags & 1 = 0
group by an.fn_get_timestamp_from_date_diff(cv_created.date_diff)::date,
         source,
         case when length(fullname) > 5 then true else false end,
         coalesce(has_experience, false),
         case when length(workPlaces) > 2 then true else false end,
         case when coalesce(has_education, false) is true or length(education_places) > 2 then true else false end,
         case when length(skills) > 5 then true else false end,
         case when length(summary) > 5 then true else false end,
         session.id_current_traf_source,
         case
             when session.flags & 16 = 16 then 1
             when session.flags & 64 = 64 or session.flags & 128 = 128 then 2
             else 0 end;


drop table temp_cv_created;
