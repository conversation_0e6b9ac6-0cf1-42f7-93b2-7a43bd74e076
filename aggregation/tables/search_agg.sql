CREATE OR <PERSON><PERSON>LACE PROCEDURE an.prc_search_agg(IN start_date integer)
    LANGUAGE plpgsql
AS
$$
DECLARE

    _start_date       int  := start_date;
    _current_database text := (SELECT CURRENT_DATABASE());
    _country_id       int  := (SELECT * FROM an.f_get_country_id());

BEGIN

    CREATE TEMP TABLE tmp_currency AS
    SELECT DISTINCT id, value_to_usd
    FROM link_dbo.info_currency;
    CREATE INDEX tmp_currency_idx ON tmp_currency (id);


    CREATE TEMP TABLE tmp_au_campaign AS
    SELECT DISTINCT id, id_site, id_project
    FROM link_auction.campaign;
    CREATE INDEX tmp_au_campaign_idx ON tmp_au_campaign (id);


    CREATE TEMP TABLE tmp_au_site AS
    SELECT DISTINCT id, id_user
    FROM link_auction.site;
    CREATE INDEX tmp_au_site_idx ON tmp_au_site (id);


    CREATE TEMP TABLE tmp_au_user AS
    SELECT id, flags
    FROM link_auction."user";
    CREATE INDEX tmp_au_user_idx ON tmp_au_user (id);
    CREATE INDEX tmp_au_user_flags_idx ON tmp_au_user (flags);

    ANALYSE tmp_au_user;
    ANALYSE tmp_au_site;
    ANALYSE tmp_au_campaign;
    ANALYSE tmp_currency;


    DELETE
    FROM an.rpl_search_agg
    WHERE load_datediff = _start_date;

    CREATE TEMP TABLE tmp_impression AS
    Select si.date,
           si.id_search,
           ac.id_project,
           count(distinct si.id)              as impression_cnt,
           count(distinct sioc.id_impression) as impression_on_screen_cnt
    from public.session_impression si
             left join public.session_impression_on_screen sioc
                       on si.date = sioc.date_diff
                           and si.id = sioc.id_impression
             LEFT JOIN tmp_au_campaign ac
                       ON ac.id = si.id_campaign
    where date = _start_date
    group by si.date,
             si.id_search,
             ac.id_project;

    CREATE TEMP TABLE tmp_paid AS
    SELECT ss.date_diff,
           ss.id,
           MAX(CASE
                   WHEN si.click_price = 0 THEN 0
                   WHEN si.click_price > 0 THEN 1
               END)                                                     AS is_paid_search,
           COUNT(DISTINCT CASE WHEN sc.click_price <> 0 THEN sc.id END) AS paid_click_cnt,
           COUNT(DISTINCT CASE WHEN sc.click_price = 0 THEN sc.id END)  AS free_click_cnt
    FROM public.session_search ss
             INNER JOIN public.session s
                        ON ss.date_diff = s.date_diff
                            AND ss.id_session = s.id
             LEFT JOIN public.session_click sc
                       ON sc.date_diff = ss.date_diff
                           AND sc.id_search = ss.id
                           AND sc.flags & 4096 = 0 /*duplicated*/
                           AND sc.flags & 16 = 0 /*test campaign*/
             LEFT JOIN public.session_impression si
                       ON si.date = ss.date_diff
                           AND si.id_search = ss.id
                           AND si.position <= 50
    WHERE ss.date_diff = _start_date
      AND COALESCE(s.flags, 0) & 1 = 0 /*session bot*/
    -- and country_id <> 1
    GROUP BY ss.date_diff, ss.id;

    CREATE INDEX temp_paid_idx ON tmp_paid (date_diff, id);
    ANALYSE tmp_paid;


    CREATE TEMP TABLE tmp_session_search AS
    SELECT ss.date_diff,
           ss.id_session,
           ss.results_total,
           ss.service_flags,
           ss.search_source,
           ss.q_flags,
           ss.q_age,
           ss.q_job_type,
           ss.q_remote_type,
           CASE WHEN ss.q_kw = '' THEN NULL ELSE q_kw END AS q_kw,
           ss.q_id_region,
           CASE
               WHEN ss.q_id_region <> - 1 AND TRIM(ss.q_kw) = '' THEN 0
               WHEN ss.q_id_region = -1 AND TRIM(ss.q_kw) <> '' THEN 1
               WHEN ss.q_id_region = -1 AND TRIM(ss.q_kw) = '' THEN 2
               WHEN ss.q_id_region <> -1 AND TRIM(ss.q_kw) <> '' THEN 3
               ELSE 4
           END                                            AS empty_searches,
           CASE
               WHEN ss.results_total = 0 THEN 0
               WHEN ss.results_total BETWEEN 1 AND 10 THEN 1
               WHEN ss.results_total BETWEEN 11 AND 20 THEN 2
               WHEN ss.results_total BETWEEN 21 AND 50 THEN 3
               WHEN ss.results_total BETWEEN 51 AND 100 THEN 4
               WHEN ss.results_total BETWEEN 101 AND 250 THEN 5
               WHEN ss.results_total BETWEEN 251 AND 500 THEN 6
               WHEN ss.results_total BETWEEN 501 AND 1000 THEN 7
               WHEN ss.results_total BETWEEN 1001 AND 5000 THEN 8
               WHEN ss.results_total > 5001 THEN 9
           END                                            AS serp_results_total,
           ss.id
    FROM public.session_search ss
    WHERE date_diff = _start_date
    GROUP BY ss.date_diff, ss.id_session, ss.results_total, ss.service_flags, ss.search_source, ss.q_flags, ss.q_age,
             ss.q_job_type, ss.q_remote_type, ss.q_kw, ss.q_id_region, empty_searches, ss.id;


    CREATE TEMP TABLE tmp_session AS
    SELECT s.date_diff,
           s.id,
           s.id_current_traf_source,
           s.id_traf_source,
           CASE
               WHEN s.flags & 16 = 16 THEN 1
               WHEN s.flags & 64 = 64 OR s.flags & 128 = 128 THEN 2
               ELSE 0
           END               AS user_device,
           SIGN(s.flags & 2) AS is_returned,
           CASE
               WHEN s.ip_cc = LOWER(_current_database)
                   OR s.ip_cc = 'gb' AND LOWER(_current_database) = 'uk'
                   THEN 1
               ELSE 0
           END               AS is_local,
           s.session_create_page_type,
           s.flags,
           s.ip_cc
    FROM public.session s
    WHERE date_diff = _start_date
      AND COALESCE(s.flags, 0) & 1 = 0 /*session bot*/
    ;

    ANALYSE tmp_session_search;
    ANALYSE tmp_session;


    CREATE TEMP TABLE tmp_we_have_conversion AS
    SELECT DISTINCT project_id
    FROM an.click_metric_agg cma
    WHERE action_datediff >= _start_date - 7
      AND click_type = 5;


    CREATE TEMP TABLE tmp_result AS
    SELECT _country_id                                                     AS country_id,
           ss.date_diff,
           s.id_current_traf_source,
           s.id_traf_source,
           s.user_device,
           s.is_returned,
           s.is_local,
           s.session_create_page_type,
           ss.serp_results_total,
           ss.service_flags                                                AS search_flags,
           ss.search_source,
           ss.q_flags,
           ss.q_age,
           ss.q_job_type,
           ss.q_remote_type,
           ss.q_kw,
           ss.q_id_region,
           ss.empty_searches,
           COUNT(DISTINCT ss.id)                                           AS search_cnt,
           0                                                               AS click_cnt,
           0::numeric                                                               AS revenue_usd,
           0                                                               AS destination_away_click,
           0                                                               AS destination_jdp_away_click,
           0                                                               AS destination_jdp_apply_click,
           0                                                               AS click_paid_cnt,
           0                                                               AS click_premium_cnt,
           0                                                               AS click_free_cnt,
           COUNT(DISTINCT CASE WHEN ps.is_paid_search = 0 THEN ss.id END)  AS with_only_free_jobs,
           COUNT(DISTINCT CASE WHEN ps.is_paid_search = 1 THEN ss.id END)  AS with_paid_jobs,
           COUNT(DISTINCT CASE WHEN ps.paid_click_cnt = 1 THEN ss.id END)  AS with_paid_clicks,
           COUNT(DISTINCT CASE
                              WHEN ps.free_click_cnt = 1 AND ps.paid_click_cnt = 0 THEN ss.id
                          END)                                             AS without_paid_clicks,
           COUNT(DISTINCT CASE
                              WHEN ps.paid_click_cnt = 0 AND ps.free_click_cnt = 0 THEN ss.id
                          END)                                             AS without_any_clicks,
           0                                                               AS apply_cnt,
           0                                                               AS conversion_cnt,
           0                                                               AS conversion_away_cnt,
           0::numeric                                                               AS conversion_revenue_usd,
           ss.date_diff                                                    AS load_datediff,
           NULL                                                            AS id_project,
           NULL                                                            AS project_search_cnt,
           NULL                                                            AS impression_cnt,
           NULL                                                            AS impression_on_screen_cnt
    FROM tmp_session_search ss
             INNER JOIN tmp_session s
                        ON ss.date_diff = s.date_diff
                            AND ss.id_session = s.id
             LEFT JOIN tmp_paid ps
                       ON ps.date_diff = ss.date_diff AND ps.id = ss.id
             LEFT JOIN public.session_click sc
                       ON sc.date_diff = ss.date_diff
                           AND sc.id_search = ss.id
                           AND sc.flags & 4096 = 0 /*duplicated*/
                           AND sc.flags & 16 = 0 /*test campaign*/
             LEFT JOIN tmp_currency ic
                       ON sc.id_currency = ic.id
             LEFT JOIN public.session_jdp sj
                       ON sj.date_diff = sc.date_diff
                           AND sj.id_click = sc.id
             LEFT JOIN public.session_away sa
                       ON sj.date_diff = sa.date_diff
                           AND sj.id = sa.id_jdp
                           AND sa.flags & 512 = 0 /*duplicated*/
                           AND sa.flags & 2 = 0 /*test campaign*/
             LEFT JOIN tmp_au_campaign ac
                       ON ac.id = sc.id_campaign
             LEFT JOIN tmp_au_site ast
                       ON ac.id_site = ast.id
             LEFT JOIN tmp_au_user au
                       ON au.id = ast.id_user
             LEFT JOIN tmp_we_have_conversion whc
                       ON sc.id_project = whc.project_id

    WHERE ss.date_diff = _start_date
      AND COALESCE(s.flags, 0) & 1 = 0 /*session bot*/
    GROUP BY ss.date_diff,
             s.id_current_traf_source,
             s.id_traf_source,
             s.user_device,
             s.is_returned,
             s.is_local,
             s.session_create_page_type,
             ss.serp_results_total,
             ss.service_flags,
             ss.search_source,
             ss.q_flags,
             ss.q_age,
             ss.q_job_type,
             ss.q_remote_type,
             ss.q_kw,
             ss.q_id_region,
             ss.empty_searches

    UNION ALL

    SELECT _country_id                                                     AS country_id,
           ss.date_diff,
           s.id_current_traf_source,
           s.id_traf_source,
           s.user_device,
           s.is_returned,
           s.is_local,
           s.session_create_page_type,
           ss.serp_results_total,
           ss.service_flags                                                AS search_flags,
           ss.search_source,
           ss.q_flags,
           ss.q_age,
           ss.q_job_type,
           ss.q_remote_type,
           ss.q_kw,
           ss.q_id_region,
           ss.empty_searches,
           0                                                               AS search_cnt,
           COUNT(DISTINCT sc.id)                                           AS click_cnt,
           SUM(CASE
                   WHEN sj.id IS NULL THEN sc.click_price * COALESCE(ic.value_to_usd, 0)
                   WHEN sj.id IS NOT NULL AND au.flags & 2 = 0 THEN sa.click_price * COALESCE(ic.value_to_usd, 0)
                   WHEN sj.id IS NOT NULL AND au.flags & 2 = 2 THEN sc.click_price * COALESCE(ic.value_to_usd, 0)
               END)::numeric                                                        AS revenue_usd,
           COUNT(DISTINCT CASE WHEN sc.job_destination = 1 THEN sc.id END) AS destination_away_click,
           COUNT(DISTINCT CASE WHEN sc.job_destination = 2 THEN sc.id END) AS destination_jdp_away_click,
           COUNT(DISTINCT CASE WHEN sc.job_destination = 3 OR sc.job_destination = 4 THEN sc.id END)
                                                                           AS destination_jdp_apply_click,
           COUNT(DISTINCT CASE WHEN sc.click_price <> 0 THEN sc.id END)    AS click_paid_cnt,
           COUNT(DISTINCT CASE
                              WHEN sc.flags & 128 = 128 OR sj.flags & 256 = 256 THEN sc.id
                          END)                                             AS click_premium_cnt,
           COUNT(DISTINCT CASE WHEN sc.click_price = 0 THEN sc.id END)     AS click_free_cnt,
           0                                                               AS with_only_free_jobs,
           0                                                               AS with_paid_jobs,
           0                                                               AS with_paid_clicks,
           0                                                               AS without_paid_clicks,
           0                                                               AS without_any_clicks,
           0                                                               AS apply_cnt,
           0                                                               AS conversion_cnt,
           COUNT(DISTINCT CASE
                              WHEN whc.project_id IS NOT NULL and sc.job_destination = 1 THEN sc.id
                              else sa.id
                          END)                                             AS conversion_away_cnt,
           SUM(CASE
                   WHEN whc.project_id IS NOT NULL THEN
                       CASE
                           WHEN sj.id IS NULL THEN sc.click_price * COALESCE(ic.value_to_usd, 0)
                           WHEN sj.id IS NOT NULL AND au.flags & 2 = 0
                               THEN sa.click_price * COALESCE(ic.value_to_usd, 0)
                           WHEN sj.id IS NOT NULL AND au.flags & 2 = 2
                               THEN sc.click_price * COALESCE(ic.value_to_usd, 0)
                       END
               END)::numeric                                                        AS conversion_revenue_usd,
           ss.date_diff                                                    AS load_datediff,
           sc.id_project                                                   AS id_project,
           count(distinct ss.id)                                           AS project_search_cnt,
           NULL                                                            AS impression_cnt,
           NULL                                                            AS impression_on_screen_cnt
    FROM tmp_session_search ss
             INNER JOIN tmp_session s
                        ON ss.date_diff = s.date_diff
                            AND ss.id_session = s.id
             LEFT JOIN tmp_paid ps
                       ON ps.date_diff = ss.date_diff AND ps.id = ss.id
             LEFT JOIN public.session_click sc
                       ON sc.date_diff = ss.date_diff
                           AND sc.id_search = ss.id
                           AND sc.flags & 4096 = 0 /*duplicated*/
                           AND sc.flags & 16 = 0 /*test campaign*/
             LEFT JOIN tmp_currency ic
                       ON sc.id_currency = ic.id
             LEFT JOIN public.session_jdp sj
                       ON sj.date_diff = sc.date_diff
                           AND sj.id_click = sc.id
             LEFT JOIN public.session_away sa
                       ON sj.date_diff = sa.date_diff
                           AND sj.id = sa.id_jdp
                           AND sa.flags & 512 = 0 /*duplicated*/
                           AND sa.flags & 2 = 0 /*test campaign*/
             LEFT JOIN tmp_au_campaign ac
                       ON ac.id = sc.id_campaign
             LEFT JOIN tmp_au_site ast
                       ON ac.id_site = ast.id
             LEFT JOIN tmp_au_user au
                       ON au.id = ast.id_user
             LEFT JOIN tmp_we_have_conversion whc
                       ON sc.id_project = whc.project_id
    WHERE ss.date_diff = _start_date
      AND COALESCE(s.flags, 0) & 1 = 0 /*session bot*/
    GROUP BY ss.date_diff,
             s.id_current_traf_source,
             s.id_traf_source,
             s.user_device,
             s.is_returned,
             s.is_local,
             s.session_create_page_type,
             ss.serp_results_total,
             ss.service_flags,
             ss.search_source,
             ss.q_flags,
             ss.q_age,
             ss.q_job_type,
             ss.q_remote_type,
             ss.q_kw,
             ss.q_id_region,
             ss.empty_searches,
             sc.id_project

    UNION ALL

        SELECT _country_id                                                     AS country_id,
           ss.date_diff,
           s.id_current_traf_source,
           s.id_traf_source,
           s.user_device,
           s.is_returned,
           s.is_local,
           s.session_create_page_type,
           ss.serp_results_total,
           ss.service_flags                                                AS search_flags,
           ss.search_source,
           ss.q_flags,
           ss.q_age,
           ss.q_job_type,
           ss.q_remote_type,
           ss.q_kw,
           ss.q_id_region,
           ss.empty_searches,
           0                                                               AS search_cnt,
           0                                                               AS click_cnt,
           0::numeric                                                               AS revenue_usd,
           0                                                               AS destination_away_click,
           0                                                               AS destination_jdp_away_click,
           0                                                               AS destination_jdp_apply_click,
           0                                                               AS click_paid_cnt,
           0                                                               AS click_premium_cnt,
           0                                                               AS click_free_cnt,
           0                                                               AS with_only_free_jobs,
           0                                                               AS with_paid_jobs,
           0                                                               AS with_paid_clicks,
           0                                                               AS without_paid_clicks,
           0                                                               AS without_any_clicks,
           0                                                               AS apply_cnt,
           0                                                               AS conversion_cnt,
           0                                                               AS conversion_away_cnt,
           0::numeric                                                               AS conversion_revenue_usd,
           ss.date_diff                                                    AS load_datediff,
           im.id_project                                                   AS id_project,
           0                                                               AS project_search_cnt,
           sum(im.impression_cnt)::varchar                                          AS impression_cnt,
           sum(im.impression_on_screen_cnt)::varchar                                AS impression_on_screen_cnt
    FROM tmp_session_search ss
             INNER JOIN tmp_session s
                        ON ss.date_diff = s.date_diff
                            AND ss.id_session = s.id
             LEFT JOIN tmp_impression im
                        ON ss.date_diff = im.date AND ss.id = im.id_search
    WHERE ss.date_diff = _start_date
      AND COALESCE(s.flags, 0) & 1 = 0 /*session bot*/
    GROUP BY ss.date_diff,
             s.id_current_traf_source,
             s.id_traf_source,
             s.user_device,
             s.is_returned,
             s.is_local,
             s.session_create_page_type,
             ss.serp_results_total,
             ss.service_flags,
             ss.search_source,
             ss.q_flags,
             ss.q_age,
             ss.q_job_type,
             ss.q_remote_type,
             ss.q_kw,
             ss.q_id_region,
             ss.empty_searches,
             im.id_project
    ;




    CREATE TEMP TABLE tmp_click_metric_agg AS
    SELECT action_datediff,
           load_datediff,
           project_id,
           current_traf_source_id,
           traf_source_id,
           device,
           is_returned,
           is_local,
           session_create_page_type,
           click_type,
           jdp_id,
           away_id,
           search_id,
           apply_id
    FROM an.click_metric_agg
    WHERE load_datediff = _start_date
      AND click_type IN (2, 5);


    INSERT INTO tmp_result(country_id, date_diff, id_current_traf_source, id_traf_source, user_device,
                           is_returned, is_local, session_create_page_type, serp_results_total, search_flags,
                           search_source, q_flags, q_age, q_job_type, q_remote_type, q_kw, q_id_region,
                           empty_searches, search_cnt, click_cnt, revenue_usd, destination_away_click,
                           destination_jdp_away_click, destination_jdp_apply_click, click_paid_cnt, click_premium_cnt,
                           click_free_cnt, with_only_free_jobs, with_paid_jobs, with_paid_clicks, without_paid_clicks,
                           without_any_clicks, apply_cnt, conversion_cnt, conversion_away_cnt, conversion_revenue_usd,
                           load_datediff, id_project, project_search_cnt, impression_cnt,impression_on_screen_cnt )
    SELECT _country_id                                                AS country_id,
           ss.date_diff,
           cma.current_traf_source_id,
           cma.traf_source_id,
           cma.device                                                 AS user_device,
           cma.is_returned,
           cma.is_local,
           cma.session_create_page_type,
           CASE
               WHEN ss.results_total = 0 THEN 0
               WHEN ss.results_total BETWEEN 1 AND 10 THEN 1
               WHEN ss.results_total BETWEEN 11 AND 20 THEN 2
               WHEN ss.results_total BETWEEN 21 AND 50 THEN 3
               WHEN ss.results_total BETWEEN 51 AND 100 THEN 4
               WHEN ss.results_total BETWEEN 101 AND 250 THEN 5
               WHEN ss.results_total BETWEEN 251 AND 500 THEN 6
               WHEN ss.results_total BETWEEN 501 AND 1000 THEN 7
               WHEN ss.results_total BETWEEN 1001 AND 5000 THEN 8
               WHEN ss.results_total > 5001 THEN 9
               END                                                    AS serp_results_total,
           ss.service_flags                                           AS search_flags,
           ss.search_source,
           ss.q_flags,
           ss.q_age,
           ss.q_job_type,
           ss.q_remote_type,
           CASE WHEN ss.q_kw = '' THEN NULL ELSE ss.q_kw END          AS q_kw,
           ss.q_id_region,
           CASE
               WHEN ss.q_id_region <> - 1 AND TRIM(ss.q_kw) = '' THEN 0
               WHEN ss.q_id_region = -1 AND TRIM(ss.q_kw) <> '' THEN 1
               WHEN ss.q_id_region = -1 AND TRIM(ss.q_kw) = '' THEN 2
               WHEN ss.q_id_region <> -1 AND TRIM(ss.q_kw) <> '' THEN 3
               ELSE 4
               END                                                    AS empty_searches,
           0                                                          AS search_cnt,
           0                                                          AS click_cnt,
           0                                                          AS revenue_usd,
           0                                                          AS destination_away_click,
           0                                                          AS destination_jdp_away_click,
           0                                                          AS destination_jdp_apply_click,
           0                                                          AS click_paid_cnt,
           0                                                          AS click_premium_cnt,
           0                                                          AS click_free_cnt,
           0                                                          AS with_only_free_jobs,
           0                                                          AS with_paid_jobs,
           0                                                          AS with_paid_clicks,
           0                                                          AS without_paid_clicks,
           0                                                          AS without_any_clicks,
           COUNT(DISTINCT CASE WHEN click_type = 2 THEN apply_id END) AS apply_cnt,
           COUNT(DISTINCT CASE WHEN click_type = 5 THEN away_id END)  AS conversion_cnt,
           0                                                          AS conversion_away_cnt,
           0                                                          AS conversion_revenue_usd,
           cma.load_datediff,
           cma.project_id                                             AS id_project,
           NULL                                                       AS project_search_cnt,
           NULL                                                       AS impression_cnt,
           NULL                                                       AS impression_on_screen_cnt
    FROM tmp_click_metric_agg cma
             JOIN public.session_search ss
                  ON cma.action_datediff = ss.date_diff
                      AND cma.search_id = ss.id

    WHERE cma.load_datediff = _start_date
      AND cma.click_type IN (2, 5)
    GROUP BY ss.date_diff,
             cma.load_datediff,
             cma.project_id,
             cma.current_traf_source_id,
             cma.traf_source_id,
             cma.device,
             cma.is_returned,
             cma.is_local,
             cma.session_create_page_type,
             serp_results_total,
             ss.service_flags,
             ss.search_source,
             ss.q_flags,
             ss.q_age,
             ss.q_job_type,
             ss.q_remote_type,
             q_kw,
             ss.q_id_region,
             empty_searches;

    INSERT INTO an.rpl_search_agg(country_id, date_diff, id_current_traf_source, id_traf_source, user_device,
                                  is_returned,
                                  is_local, session_create_page_type, serp_results_total, search_flags, search_source,
                                  q_flags, q_age,
                                  q_job_type, q_remote_type, q_kw, q_id_region, id_project, empty_searches, search_cnt,
                                  click_cnt,
                                  revenue_usd,
                                  destination_away_click, destination_jdp_away_click, destination_jdp_apply_click,
                                  click_paid_cnt,
                                  click_premium_cnt, click_free_cnt, with_only_free_jobs, with_paid_jobs,
                                  with_paid_clicks, without_paid_clicks, without_any_clicks, apply_cnt, conversion_cnt,
                                  conversion_away_cnt, conversion_revenue_usd, load_datediff, project_search_cnt, impression_cnt,
                                  impression_on_screen_cnt)
    SELECT country_id,
           date_diff,
           id_current_traf_source,
           id_traf_source,
           user_device,
           is_returned,
           is_local,
           session_create_page_type,
           serp_results_total,
           search_flags,
           search_source,
           q_flags,
           q_age,
           q_job_type,
           q_remote_type,
           q_kw,
           q_id_region,
           id_project,
           empty_searches,
           search_cnt,
           click_cnt,
           revenue_usd,
           destination_away_click,
           destination_jdp_away_click,
           destination_jdp_apply_click,
           click_paid_cnt,
           click_premium_cnt,
           click_free_cnt,
           with_only_free_jobs,
           with_paid_jobs,
           with_paid_clicks,
           without_paid_clicks,
           without_any_clicks,
           apply_cnt,
           conversion_cnt,
           conversion_away_cnt,
           conversion_revenue_usd,
           load_datediff,
           project_search_cnt,
           impression_cnt,
           impression_on_screen_cnt
    FROM tmp_result;



    -- dropping tmp tables
    DROP TABLE IF EXISTS tmp_currency;
    DROP TABLE IF EXISTS tmp_au_campaign;
    DROP TABLE IF EXISTS tmp_au_site;
    DROP TABLE IF EXISTS tmp_au_user;
    DROP TABLE IF EXISTS tmp_paid;
    DROP TABLE IF EXISTS tmp_session_search;
    DROP TABLE IF EXISTS tmp_session;
    DROP TABLE IF EXISTS tmp_we_have_conversion;
    DROP TABLE IF EXISTS tmp_click_metric_agg;
    DROP TABLE IF EXISTS tmp_result;


END;
$$;
