declare @date_diff int = datediff(day, 0, getdate() - 1);


    CREATE TEMP TABLE u_traffic_source_temp as
    SELECT id, channel
    FROM link_dbo.u_traffic_source;

    CREATE TEMP TABLE session_test_agg_temp as
    select id_session,
           session_test.date_diff,
           array_to_string(array_agg(id_test), ' ')                                                        as id_tests,
           array_to_string(array_agg(concat((id_test), '-', session_test."group", '_', (iteration))), ' ') as groups
    from public.session_test
    where iteration >= 0
      and session_test.date_diff = _date_diff
    group by id_session,
             session_test.date_diff;

    CREATE INDEX session_test_agg_temp_index ON session_test_agg_temp (date_diff, id_session);

    CREATE TEMP TABLE session_s_temp as
    SELECT id,
           s.date_diff,
           s.flags,
           s.session_create_page_type,
           s.id_traf_source,
           s.id_current_traf_source,
           s.first_visit_date_diff
    FROM session s
    where s.flags & 1 = 0
      and s.flags & 4 = 0
      and s.date_diff = _date_diff;

    CREATE INDEX session_index_first ON session_s_temp (id_traf_source);
    CREATE INDEX session_index_second ON session_s_temp (id_current_traf_source);


    analyze session_s_temp;
    analyze session_test_agg_temp;
    analyze u_traffic_source_temp;

    CREATE TEMP TABLE session_temp AS
    select stat.groups                             as groups,
           s.id                                    as id_session,
           s.date_diff                             as date_diff,
           iif(s.flags & 2 = 2, 1, 0)              as is_returned,
           iif(s.flags & 16 = 16, 1, 0)            as is_mobile,
           coalesce(s.session_create_page_type, 0) as session_create_page_type,
           s.id_traf_source                        as id_traffic_source,
           s.id_current_traf_source                as id_current_traffic_source,
           uts.channel                             as channel_1,
           uts1.channel                            as channel_2,
           case
               when (s.date_diff - s.first_visit_date_diff) < 8 then 7
               when (s.date_diff - s.first_visit_date_diff) < 15 then 14
               when (s.date_diff - s.first_visit_date_diff) <= 30 then 30
               when (s.date_diff - s.first_visit_date_diff) <= 90 then 90
               when (s.date_diff - s.first_visit_date_diff) > 90 then 100
               end                                 as user_dtd_lifetime
    from session_s_temp s
             left join session_test_agg_temp stat
                       on stat.date_diff = s.date_diff and s.id = stat.id_session
             left join u_traffic_source_temp uts
                       on s.id_traf_source = uts.id
             left join u_traffic_source_temp uts1
                       on s.id_current_traf_source = uts1.id;


    analyze session_temp;

    create temp table session_action_temp as
    select st.groups,
           st.id_session,
           st.date_diff,
           st.is_returned,
           st.is_mobile,
           st.session_create_page_type,
           st.channel_1,
           st.channel_2,
           st.user_dtd_lifetime,
           sact.type as action_type,
           sact.id   as id_session_action
    from session_temp st
             left join session_action sact
                       on sact.date_diff = st.date_diff and st.id_session = sact.id_session;

    CREATE TEMP TABLE session_alertview_temp AS
    SELECT id, sa.date_diff, sa.id_session
    FROM session_alertview sa
    WHERE sa.date_diff = _date_diff;

    CREATE INDEX session_alertview_temp_index ON session_alertview_temp (date_diff, id_session);

    CREATE TEMP TABLE session_alertview_action_j as
    SELECT *
    from session_alertview_action saa
    where saa.date_diff = _date_diff;


    analyze session_alertview_temp;

    create temp table alertview_action_temp as
    select st.groups,
           st.id_session,
           st.date_diff,
           st.is_returned,
           st.is_mobile,
           st.session_create_page_type,
           st.channel_1,
           st.channel_2,
           st.user_dtd_lifetime,
           saa.action as action_type,
           saa.id     as id_sess_alertview_action,
           sa.id      as id_sess_alertview

    from session_alertview_temp sa
             join session_temp st
                  on sa.date_diff = st.date_diff and sa.id_session = st.id_session
             left join (select saa.id,
                               saa.action,
                               saa.date_diff,
                               (select id
                                from session_alertview sa
                                where sa.sub_id_alert = saa.id_alertview
                                  and saa.date_diff = sa.date_diff
                                  and saa.date_time > sa.date
                                order by date
                                limit 1) as id_alertview
                        from session_alertview_action_j saa) saa
                       on saa.date_diff = sa.date_diff and sa.id = saa.id_alertview;

    CREATE TEMP TABLE session_jdp_temp as
    SELECT *
    FROM session_jdp sj
    where sj.date_diff = _date_diff
      and sj.flags & 4 = 4;

    analyze session_jdp_temp;
    analyze session_temp;

    CREATE TEMP TABLE apply_action_temp as
    select st.groups,
           st.id_session,
           st.date_diff,
           st.is_returned,
           st.is_mobile,
           st.session_create_page_type,
           st.channel_1,
           st.channel_2,
           st.user_dtd_lifetime,
           sapa.type as action_type,
           sapa.id   as id_sess_apply_action,
           sj.id     as id_sess_jdp
    from session_jdp_temp sj
             join session_temp st
                  on st.date_diff = sj.date_diff and st.id_session = sj.id_session
             left join session_apply_action sapa
                       on sj.date_diff = sapa.date_diff and sj.id = sapa.id_jdp;


    -------- truncate
--     truncate an.rpl_action_agg;
    -------- truncate


--     insert into an.rpl_action_agg(country, groups, date_diff, is_returned, is_mobile, session_create_page_type,
--                                   channel_1, channel_2, user_dtd_lifetime, entity_type, action_type, metric, event_cnt,
--                                   page_cnt, session_cnt)
    with _cdp_action as (select st.groups,
                                st.id_session,
                                st.date_diff,
                                st.is_returned,
                                st.is_mobile,
                                st.session_create_page_type,
                                st.channel_1,
                                st.channel_2,
                                st.user_dtd_lifetime,
                                sca.type as action_type,
                                sca.id   as id_sess_cdp_action,
                                sc.id    as id_sess_cdp

                         from session_cdp sc
                                  join session_temp st
                                       on st.date_diff = sc.date_diff and st.id_session = sc.id_session
                                  left join session_cdp_action sca
                                            on sc.date_diff = sca.date_diff and sc.id = sca.id_cdp
                         where sc.date_diff = _date_diff),

         _feature_action as (select st.groups,
                                    st.id_session,
                                    st.date_diff,
                                    st.is_returned,
                                    st.is_mobile,
                                    st.session_create_page_type,
                                    st.channel_1,
                                    st.channel_2,
                                    st.user_dtd_lifetime,
                                    sfa.type as action_type,
                                    sfa.id   as id_sess_feature_action,
                                    sf.id    as id_sess_feature

                             from session_temp st
                                      left join session_feature sf
                                                on st.date_diff = sf.date_diff and st.id_session = sf.id_session
                                      left join session_feature_action sfa
                                                on sf.date_diff = sfa.date_diff and sf.id = sfa.id_session_feature
                             where st.date_diff = _date_diff),

         _filter_action as (select st.groups,
                                   st.id_session,
                                   st.date_diff,
                                   st.is_returned,
                                   st.is_mobile,
                                   st.session_create_page_type,
                                   st.channel_1,
                                   st.channel_2,
                                   st.user_dtd_lifetime,
                                   sfla.filter_type as entity_type,
                                   sfla.action      as action_type,
                                   sfla.id          as id_sess_filter_action,
                                   ss.id            as id_sess_filter

                            from session_search ss
                                     join session_temp st
                                          on st.date_diff = ss.date_diff and st.id_session = ss.id_session
                                     left join session_filter_action sfla
                                               on ss.date_diff = sfla.date_diff and ss.id = sfla.id_search_prev
                            where ss.date_diff = _date_diff),

         _jdp_action as (select st.groups,
                                st.id_session,
                                st.date_diff,
                                st.is_returned,
                                st.is_mobile,
                                st.session_create_page_type,
                                st.channel_1,
                                st.channel_2,
                                st.user_dtd_lifetime,
                                sja.type as action_type,
                                sja.id   as id_sess_jdp_action,
                                sj.id    as id_sess_jdp

                         from session_jdp sj
                                  join session_temp st
                                       on st.date_diff = sj.date_diff and st.id_session = sj.id_session
                                  left join session_jdp_action sja
                                            on sj.date_diff = sja.date_diff and sj.id = sja.id_jdp
                         where sj.date_diff = _date_diff),

         _profile_action as (select st.groups,
                                    st.id_session,
                                    st.date_diff,
                                    st.is_returned,
                                    st.is_mobile,
                                    st.session_create_page_type,
                                    st.channel_1,
                                    st.channel_2,
                                    st.user_dtd_lifetime,
                                    spa.type as action_type,
                                    spa.id   as id_sess_profile_action,
                                    sp.id    as id_sess_profile

                             from session_profile sp
                                      join session_temp st
                                           on st.date_diff = sp.date_diff and st.id_session = sp.id_session
                                      left join session_profile_action spa
                                                on sp.date_diff = spa.date_diff and sp.id = spa.id_session_profile
                             where sp.date_diff = _date_diff),

         _notification_center_action as (select st.groups,
                                                st.id_session,
                                                st.date_diff,
                                                st.is_returned,
                                                st.is_mobile,
                                                st.session_create_page_type,
                                                st.channel_1,
                                                st.channel_2,
                                                st.user_dtd_lifetime,
                                                snca.notification_type as entity_type,
                                                snca.action_type       as action_type,
                                                snca.id                as id_sess_notification_center_action

                                         from session_temp st
                                                  left join session_notification_center_action snca
                                                            on st.date_diff = snca.date_diff and st.id_session = snca.id_session
                                         where st.date_diff = _date_diff),

         action_union as (select st.groups,
                                 st.date_diff,
                                 st.is_returned,
                                 st.is_mobile,
                                 st.session_create_page_type,
                                 st.channel_1,
                                 st.channel_2,
                                 st.user_dtd_lifetime,
                                 cast(null as int)             as entity_type,
                                 cast(-1 as int)               as action_type,
                                 'session'                     as metric,
                                 cast(0 as bigint)             as event_cnt,
                                 cast(0 as bigint)             as page_cnt,
                                 count(distinct st.id_session) as session_cnt
                          from session_temp st
                          group by st.groups,
                                   st.date_diff,
                                   st.is_returned,
                                   st.is_mobile,
                                   st.session_create_page_type,
                                   st.channel_1,
                                   st.channel_2,
                                   st.user_dtd_lifetime
                          union
                          select sa.groups,
                                 sa.date_diff,
                                 sa.is_returned,
                                 sa.is_mobile,
                                 sa.session_create_page_type,
                                 sa.channel_1,
                                 sa.channel_2,
                                 sa.user_dtd_lifetime,
                                 cast(null as int)                    as entity_type,
                                 sa.action_type                       as action_type,
                                 'session_action'                     as metric,
                                 count(distinct sa.id_session_action) as event_cnt,
                                 cast(0 as bigint)                    as page_cnt,
                                 count(distinct sa.id_session)        as session_cnt
                          from session_action_temp sa
                          group by sa.groups,
                                   sa.date_diff,
                                   sa.is_returned,
                                   sa.is_mobile,
                                   sa.session_create_page_type,
                                   sa.channel_1,
                                   sa.channel_2,
                                   sa.user_dtd_lifetime,
                                   sa.action_type
                          union
                          select sa.groups,
                                 sa.date_diff,
                                 sa.is_returned,
                                 sa.is_mobile,
                                 sa.session_create_page_type,
                                 sa.channel_1,
                                 sa.channel_2,
                                 sa.user_dtd_lifetime,
                                 cast(null as int)             as entity_type,
                                 cast(-1 as int)               as action_type,
                                 'session_action'              as metric,
                                 cast(0 as bigint)             as event_cnt,
                                 cast(0 as bigint)             as page_cnt,
                                 count(distinct sa.id_session) as session_cnt
                          from session_action_temp sa
                          group by sa.groups,
                                   sa.date_diff,
                                   sa.is_returned,
                                   sa.is_mobile,
                                   sa.session_create_page_type,
                                   sa.channel_1,
                                   sa.channel_2,
                                   sa.user_dtd_lifetime
                          union
                          select aa.groups,
                                 aa.date_diff,
                                 aa.is_returned,
                                 aa.is_mobile,
                                 aa.session_create_page_type,
                                 aa.channel_1,
                                 aa.channel_2,
                                 aa.user_dtd_lifetime,
                                 cast(null as int)                           as entity_type,
                                 aa.action_type                              as action_type,
                                 'alertview_action'                          as metric,
                                 count(distinct aa.id_sess_alertview_action) as event_cnt,
                                 count(distinct aa.id_sess_alertview)        as page_cnt,
                                 count(distinct aa.id_session)               as session_cnt
                          from alertview_action_temp aa
                          group by aa.groups,
                                   aa.date_diff,
                                   aa.is_returned,
                                   aa.is_mobile,
                                   aa.session_create_page_type,
                                   aa.channel_1,
                                   aa.channel_2,
                                   aa.user_dtd_lifetime,
                                   aa.action_type
                          union
                          select aa.groups,
                                 aa.date_diff,
                                 aa.is_returned,
                                 aa.is_mobile,
                                 aa.session_create_page_type,
                                 aa.channel_1,
                                 aa.channel_2,
                                 aa.user_dtd_lifetime,
                                 cast(null as int)             as entity_type,
                                 cast(-1 as int)               as action_type,
                                 'alertview_action'            as metric,
                                 cast(0 as bigint)             as event_cnt,
                                 cast(0 as bigint)             as page_cnt,
                                 count(distinct aa.id_session) as session_cnt
                          from alertview_action_temp aa
                          group by aa.groups,
                                   aa.date_diff,
                                   aa.is_returned,
                                   aa.is_mobile,
                                   aa.session_create_page_type,
                                   aa.channel_1,
                                   aa.channel_2,
                                   aa.user_dtd_lifetime
                          union
                          select apa.groups,
                                 apa.date_diff,
                                 apa.is_returned,
                                 apa.is_mobile,
                                 apa.session_create_page_type,
                                 apa.channel_1,
                                 apa.channel_2,
                                 apa.user_dtd_lifetime,
                                 cast(null as int)                        as entity_type,
                                 apa.action_type                          as action_type,
                                 'apply_action'                           as metric,
                                 count(distinct apa.id_sess_apply_action) as event_cnt,
                                 count(distinct apa.id_sess_jdp)          as page_cnt,
                                 count(distinct apa.id_session)           as session_cnt
                          from apply_action_temp apa
                          group by apa.groups,
                                   apa.date_diff,
                                   apa.is_returned,
                                   apa.is_mobile,
                                   apa.session_create_page_type,
                                   apa.channel_1,
                                   apa.channel_2,
                                   apa.user_dtd_lifetime,
                                   apa.action_type
                          union
                          select apa.groups,
                                 apa.date_diff,
                                 apa.is_returned,
                                 apa.is_mobile,
                                 apa.session_create_page_type,
                                 apa.channel_1,
                                 apa.channel_2,
                                 apa.user_dtd_lifetime,
                                 cast(null as int)              as entity_type,
                                 cast(-1 as int)                as action_type,
                                 'apply_action'                 as metric,
                                 cast(0 as bigint)              as event_cnt,
                                 cast(0 as bigint)              as page_cnt,
                                 count(distinct apa.id_session) as session_cnt
                          from apply_action_temp apa
                          group by apa.groups,
                                   apa.date_diff,
                                   apa.is_returned,
                                   apa.is_mobile,
                                   apa.session_create_page_type,
                                   apa.channel_1,
                                   apa.channel_2,
                                   apa.user_dtd_lifetime
                          union
                          select cdpa.groups,
                                 cdpa.date_diff,
                                 cdpa.is_returned,
                                 cdpa.is_mobile,
                                 cdpa.session_create_page_type,
                                 cdpa.channel_1,
                                 cdpa.channel_2,
                                 cdpa.user_dtd_lifetime,
                                 cast(null as int)                       as entity_type,
                                 cdpa.action_type                        as action_type,
                                 'cdp_action'                            as metric,
                                 count(distinct cdpa.id_sess_cdp_action) as event_cnt,
                                 count(distinct cdpa.id_sess_cdp)        as page_cnt,
                                 count(distinct cdpa.id_session)         as session_cnt
                          from _cdp_action cdpa
                          group by cdpa.groups,
                                   cdpa.date_diff,
                                   cdpa.is_returned,
                                   cdpa.is_mobile,
                                   cdpa.session_create_page_type,
                                   cdpa.channel_1,
                                   cdpa.channel_2,
                                   cdpa.user_dtd_lifetime,
                                   cdpa.action_type
                          union
                          select cdpa.groups,
                                 cdpa.date_diff,
                                 cdpa.is_returned,
                                 cdpa.is_mobile,
                                 cdpa.session_create_page_type,
                                 cdpa.channel_1,
                                 cdpa.channel_2,
                                 cdpa.user_dtd_lifetime,
                                 cast(null as int)               as entity_type,
                                 cast(-1 as int)                 as action_type,
                                 'cdp_action'                    as metric,
                                 cast(0 as bigint)               as event_cnt,
                                 cast(0 as bigint)               as page_cnt,
                                 count(distinct cdpa.id_session) as session_cnt
                          from _cdp_action cdpa
                          group by cdpa.groups,
                                   cdpa.date_diff,
                                   cdpa.is_returned,
                                   cdpa.is_mobile,
                                   cdpa.session_create_page_type,
                                   cdpa.channel_1,
                                   cdpa.channel_2,
                                   cdpa.user_dtd_lifetime
                          union
                          select fea.groups,
                                 fea.date_diff,
                                 fea.is_returned,
                                 fea.is_mobile,
                                 fea.session_create_page_type,
                                 fea.channel_1,
                                 fea.channel_2,
                                 fea.user_dtd_lifetime,
                                 cast(null as int)                          as entity_type,
                                 fea.action_type                            as action_type,
                                 'feature_action'                           as metric,
                                 count(distinct fea.id_sess_feature_action) as event_cnt,
                                 count(distinct fea.id_sess_feature)        as page_cnt,
                                 count(distinct fea.id_session)             as session_cnt
                          from _feature_action fea
                          group by fea.groups,
                                   fea.date_diff,
                                   fea.is_returned,
                                   fea.is_mobile,
                                   fea.session_create_page_type,
                                   fea.channel_1,
                                   fea.channel_2,
                                   fea.user_dtd_lifetime,
                                   fea.action_type
                          union
                          select fea.groups,
                                 fea.date_diff,
                                 fea.is_returned,
                                 fea.is_mobile,
                                 fea.session_create_page_type,
                                 fea.channel_1,
                                 fea.channel_2,
                                 fea.user_dtd_lifetime,
                                 cast(null as int)              as entity_type,
                                 cast(-1 as int)                as action_type,
                                 'feature_action'               as metric,
                                 cast(0 as bigint)              as event_cnt,
                                 cast(0 as bigint)              as page_cnt,
                                 count(distinct fea.id_session) as session_cnt
                          from _feature_action fea
                          group by fea.groups,
                                   fea.date_diff,
                                   fea.is_returned,
                                   fea.is_mobile,
                                   fea.session_create_page_type,
                                   fea.channel_1,
                                   fea.channel_2,
                                   fea.user_dtd_lifetime,
                                   fea.action_type
                          union
                          select fla.groups,
                                 fla.date_diff,
                                 fla.is_returned,
                                 fla.is_mobile,
                                 fla.session_create_page_type,
                                 fla.channel_1,
                                 fla.channel_2,
                                 fla.user_dtd_lifetime,
                                 fla.entity_type,
                                 fla.action_type                           as action_type,
                                 'filter_action'                           as metric,
                                 count(distinct fla.id_sess_filter_action) as event_cnt,
                                 count(distinct fla.id_sess_filter)        as page_cnt,
                                 count(distinct fla.id_session)            as session_cnt
                          from _filter_action fla
                          group by fla.groups,
                                   fla.date_diff,
                                   fla.is_returned,
                                   fla.is_mobile,
                                   fla.session_create_page_type,
                                   fla.channel_1,
                                   fla.channel_2,
                                   fla.user_dtd_lifetime,
                                   fla.entity_type,
                                   fla.action_type
                          union
                          select fla.groups,
                                 fla.date_diff,
                                 fla.is_returned,
                                 fla.is_mobile,
                                 fla.session_create_page_type,
                                 fla.channel_1,
                                 fla.channel_2,
                                 fla.user_dtd_lifetime,
                                 fla.entity_type,
                                 cast(-1 as int)                as action_type,
                                 'filter_action'                as metric,
                                 cast(0 as bigint)              as event_cnt,
                                 cast(0 as bigint)              as page_cnt,
                                 count(distinct fla.id_session) as session_cnt
                          from _filter_action fla
                          group by fla.groups,
                                   fla.date_diff,
                                   fla.is_returned,
                                   fla.is_mobile,
                                   fla.session_create_page_type,
                                   fla.channel_1,
                                   fla.channel_2,
                                   fla.user_dtd_lifetime,
                                   fla.entity_type
                          union
                          select jdpa.groups,
                                 jdpa.date_diff,
                                 jdpa.is_returned,
                                 jdpa.is_mobile,
                                 jdpa.session_create_page_type,
                                 jdpa.channel_1,
                                 jdpa.channel_2,
                                 jdpa.user_dtd_lifetime,
                                 cast(null as int)                       as entity_type,
                                 jdpa.action_type                        as action_type,
                                 'jdp_action'                            as metric,
                                 count(distinct jdpa.id_sess_jdp_action) as event_cnt,
                                 count(distinct jdpa.id_sess_jdp)        as page_cnt,
                                 count(distinct jdpa.id_session)         as session_cnt
                          from _jdp_action jdpa
                          group by jdpa.groups,
                                   jdpa.date_diff,
                                   jdpa.is_returned,
                                   jdpa.is_mobile,
                                   jdpa.session_create_page_type,
                                   jdpa.channel_1,
                                   jdpa.channel_2,
                                   jdpa.user_dtd_lifetime,
                                   jdpa.action_type
                          union
                          select jdpa.groups,
                                 jdpa.date_diff,
                                 jdpa.is_returned,
                                 jdpa.is_mobile,
                                 jdpa.session_create_page_type,
                                 jdpa.channel_1,
                                 jdpa.channel_2,
                                 jdpa.user_dtd_lifetime,
                                 cast(null as int)               as entity_type,
                                 cast(-1 as int)                 as action_type,
                                 'jdp_action'                    as metric,
                                 cast(0 as bigint)               as event_cnt,
                                 cast(0 as bigint)               as page_cnt,
                                 count(distinct jdpa.id_session) as session_cnt
                          from _jdp_action jdpa
                          group by jdpa.groups,
                                   jdpa.date_diff,
                                   jdpa.is_returned,
                                   jdpa.is_mobile,
                                   jdpa.session_create_page_type,
                                   jdpa.channel_1,
                                   jdpa.channel_2,
                                   jdpa.user_dtd_lifetime
                          union
                          select pra.groups,
                                 pra.date_diff,
                                 pra.is_returned,
                                 pra.is_mobile,
                                 pra.session_create_page_type,
                                 pra.channel_1,
                                 pra.channel_2,
                                 pra.user_dtd_lifetime,
                                 cast(null as int)                          as entity_type,
                                 pra.action_type                            as action_type,
                                 'profile_action'                           as metric,
                                 count(distinct pra.id_sess_profile_action) as event_cnt,
                                 count(distinct pra.id_sess_profile)        as page_cnt,
                                 count(distinct pra.id_session)             as session_cnt
                          from _profile_action pra
                          group by pra.groups,
                                   pra.date_diff,
                                   pra.is_returned,
                                   pra.is_mobile,
                                   pra.session_create_page_type,
                                   pra.channel_1,
                                   pra.channel_2,
                                   pra.user_dtd_lifetime,
                                   pra.action_type
                          union
                          select pra.groups,
                                 pra.date_diff,
                                 pra.is_returned,
                                 pra.is_mobile,
                                 pra.session_create_page_type,
                                 pra.channel_1,
                                 pra.channel_2,
                                 pra.user_dtd_lifetime,
                                 cast(null as int)              as entity_type,
                                 cast(-1 as int)                as action_type,
                                 'profile_action'               as metric,
                                 cast(0 as bigint)              as event_cnt,
                                 cast(0 as bigint)              as page_cnt,
                                 count(distinct pra.id_session) as session_cnt
                          from _profile_action pra
                          group by pra.groups,
                                   pra.date_diff,
                                   pra.is_returned,
                                   pra.is_mobile,
                                   pra.session_create_page_type,
                                   pra.channel_1,
                                   pra.channel_2,
                                   pra.user_dtd_lifetime
                          union
                          select nca.groups,
                                 nca.date_diff,
                                 nca.is_returned,
                                 nca.is_mobile,
                                 nca.session_create_page_type,
                                 nca.channel_1,
                                 nca.channel_2,
                                 nca.user_dtd_lifetime,
                                 nca.entity_type,
                                 nca.action_type                                        as action_type,
                                 'notification_center_action'                           as metric,
                                 count(distinct nca.id_sess_notification_center_action) as event_cnt,
                                 cast(0 as bigint)                                      as page_cnt,
                                 count(distinct nca.id_session)                         as session_cnt
                          from _notification_center_action nca
                          group by nca.groups,
                                   nca.date_diff,
                                   nca.is_returned,
                                   nca.is_mobile,
                                   nca.session_create_page_type,
                                   nca.channel_1,
                                   nca.channel_2,
                                   nca.user_dtd_lifetime,
                                   nca.entity_type,
                                   nca.action_type
                          union
                          select nca.groups,
                                 nca.date_diff,
                                 nca.is_returned,
                                 nca.is_mobile,
                                 nca.session_create_page_type,
                                 nca.channel_1,
                                 nca.channel_2,
                                 nca.user_dtd_lifetime,
                                 nca.entity_type,
                                 cast(-1 as int)                as action_type,
                                 'notification_center_action'   as metric,
                                 cast(0 as bigint)              as event_cnt,
                                 cast(0 as bigint)              as page_cnt,
                                 count(distinct nca.id_session) as session_cnt
                          from _notification_center_action nca
                          group by nca.groups,
                                   nca.date_diff,
                                   nca.is_returned,
                                   nca.is_mobile,
                                   nca.session_create_page_type,
                                   nca.channel_1,
                                   nca.channel_2,
                                   nca.user_dtd_lifetime,
                                   nca.entity_type)
    select _country_id as country,
           au.groups::varchar,
           au.date_diff::integer,
           au.is_returned::integer,
           au.is_mobile::integer,
           au.session_create_page_type::integer,
           au.channel_1::varchar,
           au.channel_2::varchar,
           au.user_dtd_lifetime::integer,
           au.entity_type::integer,
           au.action_type::integer,
           au.metric::varchar,
           au.event_cnt::integer,
           au.page_cnt::integer,
           au.session_cnt::integer
    from action_union au;
