       -- link_auction temp tables
        create temp table temp_auction_campaign as
        select * from link_auction.campaign;

        create temp table temp_info_curency as
        select * from link_dbo.info_currency;

        create temp table temp_auction_user as
        select * from link_auction.user;

        create temp table temp_auction_site as
        select * from link_auction.site;

        create temp table temp_conversion_away_connection as
        select * from link_auction.conversion_away_connection;

        create index idx_temp_conversion_away_connection on temp_conversion_away_connection (id);
        analyse temp_conversion_away_connection;

        create temp table temp_conversion as
        select * from link_auction.conversion;

        create index idx_temp_conversion on temp_conversion (id);
        analyse temp_conversion;
        -- /link_auction temp tables


        -- link_dbo temp tables
        create temp table temp_project_tracking_setting as
        select * from link_dbo.project_tracking_setting;

        create temp table temp_info_project as
        select * from link_dbo.info_project;
        -- /link_dbo temp tables


        -- public temp tables
        create temp table temp_session_impression_recommend as
        select * from public.session_impression_recommend
        where date_diff between _start_date_int - 6 and _start_date_int;

        create index idx_session_impression_recommend on temp_session_impression_recommend (id);
        analyse temp_session_impression_recommend;
        -- /public temp tables


        -----
        create temp table tmp_session_recommendation_info as
        --rec statistics
        select s_rec.date :: date as                                             action_date
             , _country_id        as                                             country_id
             , s_rec.type         as                                             recommendation_type_id
             , case
                   when s.flags & 16 = 16 then 1
                   when s.flags & 64 = 64 or s.flags & 128 = 128 then 2
                   else 0 end                                                    device_id
             , sign(s.flags & 2)                                                 is_returned
             , s.id_current_traf_source
             , (case when s_acc.id_account is not null then true else false end) is_account
             , max('default')     as                                             position
             , max('no clicks')   as                                             job_destination
             , count(distinct s_rec.id)                                          recommends
             , count(distinct s_irec.id)                                         impression_recommend
             , count(distinct s_irec_on_screen.id_impression)                    impression_on_screen_recommend
             , count(distinct s.id)                                              sessions
             , null                                                              clicks
             , null                                                              aways
             , null                                                              conversion_aways
             , null                                                              click_price_usd
             , null                                                              click_price_usd_conversions
             , 0                                                                 conversions
        from public.session_recommend s_rec
                 join public.session s on s_rec.id_session = s.id
            and s_rec.date_diff = s.date_diff
            and s.flags & 1 = 0 /*session bot*/
                 join temp_session_impression_recommend s_irec
                      on s_rec.id = s_irec.id_recommend
                          and s_rec.id_session = s_irec.id_session
                          and s_irec.date_diff = s_rec.date_diff
                 left join public.session_impression_recommend_on_screen s_irec_on_screen
                           on s_irec.id = s_irec_on_screen.id_impression
                               and s_irec.date_diff = s_irec_on_screen.date_diff
                               and s_irec_on_screen.date_diff = s_rec.date_diff
                 left join public.session_account s_acc on s_acc.id_session = s.id and s_acc.date_diff = s.date_diff
        where s_rec.date_diff = _start_date_int
        group by s_rec.date :: date
               , country_id
               , s_rec.type
               , device_id
               , is_returned
               , id_current_traf_source
               , is_account

        union all
        --click stat
        select s_rec.date :: date      as                                                       action_date
             , _country_id             as                                                       country_id
             , s_rec.type              as                                                       recommendation_type_id
             , case
                   when s.flags & 16 = 16 then 1
                   when s.flags & 64 = 64 or s.flags & 128 = 128 then 2
                   else 0 end                                                                   device_id
             , sign(s.flags & 2)                                                                is_returned
             , s.id_current_traf_source
             , (case when s_acc.id_account is not null then true else false end)                is_account
             , (s_irec.position)::text as                                                       position
             , case
                   when sc.job_destination = 1 then 'away'
                   when sc.job_destination = 2 then 'jdp_away'
                   when sc.job_destination = 3 then 'jdp_with_easy_apply'
                   when sc.job_destination = 4 then 'jdp_with_mapped_apply'
                   else 'non-define' end                                                        as job_destination
            /*, case
                                    when s.ip_cc = lower(substring(db_name(), 5, 2))
                                        or s.ip_cc = 'gb' and lower(substring(db_name(), 5, 2)) = 'uk'
                                        then 1
                                    else 0 end                          as is_local*/
             , null                                                                             recommends
             , null                                                                             impression_recommend
             , null                                                                             impression_on_screen_recommend
             , null                                                                             sessions
             , count(distinct sc.id)                                                            clicks
             , count(distinct sa.id)                                                            aways
             , count(distinct case when pts.id is not null then sa.id end) conversion_aways
             , sum(case
                       when (au.flags & 2 = 0) then sc.click_price * ic.value_to_usd
                       when (au.flags & 2 = 2) then sa.click_price * ic.value_to_usd
            end)                                                                                click_price_usd
              , sum(case
                       when (au.flags & 2 = 0) and pts.id is not null then sc.click_price * ic.value_to_usd
                       when (au.flags & 2 = 2) and pts.id is not null then sa.click_price * ic.value_to_usd
            end)                                                                                click_price_usd_conversions
             , 0                                                                                conversions
        from session_recommend s_rec
                 join session s on s_rec.id_session = s.id
            and s_rec.date_diff = s.date_diff
            and s.flags & 1 = 0 /*session bot*/
                 join temp_session_impression_recommend s_irec
                      on s_rec.id = s_irec.id_recommend
                          and s_rec.id_session = s_irec.id_session
                          and s_irec.date_diff = s_rec.date_diff
                 left join session_impression_recommend_on_screen s_irec_on_screen
                           on s_irec.id = s_irec_on_screen.id_impression
                               and s_irec.date_diff = s_irec_on_screen.date_diff
                               and s_irec_on_screen.date_diff = s_rec.date_diff
                 left join session_account s_acc on s_acc.id_session = s.id and s_acc.date_diff = s.date_diff
               join session_click sc
                      on s_rec.date_diff = sc.date_diff
                          and sc.id_session = s_rec.id_session
                          and sc.id_recommend = s_rec.id
                          and sc.id_impression_recommend = s_irec.id
                          and coalesce(sc.flags, 0) & 16 = 0
                          and coalesce(sc.flags, 0) & 4096 = 0
                 left join session_away sa
                           on sa.date_diff = s_rec.date_diff
                                and sa.id_session = sc.id_session
                                and sa.id_click = sc.id
                                and coalesce(sa.flags, 0) & 2 = 0
                                and coalesce(sa.flags, 0) & 512 = 0 --remove duplicates
                 left join temp_auction_campaign ac
                           on ac.id = sc.id_campaign --sa.id_campaign
                 left join temp_auction_site ast
                           on ac.id_site = ast.id
                 left join temp_auction_user au
                           on au.id = ast.id_user
                 left join temp_info_project ip
                           on sc.id_project = ip.id
                               and coalesce(lower(ip.name), '') not like 'j-vers.%'
                 left join temp_info_curency ic on ic.id = sc.id_currency
                 left join temp_project_tracking_setting pts on pts.id_project = ip.id
        where s_rec.date_diff = _start_date_int
        group by s_rec.date :: date
               , country_id
               , s_rec.type
               , device_id
               , is_returned
               , id_current_traf_source
               , is_account
               , s_irec.position
               , sc.job_destination

        union all
        --conversions:
        select sa.date :: date         as                                        action_date
             , _country_id             as                                        country_id
             , s_rec.type              as                                        recommendation_type_id
             , case
                   when s.flags & 16 = 16 then 1
                   when s.flags & 64 = 64 or s.flags & 128 = 128 then 2
                   else 0 end                                                    device_id
             , sign(s.flags & 2)                                                 is_returned
             , s.id_current_traf_source
             , (case when s_acc.id_account is not null then true else false end) is_account
             , (s_irec.position)::text as                                        position
             , case
                   when sc.job_destination is null then 'no destination'
                   when sc.job_destination = 1 then 'away'
                   when sc.job_destination = 2 then 'jdp_away'
                   when sc.job_destination = 3 then 'jdp_with_easy_apply'
                   when sc.job_destination = 4 then 'jdp_with_mapped_apply'
                   else 'non-define' end                                         as job_destination
             , null                                                              recommends
             , null                                                              impression_recommend
             , null                                                              impression_on_screen_recommend
             , null                                                              sessions
             , null                                                              clicks
             , null                                                              aways
             , null                                                              conversion_aways
             , null                                                              click_price_usd
             , null                                                              click_price_usd_conversions
             , count(distinct cac.id_session_away)                               conversions
        from temp_conversion c
                 join temp_conversion_away_connection cac on c.id = cac.id_conversion
                 join session_away sa
                      on cac.date_diff = sa.date_diff
                          and cac.id_session_away = sa.id
                          and coalesce(sa.flags, 0) & 2 = 0
                 join session s on sa.id_session = s.id
            and sa.date_diff = s.date_diff and s.flags & 1 = 0
                 left join session_account s_acc on s_acc.id_session = s.id and s_acc.date_diff = s.date_diff
                 join session_click sc
                      on sc.date_diff = sa.date_diff
                          and sc.id_session = sa.id_session
                          and coalesce(sc.flags, 0) & 16 = 0
                          and sa.id_click = sc.id
                          and (sc.id_recommend is not null)
                 join session_recommend s_rec
                      on sc.date_diff = s_rec.date_diff
                          and s_rec.id_session = sc.id_session
                          and sc.id_recommend = s_rec.id
                 join temp_session_impression_recommend s_irec
                      on s_rec.id = s_irec.id_recommend
                          and s_rec.id_session = s_irec.id_session
                          and sc.id_impression_recommend = s_irec.id
        where c.date :: date = _start_date_date
        group by sa.date :: date
               , country_id
               , s_rec.type
               , device_id
               , is_returned
               , id_current_traf_source
               , is_account
               , s_irec.position
               , sc.job_destination
            ;
        -----

        --
        delete from an.rpl_recommendations_agg
        where action_date = _start_date_date;
        --

        insert into an.rpl_recommendations_agg
        select action_date
              , country_id
              , recommendation_type_id
              , device_id
              , is_returned
              , id_current_traf_source
              , is_account
              , position
              , job_destination
              , sum(recommends)                     as recommends
              , sum(impression_recommend)           as impression_recommend
              , sum(impression_on_screen_recommend) as impression_on_screen_recommend
              , sum(sessions)                       as sessions
              , sum(clicks)                         as clicks
              , sum(aways)                          as aways
              , sum(conversion_aways)               as conversion_aways
              , sum(click_price_usd)                as click_price_usd
              , sum(click_price_usd_conversions)    as click_price_usd_conversions
              , sum(conversions)                    as conversions
        from tmp_session_recommendation_info
        group by action_date
               , country_id
               , recommendation_type_id
               , device_id
               , is_returned
               , id_current_traf_source
               , is_account
               , position
               , job_destination
        ;
