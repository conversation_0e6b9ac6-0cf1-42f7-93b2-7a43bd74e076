with
    t as (
        select
            country,
            id_cabinet,
            id_project,
            datetime,
            action,
            replace(replace(replace(action, 'Change Sales on ', ''), '@jooble.com', ''), '@jooble.jobs', '') +
            '@jooble.com'                                                                      as email,
            row_number() over (partition by country, id_cabinet, id_project order by datetime) as row_num
        from [dbo].[auction_cabinet_log] with (nolock)
        where action like '%Change Sales on %'
    )
select
    country,
    id_cabinet             as id_user,
    id_project,
    cast(datetime as date) as manager_set_date,
    u.name                 as manager_name,
    u.email                as manager_email,
    u.alias                as manager_alias
from t
     left join [user] u with (nolock)
               on u.email = t.email
where row_num = 1
  and alias is not null;
