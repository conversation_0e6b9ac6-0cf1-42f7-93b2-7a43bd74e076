SELECT
    -- general
    ac.id as id_campaign,
    ac.id_project,
    ac.id_site,
    
    -- campaign settings
    ac.click_price as campaign_click_price,
    ac.budget as campaign_budget,
    campaign_status,
    ac.flags as campaign_flags,
    ac.is_price_per_job as campaign_is_price_per_job,
    ac.daily_budget as campaign_daily_budget,
    ac.action_price as campaign_action_price,
    ac.conversion_rate as campaign_conversion_rate,
    ac.force_jdp_percent as campaign_force_jdp_percent,

    --site settings
    ast.url as site_name,
    ast.min_click_price as site_min_click_price,
    ast.max_click_price as site_max_click_price,
    ast.mc_flags as site_mc_flags,
    ast.site_status,
    ast.is_price_per_job as site_is_price_per_job,
    ast.def_conversion_rate as site_def_conversion_rate,
    ast.def_force_jdp_percent as site_def_force_jdp_percent,

    -- user settings
    au.company as user_company,
    au.currency as user_currency,
    au.flags as user_flags,
    au.budget as user_budget,
    au.daily_budget as user_daily_budget,
    au.unique_click_period as user_unique_click_period,
    cast(getdate() as datetime) as load_date_time

FROM [auction].[campaign] ac WITH (NOLOCK)
         INNER JOIN [auction].[site] ast WITH (NOLOCK) ON ac.id_site = ast.id
         INNER JOIN [auction].[user] au WITH (NOLOCK) ON au.id = ast.id_user
;
