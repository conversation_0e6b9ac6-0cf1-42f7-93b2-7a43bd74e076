CREATE OR REPLACE PROCEDURE aggregation.insert_ga_organic_agg(IN _date_start date)
    LANGUAGE plpgsql
AS
$$

BEGIN

    --===========
    DELETE
    FROM aggregation.ga_organic_agg
    WHERE date = _date_start;
    --===========

    INSERT INTO aggregation.ga_organic_agg
    SELECT ga.country_code                  AS country_cc,
           cc.name_country_eng              AS country,
           ga.date,
           DATE_PART('year'::text, ga.date) AS year,
           CASE
               WHEN ga.sourcemedium::text = 'google / organic'::text THEN 'google / organic'::text
               WHEN ga.sourcemedium::text = 'google_jobs_apply / organic'::text THEN 'google / jobs apply / organic'::text
               WHEN ga.sourcemedium::text = 'google_jobs_salary / organic'::text THEN 'google / jobs salary / organic'::text
               WHEN ga.sourcemedium::text = 'bing / organic'::text THEN 'bing / organic'::text
               WHEN ga.sourcemedium::text = 'yahoo / organic'::text THEN 'yahoo / organic'::text
               WHEN ga.sourcemedium::text = 'ecosia.org / organic'::text THEN 'ecosia.org / organic'::text
               WHEN ga.sourcemedium::text = 'duckduckgo / organic'::text THEN 'duckduckgo / organic'::text
               ELSE 'other / organic'::text
           END                              AS sourcemedium,
           SUM(ga.session_cnt)              AS session_cnt
    FROM aggregation.v_ga_session_data ga
             LEFT JOIN dimension.countries cc
                       ON LOWER(cc.alpha_2::text) = ga.country_code
    WHERE ga.date = _date_start
      AND ga.channelgrouping::text = 'Organic Search'::text
    GROUP BY ga.country_code,
             cc.name_country_eng,
             ga.date,
             (DATE_PART('year'::text, ga.date)),
             CASE
                 WHEN ga.sourcemedium::text = 'google / organic'::text THEN 'google / organic'::text
                 WHEN ga.sourcemedium::text = 'google_jobs_apply / organic'::text THEN 'google / jobs apply / organic'::text
                 WHEN ga.sourcemedium::text = 'google_jobs_salary / organic'::text THEN 'google / jobs salary / organic'::text
                 WHEN ga.sourcemedium::text = 'bing / organic'::text THEN 'bing / organic'::text
                 WHEN ga.sourcemedium::text = 'yahoo / organic'::text THEN 'yahoo / organic'::text
                 WHEN ga.sourcemedium::text = 'ecosia.org / organic'::text THEN 'ecosia.org / organic'::text
                 WHEN ga.sourcemedium::text = 'duckduckgo / organic'::text THEN 'duckduckgo / organic'::text
                 ELSE 'other / organic'::text
             END;


END;
$$;
