    CREATE TEMP TABLE temp_u_traffic_source AS
    SELECT id, name, is_paid, channel
    FROM link_dbo.u_traffic_source;
    ANALYZE temp_u_traffic_source;


    CREATE TEMP TABLE temp_info_project AS
    SELECT id, name
    FROM link_dbo.info_project
    WHERE COALESCE(LOWER(name), '') NOT LIKE 'j-vers.%';
    CREATE INDEX idx_temp_info_project ON temp_info_project (id);
    ANALYZE temp_info_project;


    CREATE TEMP TABLE temp_info_currency AS
    SELECT id, name, value_to_usd
    FROM link_dbo.info_currency;
    CREATE INDEX idx_temp_info_currency ON temp_info_currency (id);
    ANALYZE temp_info_currency;


    CREATE TEMP TABLE temp_conversion_away_connection AS
    SELECT id_session_away,
           COUNT(id_conversion) AS all_conversion
    FROM link_auction.conversion_away_connection
    GROUP BY id_session_away;
    CREATE INDEX idx_temp_conversion_away_connection ON temp_conversion_away_connection (id_session_away);
    ANALYZE temp_conversion_away_connection;


    CREATE TEMP TABLE temp_auction_campaign AS
    SELECT id, id_site, name
    FROM link_auction.campaign_raw;
    CREATE INDEX idx_temp_campaign_raw ON temp_auction_campaign (id, id_site);
    ANALYZE temp_auction_campaign;


    CREATE TEMP TABLE temp_auction_site AS
    SELECT id, id_user
    FROM link_auction.site;
    CREATE INDEX idx_temp_site ON temp_auction_site (id, id_user);
    ANALYZE temp_auction_site;


    CREATE TEMP TABLE temp_auction_user AS
    SELECT id, flags
    FROM link_auction.user_raw;
    ANALYZE temp_auction_user;


    CREATE TEMP TABLE temp_session AS
    SELECT id, date_diff, id_current_traf_source, id_traf_source, flags, ip_cc, session_create_page_type
    FROM public.session
    WHERE date_diff = _start_datediff;
    CREATE INDEX idx_temp_session_id ON temp_session (id);
    ANALYZE temp_session;


    CREATE TEMP TABLE temp_session_jdp_action AS
    SELECT id, date_diff, id_jdp
    FROM public.session_jdp_action
    WHERE date_diff = _start_datediff;
    CREATE INDEX idx_temp_session_jdp_action_id_jdp ON temp_session_jdp_action (id_jdp);
    ANALYZE temp_session_jdp_action;


    CREATE TEMP TABLE temp_session_apply AS
    SELECT id, date_diff, id_src_jdp_action
    FROM public.session_apply
    WHERE date_diff = _start_datediff;
    ANALYZE temp_session_apply;


    CREATE TEMP TABLE temp_session_click AS
    SELECT id, date_diff, id_campaign, id_currency
    FROM public.session_click
    WHERE date_diff = _start_datediff;
    CREATE INDEX idx_temp_session_click_id ON temp_session_click (id);
    ANALYZE temp_session_click;


    CREATE TEMP TABLE temp_results AS
    SELECT 'aways'                                     AS metric,
           DATE '1900-01-01' + sa.date_diff            AS session_date,
           sa.id_project                               AS project_id,
           ip.name                                     AS project_name,
           ac.name                                     AS campaign_name,
           CASE
               WHEN sa.id_jdp IS NOT NULL THEN 'Jdp'
               WHEN sa.letter_type IS NOT NULL THEN CONCAT('Letter Type ', letter_type)
               WHEN sa.id_click IS NOT NULL THEN 'Click'
               WHEN sa.id_click_no_serp IS NOT NULL THEN 'No serp'
               ELSE 'Other'
           END                                         AS away_type,
           id_campaign                                 AS campaign_id,
           CASE
               WHEN s.flags & 16 = 16 THEN 1
               WHEN s.flags & 64 = 64 OR s.flags & 128 = 128 THEN 2
               ELSE 0
           END                                         AS is_mobile,
           uts.name                                    AS traffic_name,
           uts.is_paid                                 AS traffic_is_paid,
           uts.channel,
           s.ip_cc,
           ic.name,
           0                                           AS away_revenue,
           0                                           AS away_revenue_origin_currency,
           COUNT(sa.id)                                AS aways,
           COUNT(cac.id_session_away)                  AS conversions,
           s.id_current_traf_source,
           s.session_create_page_type,
           CASE WHEN s.flags & 2 = 2 THEN 1 ELSE 0 END AS is_returned,
           COALESCE(jh.id_category, j.id_category)     AS job_category_id,
           SUM(cac.all_conversion)                     AS all_conversion
    FROM public.session_away sa
             LEFT JOIN temp_info_project ip
                       ON sa.id_project = ip.id
             LEFT JOIN temp_session s
                       ON sa.id_session = s.id
                           AND sa.date_diff = s.date_diff
             LEFT JOIN temp_u_traffic_source uts
                       ON s.id_traf_source = uts.id
             LEFT JOIN temp_conversion_away_connection cac
                       ON cac.id_session_away = sa.id
             LEFT JOIN temp_auction_campaign ac
                       ON sa.id_campaign = ac.id
             LEFT JOIN temp_info_currency ic
                       ON sa.id_currency = ic.id
             LEFT JOIN an.snap_job j
                       ON sa.id_job = j.id
             LEFT JOIN an.snap_job_history jh
                       ON sa.uid_job = jh.uid
    WHERE sa.date_diff = _start_datediff
      AND s.flags & 1 != 1
      AND (COALESCE(sa.flags, 0) & 2) = 0
      AND sa.flags & 512 = 0
    GROUP BY session_date,
             ip.name,
             ac.name,
             away_type,
             sa.id_project,
             uts.name,
             uts.is_paid,
             is_mobile,
             uts.channel,
             s.ip_cc,
             ic.name,
             id_campaign,
             s.id_current_traf_source,
             s.session_create_page_type,
             is_returned,
             job_category_id

    UNION ALL

    SELECT CASE WHEN is_apply = 0 THEN 'aways' ELSE 'applies' END AS metric,
           DATE '1900-01-01' + date_diff                          AS session_date,
           id_project                                             AS project_id,
           project_name,
           campaign_name,
           away_type,
           id_campaign                                            AS campaign_id,
           is_mobile,
           traffic_name,
           traffic_is_paid,
           channel,
           ip_cc,
           name,
           SUM(away_revenue)                                      AS away_revenue,
           SUM(away_revenue_origin_currency)                      AS away_revenue_origin_currency,
           NULL                                                   AS aways,
           NULL                                                   AS conversions,
           id_current_traf_source,
           session_create_page_type,
           is_returned,
           id_job_category                                        AS job_category_id,
           NULL                                                   AS all_conversion
    FROM (SELECT sa.date_diff,
                 sa.id_project,
                 ip.name                                       AS project_name,
                 ac.name                                       AS campaign_name,
                 CASE
                     WHEN sa.id_jdp IS NOT NULL THEN 'Jdp'
                     WHEN sa.letter_type IS NOT NULL THEN CONCAT('Letter Type ', sa.letter_type)
                     WHEN sa.id_click IS NOT NULL THEN 'Click'
                     WHEN sa.id_click_no_serp IS NOT NULL THEN 'No serp'
                     ELSE 'Other'
                 END                                           AS away_type,
                 sa.id_campaign,
                 CASE
                     WHEN s.flags & 16 = 16 THEN 1
                     WHEN s.flags & 64 = 64 OR s.flags & 128 = 128 THEN 2
                     ELSE 0
                 END                                           AS is_mobile,
                 uts.name                                      AS traffic_name,
                 uts.is_paid                                   AS traffic_is_paid,
                 uts.channel,
                 s.ip_cc,
                 ic.name,
                 COALESCE(sa.click_price, 0) * ic.value_to_usd AS away_revenue,
                 COALESCE(sa.click_price, 0)                   AS away_revenue_origin_currency,
                 s.id_current_traf_source,
                 s.session_create_page_type,
                 CASE WHEN s.flags & 2 = 2 THEN 1 ELSE 0 END   AS is_returned,
                 0                                             AS is_apply,
                 COALESCE(jh.id_category, j.id_category)       AS id_job_category
          FROM public.session_away sa
                   JOIN temp_session s
                        ON sa.date_diff = s.date_diff
                            AND sa.id_session = s.id
                   JOIN temp_info_currency ic
                        ON ic.id = sa.id_currency
                   LEFT JOIN temp_auction_campaign ac
                             ON ac.id = sa.id_campaign
                   LEFT JOIN temp_auction_site ast
                             ON ac.id_site = ast.id
                   LEFT JOIN temp_auction_user au
                             ON au.id = ast.id_user
              -- serp -> away
                   LEFT JOIN public.session_click sc
                             ON sc.date_diff = sa.date_diff
                                 AND sc.id = sa.id_click
              -- serp -> jdp -> away
                   LEFT JOIN public.session_jdp sj
                             ON sj.date_diff = sa.date_diff
                                 AND sj.id = sa.id_jdp
                   LEFT JOIN public.session_click scj
                             ON scj.date_diff = sj.date_diff
                                 AND scj.id = sj.id_click
                   LEFT JOIN temp_info_project ip
                             ON sa.id_project = ip.id
                   LEFT JOIN temp_u_traffic_source uts
                             ON s.id_traf_source = uts.id
                   LEFT JOIN an.snap_job j
                             ON sa.id_job = j.id
                   LEFT JOIN an.snap_job_history jh
                             ON sa.uid_job = jh.uid
          WHERE sa.date_diff = _start_datediff
            AND (COALESCE(s.flags, 0) & 1) = 0
            AND (sa.id_campaign = 0 OR au.flags & 2 = 0)
            AND (COALESCE(sa.flags, 0) & 2) = 0
            AND sa.flags & 512 = 0

          UNION ALL

          SELECT sc.date_diff,
                 sc.id_project,
                 ip.name                                            AS project_name,
                 ac.name                                            AS campaign_name,
                 CASE
                     WHEN COALESCE(sj.id, sa.id_jdp) IS NOT NULL THEN 'Jdp'
                     WHEN COALESCE(sa.letter_type, sj.letter_type) IS NOT NULL
                         THEN CONCAT('Letter Type ', COALESCE(sa.letter_type, sj.letter_type))
                     WHEN sa.id_click IS NOT NULL THEN 'Click'
                     WHEN COALESCE(sa.id_click_no_serp, sj.id_click_no_serp) IS NOT NULL THEN 'No serp'
                     ELSE 'Other'
                 END                                                AS away_type,
                 sc.id_campaign,
                 CASE
                     WHEN s.flags & 16 = 16 THEN 1
                     WHEN s.flags & 64 = 64 OR s.flags & 128 = 128 THEN 2
                     ELSE 0
                 END                                                AS is_mobile,
                 uts.name                                           AS traffic_name,
                 uts.is_paid                                        AS traffic_is_paid,
                 uts.channel,
                 s.ip_cc,
                 ic.name,
                 COALESCE(sc.click_price, 0) * ic.value_to_usd      AS away_revenue,
                 COALESCE(sc.click_price, 0)                        AS away_revenue_origin_currency,
                 s.id_current_traf_source,
                 s.session_create_page_type,
                 CASE WHEN s.flags & 2 = 2 THEN 1 ELSE 0 END        AS is_returned,
                 CASE WHEN sc.job_destination = 3 THEN 1 ELSE 0 END AS is_apply,
                 COALESCE(jh.id_category, j.id_category)            AS id_job_category
          FROM public.session_click sc
                   JOIN temp_session s
                        ON sc.date_diff = s.date_diff
                            AND sc.id_session = s.id
                   JOIN temp_info_currency ic
                        ON ic.id = sc.id_currency
                   LEFT JOIN temp_auction_campaign ac
                             ON ac.id = sc.id_campaign
                   LEFT JOIN temp_auction_site ast
                             ON ac.id_site = ast.id
                   LEFT JOIN temp_auction_user au
                             ON au.id = ast.id_user
                   LEFT JOIN public.session_away sa
                             ON sc.date_diff = sa.date_diff
                                 AND sc.id = sa.id_click
                   LEFT JOIN public.session_jdp sj
                             ON sj.date_diff = sa.date_diff
                                 AND sj.id = sa.id_jdp
                   LEFT JOIN temp_info_project ip
                             ON sc.id_project = ip.id
                   LEFT JOIN temp_u_traffic_source uts
                             ON s.id_traf_source = uts.id
                   LEFT JOIN an.snap_job j
                             ON sc.id_job = j.id
                   LEFT JOIN an.snap_job_history jh
                             ON sc.uid_job = jh.uid
          WHERE sc.date_diff = _start_datediff
            AND (COALESCE(s.flags, 0) & 1) = 0
            AND (sc.id_campaign = 0 OR au.flags & 2 = 2)
            AND (COALESCE(sc.flags, 0) & 16) = 0
            AND sc.flags & 4096 = 0

          UNION ALL

          SELECT scns.date_diff,
                 scns.id_project,
                 ip.name                                              AS project_name,
                 ac.name                                              AS campaign_name,
                 CASE
                     WHEN scns.letter_type IS NOT NULL THEN CONCAT('Letter Type ', scns.letter_type)
                     ELSE 'No serp'
                 END                                                  AS away_type,
                 scns.id_campaign,
                 CASE
                     WHEN s.flags & 16 = 16 THEN 1
                     WHEN s.flags & 64 = 64 OR s.flags & 128 = 128 THEN 2
                     ELSE 0
                 END                                                  AS is_mobile,
                 uts.name                                             AS traffic_name,
                 uts.is_paid                                          AS traffic_is_paid,
                 uts.channel,
                 s.ip_cc,
                 ic.name,
                 COALESCE(scns.click_price, 0) * ic.value_to_usd      AS away_revenue,
                 COALESCE(scns.click_price, 0)                        AS away_revenue_origin_currency,
                 s.id_current_traf_source,
                 s.session_create_page_type,
                 CASE WHEN s.flags & 2 = 2 THEN 1 ELSE 0 END          AS is_returned,
                 CASE WHEN scns.job_destination = 3 THEN 1 ELSE 0 END AS is_apply,
                 COALESCE(jh.id_category, j.id_category)              AS id_job_category
          FROM public.session_click_no_serp scns
                   JOIN temp_session s
                        ON scns.date_diff = s.date_diff
                            AND scns.id_session = s.id
                   JOIN temp_info_currency ic
                        ON ic.id = scns.id_currency
                   JOIN temp_auction_campaign ac
                        ON ac.id = scns.id_campaign
                   JOIN temp_auction_site ast
                        ON ac.id_site = ast.id
                   JOIN temp_auction_user au
                        ON au.id = ast.id_user
                   LEFT JOIN temp_info_project ip
                             ON scns.id_project = ip.id
                   LEFT JOIN temp_u_traffic_source uts
                             ON s.id_traf_source = uts.id
                   LEFT JOIN public.session_jdp sj
                             ON sj.id_click_no_serp = scns.id AND sj.date_diff = scns.date_diff
                   LEFT JOIN public.session_away sa
                             ON sa.id_click_no_serp = scns.id AND sa.date_diff = scns.date_diff
                   LEFT JOIN an.snap_job j
                             ON scns.id_job = j.id
                   LEFT JOIN an.snap_job_history jh
                             ON scns.uid_job = jh.uid
          WHERE scns.date_diff = _start_datediff
            AND (COALESCE(s.flags, 0) & 1) = 0
            AND au.flags & 2 = 2
            AND (COALESCE(scns.flags, 0) & 16) = 0
            AND scns.flags & 4096 = 0) AS Revenue
    GROUP BY session_date,
             id_project,
             project_name,
             campaign_name,
             away_type,
             id_campaign,
             is_mobile,
             traffic_name,
             traffic_is_paid,
             channel,
             ip_cc,
             name,
             id_current_traf_source,
             session_create_page_type,
             is_returned,
             is_apply,
             id_job_category

    UNION ALL

    SELECT CASE WHEN sj.flags & 4 = 4 THEN 'applies' ELSE 'jdp' END AS metric,
           DATE '1900-01-01' + sj.date_diff                         AS session_date,
           sj.job_id_project                                        AS project_id,
           ip.name                                                  AS project_name,
           ac.name                                                  AS campaign_name,
           CASE
               WHEN sj.letter_type IS NOT NULL THEN CONCAT('Letter Type ', letter_type)
               WHEN sj.id_click IS NOT NULL THEN 'Click'
               --when sj.id_jdp is not null then 'Jdp'
               WHEN sj.id_click_no_serp IS NOT NULL THEN 'No serp'
               ELSE 'Other'
           END                                                      AS jdp_type,
           sc.id_campaign                                           AS campaign,
           CASE
               WHEN s.flags & 16 = 16 THEN 1
               WHEN s.flags & 64 = 64 OR s.flags & 128 = 128 THEN 2
               ELSE 0
           END                                                      AS is_mobile,
           uts.name                                                 AS traffic_name,
           uts.is_paid                                              AS traffic_is_paid,
           uts.channel,
           s.ip_cc,
           ic.name,
           0                                                        AS revenue,
           0                                                        AS away_revenue_origin_currency,
           COUNT(DISTINCT sj.id)                                    AS value,
           COUNT(DISTINCT sa.id)                                    AS conversions,
           s.id_current_traf_source,
           s.session_create_page_type,
           CASE WHEN s.flags & 2 = 2 THEN 1 ELSE 0 END              AS is_returned,
           COALESCE(jh.id_category, j.id_category)                  AS job_category_id,
           COUNT(DISTINCT sa.id)                                    AS all_conversions
    FROM public.session_jdp sj
             JOIN temp_session s
                  ON sj.date_diff = s.date_diff
                      AND sj.id_session = s.id
             LEFT JOIN temp_session_jdp_action sja
                       ON sj.date_diff = sja.date_diff
                           AND sj.id = sja.id_jdp
             LEFT JOIN temp_session_apply sa
                       ON sa.date_diff = sja.date_diff
                           AND sa.id_src_jdp_action = sja.id
             LEFT JOIN temp_session_click sc
                       ON sj.date_diff = sc.date_diff
                           AND sj.id_click = sc.id
             LEFT JOIN temp_u_traffic_source uts
                       ON s.id_traf_source = uts.id
             LEFT JOIN temp_info_project ip
                       ON sj.job_id_project = ip.id
             LEFT JOIN temp_auction_campaign ac
                       ON sc.id_campaign = ac.id
             LEFT JOIN temp_info_currency ic
                       ON sc.id_currency = ic.id
             LEFT JOIN an.snap_job_region jr
                       ON sj.uid_job = jr.uid
             LEFT JOIN an.snap_job j
                       ON jr.id_job = j.id
             LEFT JOIN an.snap_job_history jh
                       ON sj.uid_job = jh.uid
    WHERE sj.date_diff = _start_datediff
      AND s.flags & 1 != 1
    -- and sj.flags & 4 = 4
    GROUP BY session_date,
             metric,
             sj.job_id_project,
             ip.name,
             ac.name,
             jdp_type,
             uts.name,
             uts.is_paid,
             uts.channel,
             s.ip_cc,
             ic.name,
             sc.id_campaign,
             is_mobile,
             s.id_current_traf_source,
             s.session_create_page_type,
             is_returned,
             job_category_id;


    DROP TABLE IF EXISTS temp_u_traffic_source;
    DROP TABLE IF EXISTS temp_info_project;
    DROP TABLE IF EXISTS temp_info_currency;
    DROP TABLE IF EXISTS temp_conversion_away_connection;
    DROP TABLE IF EXISTS temp_campaign_raw;
    DROP TABLE IF EXISTS temp_site;
    DROP TABLE IF EXISTS temp_user_raw;
    DROP TABLE IF EXISTS temp_session;
    DROP TABLE IF EXISTS temp_session_jdp_action;
    DROP TABLE IF EXISTS temp_session_apply;
    DROP TABLE IF EXISTS temp_session_click;


    -- DELETE
    -- FROM an.rpl_project_conversions_daily
    -- WHERE session_date = _start_date;


    -- INSERT INTO an.rpl_project_conversions_daily(country_id, metric, session_date, project_id, project_name,
    --                                              campaign_name,
    --                                              away_type, campaign_id, is_mobile, traffic_name, traffic_is_paid,
    --                                              channel,
    --                                              ip_cc, name, away_revenue, away_revenue_origin_currency, aways,
    --                                              conversions, is_returned, session_create_page_type,
    --                                              id_current_traf_source, job_category_id, all_conversion)
    SELECT _country_id AS country_id,
           metric,
           session_date,
           project_id,
           project_name,
           campaign_name,
           away_type,
           campaign_id,
           is_mobile,
           traffic_name,
           traffic_is_paid::int::bool,
           channel,
           ip_cc,
           name,
           away_revenue,
           away_revenue_origin_currency,
           aways,
           conversions,
           is_returned,
           session_create_page_type,
           id_current_traf_source,
           job_category_id,
           all_conversion
    FROM temp_results;

    DROP TABLE IF EXISTS temp_results;
