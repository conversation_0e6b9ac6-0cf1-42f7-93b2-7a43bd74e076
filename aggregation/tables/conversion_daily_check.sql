Select _country_id AS country_id,
        cac.date::date - '1900-01-01'          as load_datediff,
        sa.date_diff                           as away_datediff,
        count(distinct case
                            when first_conversion.id_first_conversion = conversion.id
                                then sa.id end) as new_unic_target_conversion,
        count(distinct cac.id)                 as all_new_conversion
from link_auction.conversion_action cac
join public.session_away sa on cac.id_session_away = sa.id
left join link_auction.conversion on cac.id = conversion.id_conversion_action
left join (Select id_session_away,
                    min(conversion.id) as id_first_conversion
            from link_auction.conversion_action
            join link_auction.conversion
                on conversion_action.id = conversion.id_conversion_action
            group by id_session_away) first_conversion
            on sa.id = first_conversion.id_session_away

where cac.date::date = _start_date
group by cac.date::date - '1900-01-01', sa.date_diff;
