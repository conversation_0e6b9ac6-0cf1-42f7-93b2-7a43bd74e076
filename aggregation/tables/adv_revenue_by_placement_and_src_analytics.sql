SET NOCOUNT ON;

DECLARE @date_diff int = :to_sqlcode_date_or_datediff_start,
    @from_email_click_flag int = 256,
    @from_external_click_flag int = 512,
    @flags_duplicate_click int = 4096,
    @flags_duplicate_away int = 512,
    @month_date_diff int = :to_sqlcode_month_date_diff;

SELECT id,
       date_diff,
       id_session,
       id_currency,
       id_campaign,
       id_project,
       id_click,
       id_jdp,
       id_click_no_serp,
       flags,
       click_price,
       letter_type,
       id_job,
       uid_job
INTO #temp_session_away
FROM dbo.session_away
WHERE date_diff = @date_diff;

SELECT id, date_diff, search_source
INTO #temp_session_search
FROM dbo.session_search
WHERE date_diff = @date_diff

SELECT id,
       date_diff,
       flags,
       id_traf_source,
       id_current_traf_source
INTO #temp_session
FROM dbo.session
WHERE date_diff = @date_diff;

SELECT id,
       date_diff,
       id_session,
       id_search,
       id_alertview,
       uid_job,
       flags,
       id_project,
       id_job,
       id_campaign,
       click_price,
       id_currency,
       id_recommend
INTO #temp_session_click
FROM dbo.session_click
WHERE date_diff = @date_diff;

SELECT id,
       date_diff,
       id_session,
       uid_job,
       flags,
       id_project,
       id_job,
       id_campaign,
       click_price,
       id_currency,
       id_recommend,
       letter_type
INTO #temp_session_click_no_serp
FROM dbo.session_click_no_serp
WHERE date_diff = @date_diff;

SELECT id,
       date_diff,
       id_click,
       letter_type,
       id_click_no_serp,
       source
INTO #temp_session_jdp
FROM dbo.session_jdp
WHERE date_diff = @date_diff;

SELECT id, date_diff, id_away, id_jdp
INTO #temp_session_external
FROM dbo.session_external
WHERE date_diff = @date_diff;


SELECT s2.date_diff                 AS date_diff,
       s2.id_traf_source,
       s2.id_current_traf_source,
       s2.placement,
       SUM(s2.click_price_usd)      AS revenue_usd,
       SUM(s2.revenue_usd_discount) AS revenue_usd_discount,
       s2.id_job_category
FROM (SELECT s1.date_diff,
             s1.id_traf_source,
             s1.id_current_traf_source,
             s1.click_price_usd,
             s1.id_session,
             CASE
                 WHEN id_project IN (17055, 17045)
                     THEN 'price per post'
                 WHEN add_placement = 1
                     THEN 'salary page'
                 WHEN add_placement = 2
                     THEN 'category page'
                 WHEN add_placement = 3
                     THEN 'company page'
                 WHEN add_placement = 4
                     THEN 'skill page'
                 WHEN add_placement = 5
                     THEN 'job description page'
                 WHEN s1.flags & 64 = 64 OR s1.flags & 128 = 128
                     THEN 'mobile app'
                 WHEN info_project.hide_in_search = 1
                     THEN 'ad exchange'
                 WHEN COALESCE(s1.letter_type, email_sent.letter_type) IS NOT NULL
                     THEN CONCAT('letter type ', COALESCE(s1.letter_type, email_sent.letter_type))
                 WHEN s1.id_recommend IS NOT NULL
                     THEN 'recommendations'
                 WHEN s1.id_alertview IS NOT NULL
                     THEN 'other letter types'
                 WHEN s1.id_search IS NOT NULL
                     THEN 'search'
                 WHEN s1.id_external IS NOT NULL
                     THEN 'external'
                 ELSE 'other'
                 END                                                AS placement,
             s1.click_price_usd * ISNULL((SELECT TOP 1 discount
                                          FROM dbo.vw_info_project_discount d (NOLOCK)
                                          WHERE d.id_project = s1.id_project
                                            AND DATEDIFF(M, 0, d.date) <= @month_date_diff
                                          ORDER BY d.date DESC), 0) AS revenue_usd_discount,
             s1.id_job_category
      FROM (SELECT sa.date_diff,
                   s.id_traf_source,
                   s.id_current_traf_source,
                   s.id                                      AS id_session,
                   sa.click_price * ic.value_to_usd          AS click_price_usd,
                   ISNULL(sa.letter_type, sj.letter_type)    AS letter_type,
                   ISNULL(sc.id_recommend, scj.id_recommend) AS id_recommend,
                   ISNULL(sc.id_alertview, scj.id_alertview) AS id_alertview,
                   ISNULL(sc.id_search, scj.id_search)       AS id_search,
                   ext.id                                    AS id_external,
                   s.flags,
                   sa.id_project,
                   CASE
                       WHEN sa.flags & 2048 = 2048 OR COALESCE(ss.search_source, ssj.search_source) IN
                                                      (118, 119, 134, 136, 137, 138, 139, 140, 141, 142, 143, 144)
                           THEN 1-- salary page
                       WHEN sa.flags & 4096 = 4096 OR COALESCE(ss.search_source, ssj.search_source) IN (145) THEN 2--  category page
                       WHEN COALESCE(ss.search_source, ssj.search_source) IN (146, 147, 148, 149, 150) THEN 3-- company page
                       WHEN COALESCE(ss.search_source, ssj.search_source) IN (151, 152, 153, 154) THEN 4-- skill page
                       WHEN COALESCE(ss.search_source, ssj.search_source) IN (155, 156, 157, 158) THEN 5-- JobDescription page
                       END                                   AS add_placement,
                   COALESCE(jh.id_category, j.id_category)   AS id_job_category
            FROM #temp_session_away sa (NOLOCK)
                     INNER JOIN #temp_session s (NOLOCK) ON sa.date_diff = s.date_diff
                AND sa.id_session = s.id
                     INNER JOIN dbo.info_currency ic (NOLOCK) ON ic.id = sa.id_currency
                     LEFT JOIN auction.campaign ac (NOLOCK) ON ac.id = sa.id_campaign
                     LEFT JOIN auction.site ast (NOLOCK) ON ac.id_site = ast.id
                     LEFT JOIN auction.[user] au (NOLOCK) ON au.id = ast.id_user
                     LEFT JOIN dbo.info_project ip WITH (NOLOCK) ON ip.id = sa.id_project
                -- serp -> away
                     LEFT JOIN #temp_session_click sc (NOLOCK) ON sc.date_diff = sa.date_diff
                AND sc.id = sa.id_click
                -- serp -> jdp -> away
                     LEFT JOIN #temp_session_jdp sj (NOLOCK) ON sj.date_diff = sa.date_diff
                AND sj.id = sa.id_jdp
                     LEFT JOIN #temp_session_click scj (NOLOCK) ON scj.date_diff = sj.date_diff
                AND scj.id = sj.id_click
                     LEFT JOIN #temp_session_external ext (NOLOCK) ON ext.date_diff = sa.date_diff
                AND ext.id_away = sa.id
                     LEFT JOIN #temp_session_search ss (NOLOCK) ON ss.date_diff = sc.date_diff
                AND ss.id = sc.id_search
                     LEFT JOIN #temp_session_search ssj (NOLOCK) ON ssj.date_diff = scj.date_diff
                AND ssj.id = scj.id_search
                     LEFT JOIN dbo.job j (NOLOCK) ON sa.id_job = j.id
                     LEFT JOIN dbo.job_history jh (NOLOCK) ON sa.uid_job = jh.uid

            WHERE sa.date_diff = @date_diff
              AND ISNULL(s.flags, 0) & 1 = 0
              AND (sa.id_campaign = 0 OR au.flags & 2 = 0)
              AND ISNULL(sa.flags, 0) & 2 = 0
              AND sa.flags & @flags_duplicate_away = 0
              AND COALESCE(LOWER(ip.name), '') NOT LIKE 'j-vers.%'

            UNION ALL

            SELECT sc.date_diff,
                   s.id_traf_source,
                   s.id_current_traf_source,
                   s.id                                    AS id_session,
                   sc.click_price * ic.value_to_usd        AS click_price_usd,
                   ISNULL(sa.letter_type, sj.letter_type)  AS letter_type,
                   sc.id_recommend,
                   sc.id_alertview,
                   sc.id_search,
                   ext.id                                  AS id_external,
                   s.flags,
                   sc.id_project,
                   CASE
                       WHEN sa.flags & 2048 = 2048 OR sj.source = 9 OR
                            ss.search_source IN (118, 119, 134, 136, 137, 138, 139, 140, 141, 142, 143, 144) THEN 1-- salary page
                       WHEN sa.flags & 4096 = 4096 OR ss.search_source IN (145) THEN 2--  category page
                       WHEN ss.search_source IN (146, 147, 148, 149, 150) THEN 3-- company page
                       WHEN ss.search_source IN (151, 152, 153, 154) THEN 4-- skill page
                       WHEN ss.search_source IN (155, 156, 157, 158) THEN 5-- JobDescription page
                       END                                 AS add_placement,
                   COALESCE(jh.id_category, j.id_category) AS id_job_category
            FROM #temp_session_click sc (NOLOCK)
                     INNER JOIN #temp_session s (NOLOCK) ON sc.date_diff = s.date_diff
                AND sc.id_session = s.id
                     INNER JOIN dbo.info_currency ic (NOLOCK) ON ic.id = sc.id_currency
                     LEFT JOIN auction.campaign ac (NOLOCK) ON ac.id = sc.id_campaign
                     LEFT JOIN auction.site ast (NOLOCK) ON ac.id_site = ast.id
                     LEFT JOIN auction.[user] au (NOLOCK) ON au.id = ast.id_user
                     LEFT JOIN dbo.info_project ip WITH (NOLOCK) ON ip.id = sc.id_project
                     LEFT JOIN #temp_session_away sa (NOLOCK) ON sc.date_diff = sa.date_diff
                AND sc.id = COALESCE(sa.id_click,sa.id_click_no_serp)
                     LEFT JOIN #temp_session_jdp sj (NOLOCK) ON sj.date_diff = sa.date_diff
                AND sj.id = sa.id_jdp
                     LEFT JOIN #temp_session_external ext (NOLOCK) ON ext.date_diff = sc.date_diff
                AND (ext.id_away = sa.id OR ext.id_jdp = sj.id)
                     LEFT JOIN #temp_session_search ss (NOLOCK) ON ss.date_diff = sc.date_diff
                AND ss.id = sc.id_search
                     LEFT JOIN dbo.job j (NOLOCK) ON sc.id_job = j.id
                     LEFT JOIN dbo.job_history jh (NOLOCK) ON sc.uid_job = jh.uid

            WHERE sc.date_diff = @date_diff
              AND ISNULL(s.flags, 0) & 1 = 0
              AND (/*sc.id_campaign = 0 or*/ au.flags & 2 = 2)
              AND ISNULL(sc.flags, 0) & 16 = 0
              AND sc.flags & @flags_duplicate_click = 0
              AND COALESCE(LOWER(ip.name), '') NOT LIKE 'j-vers.%'

           /* UNION ALL

            SELECT scns.date_diff,
                   s.id_traf_source,
                   s.id_current_traf_source,
                   s.id                                    AS id_session,
                   scns.click_price * ic.value_to_usd      AS click_price_usd,
                   scns.letter_type,
                   scns.id_recommend,
                   NULL                                    AS id_alertview,
                   NULL                                    AS id_search,
                   se.id                                   AS id_external,
                   s.flags,
                   scns.id_project,
                   NULL                                    AS add_placement,
                   COALESCE(jh.id_category, j.id_category) AS id_job_category
            FROM #temp_session_click_no_serp scns (NOLOCK)
                     INNER JOIN #temp_session s (NOLOCK) ON scns.date_diff = s.date_diff AND scns.id_session = s.id
                     INNER JOIN dbo.info_currency ic (NOLOCK) ON ic.id = scns.id_currency
                     INNER JOIN auction.campaign ac (NOLOCK) ON ac.id = scns.id_campaign
                     INNER JOIN auction.site ast (NOLOCK) ON ac.id_site = ast.id
                     INNER JOIN auction.[user] au (NOLOCK) ON au.id = ast.id_user
                     LEFT JOIN dbo.info_project ip WITH (NOLOCK) ON ip.id = scns.id_project
                     LEFT JOIN #temp_session_jdp sj (NOLOCK)
                               ON sj.id_click_no_serp = scns.id AND sj.date_diff = scns.date_diff
                     LEFT JOIN #temp_session_away sa (NOLOCK)
                               ON sa.id_click_no_serp = scns.id AND sa.date_diff = scns.date_diff
                     LEFT JOIN #temp_session_external se (NOLOCK) ON se.id_jdp = sj.id OR se.id_away = sa.id
                     LEFT JOIN dbo.job j (NOLOCK) ON scns.id_job = j.id
                     LEFT JOIN dbo.job_history jh (NOLOCK) ON scns.uid_job = jh.uid
            WHERE scns.date_diff = @date_diff
              AND ISNULL(s.flags, 0) & 1 = 0
              AND au.flags & 2 = 2
              AND ISNULL(scns.flags, 0) & 16 = 0
              AND scns.flags & @flags_duplicate_click = 0
              AND COALESCE(LOWER(ip.name), '') NOT LIKE 'j-vers.%'
              */
              ) s1
               LEFT JOIN dbo.info_project WITH (NOLOCK)
                         ON s1.id_project = info_project.id
               LEFT JOIN
           (SELECT id_alertview,
                   MIN(id_message) AS id_message
            FROM dbo.session_alertview_message WITH (NOLOCK)
            WHERE date_diff = @date_diff
            GROUP BY id_alertview) sam
           ON sam.id_alertview = s1.id_alertview
               LEFT JOIN dbo.email_sent WITH (NOLOCK)
                         ON email_sent.id_message = sam.id_message) s2
GROUP BY s2.date_diff,
         s2.id_traf_source,
         s2.placement,
         s2.id_current_traf_source,
         s2.id_job_category;


DROP TABLE #temp_session_away;
DROP TABLE #temp_session;
DROP TABLE #temp_session_click;
DROP TABLE #temp_session_click_no_serp;
DROP TABLE #temp_session_jdp;
DROP TABLE #temp_session_external;
