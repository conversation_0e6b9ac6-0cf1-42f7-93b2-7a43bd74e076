    CREATE TEMP TABLE temp_session_alertview AS
    SELECT date_diff, id, sub_id_alert, id_session
    FROM public.session_alertview
    WHERE date_diff = _start_datediff;
    CREATE INDEX idx_temp_session_alertview_id ON temp_session_alertview (id);
    CREATE INDEX idx_temp_session_alertview_id_session ON temp_session_alertview (id_session);
    CREATE INDEX idx_temp_session_alertview_sub_id_alert ON temp_session_alertview (sub_id_alert);


    CREATE TEMP TABLE temp_session_click AS
    SELECT date_diff, id, id_alertview
    FROM public.session_click
    WHERE date_diff = _start_datediff;
    CREATE INDEX idx_temp_session_click_id ON temp_session_click (id);
    CREATE INDEX idx_temp_session_click_id_alertview ON temp_session_click (id_alertview);


    CREATE TEMP TABLE temp_session_adsense_version AS
    SELECT date_diff, id_session, version
    FROM public.session_adsense_version
    WHERE date_diff = _start_datediff;
    CREATE INDEX idx_temp_session_adsense_version ON temp_session_adsense_version (id_session);


    CREATE TEMP TABLE temp_session AS
    SELECT date_diff, id, flags, id_traf_source, id_current_traf_source
    FROM public.session
    WHERE date_diff = _start_datediff;
    CREATE INDEX idx_temp_session_id ON temp_session (id);


    CREATE TEMP TABLE temp_session_jdp AS
    SELECT date_diff, id, id_click
    FROM public.session_jdp
    WHERE date_diff = _start_datediff;
    CREATE INDEX idx_temp_session_jdp_id ON temp_session_jdp (id);
    CREATE INDEX idx_temp_session_jdp_id_click ON temp_session_jdp (id_click);


    CREATE TEMP TABLE temp_u_traffic_source AS
    SELECT id, name
    FROM link_dbo.u_traffic_source;


    CREATE TEMP TABLE temp_info_currency AS
    SELECT id, value_to_eur
    FROM link_dbo.info_currency;

    ANALYZE temp_session_alertview;
    ANALYZE temp_session_click;
    ANALYZE temp_session_adsense_version;
    ANALYZE temp_session;
    ANALYZE temp_session_jdp;
    ANALYZE temp_u_traffic_source;
    ANALYZE temp_info_currency;


    CREATE TEMP TABLE temp_sav AS
    SELECT DISTINCT sav.date_diff,
                    sav.id_session,
                    sav.version
    FROM temp_session_adsense_version sav
             JOIN temp_session s
                  ON sav.id_session = s.id
                      AND sav.date_diff = s.date_diff
             LEFT JOIN temp_u_traffic_source ts
                       ON COALESCE(s.id_traf_source, s.id_current_traf_source) = ts.id
    WHERE sav.date_diff = _start_datediff
      AND s.flags & 1 = 0
      AND ts.name NOT LIKE 'LP_%';


    CREATE TEMP TABLE temp_searches AS
    SELECT date_diff,
           id_session,
           id AS id_search
    FROM public.session_search
    WHERE date_diff = _start_datediff;
    CREATE INDEX idx_temp_searches_id_session ON temp_searches (id_session);
    CREATE INDEX idx_temp_searches_id_search ON temp_searches (id_search);
    ANALYZE temp_searches;


    CREATE TEMP TABLE temp_clicks AS
    SELECT date_diff,
           id_search,
           id AS id_click
    FROM public.session_click
    WHERE date_diff = _start_datediff
      AND id_search IS NOT NULL;
    CREATE INDEX idx_temp_clicks_id_search ON temp_clicks (id_search);
    CREATE INDEX idx_temp_clicks_id_click ON temp_clicks (id_click);
    ANALYZE temp_clicks;


    CREATE TEMP TABLE temp_session_away AS
    SELECT date_diff, id, id_click, id_jdp, id_session, id_project, flags, click_price, id_currency
    FROM public.session_away
    WHERE date_diff = _start_datediff;
    CREATE INDEX idx_temp_session_away_id on temp_session_away (id);
    CREATE INDEX idx_temp_session_away_id_click on temp_session_away (id_click);
    CREATE INDEX idx_temp_session_away_id_jdp on temp_session_away (id_jdp);
    CREATE INDEX idx_temp_session_away_id_project on temp_session_away (id_project);
    CREATE INDEX idx_temp_session_away_id_currency on temp_session_away (id_currency);
    ANALYZE temp_session_away;


    CREATE TEMP TABLE temp_conversion_away_connection AS
    SELECT date_diff, id_session_away
    FROM link_auction.conversion_away_connection;
    ANALYZE temp_conversion_away_connection;


    CREATE TEMP TABLE temp_cac_dist AS
    SELECT DISTINCT id_session_away
    FROM temp_conversion_away_connection;
    CREATE INDEX idx_temp_cac_dist ON temp_cac_dist (id_session_away);
    ANALYZE temp_cac_dist;


    CREATE TEMP TABLE temp_conversion_start AS
    SELECT id_project,
           MIN(cac.date_diff) AS conversion_start
    FROM temp_conversion_away_connection cac
             JOIN public.session_away sa
                  ON cac.date_diff = sa.date_diff
                      AND cac.id_session_away = sa.id
    GROUP BY id_project;
    ANALYZE temp_conversion_start;


    CREATE TEMP TABLE temp_aways AS
    SELECT sa.date_diff,
           sa.id_session,
           sa.id_click                                   AS id_click,
           sa.id                                         AS id_away,
           cac.id_session_away,
           COALESCE(sa.click_price, 0) * ic.value_to_eur AS revenue,
           cs.conversion_start
    FROM temp_session_away sa
             LEFT JOIN temp_info_currency ic
                       ON sa.id_currency = ic.id
             LEFT JOIN temp_cac_dist cac
                       ON sa.id = cac.id_session_away
             LEFT JOIN temp_conversion_start cs
                       ON sa.id_project = cs.id_project
    WHERE sa.date_diff = _start_datediff
      AND (COALESCE(sa.flags, 0) & 2) = 0;
    ANALYZE temp_aways;


    CREATE TEMP TABLE temp_session_account_day AS
    SELECT date_diff, id_account, id_session
    FROM link_dbo.session_account
    WHERE date_diff = _start_datediff;


    CREATE TEMP TABLE temp_account_day AS
    SELECT date_add, id
    FROM link_dbo.account
    WHERE date_add::date = DATE '1900-01-01' + _start_datediff;


    CREATE TEMP TABLE temp_account_agg AS
    SELECT sa.date_diff,
           sav.version,
           COUNT(DISTINCT aa.id) AS accounts
    FROM temp_session_account_day sa
             JOIN temp_account_day aa
                  ON sa.id_account = aa.id
             JOIN temp_session_adsense_version sav
                  ON sa.date_diff = sav.date_diff AND
                     sav.id_session = sa.id_session
    WHERE sa.date_diff = _start_datediff
      AND sa.date_diff = EXTRACT(DAY FROM (aa.date_add - DATE '1900-01-01'))
    GROUP BY sa.date_diff,
             sav.version;
    ANALYZE temp_account_agg;


    CREATE TEMP TABLE temp_account AS
    SELECT date_add, id
    FROM link_dbo.account
    WHERE date_add::date >= '2022-08-09';
    CREATE INDEX idx_temp_account ON temp_account (id);
    ANALYZE temp_account;


    CREATE TEMP TABLE temp_session_account AS
    SELECT id_account, id_session
    FROM link_dbo.session_account
    WHERE date_diff >= 44780;
    ANALYZE temp_session_account;


    CREATE TEMP TABLE temp_session_adsense_version_big AS
    SELECT date_diff, id_session, version
    FROM link_dbo.session_adsense_version
    WHERE date_diff >= 44780;
    ANALYZE temp_session_adsense_version_big;


    CREATE TEMP TABLE temp_all_accounts AS
    SELECT DISTINCT sa.id_account,
                    sav.version AS version
    FROM temp_session_account sa
             JOIN temp_account a
                  ON sa.id_account = a.id
             JOIN temp_session_adsense_version_big sav
                  ON sa.id_session = sav.id_session
                      AND sav.date_diff = EXTRACT(DAY FROM (a.date_add - DATE '1900-01-01'));
    CREATE INDEX idx_temp_all_accounts ON temp_all_accounts (id_account);
    ANALYZE temp_all_accounts;


    DROP TABLE IF EXISTS temp_account_day;
    DROP TABLE IF EXISTS temp_session_adsense_version_big;


    CREATE TEMP TABLE temp_email_alert AS
    SELECT id, id_account
    FROM link_dbo.email_alert;
    CREATE INDEX idx_temp_email_alert_id ON temp_email_alert (id);
    CREATE INDEX idx_temp_email_alert_id_account ON temp_email_alert (id_account);
    ANALYZE temp_email_alert;


    CREATE TEMP TABLE temp_email_click AS
    SELECT sv.date_diff, ea.id_account, sc.id AS id_click, ea.version
    FROM temp_all_accounts ea
             JOIN temp_email_alert ev
                  ON ev.id_account = ea.id_account
             JOIN temp_session_alertview sv
                  ON ev.id = sv.sub_id_alert
             JOIN temp_session_click sc
                  ON sv.date_diff = sc.date_diff
                      AND sv.id = sc.id_alertview
    WHERE sv.date_diff = _start_datediff;


    INSERT INTO temp_email_click
    SELECT sc.date_diff, a.id_account, sc.id AS id_click, a.version
    FROM temp_session_click sc
             LEFT JOIN temp_session_away sa
                       ON sc.date_diff = sa.date_diff
                           AND sc.id = sa.id_click
             LEFT JOIN PUBLIC.session_click_message scm
                       ON sc.date_diff = scm.date_diff
                           AND sc.id = scm.id_click
             LEFT JOIN PUBLIC.session_away_message sam
                       ON sc.date_diff = sam.date_diff
                           AND sa.id = sam.id_away
             JOIN an.email_sent es
                  ON COALESCE(scm.id_message, sam.id_message) = es.id_message::uuid
             JOIN temp_all_accounts a
                  ON es.id_account = a.id_account
    WHERE sc.date_diff = _start_datediff;
    ANALYZE temp_email_click;


    CREATE TEMP TABLE temp_email_revenue AS
    SELECT a.date_diff,
           ec.version,
           SUM(revenue)                      AS email_revenue,
           COUNT(DISTINCT a.id_away)         AS email_aways,
           COUNT(DISTINCT a.id_session_away) AS email_conversions
    FROM temp_email_click ec
             JOIN temp_aways a
                  ON ec.date_diff = a.date_diff
                      AND ec.id_click = a.id_click
    GROUP BY a.date_diff,
             ec.version;
    ANALYZE temp_email_revenue;


    CREATE TEMP TABLE temp_account_revenue AS
    SELECT a.date_diff                                       AS date_diff,
           sav.version,
           SUM(COALESCE(a.click_price, 0) * IC.value_to_eur) AS account_revenue
    FROM temp_session_away a
             JOIN temp_info_currency IC
                  ON a.id_currency = IC.id
             JOIN temp_session_account_day sa
                  ON a.date_diff = sa.date_diff
                      AND a.id_session = sa.id_session
             JOIN temp_account aa
                  ON sa.id_account = aa.id
                      AND a.date_diff > EXTRACT(DAY FROM (aa.date_add - DATE '1900-01-01'))
             JOIN temp_all_accounts sav
                  ON aa.id = sav.id_account
    WHERE a.date_diff = _start_datediff
    GROUP BY a.Date_diff,
             sav.version;
    ANALYZE temp_account_revenue;


    CREATE TEMP TABLE temp_alertview AS
    SELECT session_alertview.date_diff,
           sav.version,
           COUNT(DISTINCT session_alertview.id)            AS alertview_cnt,
           COUNT(DISTINCT session_click.id)                AS click_cnt,
           COUNT(DISTINCT session_away.id)                 AS away_cnt,
           COUNT(DISTINCT cac.id_session_away)             AS alertivew_conversion_cnt,
           SUM(session_away.click_price * ic.value_to_eur) AS alertview_revenue_eur,
           COUNT(DISTINCT CASE
                              WHEN session_away.date_diff >= cs.conversion_start
                                  THEN session_away.id
                          END)                             AS alertview_conversion_away_cnt,
           SUM(CASE
                   WHEN session_away.date_diff >= cs.conversion_start
                       THEN session_away.click_price * ic.value_to_eur
               END)                                        AS alertivew_conversion_revenue_eur
    FROM temp_session_alertview session_alertview
             JOIN temp_session_adsense_version sav
                  ON session_alertview.date_diff = sav.date_diff
                      AND session_alertview.id_session = sav.id_session
             JOIN temp_session session
                  ON session_alertview.date_diff = session.date_diff
                      AND session_alertview.id_session = session.id
             LEFT JOIN temp_session_click session_click
                       ON session_alertview.date_diff = session_click.date_diff
                           AND session_alertview.id = session_click.id_alertview
             LEFT JOIN temp_session_jdp session_jdp
                       ON session_jdp.date_diff = session_click.date_diff
                           AND session_jdp.id_click = session_click.id
             LEFT JOIN temp_session_away session_away
                       ON session_away.date_diff = session_click.date_diff
                           AND (session_away.id_click = session_click.id
                               OR session_away.id_jdp = session_jdp.id)
             LEFT JOIN temp_info_currency ic
                       ON session_away.id_currency = ic.id
             LEFT JOIN temp_cac_dist cac
                       ON session_away.id = cac.id_session_away
             LEFT JOIN temp_conversion_start cs
                       ON session_away.id_project = cs.id_project
    WHERE session_alertview.date_diff = _start_datediff
      AND session.flags & 1 = 0
    GROUP BY sav.version,
             session_alertview.date_diff;
    ANALYZE temp_alertview;


--     DELETE
--     FROM an.rpl_adsense_version_daily
--     WHERE action_datediff = _start_datediff;


--     INSERT INTO an.rpl_adsense_version_daily(country_id, action_datediff, adsense_version, session_cnt, searche_cnt,
--                                              click_cnt, away_cnt, revenue_eur, account_cnt, conversion_cnt,
--                                              account_revenue, email_aways, email_conversions, email_revenue,
--                                              conversion_aways, conversion_revenue_eur, alertview_cnt,
--                                              alertview_click_cnt,
--                                              alertview_away_cnt, alertview_revenue_eur, alertview_conversion_away_cnt,
--                                              alertview_conversion_revenue_eur, alertview_conversion_cnt)

    SELECT _country_id                                                                    AS country_id,
           sav.date_diff                                                                  AS action_datediff,
           sav.version                                                                    AS adsense_version,
           COUNT(DISTINCT sav.id_session)                                                 AS session_cnt,
           COUNT(DISTINCT ss.id_search)                                                   AS searche_cnt,
           COUNT(DISTINCT c.id_click)                                                     AS click_cnt,
           COUNT(DISTINCT a.id_away)                                                      AS away_cnt,
           SUM(a.revenue)                                                                 AS revenue_eur,
           aa.accounts                                                                    AS account_cnt,
           COUNT(DISTINCT a.id_session_away)                                              AS conversion_cnt,
           ar.account_revenue                                                             AS account_revenue,
           er.email_aways,
           er.email_conversions,
           er.email_revenue,
           COUNT(DISTINCT CASE WHEN a.date_diff >= a.conversion_start THEN a.id_away END) AS conversion_aways,
           SUM(CASE WHEN a.date_diff >= a.conversion_start THEN a.revenue END)            AS conversion_revenue_eur,
           al.alertview_cnt,
           al.click_cnt                                                                   AS alertview_click_cnt,
           al.away_cnt                                                                    AS alertview_away_cnt,
           al.alertview_revenue_eur,
           al.alertview_conversion_away_cnt,
           al.alertivew_conversion_revenue_eur                                            AS alertview_conversion_revenue_eur,
           al.alertivew_conversion_cnt                                                    AS alertview_conversion_cnt
    FROM temp_sav sav
             JOIN temp_searches ss
                  ON ss.date_diff = sav.date_diff AND
                     ss.id_session = sav.id_session
             LEFT JOIN temp_account_agg aa
                       ON sav.date_diff = aa.date_diff AND
                          sav.version = aa.version
             LEFT JOIN temp_clicks c
                       ON c.date_diff = ss.date_diff AND
                          c.id_search = ss.id_search
             LEFT JOIN temp_aways a
                       ON a.date_diff = c.date_diff AND
                          a.id_click = c.id_click
             LEFT JOIN temp_account_revenue ar
                       ON ar.date_diff = sav.date_diff AND
                          ar.version = sav.version
             LEFT JOIN temp_email_revenue er
                       ON sav.date_diff = er.date_diff AND
                          sav.version = er.version
             LEFT JOIN temp_alertview al
                       ON sav.date_diff = al.date_diff AND
                          sav.version = al.version
    GROUP BY sav.version,
             sav.date_diff,
             ar.account_revenue,
             aa.accounts,
             er.email_aways,
             er.email_revenue,
             er.email_conversions,
             al.alertview_cnt,
             al.click_cnt,
             al.away_cnt,
             al.alertview_revenue_eur,
             al.alertview_conversion_away_cnt,
             al.alertivew_conversion_revenue_eur,
             al.alertivew_conversion_cnt;


    DROP TABLE IF EXISTS temp_sav;
    DROP TABLE IF EXISTS temp_searches;
    DROP TABLE IF EXISTS temp_clicks;
    DROP TABLE IF EXISTS temp_aways;
    DROP TABLE IF EXISTS temp_account;
    DROP TABLE IF EXISTS temp_all_accounts;
    DROP TABLE IF EXISTS temp_email_click;
    DROP TABLE IF EXISTS temp_email_revenue;
    DROP TABLE IF EXISTS temp_account_revenue;
    DROP TABLE IF EXISTS temp_alertview;
    DROP TABLE IF EXISTS temp_session_account_day;
    DROP TABLE IF EXISTS temp_session_away;
    DROP TABLE IF EXISTS temp_session_alertview;
    DROP TABLE IF EXISTS temp_session_click;
    DROP TABLE IF EXISTS temp_session_adsense_version;
    DROP TABLE IF EXISTS temp_session;
    DROP TABLE IF EXISTS temp_session_jdp;
    DROP TABLE IF EXISTS temp_session_account;
    DROP TABLE IF EXISTS temp_u_traffic_source;
    DROP TABLE IF EXISTS temp_info_currency;
    DROP TABLE IF EXISTS temp_conversion_away_connection;
    DROP TABLE IF EXISTS temp_cac_dist;
    DROP TABLE IF EXISTS temp_conversion_start;
    DROP TABLE IF EXISTS temp_account_agg;
    DROP TABLE IF EXISTS temp_email_alert;
