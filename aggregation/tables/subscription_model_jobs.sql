Select j.date_diff,
       j.id_employer,
       j.active,
       j.banned,
       j.stopped,
       j.moderation,
       j.deleted,
       j.deactivated,
       j.premium_active,
       j.premium_active_7,
       j.premium_stopped,
       j.have_add_question
FROM employer_statistics.employer_jobs j
         inner join employer_account.employer e on e.id = j.id_employer
WHERE e.country_code = 'rs'
  and j.date_diff between :to_sqlcode_date_start and :to_sqlcode_date_end
;
