SET NOCOUNT ON;

declare @date_start date = :to_sqlcode_date_start;
declare @date_end date = :to_sqlcode_date_end;

select Date                   as date,
       Country                as country,
       Type                   as "source",
       Label                  as label,
       sum(Imp)               as imp,
       sum(Clicks)            as clicks,
       sum(Cost_usd)          as cost_usd,
       sum(Rev)               as rev,
       sum(CSA)               as csa,
       sum(EA)                as ea,
       sum(Rev_1)             as rev_1,
       sum(CSA_1)             as csa_1,
       sum(EA_1)              as ea_1,
       case
           when Country in
                ('ca', 'ru', 'at', 'be', 'br', 'ch', 'cl', 'cz',
                 'de', 'dk', 'es', 'fr', 'hu', 'it', 'kz', 'nl',
                 'pl', 'pt', 'ro', 'se', 'sg', 'tr', 'uk', 'us', 'za')
               then 1
           else 0
           end                as is_active
from (select Date,
             Country,
             Type collate SQL_Latin1_General_CP1_CI_AS  Type,
             Label collate SQL_Latin1_General_CP1_CI_AS Label,
             Imp,
             Clicks,
             Cost_usd,
             NULL as                                    Rev,
             NULL as                                    Rev_1,
             NULL as                                    CSA,
             NULL as                                    CSA_1,
             NULL as                                    EA,
             NULL as                                    EA_1
      from dbo.paid_traf_cost_2023 with(nolock)
      UNION
      select Date,
             Country,
             Type,
             Label,
             NULL as Imp,
             NULL as Clicks,
             NULL as Cost_usd,
             Rev,
             Rev_1,
             NULL as CSA,
             NULL as CSA_1,
             NULL as EA,
             NULL as EA_1
      from dbo.direct_revenue_2023 with(nolock)
      UNION
      select Date,
             Country collate SQL_Latin1_General_CP1_CI_AS,
             Source collate SQL_Latin1_General_CP1_CI_AS as Type,
             Label collate SQL_Latin1_General_CP1_CI_AS,
             NULL                                        as Imp,
             NULL                                        as Clicks,
             NULL                                        as Cost_usd,
             NULL                                        as Rev,
             NULL                                        as Rev_1,
             sum(CSA)                                    as CSA,
             sum(CSA_1)                                  as CSA_1,
             NULL                                        as EA,
             NULL                                        as EA_1
      from dbo.csa_2023 with(nolock)
      group by Date, Country, Source, Label
      UNION
      select Date,
             Country,
             Type,
             Label,
             NULL as Imp,
             NULL as Clicks,
             NULL as Cost_usd,
             NULL as Rev,
             NULL as Rev_1,
             NULL as CSA,
             NULL as CSA_1,
             EA,
             EA_1
      from dbo.revenue_ea_2023 with(nolock)) t
where date between @date_start and @date_end
group by Date, Country, Type, Label;
