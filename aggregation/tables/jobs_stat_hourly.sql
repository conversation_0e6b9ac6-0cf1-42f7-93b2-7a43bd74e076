SET NOCOUNT ON;

SELECT CAST(GETDATE() AS datetime)                                                          AS date,
       job.id_project,
       job_region.id_campaign,
       SUM(CASE
               WHEN campaign.click_price > 0 OR
                    (COALESCE(job_region_billing_info.click_price, 0) > 0 AND campaign.is_price_per_job = 1) THEN 1
               ELSE 0 END)                                                                  AS paid_job_count,
       SUM(CASE
               WHEN (campaign.click_price = 0 AND COALESCE(job_region_billing_info.click_price, 0) = 0)
                   OR (campaign.click_price = 0 AND
                       (COALESCE(job_region_billing_info.click_price, 0) >= 0 AND campaign.is_price_per_job = 0))
                   THEN 1
               ELSE 0 END)                                                                  AS organic_job_count,
       COALESCE(SUM(CASE
                        WHEN campaign.click_price = 0 AND campaign.is_price_per_job = 1 THEN
                            COALESCE(job_region_billing_info.click_price, 0) * info_currency.value_to_usd
                        ELSE campaign.click_price * info_currency.value_to_usd END) / NULLIF(SUM(CASE
                                                                                                     WHEN
                                                                                                         campaign.click_price >
                                                                                                         0 OR
                                                                                                         COALESCE(job_region_billing_info.click_price, 0) >
                                                                                                         0 THEN 1
                                                                                                     ELSE 0 END), 0),
                0)                                                                          AS avg_cpc_for_paid_job_count_usd,
       COALESCE(SUM(CASE
                        WHEN campaign.click_price = 0 AND campaign.is_price_per_job = 1
                            THEN COALESCE(job_region_billing_info.click_price, 0)
                        ELSE campaign.click_price END) / NULLIF(SUM(CASE
                                                                        WHEN campaign.click_price > 0 OR
                                                                             COALESCE(job_region_billing_info.click_price, 0) >
                                                                             0 THEN 1
                                                                        ELSE 0 END), 0),
                0)                                                                          AS avg_cpc_for_paid_job_count,
       SUM(CASE WHEN campaign.flags & 32 = 32 THEN 1 ELSE 0 END)                            AS flag_32_jobs_count,
       campaign.budget * info_currency.value_to_usd                                         AS campaign_budget,
       campaign.daily_budget * info_currency.value_to_usd                                   AS campaign_daily_budget,
       campaign.is_price_per_job,
       campaign.flags                                                                       AS campaign_flags,
       campaign.campaign_status,
       SUM(CASE WHEN job_region_billing_info.inactive_reason & 2 = 2 THEN 1 ELSE 0 END)     AS min_cpc_job_count,
       SUM(CASE WHEN job_region_billing_info.inactive_reason & 4 = 4 THEN 1 ELSE 0 END)     AS max_cpc_job_count,
       job.id_category                                                                      AS job_category_id
FROM dbo.job WITH (NOLOCK)
         JOIN dbo.job_region WITH (NOLOCK)
              ON job.id = job_region.id_job
         JOIN auction.campaign WITH (NOLOCK)
              ON job_region.id_campaign = campaign.id
         JOIN dbo.info_currency WITH (NOLOCK)
              ON info_currency.id = campaign.currency
         LEFT JOIN dbo.job_region_billing_info WITH (NOLOCK)
                   ON job_region.uid = job_region_billing_info.uid_job
WHERE (job.date_expired IS NULL OR CAST(job.date_expired AS date) > GETDATE())
  AND job_region.inactive = 0
  AND (campaign.date_end IS NULL OR CAST(campaign.date_end AS date) > GETDATE())
  AND campaign.campaign_status = 0
GROUP BY job.id_project,
         job_region.id_campaign,
         campaign.budget * info_currency.value_to_usd,
         campaign.daily_budget * info_currency.value_to_usd,
         campaign.is_price_per_job,
         campaign.flags,
         campaign.campaign_status,
         job.id_category;


-- last updated by ono, 2/8/2023 - added new columns min/max cpc --
-- ono 31/5/23 add id_category
-- R&P 06/07/23 - add avg_cpc_for_paid_job_count
