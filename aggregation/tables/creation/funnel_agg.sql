-- auto-generated definition
create table funnel_agg
(
    country_id               smallint not null,
    action_date              timestamp,
    device_name              varchar(50),
    traffic_source_name      varchar(100),
    is_paid_traffic          boolean,
    project_name             varchar(255),
    step                     varchar(255),
    session_cnt              integer,
    searches_cnt             integer,
    event_cnt                integer,
    away_revenue_usd         numeric(18, 9),
    session_create_page_type integer
);

alter table funnel_agg
    owner to postgres;

grant select on funnel_agg to readonly;

grant delete, insert, select, update on funnel_agg to ono;

grant delete, insert, references, select, trigger, truncate, update on funnel_agg to vbe;

