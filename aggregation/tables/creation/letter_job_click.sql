create table aggregation.letter_job_click
(
    country_id         smallint,
    account_id         integer,
    verify_datediff    integer,
    job_click_datediff integer,
    keyword            varchar,
    salary             integer,
    region_name        varchar,
    region_id          integer,
    radius_km          smallint,
    letter_type        integer,
    click_source_id    integer,
    away_cnt           bigint,
    jdp_cnt            bigint
);

alter table aggregation.letter_job_click
    owner to postgres;


create unique index uidx_letter_job_click on aggregation.letter_job_click (country_id, account_id, verify_datediff, job_click_datediff, keyword, salary, region_id, radius_km, letter_type, click_source_id);
