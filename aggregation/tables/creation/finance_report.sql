-- auto-generated definition
create table finance_report
(
    date                    date,
    year_date               integer,
    quarter_date            integer,
    month_date              integer,
    week_date               integer,
    country                 char(100),
    country_code            char(10),
    revenue_type            char(50),
    revenue_usd             double precision,
    test_revenue_usd        double precision,
    adv_impression          integer,
    adv_view                integer,
    adv_click               integer,
    project_site            char(100),
    manager_name            char(50),
    cost_type               char(50),
    ga_channel              char(100),
    cost_usd                double precision,
    ga_users                integer,
    ga_session              integer,
    project_site_budget_usd double precision
);

alter table finance_report
    owner to ono;

grant select on finance_report to readonly;

grant select on finance_report to vbe;

