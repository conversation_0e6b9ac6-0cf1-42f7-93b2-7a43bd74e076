create table aggregation.registration_email_funnel
(
    country_id                                      integer,
    update_datetime                                 timestamp,
    session_datediff                                integer,
    is_complete_data                                smallint,
    current_traffic_source_id                       integer,
    is_returned                                     integer,
    device_type_id                                  integer,
    is_local                                        integer,
    session_create_page_type                        integer,
    registration_trigger_cnt                        bigint,
    registration_source_id                          smallint,
    serp_test_group_lists                           text,
    email_test_group_lists                          text,
    account_id                                      integer,
    session_cnt                                     bigint,
    session_with_registration_trigger_cnt           bigint,
    new_account_cnt                                 bigint,
    new_verified_account_cnt                        bigint,
    account_with_at_least_1_open_letter_cnt_0_7     bigint,
    account_with_at_least_2_open_letter_cnt_0_7     bigint,
    account_with_at_least_1_interact_letter_cnt_0_7 bigint,
    account_with_at_least_2_interact_letter_cnt_0_7 bigint,
    verified_email_revenue                          double precision,
    verified_email_revenue_0_3                      double precision,
    verified_email_revenue_4_7                      double precision,
    verified_email_revenue_8_14                     double precision,
    verified_total_revenue                          double precision,
    verified_total_revenue_0_3                      double precision,
    verified_total_revenue_4_7                      double precision,
    verified_total_revenue_8_14                     double precision,
    verified_away_clicks_premium                    numeric,
    verified_away_clicks_premium_0_3                numeric,
    verified_away_clicks_premium_4_7                numeric,
    verified_away_clicks_premium_8_14               numeric,
    verified_clicks_free                            numeric,
    verified_away_clicks_free_0_3                   numeric,
    verified_away_clicks_free_4_7                   numeric,
    verified_away_clicks_free_8_14                  numeric,
    letter_1_open_cnt_0_14                          numeric,
    letter_1_open_cnt_0_3                           numeric,
    letter_1_open_cnt_4_7                           numeric,
    letter_1_open_cnt_8_14                          numeric,
    letter_8_open_cnt_0_14                          numeric,
    letter_8_open_cnt_0_3                           numeric,
    letter_8_open_cnt_4_7                           numeric,
    letter_8_open_cnt_8_14                          numeric,
    letter_71_open_cnt_0_14                         numeric,
    letter_71_open_cnt_0_3                          numeric,
    letter_71_open_cnt_4_7                          numeric,
    letter_71_open_cnt_8_14                         numeric,
    letter_1_interact_cnt_0_14                      numeric,
    letter_1_interact_cnt_0_3                       numeric,
    letter_1_interact_cnt_4_7                       numeric,
    letter_1_interact_cnt_8_14                      numeric,
    letter_8_interact_cnt_0_14                      numeric,
    letter_8_interact_cnt_0_3                       numeric,
    letter_8_interact_cnt_4_7                       numeric,
    letter_8_interact_cnt_8_14                      numeric,
    letter_71_interact_cnt_0_14                     numeric,
    letter_71_interact_cnt_0_3                      numeric,
    letter_71_interact_cnt_4_7                      numeric,
    letter_71_interact_cnt_8_14                     numeric,
    account_with_verified_email_revenue_cnt_0_7     bigint
);

alter table aggregation.registration_email_funnel
    owner to postgres;

create unique index uidx_registration_email_funnel
    on aggregation.registration_email_funnel (country_id, update_datetime, session_datediff, is_complete_data,
                                              current_traffic_source_id, is_returned, device_type_id, is_local,
                                              session_create_page_type, registration_trigger_cnt,
                                              registration_source_id, serp_test_group_lists, email_test_group_lists,
                                              account_id);


