create table aggregation.serp_campaign_rivals_agg
(
    country_id               smallint       not null,
    date_diff                integer        not null,
    target_campaign_id       integer        not null,
    rival_campaign_id        integer        not null,
    click_price_usd          numeric(14, 5) not null,
    serp_click_value         numeric(14, 5) not null,
    total_search_cnt         integer,
    search_cnt               integer,
    impression_cnt           integer,
    impression_on_screen_cnt integer,
    click_cnt                integer,
    position_mean            numeric(14, 5),
    position_min             integer,
    position_max             integer,
    primary key (country_id, date_diff, target_campaign_id, rival_campaign_id, click_price_usd, serp_click_value)
);