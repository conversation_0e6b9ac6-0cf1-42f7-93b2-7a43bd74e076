-- auto-generated definition
create table session_abtest_agg
(
    country_id                smallint not null,
    group_num                 varchar(255),
    action_datediff           integer,
    is_returned               integer,
    is_mobile                 integer,
    is_local                  integer,
    session_create_page_type  integer,
    traffic_source_id         integer,
    current_traffic_source_id integer,
    is_anomalistic            integer,
    attribute_name            varchar(255),
    attribute_value           integer,
    metric_name               varchar(255),
    metric_value              numeric(18, 9)
);

alter table session_abtest_agg
    owner to postgres;

grant select on session_abtest_agg to readonly;

