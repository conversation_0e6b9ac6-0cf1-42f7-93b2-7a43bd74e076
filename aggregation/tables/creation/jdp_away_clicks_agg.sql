-- auto-generated definition
create table jdp_away_clicks_agg
(
    country_id               smallint not null,
    action_datediff          integer  not null,
    traffic_source_name      varchar(100),
    placement                varchar(100),
    away_type                varchar(100),
    project_name             varchar(100),
    campaign_name            varchar(500),
    id_campaign              integer,
    is_paid                  varchar(100),
    is_mobile                integer,
    revenue_usd              numeric(18, 12),
    jdp_away_count           integer,
    project_id               integer,
    ip_cc                    varchar(50),
    is_paid_overflow         smallint,
    is_returned              smallint,
    session_create_page_type integer,
    id_current_traf_source   integer,
    id_traf_source           integer,
    is_local                 integer, 
    id_account_chatbot       bigint,
    duplicated_count         integer,
    duplicated_revenue_usd   numeric
);

alter table jdp_away_clicks_agg
    owner to postgres;

grant select on jdp_away_clicks_agg to readonly;

