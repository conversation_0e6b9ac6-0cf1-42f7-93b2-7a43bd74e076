create table if not exists aggregation.session_daily_agg
(
	country_id smallint not null,
	action_datediff integer not null,
	id_traf_source integer,
	id_current_traf_source integer,
	session_create_page_type integer,
	total_session_cnt integer,
	bot_session_cnt integer,
	local_session_nobot_cnt integer,
	mobile_session_nobot_cnt integer,
	returned_session_nobot_cnt integer
);

alter table aggregation.session_daily_agg owner to postgres;

grant select on aggregation.session_daily_agg to readonly;
