-- auto-generated definition
create table project_conversions_daily
(
    id                           bigserial,
    country_id                   integer not null,
    metric                       varchar,
    session_date                 date,
    project_id                   integer,
    project_name                 varchar,
    campaign_name                varchar,
    away_type                    varchar,
    campaign_id                  integer,
    is_mobile                    smallint,
    traffic_name                 varchar,
    traffic_is_paid              boolean,
    channel                      varchar,
    ip_cc                        varchar,
    name                         varchar,
    away_revenue                 numeric(15, 5),
    away_revenue_origin_currency numeric(15, 5),
    aways                        integer,
    conversions                  integer,
    is_returned                  smallint,
    session_create_page_type     integer,
    id_current_traf_source       integer,
    constraint pk_project_conversions_daily_id
        primary key (id, country_id)
);

alter table project_conversions_daily
    owner to yiv;

grant select on project_conversions_daily to readonly;

