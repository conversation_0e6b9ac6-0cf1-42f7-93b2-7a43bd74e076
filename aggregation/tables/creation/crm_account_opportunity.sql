create table aggregation.crm_account_opportunity
(
    account_id                      varchar(50),
    account_name                    varchar(100),
    account_owner_name              varchar(50),
    account_owner_email             varchar(50),
    opportunity_created_name        varchar(50),
    opportunity_created_email       varchar(50),
    account_country                 varchar(10),
    account_found_in                varchar(50),
    account_source_type             varchar(50),
    account_client_segment          varchar(50),
    account_sales_potential         numeric(14, 5),
    opportunity_id                  varchar(50),
    opportunity_name                varchar(100),
    opportunity_owner_name          varchar(50),
    opportunity_owner_email         varchar(50),
    opportunity_amount              numeric(14, 5),
    opportunity_found_in            varchar(50),
    opportunity_country             varchar(10),
    opportunity_stage               varchar(50),
    opportunity_close_reason        varchar(50),
    opportunity_cancel_reason       varchar(50),
    opportunity_stage_before_losing varchar(50),
    opportunity_losing_date         timestamp,
    opportunity_created_date        timestamp,
    opportunity_due_date            timestamp,
    activity_id                     varchar(50),
    activity_result                 varchar(50),
    activity_start_date             timestamp,
    activity_due_date               timestamp
);