create table aggregation.retention_agg
(
	country_id smallint,
	first_visit_date_diff integer,
	groups varchar(200),
	is_mobile smallint,
	is_local smallint,
	session_create_page_type integer,
	id_traffic_source integer,
	id_current_traffic_source integer,
	session_cnt integer,
	user_cnt integer,
	week_retention_user_cnt integer,
	second_week_retention_user_cnt integer,
	third_week_retention_user_cnt integer,
	fourth_week_retention_user_cnt integer,
	month_retention_user_cnt integer
);

alter table aggregation.retention_agg owner to postgres;

grant select on aggregation.retention_agg to readonly;

