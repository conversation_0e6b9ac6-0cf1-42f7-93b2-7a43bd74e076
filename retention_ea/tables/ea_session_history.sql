with ea as (
    select id as id_employer
        , upper(country_code) as country_code
        , moderation_status
        , sources
    from imp_employer.employer
),
rel_acc as (
    select id_employer
        , id_account
        , sources
    from imp_employer.employer_account
),
sess as (
    select id_account
        , cast(date_started as date) as date_session
        , case when using_secret_key=FALSE then 0 else 1 end as is_joobler
        , count(id) as cnt_sess_by_day
        , sources
    from imp_employer.employer_account_session
    where cast(date_started as date) = '${CT_NOW}'
    group by id_account
           , cast(date_started as date)
           , case when using_secret_key=FALSE then 0 else 1 end
           , sources
)
select  ea.country_code
    , ea.id_employer as employer_id
    , ea.moderation_status as moderation_status_ea
    , sess.id_account as account_id
    , sess.is_joobler
    , sess.date_session as session_date
    , sess.cnt_sess_by_day
    , sess.sources
    , current_date as time_key
from ea
    inner join rel_acc on ea.id_employer=rel_acc.id_employer and ea.sources=rel_acc.sources
    inner join sess on sess.id_account=rel_acc.id_account and sess.sources=rel_acc.sources;
