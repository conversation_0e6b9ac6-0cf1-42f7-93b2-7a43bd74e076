with ea as (
    select id as id_employer
        , id_cdp
        , upper(country_code) as country_code
        , id_account as id_account_creator
        , cast(date_created as date) as date_created_ea
        , moderation_status
        , type as account_type
        , id_traffic_source
        , id_counterparty
        , source as register_source
        , sources
    from imp_employer.employer
),
rel_acc as (
    select id_employer
        , id_account
        , case when date_deleted is not null then 1 else 0 end as is_delete
        , sources
    from imp_employer.employer_account
),
acc_inf as (
    select id as id_account
        , cast(date_created as date) as acc_date_created
        , is_verified
        , sources
    from imp_employer.account_service_account
)
select distinct ea.id_employer as employer_id
    , coalesce(cdp.company_name,'-99') as employer_name
    , upper(country_code) as country_code
    , coalesce(cdp.industry,'-99') as industry
    , coalesce(cdp.staff_size,'-99') as staff_size
    , coalesce(cdp.type,'-99') as type_business
    , date_created_ea
    , moderation_status as moderation_status_ea
    , account_type
    , coalesce(uts.name, '-99') as name_source
    , register_source
    , id_counterparty as counterparty_id
    , count(acc_inf.id_account) over (partition by ea.id_employer, ea.sources) as cnt_acc_by_employer
    , rank() over (partition by ea.id_employer, ea.sources order by acc_date_created asc) as rank_created_account
    , acc_inf.id_account as account_id
    , acm.roles  -- ('supervisor', 1); -- ('Moderator', 2); -- ('Administrator', 4);-- ('user', 8); есть ли зависимость кол-ва сессий типа
    , case when ea.id_account_creator=acc_inf.id_account then 1 else 0 end as is_account_creator
    , acc_inf.acc_date_created
    , is_verified
    , rel_acc.is_delete
    , ea.sources
    , current_date as time_key -- партицию по sources, country_code
from ea
    inner join rel_acc on ea.id_employer=rel_acc.id_employer and ea.sources=rel_acc.sources
    inner join acc_inf on rel_acc.id_account=acc_inf.id_account and rel_acc.sources=acc_inf.sources
    inner join imp_employer.account_modules acm on acc_inf.id_account=acm.id_account and acc_inf.sources=acm.sources
    left join nsh.u_traffic_source uts on ea.id_traffic_source=uts.id and ea.sources=uts.sources
    left join imp_employer.employer_cdp cdp on ea.id_cdp=cdp.id and ea.sources=cdp.sources
;
