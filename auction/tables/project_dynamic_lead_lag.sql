select country_id, click_statistic_date, project_id, url_address, campaign_id, click_price, currency, click_price_eur,
       click_price_next_day_eur, click_price_next_day, click_price_change, currency_change, click_cnt, job_cnt, impression_cnt,
       click_price_change_2day, click_price_change_3day, click_price_change_4day, click_price_change_5day, click_price_change_6day,
       click_price_change_7day, lag_click_price_change_1day, lag_click_price_change_2day, lag_click_price_change_3day, lag_click_price_change_4day,
       lag_click_price_change_5day, lag_click_price_change_6day, lag_click_price_change_7day
from (
        select pd.country_id,
               pd.click_statistic_date,
               pd.project_id,
               url_address,
               campaign_id,
               click_price,
               currency,
               click_price_eur,
               click_price_next_day_eur,
               click_price_next_day,
               click_price_change,
               currency_change,
               click_cnt,
               job_cnt,
               impression_cnt,
               lead(pd.click_price, 2) OVER (PARTITION BY pd.country_id, pd.project_id, pd.campaign_id order by click_statistic_date)
                   - lead(pd.click_price, 1)
                          OVER (PARTITION BY pd.country_id, pd.project_id, pd.campaign_id order by click_statistic_date)                    as click_price_change_2day,
               lead(pd.click_price, 3) OVER (PARTITION BY pd.country_id, pd.project_id, pd.campaign_id order by click_statistic_date)
                   - lead(pd.click_price, 2)
                          OVER (PARTITION BY pd.country_id, pd.project_id, pd.campaign_id order by click_statistic_date)                    as click_price_change_3day,
               lead(pd.click_price, 4) OVER (PARTITION BY pd.country_id, pd.project_id, pd.campaign_id order by click_statistic_date)
                   - lead(pd.click_price, 3)
                          OVER (PARTITION BY pd.country_id, pd.project_id, pd.campaign_id order by click_statistic_date)                    as click_price_change_4day,
               lead(pd.click_price, 5) OVER (PARTITION BY pd.country_id, pd.project_id, pd.campaign_id order by click_statistic_date)
                   - lead(pd.click_price, 4)
                          OVER (PARTITION BY pd.country_id, pd.project_id, pd.campaign_id order by click_statistic_date)                    as click_price_change_5day,
               lead(pd.click_price, 6) OVER (PARTITION BY pd.country_id, pd.project_id, pd.campaign_id order by click_statistic_date)
                   - lead(pd.click_price, 5)
                          OVER (PARTITION BY pd.country_id, pd.project_id, pd.campaign_id order by click_statistic_date)                    as click_price_change_6day,
               lead(pd.click_price, 7) OVER (PARTITION BY pd.country_id, pd.project_id, pd.campaign_id order by click_statistic_date)
                   - lead(pd.click_price, 6)
                          OVER (PARTITION BY pd.country_id, pd.project_id, pd.campaign_id order by click_statistic_date)                    as click_price_change_7day,

               pd.click_price -
               lag(pd.click_price, 1)
                   OVER (PARTITION BY pd.country_id, pd.project_id, pd.campaign_id order by click_statistic_date)                           as lag_click_price_change_1day,
               lag(pd.click_price, 1) OVER (PARTITION BY pd.country_id, pd.project_id, pd.campaign_id order by click_statistic_date)
                   - lag(pd.click_price, 2)
                         OVER (PARTITION BY pd.country_id, pd.project_id, pd.campaign_id order by click_statistic_date)                     as lag_click_price_change_2day,
               lag(pd.click_price, 2) OVER (PARTITION BY pd.country_id, pd.project_id, pd.campaign_id order by click_statistic_date)
                   - lag(pd.click_price, 3)
                         OVER (PARTITION BY pd.country_id, pd.project_id, pd.campaign_id order by click_statistic_date)                     as lag_click_price_change_3day,
               lag(pd.click_price, 3) OVER (PARTITION BY pd.country_id, pd.project_id, pd.campaign_id order by click_statistic_date)
                   - lag(pd.click_price, 4)
                         OVER (PARTITION BY pd.country_id, pd.project_id, pd.campaign_id order by click_statistic_date)                     as lag_click_price_change_4day,
               lag(pd.click_price, 4) OVER (PARTITION BY pd.country_id, pd.project_id, pd.campaign_id order by click_statistic_date)
                   - lag(pd.click_price, 5)
                         OVER (PARTITION BY pd.country_id, pd.project_id, pd.campaign_id order by click_statistic_date)                     as lag_click_price_change_5day,
               lag(pd.click_price, 5) OVER (PARTITION BY pd.country_id, pd.project_id, pd.campaign_id order by click_statistic_date)
                   - lag(pd.click_price, 6)
                         OVER (PARTITION BY pd.country_id, pd.project_id, pd.campaign_id order by click_statistic_date)                     as lag_click_price_change_6day,
               lag(pd.click_price, 6) OVER (PARTITION BY pd.country_id, pd.project_id, pd.campaign_id order by click_statistic_date)
                   - lag(pd.click_price, 7)
                         OVER (PARTITION BY pd.country_id, pd.project_id, pd.campaign_id order by click_statistic_date)                     as lag_click_price_change_7day
        from auction.project_dynamic pd
        where click_statistic_date between (current_date::date - interval '17 days') and (current_date::date - interval '2 day')) as d
where click_statistic_date = (current_date::date - interval '10 days');
