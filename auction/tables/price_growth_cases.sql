select a.country_id,
       a.project_id,
       a.url_address,
       a.campaign_id,
       a.click_statistic_date,
       a.click_price,
       a.currency,
       a.click_price_next_day,
       a.click_price_eur,
       a.click_price_next_day_eur,
       (a.click_price_change_2day + a.click_price_change_3day + a.click_price_change_4day + a.click_price_change_5day
                                 + a.click_price_change_6day + a.click_price_change_7day)::numeric(18, 4) as click_price_change_2_7day,
       (select sum(cs_before.click_count)
        from imp_statistic.click_statistic cs_before
         where cs_before.country_id = a.country_id and cs_before.id_project = a.project_id and cs_before.id_campaign = a.campaign_id and cs_before.date between (a.click_statistic_date - interval '6 days')::date and a.click_statistic_date
       ) as click_count_7days_before,
       (select sum(cs_after.click_count)
        from imp_statistic.click_statistic cs_after
        where cs_after.country_id = a.country_id and  cs_after.id_project = a.project_id and cs_after.id_campaign = a.campaign_id and cs_after.date between (a.click_statistic_date + interval '1 days')::date and (a.click_statistic_date + interval '7 days')::date
           ) as click_count_7days_after,
       (select sum(cs_before_all.click_count)
        from imp_statistic.click_statistic cs_before_all
         where cs_before_all.country_id = a.country_id and cs_before_all.date between (a.click_statistic_date -interval '6 days')::date and a.click_statistic_date
       ) as all_click_count_7days_before,
       (select sum(cs_after_all.click_count)
       from imp_statistic.click_statistic cs_after_all
        where cs_after_all.country_id = a.country_id and cs_after_all.date between (a.click_statistic_date + interval '1 days')::date and (a.click_statistic_date + interval '7 days')::date
           ) as all_click_count_7days_after,
       (select sum(job_c_before.jobs_count)
        from imp_statistic.campaign_jobs_count job_c_before
         where job_c_before.date between (a.click_statistic_date - interval '6 days')::date and a.click_statistic_date
           and job_c_before.id_campaign = a.campaign_id and job_c_before.country = a.country_id
       ) as jobs_7days_before,
       (select sum(job_c_after.jobs_count)
        from imp_statistic.campaign_jobs_count job_c_after
         where job_c_after.date between (a.click_statistic_date + interval '1 days')::date and (a.click_statistic_date + interval '7 days')::date
           and job_c_after.id_campaign = a.campaign_id and job_c_after.country = a.country_id
           ) as jobs_7days_after,
       (select sum(job_c_before.jobs_count)
        from imp_statistic.campaign_jobs_count job_c_before
         where job_c_before.date between (a.click_statistic_date - interval '6 days')::date and a.click_statistic_date and job_c_before.country = a.country_id
       ) as all_jobs_7days_before,
       (select sum(job_c_after.jobs_count)
        from imp_statistic.campaign_jobs_count job_c_after
         where job_c_after.date between (a.click_statistic_date + interval '1 days')::date and (a.click_statistic_date + interval '7 days')::date and job_c_after.country = a.country_id
           ) as all_jobs_7days_after,
       (select sum(i_before.impressions_count)
        from imp_statistic.impression_statistic i_before
         where public.fn_get_timestamp_from_date_diff(i_before.date_diff) between (a.click_statistic_date - interval '6 days')::date and a.click_statistic_date
           and i_before.id_campaign = a.campaign_id and i_before.country = a.country_id
       ) as impressions_7days_before,
       (select sum(i_after.impressions_count)
        from imp_statistic.impression_statistic i_after
         where public.fn_get_timestamp_from_date_diff(i_after.date_diff) between (a.click_statistic_date + interval '1 days')::date and (a.click_statistic_date + interval '7 days')::date
           and i_after.id_campaign = a.campaign_id and i_after.country = a.country_id
           ) as impressions_7days_after,
       (select sum(i_before.impressions_count)
        from imp_statistic.impression_statistic i_before
         where public.fn_get_timestamp_from_date_diff(i_before.date_diff) between (a.click_statistic_date - interval '6 days')::date and a.click_statistic_date and i_before.country = a.country_id
       ) as all_impressions_7days_before,
       (select sum(i_after.impressions_count)
        from imp_statistic.impression_statistic i_after
         where public.fn_get_timestamp_from_date_diff(i_after.date_diff) between (a.click_statistic_date + interval '1 days')::date and (a.click_statistic_date + interval '7 days')::date and i_after.country = a.country_id
           ) as all_impressions_7days_after
    from auction.project_dynamic_lead_lag a
where a.click_price_change/nullif(a.click_price,0) > 0.1
 and a.click_price_change_2day >= 0
and a.click_price_change_3day >= 0
 and a.click_price_change_4day >= 0
and a.click_price_change_5day >= 0
 and a.click_price_change_6day >= 0
and a.click_price_change_7day >= 0
  and a.lag_click_price_change_1day >=0
  and a.lag_click_price_change_2day>=0
  and a.lag_click_price_change_3day >= 0
  and a.lag_click_price_change_4day >= 0
  and a.lag_click_price_change_5day >= 0
  and a.lag_click_price_change_6day >= 0
  and a.lag_click_price_change_7day >= 0
and a.currency = a.currency_change;
