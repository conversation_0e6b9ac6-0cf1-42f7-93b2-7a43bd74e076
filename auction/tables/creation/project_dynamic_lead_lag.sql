create table auction.project_dynamic_lead_lag
(
	id serial
		constraint pk_project_dynamic_lead_lag_id
			primary key,
	country_id smallint not null,
	click_statistic_date date,
	project_id integer,
	url_address varchar(400),
	campaign_id integer,
	click_price numeric(18,4),
	currency varchar(5),
	click_price_eur numeric(18,4),
	click_price_next_day_eur numeric(18,4),
	click_price_next_day numeric(18,4),
	click_price_change numeric(18,4),
	currency_change varchar(5),
	click_cnt integer,
	job_cnt integer,
	impression_cnt bigint,
	click_price_change_2day numeric(18,4),
	click_price_change_3day numeric(18,4),
	click_price_change_4day numeric(18,4),
	click_price_change_5day numeric(18,4),
	click_price_change_6day numeric(18,4),
	click_price_change_7day numeric(18,4),
	lag_click_price_change_1day numeric(18,4),
	lag_click_price_change_2day numeric(18,4),
	lag_click_price_change_3day numeric(18,4),
	lag_click_price_change_4day numeric(18,4),
	lag_click_price_change_5day numeric(18,4),
	lag_click_price_change_6day numeric(18,4),
	lag_click_price_change_7day numeric(18,4)
);

alter table auction.project_dynamic_lead_lag owner to postgres;

grant select on auction.project_dynamic_lead_lag to readonly;

grant select on auction.project_dynamic_lead_lag to writeonly_product;

