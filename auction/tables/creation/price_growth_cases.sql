create table auction.price_growth_cases
(
	id serial
		constraint pk_price_growth_cases_id
			primary key,
	country_id smallint not null,
	project_id integer,
	url_address varchar(400),
	campaign_id integer,
	click_statistic_date date,
	click_price numeric(18,4),
	currency varchar(10),
	click_price_next_day numeric(18,4),
	click_price_eur numeric(18,4),
	click_price_next_day_eur numeric(18,4),
	click_price_change_2_7day numeric(18,4),
	click_cnt_7days_before integer,
	click_cnt_7days_after integer,
	all_click_cnt_7days_before integer,
	all_click_cnt_7days_after integer,
	jobs_7days_before integer,
	jobs_7days_after integer,
	all_jobs_7days_before integer,
	all_jobs_7days_after integer,
	impressions_7days_before integer,
	impressions_7days_after integer,
	all_impressions_7days_before integer,
	all_impressions_7days_after integer
);

alter table auction.price_growth_cases owner to postgres;

grant select on auction.price_growth_cases to readonly;

grant select on auction.price_growth_cases to writeonly_product;

