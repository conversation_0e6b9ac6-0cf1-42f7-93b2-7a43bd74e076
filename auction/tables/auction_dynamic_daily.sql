select cs.country_id,
       cs.date,
       cs.id_project,
       cs.site,
       cs.id_campaign,
       avg(cs.click_price) as click_price,
       round(avg(cs.click_price)*(select ic.value_to_eur from auction.v_info_currency ic where ic.currency_date <= cs.date and ic.currency_name = cs.currency and ic.country_id = cs.country_id order by ic.currency_date desc limit 1),6) as click_price_eur,
       cs.currency,
       sum(cs.click_count) as click_count,
       sum(cs.job_count) as job_count,
       sum(cs.organic_count) as organic_count
from imp_statistic.click_statistic cs
where date >= '2020-08-01'
group by cs.country_id,
         cs.date,
         cs.id_project,
         cs.site,
         cs.id_campaign,
         cs.currency;
