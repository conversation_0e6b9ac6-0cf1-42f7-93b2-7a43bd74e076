select cs.country_id,
       cs.date,
       cs.id_project,
       cs.site,
       cs.id_campaign,
       cs.click_price,
       cs.currency,
       round(cs.click_price*(select ic.value_to_eur from auction.v_info_currency ic where ic.currency_date <= cs.date and ic.currency_name = cs.currency and ic.country_id = cs.country_id order by ic.currency_date desc limit 1),6) as click_price_eur,
       round(lead(cs.click_price) OVER (PARTITION BY cs.country_id, CS.id_project, cs.id_campaign order by date)*(select ic.value_to_eur from auction.v_info_currency ic where ic.currency_date <= cs.date and ic.currency_name = cs.currency and ic.country_id = cs.country_id order by ic.currency_date desc limit 1),6) as click_price_next_day_eur,
       lead(cs.click_price) OVER (PARTITION BY cs.country_id, CS.id_project, cs.id_campaign order by date) as click_price_next_day,
       lead(cs.click_price) OVER (PARTITION BY cs.country_id, CS.id_project, cs.id_campaign order by date) - cs.click_price as click_price_change,
       lead(cs.currency) OVER (PARTITION BY cs.country_id, CS.id_project, cs.id_campaign order by date)  as currency_change,
       cs.click_count,
       cs.job_count,
       cs.impression_count
from (select cs.country_id,
             cs.date,
             cs.id_project,
             cs.site,
             cs.id_campaign,
             avg(cs.click_price) as click_price,
             cs.currency,
             sum(cs.click_count) as click_count,
             sum(cs.job_count) as job_count,
             (select max(ist.impressions_count)
              from imp_statistic.impression_statistic ist
              where ist.id_campaign = cs.id_campaign and ist.country = cs.country_id
                    and public.fn_get_timestamp_from_date_diff(ist.date_diff) = cs.date
                        ) as impression_count
      from imp_statistic.click_statistic cs
      where date >= '2020-08-01'
      group by cs.country_id,
               cs.date,
               cs.id_project,
               cs.site,
               cs.id_campaign,
               cs.currency
     ) cs;
