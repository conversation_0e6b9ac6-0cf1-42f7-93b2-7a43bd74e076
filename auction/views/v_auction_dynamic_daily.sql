create or replace view auction.v_auction_dynamic_daily(id, country_id, click_statistic_date, project_id, url_address, campaign_id, click_price, click_price_eur, currency, click_cnt, job_cnt, organic_cnt, country_name) as
	SELECT add.id,
       add.country_id,
       add.click_statistic_date,
       add.project_id,
       add.url_address,
       add.campaign_id,
       add.click_price,
       add.click_price_eur,
       add.currency,
       add.click_cnt,
       add.job_cnt,
       add.organic_cnt,
       c.name_country_eng AS country_name
FROM auction.auction_dynamic_daily add
         LEFT JOIN dimension.countries c ON c.id = add.country_id;
