-- дзвінки по кожній вакан<PERSON><PERSON><PERSON>
create table employer.job_call_daily as
select fn_get_date_diff(jc.date)                                           as action_datediff,
       jc.id_employer                                                      as employer_id,
       jc.id_job                                                           as job_id,
       count(distinct case when jc.flags & 4 = 0 /*not repeated*/ then jc.phone end) as call_cnt,
       count(distinct case
                          when jc.status = 1 /* Answered */
                              and jc.flags & 4 = 0 /* not repeated */
                              and (jc.flags & 1) = 1 /* paid */
                              then jc.id
                      end)                                                 as call_jcoin_cnt,
       count(distinct case
                          when jc.status = 1 /* Answered */
                              and jc.flags & 4 = 0 /* not repeated */
                              then jc.phone
                      end)                                                 as call_answered_cnt,
       count(distinct case when jc.status = 2 then jc.phone end)           as call_missed_cnt,
       count(distinct case when jc.status = 3 then jc.phone end)           as call_rejected_cnt,
       count(distinct case when jc.flags & 4 = 4 then jc.phone end)        as calls_repeated_after_proper_cnt
from imp_employer.job_call jc
where jc.sources = 1
  and date(jc.date) >= '2021-08-01'::date
group by fn_get_date_diff(jc.date), jc.id_employer, jc.id_job;

alter table employer.job_call_daily
    add primary key (action_datediff, job_id);