create table employer.subscription_break as
with vspi_inner as (
    select database_source_id,
           e.country_code,
           employer_id,
           subscription_id,
           subscription_order_datetime,
           packet_start_datetime as first_packet_start_datetime,
           subscription_expiring_datetime
    from employer.v_subscription_packet vspi
             join imp_employer.employer e
                  on vspi.database_source_id = e.sources
                      and vspi.employer_id = e.id
    where database_source_id = 1
      and packet_rank = 1
      and e.country_code in ('ua', 'hu')
)
select vspi.database_source_id,
       e.country_code,
       vspi.employer_id,
       vspi.subscription_id                      as parent_subscription_id,
       vspi.subscription_order_datetime          as parent_subscription_order_datetime,
       vspi.subscription_expiring_datetime       as parent_subscription_expiring_datetime,
       vspi_inner.subscription_id                as break_subscription_id,
       vspi_inner.subscription_order_datetime    as break_subscription_order_datetime,
       vspi_inner.first_packet_start_datetime    as break_start_datetime,
       vspi_inner.subscription_expiring_datetime as break_end_datetime
from employer.v_subscription_packet_info vspi
         join imp_employer.employer e
              on vspi.database_source_id = e.sources
                  and vspi.employer_id = e.id
         join vspi_inner
              on vspi.database_source_id = vspi_inner.database_source_id
                  and vspi.employer_id = vspi_inner.employer_id
                  and vspi.subscription_id <> vspi_inner.subscription_id
                  and vspi_inner.first_packet_start_datetime > vspi.subscription_order_datetime
                  and vspi_inner.first_packet_start_datetime < vspi.subscription_expiring_datetime
where vspi.database_source_id = 1
  and e.country_code in ('ua', 'hu');

alter table employer.subscription_break
    add primary key (database_source_id, country_code, parent_subscription_id, break_subscription_id);
