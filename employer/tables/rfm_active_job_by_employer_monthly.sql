with monthly_job as
(
select distinct id_job
    , id_employer
    -- если за месяц опубликованная вакансия была хотя бы раз в премиуме - значит она премиум
    -- active_state=1 - premium, active_state=2 - free
    , min(active_state) over (partition by sources, id_employer, id_job) as active_state
    , sources
from imp_employer.snaphot_employer_jobs
where active_state!=0 --активная
    -- отбираем все вакансии не старше 1-го числа предыдущего месяца
    and date_diff >= public.fn_get_date_diff(cast((date_trunc('month', current_date) - interval '1 month') as date))
    ),
ea as (
select database_source_id
    , employer_id
from employer.ea_payment_history
group by database_source_id, employer_id
    ),
job_cnt as (
select distinct ea.employer_id as employer_id
    , coalesce(count(id_job) over (partition by ea.employer_id, ea.database_source_id),0) as job_total_cnt
    , coalesce(count(id_job) over (partition by ea.employer_id, ea.database_source_id, active_state),0) as job_by_state_cnt
    , coalesce(active_state, 0) as active_state
    , ea.database_source_id
from ea
    left join monthly_job on ea.database_source_id=monthly_job.sources
                            and ea.employer_id=monthly_job.id_employer
    )
select employer_id
    , job_total_cnt
    , job_by_state_cnt as premium_job_cnt
    , database_source_id
from job_cnt
where active_state=1;
