-- Хранит данные первых работодателей, которым мы набрасывали рекомендации до общего (глобального) релиза - 2021-02-01.
-- Первые рекомендации на всех работодателей сформировались 2020-02-02.
with active_j as
    (
select distinct date_diff
     , country_code
     , active_state
     , id_job
     , id_employer
     , sources
from imp_employer.snaphot_employer_jobs sej
where active_state!=0
    and country_code='UA'
    and date_diff<44227 -- 2021-02-02 - релиз для всех
    and id_employer in (173612, 169801, 172163, 69629, 14,12649,232428,321431) -- первые ЕА до релиза
    )
select distinct active_j.date_diff
     , country_code
     , id_employer
     , active_j.id_job
     , active_state
     , case when arg.id_job is not null then 1 else 0 end as has_recommendation
     , active_j.sources
from active_j
    left join imp_employer.apply_recommendation_group arg on arg.sources=active_j.sources
        and arg.id_job=active_j.id_job and arg.date_diff=active_j.date_diff
;
