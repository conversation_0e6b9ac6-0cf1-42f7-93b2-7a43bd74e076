select case when f.sources is null then p.sources else f.sources end                                          as sources,
       case
           when f.id_employer is null then p.id_employer
           else f.id_employer end                                                                             as id_employer,
       case when f.id_job is null then p.id_job else f.id_job end                                             as id_job,
       case when f.date_diff is null then p.date_diff else f.date_diff end                                    as date_diff,
       case
           when (case when f.date_diff is null then p.date_diff else f.date_diff end) < fn_get_date_diff('2021-08-01')
               then (case
                         when f.is_premium is not null and p.is_premium is null then 'free'
                         when f.is_premium is null and p.is_premium is not null then 'paid'
                         when f.is_premium is not null and p.is_premium is not null and p.impressions/f.impressions >= 0.7
                              then 'paid'
                         when f.is_premium is not null and p.is_premium is not null and p.impressions/f.impressions < 0.7
                              then 'paid/free' end) end                                                        as premium_type,
       case when f.impressions is not null and p.impressions is not null and p.impressions/f.impressions >= 0.7 then p.impressions
            else coalesce(f.impressions,0) + coalesce(p.impressions,0) end as impressions
from (select jsd.sources,
             id_employer,
             id_job,
             date_diff,
             is_premium,
             sum(impressions) as impressions
      from imp_employer.job_statistics_detailed jsd
               join imp_employer.job j
                    on j.sources = jsd.sources
                        and j.id = jsd.id_job
               join imp_employer.employer e
                    on j.sources = e.sources
                        and j.id_employer = e.id
      where jsd.sources = 1
        and e.country_code = 'ua'
        and is_premium = false
        and jsd.date_diff between 44376 /*2021-07-01*/ and 44467--fn_get_date_diff(current_date - 1)
        and impressions > 0
      group by jsd.sources,
               id_employer,
               id_job,
               date_diff,
               is_premium
     ) f
         full outer join (select jsd.sources,
                                 id_employer,
                                 id_job,
                                 date_diff,
                                 is_premium,
                                 sum(impressions) as impressions
                          from imp_employer.job_statistics_detailed jsd
                                   join imp_employer.job j
                                        on j.sources = jsd.sources
                                            and j.id = jsd.id_job
                                   join imp_employer.employer e
                                        on j.sources = e.sources
                                            and j.id_employer = e.id
                          where jsd.sources = 1
                            and is_premium = true
                            and jsd.date_diff between 44376 /*2021-07-01*/ and 44467--fn_get_date_diff(current_date - 1)
                            and impressions > 0
                            --and id_job = 1297162
                          group by jsd.sources,
                                   id_employer,
                                   id_job,
                                   date_diff,
                                   is_premium
) p
                         on f.sources = p.sources
                             and f.id_employer = p.id_employer
                             and f.id_job = p.id_job
                             and f.date_diff = p.date_diff;
