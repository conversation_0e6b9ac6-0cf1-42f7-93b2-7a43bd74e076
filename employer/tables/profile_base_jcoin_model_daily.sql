create table employer.profile_base_jcoin_model_daily as
select ps.sources                                                                             as database_source_id,
       e.country_code,
       vsp.subscription_id,
       vsp.packet_rank,
       ps.date_diff                                                                           as action_datediff,
       vsp.employer_id,
       count(distinct ps.id)                                                                  as search_cnt,
       count(distinct case when psa.action = 0 then psa.id_profile end)                       as profile_viewed_cnt,
       count(distinct case when poc.is_paid_jcoin = 0 then poc.profile_id end)                as profile_open_contact_free_packet_cnt,
       count(distinct case when poc.is_paid_jcoin = 1 then poc.profile_id end)                as profile_open_contact_paid_packet_cnt,
       count(distinct case when poc.is_paid_jcoin = 0 then poc.profile_id end) *
       ftjp.jcoin_cnt                                                                         as profile_free_packet_jcoin_cnt,
       count(distinct case when poc.is_paid_jcoin = 1 then poc.profile_id end) *
       ftjp.jcoin_cnt                                                                         as profile_paid_packet_jcoin_cnt,
       coalesce(sum(case when poc.is_paid_jcoin = 0 then poc.jcoin_utilized_real_cnt end),
                0)                                                                            as profile_free_packet_jcoin_real_cnt,
       coalesce(sum(case when poc.is_paid_jcoin = 1 then poc.jcoin_utilized_real_cnt end),
                0)                                                                            as profile_paid_packet_jcoin_real_cnt,
       count(distinct case when psa.action = 4 then psa.id_profile end)                       as profile_message_cnt
from imp_employer.profile_search ps
         join imp_employer.employer_account_session eas
              on eas.sources = ps.sources and eas.date_diff = ps.date_diff and eas.id = ps.id_session and
                 eas.using_secret_key = false
         join imp_employer.employer_account ea
              on ps.sources = ea.sources and ps.id_account = ea.id_account
         join imp_employer.employer e
              on e.sources = ea.sources and e.id = ea.id_employer
         join dimension.feature_type_jcoin_price ftjp
              on ftjp.sources = e.sources and ftjp.country_code = e.country_code
                  and ftjp.feature_type_id = 3 /* profile base */
         join employer.m_subscription_cont_period vscp
              on vscp.database_source_id = e.sources and vscp.employer_id = e.id and
                 ps.date between vscp.period_start_datetime and vscp.period_end_datetime
         join employer.m_subscription_packet vsp
              on vsp.database_source_id = vscp.database_source_id and vsp.employer_id = vscp.employer_id
                  and vsp.subscription_id = vscp.subscription_id
                  and ps.date between vsp.packet_start_datetime and vsp.packet_end_datetime
         left join imp_employer.profile_search_action psa
                   on ps.sources = psa.sources and ps.id = psa.id_search and ps.date_diff = psa.date_diff
         left join employer.m_profile_open_contact_with_packet poc
                   on psa.sources = poc.database_source_id and ea.id_employer = poc.employer_id and
                      psa.id_profile = poc.profile_id and psa.date_diff = fn_get_date_diff(poc.action_datetime) and
                      poc.feature_type_id = 3 /* profile base */ and psa.action = 9
where ps.sources = 1
  and e.country_code in ('ua', 'hu')
group by ps.sources,
         e.country_code,
         ps.date_diff,
         vsp.employer_id,
         vsp.subscription_id,
         vsp.packet_rank,
         ftjp.jcoin_cnt;
