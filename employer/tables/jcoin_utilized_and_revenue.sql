-- аналог m_jcoin_utilized
create table employer.jcoin_utilized_and_revenue as
SELECT poc.database_source_id,
       poc.country_code,
       poc.subscription_id,
       poc.packet_rank,
       poc.action_datediff,
       poc.employer_id,
       poc.packet_price,
       poc.jcoin_cnt,
       poc.is_paid_jcoin                                                              as is_paid,
       count(distinct case when poc.feature_type_id = 1 then poc.open_contact_id end) as apply_profile_open_contact_cnt,
       count(distinct
             case
                 when poc.feature_type_id = 2
                     then poc.open_contact_id end)                                    as digital_recruiter_profile_open_contact_cnt,
       count(distinct
             case
                 when poc.feature_type_id = 3
                     then poc.open_contact_id end)                                    as profile_base_profile_open_contact_cnt,
       count(distinct case when poc.feature_type_id = 4 then poc.open_contact_id end) as call_open_contact_cnt,
       coalesce(sum(case when poc.feature_type_id = 1 then jcoin_utilized_real_cnt end),
                0)                                                                    as apply_profile_open_contact_real_jcoin_cnt,
       coalesce(sum(case when poc.feature_type_id = 2 then jcoin_utilized_real_cnt end),
                0)                                                                    as digital_recruiter_profile_open_contact_real_jcoin_cnt,
       coalesce(sum(case when poc.feature_type_id = 3 then jcoin_utilized_real_cnt end),
                0)                                                                    as profile_base_profile_open_contact_real_jcoin_cnt,
       coalesce(sum(case when poc.feature_type_id = 4 then jcoin_utilized_real_cnt end),
                0)                                                                    as call_real_jcoin_cnt,
       sum(case when poc.feature_type_id = 1 then poc.jcoin_utilized_real_cnt end) *
       COALESCE(vjmjp.jcoin_paid_price_without_vat_uah, 0::numeric)                   as apply_revenue_uah,
       sum(case when poc.feature_type_id = 2 then poc.jcoin_utilized_real_cnt end) *
       COALESCE(vjmjp.jcoin_paid_price_without_vat_uah, 0::numeric)                   as digital_recruiter_revenue_uah,
       sum(case when poc.feature_type_id = 3 then poc.jcoin_utilized_real_cnt end) *
       COALESCE(vjmjp.jcoin_paid_price_without_vat_uah, 0::numeric)                   as profile_base_revenue_uah,
       sum(case when poc.feature_type_id = 4 then poc.jcoin_utilized_real_cnt end) *
       COALESCE(vjmjp.jcoin_paid_price_without_vat_uah, 0::numeric)                   as call_revenue_uah,
       round(sum(case when poc.feature_type_id = 1 then poc.jcoin_utilized_real_cnt end) *
             COALESCE(vjmjp.jcoin_paid_price_without_vat_uah, 0::numeric) / cs.to_eur,
             3)                                                                       as apply_revenue_eur,
       round(sum(case when poc.feature_type_id = 2 then poc.jcoin_utilized_real_cnt end) *
             COALESCE(vjmjp.jcoin_paid_price_without_vat_uah, 0::numeric) / cs.to_eur,
             3)                                                                       as digital_recruiter_revenue_eur,
       round(sum(case when poc.feature_type_id = 3 then poc.jcoin_utilized_real_cnt end) *
             COALESCE(vjmjp.jcoin_paid_price_without_vat_uah, 0::numeric) / cs.to_eur,
             3)                                                                       as profile_base_revenue_eur,
       round(sum(case when poc.feature_type_id = 4 then poc.open_contact_id end) *
             COALESCE(vjmjp.jcoin_paid_price_without_vat_uah, 0::numeric) / cs.to_eur,
             3)                                                                       as call_revenue_eur,
       COALESCE(vjmjp.jcoin_paid_price_without_vat_uah, 0::numeric)                   AS jcoin_paid_price_without_vat_uah,
       round(COALESCE(vjmjp.jcoin_paid_price_without_vat_uah, 0::numeric) / cs.to_eur,
             3)                                                                       as jcoin_paid_price_without_vat_eur
FROM employer.m_profile_open_contact_with_packet poc
         join imp_statistic.currency_source cs
              on cs.currency = 'UAH'
                  and poc.action_datediff = fn_get_date_diff(cs.date)
         left join employer.v_jcoin_model_jcoin_price vjmjp on poc.database_source_id = vjmjp.database_source_id and
                                                               poc.country_code = vjmjp.country_code and
                                                               poc.subscription_id = vjmjp.subscription_id and
                                                               poc.action_datetime >= vjmjp.payment_start_datetime and
                                                               poc.action_datetime <= vjmjp.payment_end_datetime
WHERE poc.action_datetime IS NOT NULL
  AND poc.packet_rank IS NOT NULL
  AND poc.country_code in ('ua', 'hu', 'ro')
  AND poc.action_datediff >= 44680 /* datediff strart monetization Hungary*/
GROUP BY poc.database_source_id, poc.country_code, poc.employer_id, poc.subscription_id, poc.packet_price,
         poc.jcoin_cnt, poc.is_paid_jcoin,
         poc.action_datediff, (COALESCE(vjmjp.jcoin_paid_price_without_vat_uah, 0::numeric)), poc.packet_rank,
         cs.to_eur;

ALTER TABLE employer.jcoin_utilized_and_revenue
    ADD CONSTRAINT orderjcoin_utilized_and_revenue_pk
        PRIMARY KEY (database_source_id,
                     country_code,
                     subscription_id,
                     packet_rank,
                     action_datediff,
                     employer_id,
                     packet_price,
                     jcoin_cnt,
                     is_paid);
