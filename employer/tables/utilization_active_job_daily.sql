select vspi.database_source_id,
       vspi.subscription_id,
       vspi.packet_rank,
       fn_get_date_diff(ic.dt) as utilization_datediff,
       vspi.packet_start_datetime,
       vspi.packet_end_datetime,
       vspi.employer_id,
       sum(active) as job_active_cnt

FROM  employer.v_subscription_packet vspi
join dimension.info_calendar ic  ON vspi.database_source_id = 1 AND
                                    ic.dt >= date_trunc('day'::text, vspi.packet_start_datetime) AND
                                    ic.dt <= date_trunc('day',vspi.packet_end_datetime) AND
                                    ic.dt <= (CURRENT_DATE - 1)
          left join imp_employer.employer_jobs ej
                 on ej.sources = vspi.database_source_id
                and ej.id_employer = vspi.employer_id
                and ej.date_diff = ic.date_diff
where vspi.database_source_id = 1
group by vspi.database_source_id,
       vspi.employer_id,
       vspi.subscription_id,
       vspi.packet_rank,
       vspi.packet_start_datetime,
       vspi.packet_end_datetime,
         fn_get_date_diff(ic.dt)
;
