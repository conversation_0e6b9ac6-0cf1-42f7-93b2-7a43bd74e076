create table employer.apply_viewed_jcoin_model_daily as
-- Код, яким записуємо таблицю за проміжок часу >= '2022-03-02'
with digital_recruiter_data as (select vpi.database_source_id,
                                       e.country_code,
                                       vpi.subscription_id,
                                       vpi.packet_rank,
                                       ri.date_diff          as viewed_datediff,
                                       vpi.employer_id,
                                       count(distinct ri.id) as digital_recruiter_profile_viewed_cnt
                                from imp_employer.recommendation_impression ri
                                         join imp_employer.recommendation_view rv
                                              on ri.sources = rv.sources
                                                  and ri.date_diff = rv.date_diff
                                                  and ri.id_profile = rv.id_profile
                                                  and ri.id = rv.id_impression
                                         join imp_employer.job j
                                              on ri.sources = j.sources and ri.id_job = j.id
                                         join imp_employer.employer e
                                              on e.sources = j.sources and e.id = j.id_employer
                                         join employer.v_subscription_cont_period vscp
                                              on vscp.database_source_id = e.sources and vscp.employer_id = e.id and
                                                 ri.date between vscp.period_start_datetime and vscp.period_end_datetime
                                         join employer.m_subscription_packet vpi
                                              on vpi.database_source_id = vscp.database_source_id and
                                                 vpi.employer_id = vscp.employer_id
                                                  and vpi.subscription_id = vscp.subscription_id
                                                  and
                                                 ri.date between vpi.packet_start_datetime and vpi.packet_end_datetime
                                where vpi.database_source_id = 1
                                  and e.country_code in ('ua', 'hu')
                                  and ri.date_diff >= 44620
                                group by vpi.database_source_id,
                                         e.country_code,
                                         ri.date_diff,
                                         vpi.employer_id,
                                         vpi.subscription_id,
                                         vpi.packet_rank),
     apply_data as (select vpi.database_source_id,
                           e.country_code,
                           vpi.subscription_id,
                           vpi.packet_rank,
                           fn_get_date_diff(ja.date_seen)                                           as viewed_datediff,
                           vpi.employer_id,
                           count(distinct
                                 case
                                     when ja.flags & 1024 = 0 and ja.flags & 2 = 2
                                         then ja.id end)                                            as apply_profile_viewed_cnt
                    from imp_employer.job_apply ja
                             join imp_employer.job_apply_profile jap
                                  on ja.sources = jap.sources and ja.id = jap.id_apply
                             join imp_employer.job j
                                  on ja.sources = j.sources and ja.id_job = j.id
                             join imp_employer.employer e
                                  on e.sources = j.sources and e.id = j.id_employer
                             join employer.v_subscription_cont_period vscp
                                  on vscp.database_source_id = e.sources and vscp.employer_id = e.id and
                                     ja.date between vscp.period_start_datetime and vscp.period_end_datetime
                             join employer.m_subscription_packet vpi
                                  on vpi.database_source_id = vscp.database_source_id and
                                     vpi.employer_id = vscp.employer_id
                                      and vpi.subscription_id = vscp.subscription_id
                                      and ja.date between vpi.packet_start_datetime and vpi.packet_end_datetime
                    where vpi.database_source_id = 1
                      and e.country_code in ('ua', 'hu')
                      and date_trunc('day', ja.date_seen) >= '2022-03-02'
                    group by vpi.database_source_id,
                             e.country_code,
                             fn_get_date_diff(ja.date_seen),
                             vpi.employer_id,
                             vpi.subscription_id,
                             vpi.packet_rank)
select case
           when dr.database_source_id is null then a.database_source_id
           else dr.database_source_id end                   as database_source_id,
       case
           when dr.country_code is null then a.country_code
           else dr.country_code end                         as country_code,
       case
           when dr.subscription_id is null then a.subscription_id
           else dr.subscription_id end                      as subscription_id,
       case
           when dr.packet_rank is null then a.packet_rank
           else dr.packet_rank end                          as packet_rank,
       case
           when dr.viewed_datediff is null then a.viewed_datediff
           else dr.viewed_datediff end                      as viewed_datediff,
       case
           when dr.employer_id is null then a.employer_id
           else dr.employer_id end                          as employer_id,
       coalesce(a.apply_profile_viewed_cnt, 0)              as apply_profile_viewed_cnt,
       coalesce(dr.digital_recruiter_profile_viewed_cnt, 0) as digital_recruiter_profile_viewed_cnt
from digital_recruiter_data dr
         full outer join apply_data a
                         on dr.database_source_id = a.database_source_id
                             and dr.country_code = a.country_code
                             and dr.subscription_id = a.subscription_id
                             and dr.packet_rank = a.packet_rank
                             and dr.viewed_datediff = a.viewed_datediff
                             and dr.employer_id = a.employer_id;


/*
-- Код, яким записуємо таблицю за проміжок часу '2021-08-01' and '2022-03-01'
select vpi.database_source_id,
       e.country_code,
       vpi.subscription_id,
       vpi.packet_rank,
       fn_get_date_diff(ja.date_seen)                                                    as viewed_datediff,
       vpi.employer_id,
       count(distinct case when ja.flags & 1024 = 0 and ja.flags & 2 = 2 then ja.id end) as apply_profile_viewed_cnt,
       count(distinct case
                          when ja.flags & 1024 = 1024 and ja.flags & 2 = 2 then ja.id
                      end)                                                               as digital_recruiter_profile_viewed_cnt
from imp_employer.job_apply ja
     join imp_employer.job_apply_profile jap
     on ja.sources = jap.sources and ja.id = jap.id_apply
     join imp_employer.job j
     on ja.sources = j.sources and ja.id_job = j.id
     join imp_employer.employer e
     on e.sources = j.sources and e.id = j.id_employer
     join employer.v_subscription_cont_period vscp
     on vscp.database_source_id = e.sources and vscp.employer_id = e.id and
        ja.date between vscp.period_start_datetime and vscp.period_end_datetime
     join employer.m_subscription_packet vpi
     on vpi.database_source_id = vscp.database_source_id and vpi.employer_id = vscp.employer_id
         and vpi.subscription_id = vscp.subscription_id
         and ja.date between vpi.packet_start_datetime and vpi.packet_end_datetime
where vpi.database_source_id = 1
  and e.country_code = 'ua'
  and date_trunc('day', ja.date_seen) >= '2021-08-01'
group by vpi.database_source_id,
         e.country_code,
         fn_get_date_diff(ja.date_seen),
         vpi.employer_id,
         vpi.subscription_id,
         vpi.packet_rank;
*/

