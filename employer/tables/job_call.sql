select sj.country as country_id,
       sja.date_diff,
       j.id_employer,
       j.id as id_job,
       count(distinct case when sja.type in (13, 19, 21, 34) then sja.id_jdp end) as call_click_cnt,
       count(distinct case when sja.type = 56 and sja.flags & 4 = 0 then sja.id_jdp end) as call_cnt,
       count(distinct case when sja.type = 56 and sja.flags & 4 = 0 and sja.flags & 2 = 2 then sja.id_jdp end) as call_answered_30_sec_cnt
from imp.session_jdp sj
join imp.session_jdp_action sja on sj.country = sja.country and sj.date_diff = sja.date_diff and sj.id = sja.id_jdp and sja.type in (13, 19, 21, 34, 56)
join imp_employer.job_to_uid_mapping uid on sj.uid_job = uid.uid_job and uid.sources = 1
join imp_employer.job j on uid.sources = j.sources and uid.id_job = j.id
where sj.country = 1
  and j.sources = 1
  and sj.job_id_project = -1
  and sja.date_diff between 44376 and 44467
group by sj.country,
         sja.date_diff,
         j.id_employer,
         j.id;
