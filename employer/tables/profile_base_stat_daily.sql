create temp table profile_base_message_daily as
with
    profile_base_message as (
        select sources                                              as database_source_id,
               id_employer                                          as employer_id,
               id_account_author                                    as employer_account_id,
               fn_get_date_diff(pm.date_created)                    as action_datediff,
               id_profile                                           as profile_id,
               author_type,
               max(case when author_type = 1 /* job seeker */ then 1 else 0 end)
               over (partition by sources, id_employer, id_profile) as is_profile_with_respond
        from imp_employer.profile_message pm
        where sources = 1
          and fn_get_date_diff(pm.date_created) >= 44407
    )
select database_source_id,
       employer_id,
       employer_account_id,
       action_datediff,
       count(distinct profile_id)                                                as profile_message_employer_cnt,
       count(distinct case when is_profile_with_respond = 0 then profile_id end) as profile_message_no_respond_cnt
from profile_base_message m
where author_type = 0 /* from employer */
group by database_source_id, employer_id, employer_account_id, action_datediff;

drop table employer.profile_base_stat_daily;
create table employer.profile_base_stat_daily as
with
    profile_base_action as (
        select psa.sources                                                      as database_source_id,
               ea.id_employer                                                   as employer_id,
               ea.id_account                                                    as employer_account_id,
               fn_get_date_diff(psa.date)                                       as action_datediff,

               count(distinct psa.id_profile)                                   as profile_opened_cnt,
               count(distinct case when psa.action = 5 then psa.id_profile end) as profile_noted_cnt,
               count(distinct case when psa.action = 9 then psa.id_profile end) as profile_base_jcoin_cnt
        from imp_employer.profile_search_action psa
             join imp_employer.employer_account ea
             on psa.sources = ea.sources
                 and psa.id_account = ea.id_account
        where psa.sources = 1
          and fn_get_date_diff(psa.date) >= 44407
        group by psa.sources, ea.id_employer, ea.id_account, fn_get_date_diff(psa.date)
    )
select coalesce(pba.database_source_id, pm.database_source_id)   as database_source_id,
       coalesce(pba.employer_id, pm.employer_id)                 as employer_id,
       coalesce(pba.employer_account_id, pm.employer_account_id) as employer_account_id,
       coalesce(pba.action_datediff, pm.action_datediff)         as action_datediff,
       coalesce(pba.profile_opened_cnt, 0)                       as profile_opened_cnt,
       coalesce(pba.profile_base_jcoin_cnt, 0)                   as profile_base_jcoin_cnt,
       coalesce(pba.profile_noted_cnt, 0)                        as profile_noted_cnt,
       coalesce(pm.profile_message_employer_cnt, 0)              as profile_message_employer_cnt,
       coalesce(pm.profile_message_no_respond_cnt, 0)            as profile_message_no_respond_cnt
from profile_base_action pba
     full outer join profile_base_message_daily pm
     using (database_source_id, employer_id, employer_account_id, action_datediff);


alter table employer.profile_base_stat_daily
    add primary key (database_source_id, employer_id, employer_account_id, action_datediff);