create table employer.call_jcoin_model_daily
(
    database_source_id                   smallint,
    subscription_id                      integer   not null,
    packet_rank                          integer not null,
    action_datediff                      integer not null,
    employer_id                          integer   not null,
    --packet_price                         numeric(10, 2),
    --jcoin_cnt                            integer,
    call_cnt                             bigint,
    call_answered_30_sec_free_packet_cnt bigint,
    call_answered_30_sec_paid_packet_cnt bigint,
    call_answered_cnt                    bigint,
    constraint call_jcoin_model_daily_pk
        primary key (database_source_id, subscription_id, packet_rank, action_datediff)
);


grant select on employer.call_jcoin_model_daily to readonly;
