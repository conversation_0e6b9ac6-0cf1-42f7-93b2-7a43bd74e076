create table  employer.subscription_packet_job_active_agg
(
    database_source_id         smallint not null,
    subscription_id            integer  not null,
    packet_rank                integer not null,
    employer_id                integer  not null,
    packet_start_datetime      timestamp,
    packet_end_datetime        timestamp,
    max_active_job_cnt         integer,
    constraint subscription_active_job_agg_pk
        primary key (database_source_id, subscription_id, packet_rank)
);

alter table employer.subscription_packet_job_active_agg
    owner to dap;

grant select on employer.subscription_packet_job_active_agg to readonly;
