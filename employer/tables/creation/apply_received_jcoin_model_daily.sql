create table employer.apply_received_jcoin_model_daily
(   database_source_id              smallint not null,
    subscription_id                     integer   not null,
    packet_rank                         integer   not null,
    recieved_datediff             integer not null,
    employer_id                   integer   not null,
    --packet_price                  numeric(10, 2),
    --jcoin_cnt                     integer,
    apply_cnt                     bigint,
    digital_recruiter_profile_cnt bigint,
    constraint apply_received_jcoin_model_daily_pk
        primary key (database_source_id, subscription_id, packet_rank, recieved_datediff)
);

grant select on employer.apply_received_jcoin_model_daily to readonly;
