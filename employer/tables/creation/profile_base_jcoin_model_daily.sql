create table employer.profile_base_jcoin_model_daily
(
    database_source_id              smallint not null,
    subscription_id                      integer   not null,
    packet_rank                         integer   not null,
    action_datediff                      integer not null,
    employer_id                          integer   not null,
    --packet_price                         numeric(10, 2),
    --jcoin_cnt                            integer,
    search_cnt                           bigint,
    profile_viewed_cnt                   bigint,
    profile_open_contact_free_packet_cnt bigint,
    profile_open_contact_paid_packet_cnt bigint,
    profile_message_cnt                  bigint,
    constraint profile_base_jcoin_model_daily_pk
        primary key (database_source_id, subscription_id, packet_rank, action_datediff)
);
