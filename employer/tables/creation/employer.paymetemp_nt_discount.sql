create table employer.paymetemp_nt_discount
(
	database_source_id smallint not null,
	employer_id integer,
	subscription_id integer,
	payment_id integer not null,
	payment_discount numeric(18,4),
	discount_type integer,
	packet_price_uah numeric(19,5),
	subscription_month_cnt integer,
	expected_price_uah numeric(19,5),
	observed_price_uah numeric(19,5),
	constraint pk_paymetemp_nt_discount
		primary key (database_source_id, payment_id)
);

alter table employer.paymetemp_nt_discount owner to postgres;

grant select on employer.paymetemp_nt_discount to readonly;

grant select on employer.paymetemp_nt_discount to writeonly_product;

grant select on employer.paymetemp_nt_discount to readonly_ds;

