create table employer.jcoin_utilization_plan_daily
(
    database_source_id       smallint not null,
    subscription_id          integer  not null,
    packet_rank              integer  not null,
    employer_id              integer,
    utilization_datediff     integer  not null,
    packet_jcoin_cnt         integer,
    packet_start_datetime    timestamp,
    packet_end_datetime      timestamp,
    packet_plan_end_datetime timestamp,
    packet_day_cnt           double precision,
    jcoin_plan               numeric,
    constraint jcoin_utilization_plan_daily_pk
        primary key (database_source_id, subscription_id, packet_rank, utilization_datediff)
);

alter table jcoin_utilization_plan_daily
    owner to dap;
