create table employer.utilization_active_job_daily
(
    database_source_id   smallint not null,
    subscription_id      integer  not null,
    packet_rank         integer  not null,
    utilization_datediff integer  not null,
    packet_start_datetime       timestamp,
    packet_end_datetime         timestamp,
    employer_id          integer  not null,
    job_active_cnt   bigint,
    constraint utilization_active_job_daily_pk
        primary key (database_source_id, subscription_id, packet_rank, utilization_datediff)
);

alter table employer.utilization_active_job_daily
    owner to dap;

grant select on employer.utilization_active_job_daily to readonly;
