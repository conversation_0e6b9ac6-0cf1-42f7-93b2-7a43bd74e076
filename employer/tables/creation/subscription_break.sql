create table employer.subscription_break
(
    database_source_id                    smallint not null,
    employer_id                           integer,
    parent_subscription_id                integer  not null,
    parent_subscription_order_datetime    timestamp,
    parent_subscription_expiring_datetime timestamp,
    break_subscription_id                 integer  not null,
    break_subscription_order_datetime     timestamp,
    break_start_datetime                  timestamp,
    break_end_datetime                    timestamp,

    primary key (database_source_id, parent_subscription_id, break_subscription_id)
);

alter table employer.subscription_break
    owner to postgres;
