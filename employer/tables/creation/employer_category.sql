create table if not exists employer.employer_category
(
	database_source_id smallint,
	country_code varchar(2),
	employer_id integer,
	company_name varchar(2048),
	registration_date timestamp,
	first_subscription_order_datetime timestamp,
	first_paid_subscription_order_datetime timestamp,
	industry varchar(1024),
	max_active_job_cnt integer,
	active_job_group integer,
	registration_source varchar(255),
	channel varchar(255),
	blue_score_avg numeric,
	blue_score_group integer,
	job_with_blue_score_cnt bigint,
	max_subscription_month_cnt integer,
	max_utilization_velocity numeric,
	utilization_group integer,
	avg_revenue_per_payment numeric,
	revenue_total numeric
);

alter table employer.employer_category owner to postgres;

grant select on employer.employer_category to readonly;

