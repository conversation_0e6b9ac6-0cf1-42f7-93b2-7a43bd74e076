-- кліки на номер телефону
create table employer.job_call_click as
select ucc.jdp_viewed_datediff    as action_datediff,
       j.id_employer              as employer_id,
       j.id                       as job_id,
       count(distinct ucc.jdp_id) as call_click_cnt
from job_seeker.user_call_click ucc
     join imp_employer.job_to_uid_mapping jtum
     on jtum.sources = 1
         and ucc.job_uid = jtum.uid_job
     join imp_employer.job j
     on jtum.sources = j.sources
         and jtum.id_job = j.id
where ucc.country_id = 1
  and j.id_employer in (344240)
  and jdp_viewed_datediff >= 44407 -- '2021-08-01'
group by ucc.jdp_viewed_datediff,
         j.id_employer,
         j.id;

alter table employer.job_call_click
    add primary key (action_datediff, job_id);
