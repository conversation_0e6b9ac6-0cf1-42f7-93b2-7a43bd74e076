create table employer.client_value_daily as
select database_source_id,
       country_code,
       action_datediff                                            as date_diff,

       sum(apply_cnt)                                             as apply_cnt,
       sum(apply_profile_viewed_cnt)                              as apply_profile_viewed_cnt,
       sum(apply_profile_message_cnt)                             as apply_profile_message_cnt,
       sum(apply_profile_open_contact_cnt)                        as apply_profile_open_contact_cnt,
       sum(apply_profile_open_contact_real_jcoin_cnt)             as apply_profile_open_contact_real_jcoin_cnt,
       sum(apply_revenue_uah)                                     as apply_revenue_uah,
       sum(apply_revenue_eur)                                     as apply_revenue_eur,

       sum(digital_recruiter_profile_cnt)                         as digital_recruiter_profile_cnt,
       sum(digital_recruiter_profile_viewed_cnt)                  as digital_recruiter_profile_viewed_cnt,
       sum(digital_recruiter_profile_message_cnt)                 as digital_recruiter_profile_message_cnt,
       sum(digital_recruiter_profile_open_contact_cnt)            as digital_recruiter_profile_open_contact_cnt,
       sum(digital_recruiter_profile_open_contact_real_jcoin_cnt) as digital_recruiter_profile_open_contact_real_jcoin_cnt,
       sum(digital_recruiter_revenue_uah)                         as digital_recruiter_revenue_uah,
       sum(digital_recruiter_revenue_eur)                         as digital_recruiter_revenue_eur,

       sum(profile_base_search_cnt)                               as profile_base_search_cnt,
       sum(profile_base_profile_viewed_cnt)                       as profile_base_profile_viewed_cnt,
       sum(profile_base_profile_message_cnt)                      as profile_base_profile_message_cnt,
       sum(profile_base_profile_open_contact_cnt)                 as profile_base_profile_open_contact_cnt,
       sum(profile_base_profile_open_contact_real_jcoin_cnt)      as profile_base_profile_open_contact_real_jcoin_cnt,
       sum(profile_base_revenue_uah)                              as profile_base_revenue_uah,
       sum(profile_base_revenue_eur)                              as profile_base_revenue_eur,

       sum(call_open_contact_cnt)                                 as call_open_contact_cnt,
       sum(call_real_jcoin_cnt)                                   as call_real_jcoin_cnt,
       sum(call_revenue_uah)                                      as call_revenue_uah,
       sum(call_revenue_eur)                                      as call_revenue_eur,


       sum(revenue_pnl_uah)                                       as revenue_pnl_uah,
       sum(revenue_pnl_eur)                                       as revenue_pnl_eur,

       sum(revenue_cashflow_uah)                                  as cashflow_uah,
       sum(revenue_cashflow_eur)                                  as cashflow_eur,

       sum(revenue_pnl_uah) - sum(apply_revenue_uah) - sum(digital_recruiter_revenue_uah) -
       sum(profile_base_revenue_uah) - sum(call_revenue_uah)      as unutilized_revenue_uah,
       sum(revenue_pnl_eur) - sum(apply_revenue_eur) - sum(digital_recruiter_revenue_eur) -
       sum(profile_base_revenue_eur) - sum(call_revenue_eur)      as unutilized_revenue_eur
FROM employer.jcoin_utilized_and_revenue_daily
where database_source_id = 1
  and country_code in ('ua', 'hu', 'ro')
group by 1, 2, 3;
