select e.sources as database_source_id,
       e.id as employer_id,
       ec.company_name,
       ji.job_id as job_id,
       ji.premium_type,
       fn_get_timestamp_from_date_diff(ji.impression_datediff) as action_date,
       ji.impression_datediff,
       ji.impression_cnt as impressions_cnt,
       coalesce(jv.view_cnt,0) as views_cnt,
       coalesce(jc.call_click_cnt,0) as call_click_cnt,
       case when ji.impression_datediff >= 44407 then coalesce(jc.call_cnt,0) end as call_cnt,
       case when ji.impression_datediff >= 44407 then coalesce(jc.call_answered_30_sec_cnt,0) end as paid_call_cnt,
       coalesce(ja.apply_cnt,0) as apply_cnt,
       case when ji.impression_datediff >= 44407 then coalesce(jav.apply_profile_view_cnt,0) end as apply_profile_view_cnt,
       case when ji.impression_datediff >= 44407 then coalesce(jaoc.apply_profile_open_contact_cnt,0) end as apply_profile_open_contact_cnt
from imp_employer.employer e
join imp_employer.employer_cdp ec on e.sources = ec.sources and e.id_cdp = ec.id
join employer.job_impression ji on e.sources = ji.database_source_id and ji.employer_id = e.id
left join employer.job_view jv on e.sources = 1 and ji.employer_id = jv.employer_id and ji.job_id = jv.job_id and ji.impression_datediff = jv.jdp_viewed_datediff
left join employer.job_call jc on e.sources = 1 and ji.employer_id = jc.employer_id and ji.job_id = jc.job_id and ji.impression_datediff = jc.jdp_viewed_datediff
left join employer.job_apply ja on e.sources = 1 and ji.employer_id = ja.employer_id and ji.job_id = ja.job_id and ji.impression_datediff = ja.apply_datediff
left join employer.job_apply_view jav on e.sources = 1 and ji.employer_id = jav.employer_id and ji.job_id = jav.job_id and ji.impression_datediff = jav.seen_datediff
left join employer.job_apply_open_contact jaoc on e.sources = 1 and ji.employer_id = jaoc.employer_id and ji.job_id = jaoc.job_id and ji.impression_datediff = jaoc.contact_open_datediff
where e.sources = 1
  and e.country_code = 'ua'
  and ji.impression_datediff <= 44467;
