select date_trunc('month', ja.date)::date as month_first_date,
       e.id as employer_id,
       count(distinct case when ja.flags & 1024 = 0 then ja.id end) as apply_cnt,
       count(distinct case when ja.flags & 1024 = 0 and DATE_PART('day', date_seen - date) <= 14  then ja.id end) as apply_profile_viewed_cnt,
       count(distinct case when ja.flags & 1024 = 0 and DATE_PART('day', date_seen - date) <= 14 and aitc.action_type_id in (1,2,3,6)  then ja.id end) as apply_profile_open_contact_cnt,
       count(distinct case when ja.flags & 1024 = 0 and DATE_PART('day', date_seen - date) <= 14 and aitc.action_type_id in (4,101,102) then ja.id end) as apply_profile_message_cnt,
       count(distinct case when ja.flags & 1024 = 1024 then ja.id end) as digital_recruiter_profile_cnt,
       count(distinct case when ja.flags & 1024 = 1024 and DATE_PART('day', date_seen - date) <= 14  then ja.id end) as digital_recruiter_profile_viewed_cnt,
       count(distinct case when ja.flags & 1024 = 1024 and DATE_PART('day', date_seen - date) <= 14 and aitc.action_type_id in (1,2,3,6)  then ja.id end) as digital_recruiter_profile_open_contact_cnt,
       count(distinct case when ja.flags & 1024 = 1024 and DATE_PART('day', date_seen - date) <= 14 and aitc.action_type_id in (4,101,102) then ja.id end) as digital_recruiter_profile_message_cnt
from imp_employer.job_apply ja
join imp_employer.job j
  on j.sources = ja.sources
      and j.id = ja.id_job
join imp_employer.employer e
  on j.sources = e.sources
 and e.id = j.id_employer
left join employer.apply_intention_to_contact aitc
     on aitc.database_source_id = ja.sources
    and aitc.apply_id = ja.id
where e.country_code = 'ua'
 and ja.sources = 1
and ja.date between '2021-01-01' and '2021-08-01'
group by e.id,
       date_trunc('month', ja.date)

;
