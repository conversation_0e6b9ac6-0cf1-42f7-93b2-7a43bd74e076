create table employer.job_apply_message_daily as
with
    a as (select j.sources                                                as database_source_id,
                 j.id_employer                                            as employer_id,
                 j.id_account                                             as employer_account_id,
                 j.id                                                     as job_id,

                 fn_get_date_diff(jam.date_created)                       as action_datediff,
                 jam.id_apply                                             as apply_id,
                 case when ja.flags & 1024 <> 1024 then 1 else 2 end      as apply_type,
                 jam.author_type,
                 max(case when jam.author_type = 1 /* job seeker */ then 1 else 0 end)
                 over (partition by j.sources, id_employer, jam.id_apply) as is_apply_with_respond
          from imp_employer.job j
               left join imp_employer.job_apply ja
               on j.sources = ja.sources
                   and j.id = ja.id_job
               join      imp_employer.job_apply_message jam
               on ja.sources = jam.sources
                   and ja.id = jam.id_apply
          where j.sources = 1
            and j.id_employer in (344240)
            and fn_get_date_diff(jam.date_created) >= 44407
    )
select database_source_id,
       employer_id,
       employer_account_id,
       job_id,
       action_datediff,

       count(
               distinct
               case when apply_type = 1 /* apply */ then apply_id end) as apply_message_cnt,
       count(
               distinct
               case
                   when apply_type = 1 /* apply */ and is_apply_with_respond = 0 then apply_id
               end)                                                    as apply_message_no_respond_cnt,
       count(distinct
             case
                 when apply_type = 2 /* reccomendation */ then apply_id
             end)                                                      as reccomendation_message_cnt,
       count(
               distinct
               case
                   when apply_type = 2 /* reccomendation */ and is_apply_with_respond = 0 then apply_id
               end)                                                    as reccomendation_message_no_respond_cnt
from a
where author_type = 0
group by database_source_id, employer_id, employer_account_id, job_id, action_datediff;


alter table employer.job_apply_message_daily
    add primary key (database_source_id, action_datediff, job_id);
