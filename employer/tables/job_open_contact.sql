-- витрачені J'coin на відкриття контактів
create table employer.job_open_contact as
with
    jcoin_by_type as (
        select fn_get_date_diff(poc.date) as action_datediff,
               j.id_employer              as employer_id,
               ja.id_job                  as job_id,
               type,
               sum(poc.points_deducted)   as jcoin_cnt
        from imp_employer.profile_open_contact poc
             join imp_employer.job_apply ja
             on ja.sources = poc.sources
                 and ja.id = poc.id_apply
             join (select 1 as type
                   union all
                   select 2 as type) as jcoin_types
             on (ja.flags & 1024 = 0 and type = 1)
                 or (ja.flags & 1024 = 1024 and type = 2)
             join imp_employer.job j
             on j.sources = ja.sources
                 and j.id = ja.id_job
        where poc.sources = 1
          and poc.date is not null
          and date(poc.date) between ${date_start} and ${date_end}
        group by action_datediff, employer_id, j.id_account, job_id, type

        union all

        select fn_get_date_diff(joc.date) as action_datediff,
               j.id_employer              as employer_id,
               joc.id_job                 as job_id,
               5                          as type, /* jdp open contact */
               sum(points_deducted)       as jcoin_cnt
        from imp_employer.jdp_open_contact joc
             left join imp_employer.job j
             on joc.sources = j.sources
                 and joc.id_job = j.id
        where joc.sources = 1
          and date(joc.date) between ${date_start} and ${date_end}
        group by fn_get_date_diff(joc.date), j.id_employer, j.id_account, joc.id_job
    )
select action_datediff,
       employer_id,
       job_id,
       sum(case when type = 1 then jcoin_cnt end) as apply_jcoin_cnt,
       sum(case when type = 2 then jcoin_cnt end) as reccomendation_jcoin_cnt,
       sum(case when type = 5 then jcoin_cnt end) as jdp_open_contact_jcoin_cnt
from jcoin_by_type jcoin
     left join imp_employer.job j
     on j.sources = 1
         and jcoin.job_id = j.id
group by action_datediff,
         employer_id,
         job_id
;

alter table employer.job_open_contact
    add primary key (action_datediff, job_id);
