SELECT poc.database_source_id,
       e.country_code,
       poc.subscription_id,
       poc.packet_rank,
       poc.action_datediff,
       poc.employer_id,
       poc.packet_price,
       poc.jcoin_cnt,
       count(DISTINCT
             CASE
                 WHEN poc.feature_type_id = 1 AND poc.is_paid_jcoin = 1 THEN poc.open_contact_id
                 ELSE NULL::bigint
                 END)                                                                         AS apply_profile_open_contact_paid_packet_cnt,
       count(DISTINCT
             CASE
                 WHEN poc.feature_type_id = 2 AND poc.is_paid_jcoin = 1 THEN poc.open_contact_id
                 ELSE NULL::bigint
                 END)                                                                         AS digital_recruiter_profile_open_contact_paid_packet_cnt,
       count(DISTINCT
             CASE
                 WHEN poc.feature_type_id = 3 AND poc.is_paid_jcoin = 1 THEN poc.open_contact_id
                 ELSE NULL::bigint
                 END)                                                                         AS profile_base_profile_open_contact_paid_packet_cnt,
       count(DISTINCT
             CASE
                 WHEN poc.feature_type_id = 4 AND poc.is_paid_jcoin = 1 THEN poc.open_contact_id
                 ELSE NULL::bigint
                 END)                                                                         AS call_answered_30_sec_paid_packet_cnt,
       count(DISTINCT
             CASE
                 WHEN poc.feature_type_id = 1 AND poc.is_paid_jcoin = 0 THEN poc.open_contact_id
                 ELSE NULL::bigint
                 END)                                                                         AS apply_profile_open_contact_free_packet_cnt,
       count(DISTINCT
             CASE
                 WHEN poc.feature_type_id = 2 AND poc.is_paid_jcoin = 0 THEN poc.open_contact_id
                 ELSE NULL::bigint
                 END)                                                                         AS digital_recruiter_profile_open_contact_free_packet_cnt,
       count(DISTINCT
             CASE
                 WHEN poc.feature_type_id = 3 AND poc.is_paid_jcoin = 0 THEN poc.open_contact_id
                 ELSE NULL::bigint
                 END)                                                                         AS profile_base_profile_open_contact_free_packet_cnt,
       count(DISTINCT
             CASE
                 WHEN poc.feature_type_id = 4 AND poc.is_paid_jcoin = 0 THEN poc.open_contact_id
                 ELSE NULL::bigint
                 END)                                                                         AS call_answered_30_sec_free_packet_cnt,
       count(DISTINCT
             CASE
                 WHEN poc.feature_type_id = 1 AND poc.is_paid_jcoin = 1 THEN poc.open_contact_id
                 ELSE NULL::bigint
                 END)::numeric * COALESCE(vjmjp.jcoin_paid_price_without_vat_uah, 0::numeric) AS apply_revenue,
       count(DISTINCT
             CASE
                 WHEN poc.feature_type_id = 2 AND poc.is_paid_jcoin = 1 THEN poc.open_contact_id
                 ELSE NULL::bigint
                 END)::numeric *
       COALESCE(vjmjp.jcoin_paid_price_without_vat_uah, 0::numeric)                           AS digital_recruiter_revenue,
       count(DISTINCT
             CASE
                 WHEN poc.feature_type_id = 3 AND poc.is_paid_jcoin = 1 THEN poc.open_contact_id
                 ELSE NULL::bigint
                 END)::numeric * COALESCE(vjmjp.jcoin_paid_price_without_vat_uah, 0::numeric) AS profile_base_revenue,
       count(DISTINCT
             CASE
                 WHEN poc.feature_type_id = 4 AND poc.is_paid_jcoin = 1 THEN poc.open_contact_id
                 ELSE NULL::bigint
                 END)::numeric * COALESCE(vjmjp.jcoin_paid_price_without_vat_uah, 0::numeric) AS call_revenue,
       COALESCE(vjmjp.jcoin_paid_price_without_vat_uah, 0::numeric)                           AS jcoin_paid_price_without_vat_uah
FROM employer.m_profile_open_contact_with_packet poc
         JOIN imp_employer.employer e
              ON poc.database_source_id = e.sources
                  AND poc.employer_id = e.id
         LEFT JOIN employer.v_jcoin_model_jcoin_price vjmjp ON poc.database_source_id = vjmjp.database_source_id AND
                                                               poc.subscription_id = vjmjp.subscription_id AND
                                                               poc.action_datetime >= vjmjp.payment_start_datetime AND
                                                               poc.action_datetime <= vjmjp.payment_end_datetime
WHERE poc.action_datetime IS NOT NULL
  AND poc.packet_rank IS NOT NULL
  AND e.country_code = 'ua'
  AND poc.database_source_id = 1
GROUP BY poc.database_source_id, e.country_code, poc.employer_id, poc.subscription_id, poc.packet_price, poc.jcoin_cnt,
         poc.action_datediff, (COALESCE(vjmjp.jcoin_paid_price_without_vat_uah, 0::numeric)), poc.packet_rank;
