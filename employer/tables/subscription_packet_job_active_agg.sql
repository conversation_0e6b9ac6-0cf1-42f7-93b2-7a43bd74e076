create table employer.subscription_packet_job_active_agg as
select ps.database_source_id,
       ps.subscription_id,
       ps.packet_rank,
       ps.employer_id,
       ps.packet_start_datetime,
       ps.packet_end_datetime,
       max(active) as max_active_job_cnt
from employer.v_subscription_packet ps
     join imp_employer.employer_jobs ej
       on ps.database_source_id = ej.sources
          and ps.employer_id = ej.id_employer
          and ej.date_diff between  fn_get_date_diff(ps.packet_start_datetime)
          and  fn_get_date_diff(ps.packet_end_datetime)

     -- визначимо, яка підписка була активна в цей день
     join employer.subscription_active_date sda
     on sda.database_source_id = ej.sources
            and sda.employer_id = ej.id_employer
            and ej.date_diff = sda.date_diff
where ps.database_source_id = 1
group by ps.database_source_id,
       ps.subscription_id,
       ps.packet_rank,
       ps.employer_id,
       ps.packet_start_datetime,
       ps.packet_end_datetime;
