create table employer.packet_account_activity_agg as
select msp.database_source_id,
       msp.employer_id,
       msp.subscription_id,
       msp.packet_rank,
       msp.packet_start_datetime,
       msp.packet_end_datetime,
       eaa.id_account as account_id,
       count(distinct eas.id) as session_cnt
from employer.m_subscription_packet msp
join imp_employer.employer_account_account eaa
      on msp.database_source_id = eaa.sources and
         msp.employer_id = eaa.id_employer
left join imp_employer.employer_account_session eas
       on eas.sources = eaa.sources and
          eas.id_account = eaa.id_account and
          eas.date_started between msp.packet_start_datetime and msp.packet_end_datetime and
          eas.using_secret_key = false

group by msp.database_source_id,
       msp.employer_id,
       msp.subscription_id,
       msp.packet_rank,
       msp.packet_start_datetime,
       msp.packet_end_datetime,
       eaa.id_account;

alter table employer.packet_account_activity_agg
    owner to dap;

grant select on employer.packet_account_activity_agg to readonly;

alter table employer.packet_account_activity_agg
add constraint packet_account_activity_agg_pk
primary key (database_source_id,subscription_id, packet_rank, account_id)
;