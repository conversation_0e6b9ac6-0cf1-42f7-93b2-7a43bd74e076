select msp.database_source_id,
       pss.country_id,
       msp.employer_id,
       msp.subscription_id,
       msp.packet_rank,
       pss.uid_job as job_seeker_job_uid,
       j.id        as employer_job_id,
       pss.profile_id,
       pss.submission_datediff,
       pss.source_type_id
from profile.v_profile_jdp_submission_source_unique pss
     join imp_employer.job_to_uid_mapping jtum
     on jtum.sources = 1 and pss.uid_job = jtum.uid_job and pss.country_id = 1
     join imp_employer.job j
     on jtum.sources = j.sources and jtum.id_job = j.id

     join employer.v_subscription_cont_period vscp
     on vscp.database_source_id = j.sources and vscp.employer_id = j.id_employer and
        pss.submission_datetime between vscp.period_start_datetime and vscp.period_end_datetime

     join employer.m_subscription_packet msp
     on msp.database_source_id = vscp.database_source_id and msp.employer_id = vscp.employer_id
         and msp.subscription_id = vscp.subscription_id
         and pss.submission_datetime between msp.packet_start_datetime and msp.packet_end_datetime
where pss.submission_datediff = fn_get_date_diff(current_date - 1);