-- відгуки та рекомендац<PERSON><PERSON>
create table employer.job_apply_daily as
select j.id_employer                                                               as employer_id,
       j.id                                                                        as job_id,
       fn_get_date_diff(ja.date)                                                   as apply_datediff,
       count(distinct
             case when ja.flags & 1024 = 1024 /* reccomendation */ then ja.id end) as reccomended_apply_cnt,
       count(distinct
             case when ja.flags & 1024 <> 1024 /* apply */ then ja.id end)         as apply_cnt
from imp_employer.job j
     left join imp_employer.job_apply ja
     on j.sources = ja.sources
         and j.id = ja.id_job
where j.sources = 1
  and j.id_employer in (344240)
  and date(ja.date) >= '2021-08-01'::date
group by j.id_employer,
         j.id,
         fn_get_date_diff(ja.date);

alter table employer.job_apply_daily
    add primary key (apply_datediff, job_id);
