create table employer.call_jcoin_model_daily as
select jc.sources                as database_source_id,
       e.country_code,
       vsp.subscription_id,
       vsp.packet_rank,
       fn_get_date_diff(jc.date) as action_datediff,
       e.id                      as employer_id,
       count(distinct case
                          when jc.flags & 4 = 0 /*not repeated*/ then jc.phone::text || '_' || jc.id_job
           end)                  as call_cnt,
       count(distinct case
                          when jc.status = 1 /*answered*/ and jc.flags & 4 = 0 /*not repeated*/
                              then jc.phone::text || '_' || jc.id_job
           end)                  as call_answered_cnt,
       count(distinct case
                          when jc.status = 1 /*answered*/ and jc.flags & 1 = 1 /*paid*/
                              and vsp.packet_type_paid_result_id <> 1 /*Not Paid*/
                              then jc.id
           end)                  as call_answered_30_sec_free_packet_cnt,
       count(distinct case
                          when jc.status = 1 /*answered*/ and jc.flags & 4 = 0 /*not repeated*/ and
                               jc.flags & 1 = 1 /*paid*/
                              and vsp.packet_type_paid_result_id = 1 /*Paid*/
                              then jc.id
           end)                  as call_answered_30_sec_paid_packet_cnt
from imp_employer.job_call jc
         join employer.v_subscription_cont_period vscp
              on vscp.database_source_id = jc.sources and vscp.employer_id = jc.id_employer and
                 jc.date between vscp.period_start_datetime and vscp.period_end_datetime
         join employer.m_subscription_packet vsp
              on vsp.database_source_id = vscp.database_source_id and vsp.employer_id = vscp.employer_id
                  and vsp.subscription_id = vscp.subscription_id
                  and jc.date between vsp.packet_start_datetime and vsp.packet_end_datetime
         join imp_employer.employer e
              on jc.sources = e.sources
                  and jc.id_employer = e.id
where jc.sources = 1
  and e.country_code = 'ua'
group by fn_get_date_diff(jc.date),
         e.id,
         e.country_code,
         vsp.subscription_id,
         vsp.packet_rank,
         jc.sources;
