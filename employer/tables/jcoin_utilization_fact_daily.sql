create table employer.jcoin_utilization_fact_daily as
select vsp.database_source_id,
       vsp.subscription_id,
       packet_rank,
       vsp.employer_id,
       fn_get_date_diff(vju.action_datetime)                                           as utilization_datediff,
       packet_jcoin_cnt,
       packet_start_datetime,
       packet_end_datetime,
       packet_plan_end_datetime,
       count(distinct vju.feature_type_id::varchar || '_' || vju.open_contact_id)::int as jcoin_fact
from employer.v_subscription_packet vsp
         join employer.v_profile_open_contact_with_packet vju
              on vju.database_source_id = vsp.database_source_id
                  and vju.subscription_id = vsp.subscription_id
                  and
                 vju.action_datetime between vsp.packet_start_datetime and coalesce(vsp.packet_end_datetime, vsp.packet_plan_end_datetime)
group by vsp.database_source_id,
         vsp.subscription_id,
         packet_rank,
         vsp.employer_id,
         packet_jcoin_cnt,
         packet_start_datetime,
         packet_end_datetime,
         packet_plan_end_datetime,
         fn_get_date_diff(vju.action_datetime)
;
