create table employer.employer_job_stat as
select 1                                                       as database_source_id,
       datediff,
       j.id_employer                                           as employer_id,
       j.id_account                                            as employer_account_id,
       j.id                                                    as job_id,
       coalesce(ji.impression_cnt, 0)                          as impression_cnt,
       coalesce(jv.jdp_view_cnt, 0)                            as jdp_view_cnt,
       coalesce(cc.call_click_cnt, 0)                          as call_click_cnt,

       coalesce(a.apply_cnt, 0)                                as apply_cnt,
       coalesce(a.reccomended_apply_cnt, 0)                    as reccomended_apply_cnt,

       coalesce(apply_jcoin_cnt, 0)                            as apply_jcoin_cnt,
       coalesce(reccomendation_jcoin_cnt, 0)                   as reccomendation_jcoin_cnt,

       0::bigint                                               as call_jcoin_cnt,

       0::bigint                                               as call_cnt,
       0::bigint                                               as call_answered_cnt,
       0::bigint                                               as call_missed_cnt,
       0::bigint                                               as call_rejected_cnt,

       coalesce(jamd.apply_message_cnt, 0)                     as apply_message_cnt,
       coalesce(jamd.apply_message_no_respond_cnt, 0)          as apply_message_no_respond_cnt,
       coalesce(jamd.reccomendation_message_cnt, 0)            as reccomendation_message_cnt,
       coalesce(jamd.reccomendation_message_no_respond_cnt, 0) as reccomendation_message_no_respond_cnt,
       coalesce(jand.apply_note_cnt, 0)                        as apply_note_cnt,
       coalesce(jand.reccomendation_note_cnt, 0)               as reccomendation_note_cnt,
       coalesce(jdp_open_contact_jcoin_cnt, 0)                 as jdp_open_contact_jcoin_cnt
from imp_employer.job j
     left join generate_series(fn_get_date_diff('2021-08-01'),
                               fn_get_date_diff(current_date) - 1) as datediff
     on datediff >= fn_get_date_diff(j.date_created)
     left join employer.job_impression ji
     on j.sources = 1
         and j.id = ji.job_id
         and datediff = ji.impression_datediff

     left join employer.job_jdp_view jv
     on j.sources = 1
         and j.id = jv.job_id
         and datediff = jv.jdp_view_datediff

     left join employer.job_call_click cc
     on j.sources = 1
         and j.id = cc.job_id
         and datediff = cc.action_datediff

     left join employer.job_apply_daily a
     on j.sources = 1
         and j.id = a.job_id
         and datediff = a.apply_datediff

     left join employer.job_open_contact ocj
     on j.sources = 1
         and j.id = ocj.job_id
         and datediff = ocj.action_datediff

/*     left join employer.job_call_daily cj
     on j.sources = 1
         and j.id = cj.job_id
         and datediff = cj.action_datediff*/

     left join employer.job_apply_message_daily jamd
     on j.sources = jamd.database_source_id
         and j.id = jamd.job_id
         and datediff = jamd.action_datediff

     left join employer.job_apply_note_daily jand
     on j.sources = jand.database_source_id
         and j.id = jand.job_id
         and datediff = jand.action_datediff
where j.sources = 1
  and j.id_employer in (344240)
  and coalesce(impression_cnt, jdp_view_datediff, call_click_cnt, apply_cnt, reccomended_apply_cnt,
               apply_jcoin_cnt, reccomendation_jcoin_cnt, /*call_jcoin_cnt, call_answered_cnt,
               call_missed_cnt, call_rejected_cnt,*/ apply_message_cnt, apply_message_no_respond_cnt,
               reccomendation_message_cnt, reccomendation_message_no_respond_cnt, apply_note_cnt,
               reccomendation_note_cnt) is not null;


alter table employer.employer_job_stat
    add primary key (database_source_id, datediff, job_id);

create index employer_id_idx on employer.employer_job_stat (employer_id);
