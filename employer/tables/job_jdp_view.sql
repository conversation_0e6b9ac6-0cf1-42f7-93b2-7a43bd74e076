-- перегляди jdp
create table employer.job_jdp_view as
select j.id_employer as employer_id,
       jsd.id_job    as job_id,
       jsd.date_diff as jdp_view_datediff,
       sum(views)    as jdp_view_cnt
from imp_employer.job_statistics_detailed jsd
     join imp_employer.job j
     on jsd.sources = j.sources
         and jsd.id_job = j.id
where jsd.sources = 1
  and j.id_employer in (344240)
  and jsd.date_diff >= 44407 -- '2021-08-01'
group by j.id_employer,
         jsd.id_job,
         jsd.date_diff;

alter table employer.job_jdp_view
    add primary key (jdp_view_datediff, job_id);
