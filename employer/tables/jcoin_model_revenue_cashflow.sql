select spi.database_source_id,
       spi.employer_id,
       spi.subscription_id,
       spi.subscription_status,
       spi.packet_id,
       spi.subscription_month_cnt as subscription_payment_month_cnt,
       spi.order_price as subscription_payment_price,
       spi.packet_price,
       spi.subscription_order_datetime as subscription_order_datetime,
       pr.order_datetime as payment_order_datetime,
       pr.payment_datetime,
       subscription_fact_expiring_datetime as subscription_fact_expiring_datetime,
       subscription_plan_expiring_datetime as subscription_plan_expiring_datetime,
       pr.payment_without_vat_uah,
       pr.payment_vat_uah,
       case when pr.payment_datetime is null then 0 else row_number() over (partition by spi.database_source_id, spi.employer_id, spi.subscription_id order by pr.payment_datetime) end as payment_rank,
       pr.payment_id
  from employer.v_payment_revenue pr
       join (select *,
                         spi.packet_price * spi.subscription_month_cnt::numeric as order_price
                  from employer.v_subscription_packet_info spi
                  where spi.packet_price > 0
                    and spi.database_source_id = 1
  ) spi
       on spi.database_source_id = pr.database_source_id
          and spi.subscription_id = pr.subscription_id
 where pr.payment_status = 2
   and pr.refund_datetime is null
