select pa.country_id,
       fn_get_date_diff(date_seen) as seen_datediff,
       uid.id_job,
       e.id as employer_id,
       count(distinct pa.profile_id) as apply_profile_view_cnt
from profile.profile_apply pa
join imp_employer.job_to_uid_mapping uid on pa.job_uid = uid.uid_job
join imp_employer.employer_account ea on ea.sources = uid.sources and ea.id_account = pa.employer_account_id
join imp_employer.employer e on e.sources = uid.sources and e.id = ea.id_employer
join imp_employer.job_apply ja on ja.sources = uid.sources and pa.employer_apply_id = ja.id
where pa.country_id = 1
  and uid.sources = 1
  and e.country_code = 'ua'
  and apply_datediff between 44407 and 44467
  and date_seen is not null
group by pa.country_id,
         fn_get_date_diff(date_seen),
         uid.id_job,
         e.id;
