select date_trunc('month', fn_get_timestamp_from_date_diff(ps.date_diff))::date as month_first_date,
       e.id                                                                     as employer_id,
       count(distinct ps.id)                                                    as search_cnt,
       count(distinct case when psa.action in (0/*view*/) then psa.id_profile end)  as profile_viewed_cnt,
       count(distinct case when psa.action in (1/*phone*/, 2/*email*/, 3/*click Viber*/, 6/*download cv*/) then psa.id_profile end) as profile_open_contact_cnt,
       count(distinct case when psa.action = 4 /*message*/ then psa.id_profile end) as profile_message_cnt
from imp_employer.profile_search ps
         join imp_employer.employer_account ea
           on ea.sources = ps.sources
          and ea.id_account = ps.id_account
         join imp_employer.employer e
           on ea.sources = e.sources
          and ea.id_account = e.id_account
          -- щоб порахувати кількість сьорчів роблю лефт джойн, бо коли просто буде join відкинемо ті сьорчі, в яких не було action
         left join imp_employer.profile_search_action psa
                   on e.sources = psa.sources
                       and ps.date_diff = psa.date_diff
                       and ps.id = psa.id_search

where ps.date_diff between 44195 /*2021-01-01*/ and 44406 /*2021-07-31*/
  and ps.sources = 1
  and e.country_code = 'ua'
group by date_trunc('month', fn_get_timestamp_from_date_diff(ps.date_diff))::date,
       e.id;
