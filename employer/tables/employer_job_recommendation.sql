with profiles as (
        select id_profile,
               id_recommendation_group,
               sources
        from imp_employer.apply_recommendation),
        job_apply as (
        select id as apply_id,
               id_job,
               date as date_apply,
               case when (flags & 1024 != 1024) then 0 else 1 end as is_reccomendation,
               sources
        from imp_employer.job_apply
        -- оставить только отклики, без рекомендаций
        where case when (flags & 1024 != 1024) then 0 else 1 end = 0),
        pull_recc as (
            select j.id_employer,
                   arg.date as recommendation_date,
                   arg.job_title_tags,
                   arg.job_region,
                   arg.id_job,
                   j.title,
                   case when EXTRACT(DAY FROM arg.date-date_created) < 7 then 1 else 0 end as is_new_job,
                   CASE WHEN flags & 7 = 7 THEN 1 ELSE 0 END as is_premium, -- на текущий момент
                 -- , arg.source as source_recommendation -- job builder, apply
                   count(p.id_profile) as profile_count_by_job,
                   j.sources
        from imp_employer.apply_recommendation_group arg
        inner join profiles p on arg.id = p.id_recommendation_group and arg.sources = p.sources
        inner join imp_employer.job j on arg.id_job = j.id and arg.sources = j.sources
        group by j.id_employer,
                 arg.date,
                 arg.job_title_tags,
                 arg.job_region,
                 arg.id_job,
                 j.title,
                 case when EXTRACT(DAY FROM arg.date-date_created) < 7 then 1 else 0 end,
                 CASE WHEN flags & 7 = 7 THEN 1 ELSE 0 END,
                 j.sources
        )
        select pull_recc.id_job as job_id,
               job_title_tags as job_title_tag,
               job_region,
               title,
               is_new_job,
               cast(recommendation_date as date) as recommendation_date,
               id_employer as employer_id,
               is_premium,
               profile_count_by_job as profile_by_job_cnt,
               sum(case when date_apply > recommendation_date then 0 else 1 end) as apply_before_recommendation,
               pull_recc.sources as database_source_id
        from pull_recc
        left join job_apply ja on ja.id_job=pull_recc.id_job and ja.sources=pull_recc.sources
        group by pull_recc.id_job,
                 cast(recommendation_date as date),
                 job_title_tags,
                 title,
                 is_new_job,
                 job_region,
                 id_employer,
                 is_premium,
                 profile_count_by_job,
                 pull_recc.sources;
