select jc.sources                       as database_source_id,
       e.country_code,
       fn_get_date_diff(jc.date)        as action_datediff,
       vscp.employer_id,
       jc.id_subscription               as subscription_id,
       vsp.packet_id,
       vsp.packet_name,
       vsp.packet_price,
       vsp.packet_jcoin_cnt,
       count(distinct vscp.employer_id) as employer_cnt,
       count(distinct id_job)           as job_with_call_open_contact_cnt,
       count(distinct jc.id)            as call_open_contact_cnt,
       count(distinct jc.id) * 1        as call_jcoin_cnt,
       sum(points_deducted)             as call_real_jcoin_cnt,
       count(jc.id_profile)             as submitted_profile_open_contact_cnt,
       count(distinct jc.id_profile)    as submitted_profile_cnt

from imp_employer.jdp_open_contact jc
         join employer.m_subscription_cont_period vscp
              on vscp.database_source_id = jc.sources and vscp.subscription_id = jc.id_subscription and
                 jc.date between vscp.period_start_datetime and vscp.period_end_datetime
         join imp_employer.employer e
              on e.id = vscp.employer_id
                  and e.sources = vscp.database_source_id
         join employer.m_subscription_packet vsp
              on vsp.database_source_id = vscp.database_source_id and vsp.employer_id = vscp.employer_id
                  and vsp.subscription_id = vscp.subscription_id
                  and jc.date between vsp.packet_start_datetime and vsp.packet_end_datetime
where jc.sources = 1
  and e.country_code = 'hu'
  and fn_get_date_diff(jc.date) between 44689 and fn_get_date_diff(current_date) - 1
group by 1, 2, 3, 4, 5, 6, 7, 8, 9;
