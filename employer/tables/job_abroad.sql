with job_abroad_status as
     (select job.sources as database_source_id,
             case
                 when j.id_region not in
                      (29771, 30772, 30773, 30774, 30775, 30776, 30777, 30778, 30779, 30780, 30782,
                       30783, 30784, 30785, 30786, 30787, 30788, 30789, 30790, 30791, 30792, 30793, 30794,
                       30795, 29772, 29773, 29774, 29775, 30796, 30797, 30798, 30799, 31562, 31559, 31563,
                       31564, 31567, 31568, 31569, 31570, 31565, 31572, 31573, 31574, 31575, 31566, 31577,
                       31578, 31579, 31580, 31581, 31582, 31583, 31584, 31585, 31586, 31587, 31590, 31342,
                       31343, 31560, 31593, 31594, 31595, 31599, 31600, 31601, 31602, 31603, 31604, 31571,
                       31605, 31606, 31607, 31608, 31609, 31610, 31611, 31612, 31613, 31614, 31615, 31616,
                       31617, 31618, 31619, 31620, 31621, 31622, 31623, 31624, 31625, 31626, 31627, 31628,
                       31576, 31629, 31630, 30889, 31591, 30947, 30948, 30949, 30950, 30951, 30954, 30955,
                       30956, 30957, 30958, 30959, 30960, 30961, 30962, 30963, 30964, 30965) then 0
                 when j.id_region is not null then 1 end as is_abroad,
             job.id                                      as job_id,
             job.id_employer                             as employer_id
      from imp_employer.job
           join imp_employer.employer e
           on job.sources = e.sources
              and job.id_employer = e.id
           join imp_employer.job_to_uid_mapping uid
           on job.sources = uid.sources
              and job.id = uid.id_job
           left join (select jh.uid as uid_job,
                             id_region
                      from imp.job_history jh
                      where jh.country_id = 1
                        and id_project = -1) j
           on uid.uid_job = j.uid_job
      where job.sources = 1
        and e.country_code = 'ua'
        and job.moderation_status = 1),
     job_impression as
         (select sources as database_source_id,
                 id_job,
                 max(impressions) as impression_max
          from imp_employer.job_statistics_detailed
          where sources = 1
          group by sources,
                   id_job)
select jas.database_source_id,
       jas.job_id,
       jas.employer_id,
       max(jas.is_abroad) as is_abroad
  from job_abroad_status jas
       join job_impression ji
       on jas.database_source_id = ji.database_source_id and
          jas.job_id = ji.id_job and
          ji.impression_max > 0
 group by jas.database_source_id,
          jas.job_id,
          jas.employer_id
