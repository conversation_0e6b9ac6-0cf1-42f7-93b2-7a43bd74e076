with app_proc as (
    select id_apply,
           old_status,
           new_status,
            cast(date_change as date) as date_change,
            sources
    from imp_employer.statistics_job_apply_status
),
chan as (
    select distinct country_code,
          sources,
          id_employer,
          id_apply,
          current_status
    from employer.employer_apply_status
    where change_st_user=1 and time_key = current_date
)
select chan.*,
       coalesce(old_status, -1) as old_status,
       coalesce(new_status, -1) as new_status,
       date_change,
       current_date as time_key
from chan
left join app_proc on chan.id_apply=app_proc.id_apply and chan.sources=app_proc.sources;
