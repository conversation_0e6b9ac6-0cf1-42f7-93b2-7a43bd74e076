with j as (
    select id_employer
         , id_account
        , id as id_job
        , sources
    from imp_employer.job
    where moderation_status=1
),
j_apply as (
    select id_job
         , id AS id_apply
        , cast(date as date) as date_reciev_apply
         ,  CASE WHEN date_seen is not null THEN 1
            else 0  END as is_seen
         ,  CASE WHEN date_deleted is not null THEN 1
            else 0  END as is_drop
        , CAST(COALESCE(date_seen, '2999-12-31') AS DATE) AS date_seen
        ,  CASE WHEN status!=0 THEN 1
            else 0  END as have_change_status
        , status as current_status
        , sources
    from imp_employer.job_apply
    where cast(date as date) >= '2019-11-07'
),
app_proc as (
    select distinct id_apply
        , sources
    from imp_employer.statistics_job_apply_status
)
select ea.id_employer
    , ea.sources
    , upper(ea.country_code) as country_code
    , coalesce(ea.company_name) as company_name
    , coalesce(ea.industry) as industry
    , ea.id_account
    , j_apply.id_job
    , j_apply.id_apply
    , case when is_seen=1 OR (ea.exist_session!=0 and ea.date_last_session >= j_apply.date_reciev_apply) then 1 else 0 end as have_sess_after_apply
    , j_apply.date_reciev_apply
    , j_apply.is_seen
    , j_apply.date_seen
    , j_apply.is_drop
    , case
        when j_apply.have_change_status=1 and app_proc.id_apply is not null then 1
        else 0 end as change_st_user
    , j_apply.current_status
    , current_date as time_key
from j_apply
    inner join j on j_apply.id_job=j.id_job and j_apply.sources=j.sources
    left join employer.active_session_ea_3m ea on j.id_account=ea.id_account and j.sources=ea.sources
    left join app_proc on j_apply.id_apply=app_proc.id_apply and j_apply.sources=app_proc.sources
;
