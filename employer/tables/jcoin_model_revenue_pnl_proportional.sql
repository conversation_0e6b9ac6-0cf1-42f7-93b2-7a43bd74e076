select jmrc.database_source_id,
       jmrc.subscription_id,
       PUBLIC.fn_get_date_diff(dt) as revenue_datediff,
       jmrc.employer_id,
       jmrc.packet_id,
       jmrc.packet_price,
       date(payment_order_datetime + (subscription_payment_month_cnt::text || ' mon'::text)::interval)-date(payment_order_datetime)+1 as payment_day_cnt,
       jmrc.payment_id,
       payment_datetime,
       payment_order_datetime,
       payment_order_datetime + (subscription_payment_month_cnt::text || ' mon'::text)::interval as payment_expiring_datetime,
       jmrc.payment_rank,
       payment_without_vat_uah,
       payment_vat_uah,
       jmrc.subscription_order_datetime,
       jmrc.subscription_status,
       jmrc.subscription_payment_month_cnt,
       subscription_payment_price,
       round((payment_without_vat_uah)/(date(payment_order_datetime + (subscription_payment_month_cnt::text || ' mon'::text)::interval)-date(payment_order_datetime)+1),4) as revenue_without_vat_uah
  from employer.jcoin_model_revenue_cashflow jmrc
       left join dimension.info_calendar ic
       on ic.dt >= date(jmrc.payment_order_datetime)
          and ic.dt <= date(payment_order_datetime + (subscription_payment_month_cnt::text || ' mon'::text)::interval)
