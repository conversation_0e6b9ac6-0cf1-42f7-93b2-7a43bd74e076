select  poc.sources             as database_source_id,
        poc.id_employer         as employer_id,
        poc.id_profile          as profile_id,
        pcs.submission_datediff as profile_submission_datediff,
        poc.date                as open_contact_datetime,
        pcs.session_traffic_source_id,
        pcs.session_traffic_source_group_id,
        sp.packet_type_id,
        sp.packet_type_paid_result_id,
        case
            when jp.jcoin_paid_price_without_vat_uah is not null then jp.jcoin_paid_price_without_vat_uah
            else 0 end          as open_contact_price
from imp_employer.profile_open_contact poc
join employer.m_subscription_packet sp on poc.sources = sp.database_source_id and poc.id_employer = sp.employer_id and poc.id_subscription = sp.subscription_id
                                                and poc.date between sp.packet_start_datetime and sp.packet_end_datetime
join imp_employer.job_apply ja on ja.sources = poc.sources and ja.id = poc.id_apply and ja.flags & 1024 = 1024
join profile.profile_submission_traffic_source pcs on pcs.profile_id = poc.id_profile
left join m_jcoin_model_jcoin_price jp on sp.database_source_id = jp.database_source_id and sp.payment_id = jp.payment_id
where poc.sources = 1 and pcs.submission_datediff between _datediff - 6 and _datediff;
