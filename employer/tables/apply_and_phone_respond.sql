create temp table temp_v_agg_job_phone_respond
as
with phone_respond as (
    select id_job
         , date_diff
         , date('1900-01-01') + date_diff                as date_action
         , case when is_premium = true then 1 else 0 end as is_premium
         , sum(phone_responds)                           as phone_responds_sum
         , sources
    from imp_employer.job_statistics_detailed
    where date_diff >= 43981 -- '2020-06-01'
    group by id_job
           , date_diff
           , date('1900-01-01') + date_diff
           , case when is_premium = true then 1 else 0 end
           , sources
    having sum(phone_responds) > 0
)
select id_job
     , phone_responds_sum
     , is_premium
     , date_action
     , phone_respond.sources
from phone_respond;

truncate table employer.apply_and_phone_respond;

insert into employer.apply_and_phone_respond(job_id, apply_cnt, vacancy_type, reciev_apply_date, apply_group,
                                             employer_id, country_code, account_type, employer_name, industry,
                                             database_source_id)
with j as (
    select id_employer
         , id as id_job
         , sources
    from imp_employer.job
    where moderation_status = 1 -- только промодерированные вакансии
)
        ,
     j_apply as (
         select id_job
              , cast(date as date)                                 as date_reciev_apply
              , case
                    when flags & 16 = 16 then 1
                    else 0 end                                     as vacancy_type
              , count(id)                                          as cnt_apply
              , case when (flags & 1024 != 1024) then 0 else 1 end as is_reccomendation
              , sources
         from imp_employer.job_apply
         where cast(date as date) >= '2020-06-01'
           and flags & 1024 != 1024 -- без учета рекомендаций
         group by id_job
                , cast(date as date)
                , case
                      when flags & 16 = 16 then 1
                      else 0 end
                , case when (flags & 1024 != 1024) then 0 else 1 end
                , sources
     ),
     union_phone_respond as (
         -- объединение отликов, которые приходят в кабинет и кликов соикателей на "Показать телефон"
         select j_apply.*
              , 1 as apply_group -- отклики в кабинете
         from j_apply
         union
         select id_job
              , date_action
              , is_premium
              , phone_responds_sum
              , 0 as is_reccomendation
              , sources
              , 2 as apply_group -- телефон.звонки
         from temp_v_agg_job_phone_respond
     )
select distinct union_phone_respond.id_job as job_id
              , union_phone_respond.cnt_apply
              , union_phone_respond.vacancy_type
              , union_phone_respond.date_reciev_apply
              , apply_group
              , acc.employer_id
              , acc.country_code
              , acc.account_type
              , acc.employer_name
              , acc.industry
              , union_phone_respond.sources
from union_phone_respond
         inner join j on union_phone_respond.id_job = j.id_job
    and union_phone_respond.sources = j.sources
         inner join retention_ea.stg_employer_related_account acc on
    acc.sources = j.sources and j.id_employer = acc.employer_id
;

drop table temp_v_agg_job_phone_respond;