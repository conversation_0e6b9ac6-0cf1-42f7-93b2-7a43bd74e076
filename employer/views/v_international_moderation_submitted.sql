create or replace view employer.v_international_moderation_submitted
            (statistics_type, moderation_date, moderator_name, moderator_email, moderated_cnt) as
select
    'employer'                                                             as statistics_type,
    (sem.date_created at time zone 'utc' at time zone 'europe/kiev')::date as moderation_date,
    m.name                                                                 as moderator_name,
    m.email                                                                as moderator_email,
    count(distinct concat(sem.sources, '_', sem.id_employer))              as moderated_cnt
from
    imp_employer.statistics_employer_moderation sem
    left join imp_employer.moderator m
              on m.sources = sem.sources and m.email = sem.moderator_email
where
      action_type = 3
  and (sem.date_created at time zone 'utc' at time zone 'europe/kiev')::date between '2022-11-01' and current_date - 1
  and m.moderation_access = 2
group by
    (sem.date_created at time zone 'utc' at time zone 'europe/kiev')::date,
    m.name, m.email

union all

select
    'job'                                                                  as statistics_type,
    (sjm.date_created at time zone 'utc' at time zone 'europe/kiev')::date as moderation_date,
    m.name                                                                 as moderator_name,
    m.email                                                                as moderator_email,
    count(distinct concat(sjm.sources, '_', sjm.id_job))                   as moderated_cnt
from
    imp_employer.statistics_job_moderation sjm
    left join imp_employer.moderator m
              on m.sources = sjm.sources and m.email = sjm.moderator_email
where
      action_type = 3
  and (sjm.date_created at time zone 'utc' at time zone 'europe/kiev')::date between '2022-11-01' and current_date - 1
  and m.moderation_access = 2
group by
    (sjm.date_created at time zone 'utc' at time zone 'europe/kiev')::date,
    m.name, m.email;

grant select on employer.v_international_moderation_submitted to ypr, user_agg_team;
