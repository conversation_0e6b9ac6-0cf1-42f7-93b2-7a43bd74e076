create view employer.v_payment_revenue as
select ord.database_source_id,
       ord.payment_id,
       ord.employer_id,
       ord.subscription_id,
       ord.order_datetime,
       ord.payment_datetime,
       ord.refund_datetime,
       ord.payment_status,
       order_price_without_vat_uah,
       order_price_without_vat_usd,
       order_price_without_vat_eur,
       payment_without_vat_uah,
       payment_vat_uah,
       payment_without_vat_usd,
       payment_vat_usd,
       payment_without_vat_euro,
       payment_vat_euro
from (select database_source_id,
             id_employer                                       as employer_id,
             id_subscription                                   as subscription_id,
             date_ordered                                      as order_datetime,
             date_paid                                         as payment_datetime,
             date_refunded                                     as refund_datetime,
             so.status                                         as payment_status,
             round(order_price_without_vat_usd * cs.to_usd, 2) as order_price_without_vat_uah,
             order_price_without_vat_usd,
             order_price_without_vat_eur,
             payment_id
      from (select so.sources                                 as database_source_id,
                   date_ordered,
                   date_paid,
                   date_refunded,
                   id_subscription,
                   id_employer,
                   so.status,
                   round(price / (1 + vat / 100) / to_usd, 2) as order_price_without_vat_usd,
                   round(price / (1 + vat / 100) / to_eur, 2) as order_price_without_vat_eur,
                   so.id                                      as payment_id
            from imp_employer.subscription_order so
                     join imp_employer.packet_subscription ps
                          on ps.sources = 1
                              and so.id_subscription = ps.id
                     join imp_statistic.currency_source cs
                          on so.sources = 1
                              and so.currency_iso_code = cs.currency
                              and date(so.date_ordered) = cs.date
           ) so
               join imp_statistic.currency_source cs
                    on cs.currency = 'UAH'
                        and date(so.date_ordered) = date(cs.date)) ord
         left join (select database_source_id,
                           id_subscription                                   as subscription_id,
                           date_paid                                         as payment_datetime,
                           round(paid_price_without_vat_usd * cs.to_usd, 2) as payment_without_vat_uah,
                           round(paid_vat_usd * cs.to_usd, 2)                     as payment_vat_uah,
                           paid_price_without_vat_usd                       as payment_without_vat_usd,
                           paid_vat_usd                                           as payment_vat_usd,
                           paid_price_without_vat_euro   as payment_without_vat_euro,
                           paid_vat_eur as payment_vat_euro,
                           payment_id
                    from (select so.sources                                 as database_source_id,
                                 date_paid,
                                 id_subscription,
                                 row_number() over (partition by id_subscription order by date_paid) as order_paid,
                                 round(price / (1 + vat / 100) / to_usd, 2) as paid_price_without_vat_usd,
                                 round((price - price / (1 + vat / 100)) / to_usd, 2)                as paid_vat_usd,
                                 round(price /(1 + vat / 100) / to_eur, 2) as paid_price_without_vat_euro,
                                 round((price - price/( 1+ vat / 100)) / to_eur, 2) as paid_vat_eur,
                                 so.id                                      as payment_id
                          from imp_employer.subscription_order so
                                   join imp_statistic.currency_source cs
                                        on so.sources = 1
                                            and so.currency_iso_code = cs.currency
                                            and date(so.date_paid) = cs.date
                         ) so
                             join imp_statistic.currency_source cs
                                  on cs.currency = 'UAH'
                                      and date(so.date_paid) = date(cs.date)) paid
         on ord.database_source_id = paid.database_source_id
            and ord.payment_id = paid.payment_id;

alter table employer.v_payment_revenue owner to ypi;

grant select on employer.v_payment_revenue to readonly;
