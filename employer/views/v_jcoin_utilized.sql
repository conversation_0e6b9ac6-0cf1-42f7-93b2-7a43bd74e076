create or replace view employer.v_jcoin_utilized
            (database_source_id, subscription_id, packet_rank, action_datediff, employer_id, packet_price, jcoin_cnt,
             apply_profile_open_contact_paid_packet_cnt, digital_recruiter_profile_open_contact_paid_packet_cnt,
             profile_base_profile_open_contact_paid_packet_cnt, call_answered_30_sec_paid_packet_cnt,
             apply_profile_open_contact_free_packet_cnt, digital_recruiter_profile_open_contact_free_packet_cnt,
             profile_base_profile_open_contact_free_packet_cnt, call_answered_30_sec_free_packet_cnt, apply_revenue,
             digital_recruiter_revenue, profile_base_revenue, call_revenue, jcoin_paid_price_without_vat_uah)
as
SELECT poc.database_source_id,
       poc.subscription_id,
       poc.packet_rank,
       poc.action_datediff,
       poc.employer_id,
       poc.packet_price,
       poc.jcoin_cnt,
       count(DISTINCT
             CASE
                 WHEN poc.feature_type_id = 1 AND poc.is_paid_jcoin = 1 THEN poc.open_contact_id
                 ELSE NULL::bigint
                 END)                                                      AS apply_profile_open_contact_paid_packet_cnt,
       count(DISTINCT
             CASE
                 WHEN poc.feature_type_id = 2 AND poc.is_paid_jcoin = 1 THEN poc.open_contact_id
                 ELSE NULL::bigint
                 END)                                                      AS digital_recruiter_profile_open_contact_paid_packet_cnt,
       count(DISTINCT
             CASE
                 WHEN poc.feature_type_id = 3 AND poc.is_paid_jcoin = 1 THEN poc.open_contact_id
                 ELSE NULL::bigint
                 END)                                                      AS profile_base_profile_open_contact_paid_packet_cnt,
       count(DISTINCT
             CASE
                 WHEN poc.feature_type_id = 4 AND poc.is_paid_jcoin = 1 THEN poc.open_contact_id
                 ELSE NULL::bigint
                 END)                                                      AS call_answered_30_sec_paid_packet_cnt,
       count(DISTINCT
             CASE
                 WHEN poc.feature_type_id = 1 AND poc.is_paid_jcoin = 0 THEN poc.open_contact_id
                 ELSE NULL::bigint
                 END)                                                      AS apply_profile_open_contact_free_packet_cnt,
       count(DISTINCT
             CASE
                 WHEN poc.feature_type_id = 2 AND poc.is_paid_jcoin = 0 THEN poc.open_contact_id
                 ELSE NULL::bigint
                 END)                                                      AS digital_recruiter_profile_open_contact_free_packet_cnt,
       count(DISTINCT
             CASE
                 WHEN poc.feature_type_id = 3 AND poc.is_paid_jcoin = 0 THEN poc.open_contact_id
                 ELSE NULL::bigint
                 END)                                                      AS profile_base_profile_open_contact_free_packet_cnt,
       count(DISTINCT
             CASE
                 WHEN poc.feature_type_id = 4 AND poc.is_paid_jcoin = 0 THEN poc.open_contact_id
                 ELSE NULL::bigint
                 END)                                                      AS call_answered_30_sec_free_packet_cnt,
       count(DISTINCT
             CASE
                 WHEN poc.feature_type_id = 1 AND poc.is_paid_jcoin = 1 THEN poc.open_contact_id
                 ELSE NULL::bigint
                 END)::numeric * coalesce(vjmjp.jcoin_paid_price_without_vat_uah,0) AS apply_revenue,
       count(DISTINCT
             CASE
                 WHEN poc.feature_type_id = 2 AND poc.is_paid_jcoin = 1 THEN poc.open_contact_id
                 ELSE NULL::bigint
                 END)::numeric * coalesce(vjmjp.jcoin_paid_price_without_vat_uah,0) AS digital_recruiter_revenue,
       count(DISTINCT
             CASE
                 WHEN poc.feature_type_id = 3 AND poc.is_paid_jcoin = 1 THEN poc.open_contact_id
                 ELSE NULL::bigint
                 END)::numeric * coalesce(vjmjp.jcoin_paid_price_without_vat_uah,0) AS profile_base_revenue,
       count(DISTINCT
             CASE
                 WHEN poc.feature_type_id = 4 AND poc.is_paid_jcoin = 1 THEN poc.open_contact_id
                 ELSE NULL::bigint
                 END)::numeric * coalesce(vjmjp.jcoin_paid_price_without_vat_uah,0) AS call_revenue,
       coalesce(vjmjp.jcoin_paid_price_without_vat_uah,0) as jcoin_paid_price_without_vat_uah
FROM employer.v_profile_open_contact_with_packet poc
left join employer.v_jcoin_model_jcoin_price vjmjp
      on poc.database_source_id = vjmjp.database_source_id
     and poc.subscription_id = vjmjp.subscription_id
     and poc.action_datetime between vjmjp.payment_start_datetime and vjmjp.payment_end_datetime
WHERE poc.action_datetime IS NOT NULL
 and poc.packet_rank is not null
GROUP BY poc.database_source_id, poc.employer_id, poc.subscription_id, poc.packet_price, poc.jcoin_cnt,
         poc.action_datediff, coalesce(vjmjp.jcoin_paid_price_without_vat_uah,0),
         poc.packet_rank;

alter table employer.v_jcoin_utilized
    owner to ypi;

grant select on  employer.v_jcoin_utilized to readonly;
