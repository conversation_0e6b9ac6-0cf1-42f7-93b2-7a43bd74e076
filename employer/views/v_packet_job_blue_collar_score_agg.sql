create or replace view employer.v_packet_job_blue_collar_score_agg as
select jp.database_source_id,
       e.country_code,
       jp.subscription_id,
       jp.packet_rank,
       jp.employer_id,
       jp.packet_start_datetime,
       jp.packet_end_datetime,
       count(jp.job_id)    as job_cnt,
       avg(blue_score_avg) as blue_score_avg
from employer.job_blue_collar_score jbcs
         join employer.job_packet jp
              on jbcs.database_source_id = jp.database_source_id and
                 jbcs.job_id = jp.job_id
         join imp_employer.employer e
              on jp.database_source_id = e.sources
                  and jp.employer_id = e.id
where e.country_code = 'ua'
  and jbcs.database_source_id = 1
group by jp.database_source_id,
         e.country_code,
         jp.subscription_id,
         jp.packet_rank,
         jp.employer_id,
         jp.packet_start_datetime,
         jp.packet_end_datetime;

alter table employer.v_packet_job_blue_collar_score_agg
    owner to dap;

grant select on employer.v_packet_job_blue_collar_score_agg to readonly;
