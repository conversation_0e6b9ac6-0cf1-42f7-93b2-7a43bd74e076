create or replace view employer.v_paid_packet_retention
            (database_source_id, country_code, employer_id, company_name, subscription_id,
             packet_name, packet_start_datetime,
             packet_end_datetime, packet_rank, packet_global_rank,
             next_packet_name, packet_month_cnt,
             next_packet_month_cnt, has_discount_next_packet, promocode_type,
             next_packet_promocode_type,
             payment_discount_percent, is_active_packet,
             next_packet_start_datetime, first_packet_start_datetime,
             is_packet_start_payment, has_discount, is_packet_end_payment,
             payment_start_datetime, payment_end_datetime,
             blue_score_avg, packet_payment_without_vat_uah,
             next_packet_payment_without_vat_uah,
             next_payment_start_datetime, payment_next_packet_start_datetime)
as
select vsp.database_source_id,
       e.country_code,
       vsp.employer_id,
       ecdp.company_name,
       vsp.subscription_id,
       vsp.packet_name,
       vsp.packet_start_datetime,
       vsp.packet_end_datetime,
       vsp.packet_rank,
       dense_rank()
       over (partition by vsp.database_source_id, vsp.employer_id order by vsp.packet_start_datetime)                                             as packet_global_rank,
       max(vsp.packet_name::text)
       over (partition by vsp.employer_id order by vsp.payment_start_datetime groups between 1 following and 1 following)::character varying      as next_packet_name,
       vsp.subscription_month_cnt                                                                                                                 as packet_month_cnt,
       max(vsp.subscription_month_cnt)
       over (partition by vsp.employer_id order by vsp.payment_start_datetime groups between 1 following and 1 following)                         as next_packet_month_cnt,
       lead(
       case
           when pd.payment_id is not null then 1
           else 0
           end)
       over (partition by vsp.database_source_id, vsp.employer_id order by vsp.packet_start_datetime)                                             as has_discount_next_packet,
       pd.discount_type::smallint                                                                                                                 as promocode_type,
       lead(pd.discount_type::smallint)
       over (partition by vsp.database_source_id, vsp.employer_id order by vsp.packet_start_datetime)                                             as next_packet_promocode_type,
       pd.payment_discount / 100::numeric                                                                                                         as payment_discount_percent,
       case
           when date(vsp.packet_end_datetime) >= current_date then 1
           else 0
           end                                                                                                                                    as is_active_packet,
       lead(vsp.packet_start_datetime)
       over (partition by vsp.database_source_id, vsp.employer_id order by vsp.packet_start_datetime)                                             as next_packet_start_datetime,
       min(vsp.packet_start_datetime)
       over (partition by vsp.database_source_id, vsp.employer_id)                                                                                as first_packet_start_datetime,
       case
           when date(vsp.packet_start_datetime) = date(vsp.payment_start_datetime) then 1
           else 0
           end                                                                                                                                    as is_packet_start_payment,
       case
           when pd.payment_id is not null then 1
           else 0
           end                                                                                                                                    as has_discount,
       case
           when vsp.packet_end_datetime = vsp.payment_end_datetime then 1
           else 0
           end                                                                                                                                    as is_packet_end_payment,
       vsp.payment_start_datetime,
       vsp.payment_end_datetime,
       coalesce(bcs.blue_score_avg, 0) /* 0 for hungary*/,
       round(coalesce(vpr.payment_without_vat_uah, 0::numeric) / vsp.subscription_month_cnt::numeric,
             2)                                                                                                                                   as packet_payment_without_vat_uah,
       lead(round(coalesce(vpr.payment_without_vat_uah, 0::numeric) / vsp.subscription_month_cnt::numeric, 2))
       over (partition by vsp.employer_id order by vsp.packet_start_datetime)                                                                     as next_packet_payment_without_vat_uah,
       min(vsp.payment_start_datetime)
       over (partition by vsp.database_source_id, vsp.employer_id order by vsp.payment_start_datetime groups between 1 following and 1 following) as next_payment_start_datetime,
       lead(vsp.payment_start_datetime)
       over (partition by vsp.employer_id order by vsp.packet_start_datetime)                                                                     as payment_next_packet_start_datetime
from employer.v_subscription_packet vsp
         join imp_employer.employer e
              on vsp.database_source_id = e.sources and vsp.employer_id = e.id
         join imp_employer.employer_cdp ecdp
              on ecdp.sources = e.sources and ecdp.id = e.id_cdp
         left join employer.paymetemp_nt_discount pd
                   on vsp.database_source_id = pd.database_source_id and vsp.payment_id = pd.payment_id
         left join employer.v_packet_job_blue_collar_score_agg bcs
                   on vsp.database_source_id = bcs.database_source_id and vsp.subscription_id = bcs.subscription_id and
                      vsp.packet_rank = bcs.packet_rank
         left join employer.v_payment_revenue vpr
                   on vsp.database_source_id = vpr.database_source_id and vsp.payment_id = vpr.payment_id and
                      vpr.payment_status = 2 and vpr.refund_datetime is null
where vsp.database_source_id = 1
  and vsp.packet_type_paid_result_id = 1
  and e.country_code in ('ua', 'hu');

alter table employer.v_paid_packet_retention
    owner to pbi;

grant select on employer.v_paid_packet_retention to readonly;
