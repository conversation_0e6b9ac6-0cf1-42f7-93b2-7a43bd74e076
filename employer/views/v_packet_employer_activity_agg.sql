create or replace view employer.v_packet_employer_activity_agg as
select database_source_id,
       employer_id,
       subscription_id,
       packet_rank,
       packet_start_datetime,
       packet_end_datetime,
       count(distinct account_id) as account_cnt,
       count(distinct case when session_cnt > 0 then  account_id end) as active_account_cnt,
       sum(session_cnt) as session_cnt
from employer.packet_account_activity_agg
group by database_source_id,
       employer_id,
       subscription_id,
       packet_rank,
       packet_start_datetime,
       packet_end_datetime;

alter table employer.v_packet_employer_activity_agg
    owner to dap;

grant select on employer.v_packet_employer_activity_agg to readonly;