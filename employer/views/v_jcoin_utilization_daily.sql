create view employer.v_jcoin_utilization_daily
            (database_source_id, subscription_id, packet_rank, employer_id, utilization_datediff, packet_jcoin_cnt,
             packet_start_datetime, packet_end_datetime, packet_plan_end_datetime, packet_day_cnt, jcoin_plan_cnt,
             subscription_status, packet_price, packet_id, subscription_month_cnt, subscription_order_datetime,
             subscription_last_order_datetime, packet_type_id, payment_id, payment_rank, payment_start_datetime,
             payment_end_datetime, packet_type_paid_result_id, packet_next_start_datetime, jcoin_utilized_cnt, call_cnt,
             call_answered_cnt, call_answered_30_sec_free_packet_cnt, call_answered_30_sec_paid_packet_cnt, apply_cnt,
             apply_profile_viewed_cnt, apply_profile_open_contact_free_packet_cnt,
             apply_profile_open_contact_paid_packet_cnt, apply_profile_message_cnt, profile_base_search_cnt,
             profile_base_profile_viewed_cnt, profile_base_profile_open_contact_free_packet_cnt,
             profile_base_profile_open_contact_paid_packet_cnt, profile_base_profile_message_cnt,
             digital_recruiter_profile_cnt, digital_recruiter_profile_viewed_cnt,
             digital_recruiter_profile_open_contact_free_packet_cnt,
             digital_recruiter_profile_open_contact_paid_packet_cnt, digital_recruiter_profile_message_cnt,
             max_active_job_cnt, job_active_cnt, apply_revenue, call_revenue, digital_recruiter_revenue,
             profile_base_revenue, has_payment_subscription)
as
SELECT DISTINCT jupd.database_source_id,
                jupd.subscription_id,
                jupd.packet_rank,
                jupd.employer_id,
                jupd.utilization_datediff,
                jupd.packet_jcoin_cnt,
                jupd.packet_start_datetime,
                jupd.packet_end_datetime,
                jupd.packet_plan_end_datetime,
                jupd.packet_day_cnt,
                jupd.jcoin_plan_cnt,
                vsp.subscription_status,
                vsp.packet_price,
                vsp.packet_id,
                vsp.subscription_month_cnt,
                vsp.subscription_order_datetime,
                vsp.subscription_last_order_datetime,
                vsp.packet_type_id,
                vsp.payment_id,
                vsp.payment_rank,
                vsp.payment_start_datetime,
                vsp.payment_end_datetime,
                vsp.packet_type_paid_result_id,
                vsp.packet_next_start_datetime,
                coalesce(jmd.apply_profile_open_contact_free_packet_cnt + jmd.apply_profile_open_contact_paid_packet_cnt +
                jmd.digital_recruiter_profile_open_contact_free_packet_cnt +
                jmd.digital_recruiter_profile_open_contact_paid_packet_cnt +
                jmd.profile_base_profile_open_contact_free_packet_cnt +
                jmd.profile_base_profile_open_contact_paid_packet_cnt + jmd.call_answered_30_sec_paid_packet_cnt +
                jmd.call_answered_30_sec_free_packet_cnt,0) AS jcoin_utilized_cnt,
                coalesce(jmd.call_cnt,0) as call_cnt,
                coalesce(jmd.call_answered_cnt,0) as call_answered_cnt,
                coalesce(jmd.call_answered_30_sec_free_packet_cnt,0) as call_answered_30_sec_free_packet_cnt,
                coalesce(jmd.call_answered_30_sec_paid_packet_cnt,0) as call_answered_30_sec_paid_packet_cnt,
                coalesce(jmd.apply_cnt,0) as apply_cnt,
                coalesce(jmd.apply_profile_viewed_cnt,0) as apply_profile_viewed_cnt,
                coalesce(jmd.apply_profile_open_contact_free_packet_cnt,0) as apply_profile_open_contact_free_packet_cnt,
                coalesce(jmd.apply_profile_open_contact_paid_packet_cnt,0) as apply_profile_open_contact_paid_packet_cnt,
                coalesce(jmd.apply_profile_message_cnt,0) as apply_profile_message_cnt,
                coalesce(jmd.profile_base_search_cnt,0) as profile_base_search_cnt,
                coalesce(jmd.profile_base_profile_viewed_cnt,0) as profile_base_profile_viewed_cnt,
                coalesce(jmd.profile_base_profile_open_contact_free_packet_cnt,0) as profile_base_profile_open_contact_free_packet_cnt,
                coalesce(jmd.profile_base_profile_open_contact_paid_packet_cnt,0) as profile_base_profile_open_contact_paid_packet_cnt,
                coalesce(jmd.profile_base_profile_message_cnt,0) as profile_base_profile_message_cnt,
                coalesce(jmd.digital_recruiter_profile_cnt,0) as digital_recruiter_profile_cnt,
                coalesce(jmd.digital_recruiter_profile_viewed_cnt,0) as digital_recruiter_profile_viewed_cnt,
                coalesce(jmd.digital_recruiter_profile_open_contact_free_packet_cnt,0) as digital_recruiter_profile_open_contact_free_packet_cnt,
                coalesce(jmd.digital_recruiter_profile_open_contact_paid_packet_cnt,0) as digital_recruiter_profile_open_contact_paid_packet_cnt,
                coalesce(jmd.digital_recruiter_profile_message_cnt,0) as digital_recruiter_profile_message_cnt,
                coalesce(spjaa.max_active_job_cnt,0) as max_active_job_cnt,
                coalesce(uajd.job_active_cnt,0) as job_active_cnt,
                coalesce(jmd.apply_revenue,0) as apply_revenue,
                coalesce(jmd.call_revenue,0) as call_revenue,
                coalesce(jmd.digital_recruiter_revenue,0) as digital_recruiter_revenue,
                coalesce(jmd.profile_base_revenue,0) as profile_base_revenue,
                vsp.has_payment_subscription
FROM employer.jcoin_utilization_plan_daily jupd
         JOIN employer.m_subscription_packet vsp
              ON vsp.database_source_id = jupd.database_source_id AND vsp.subscription_id = jupd.subscription_id AND
                 vsp.packet_rank = jupd.packet_rank
         LEFT JOIN employer.jcoin_model_daily jmd ON jmd.database_source_id = jupd.database_source_id AND
                                                     jmd.subscription_id = jupd.subscription_id AND
                                                     jmd.packet_rank = jupd.packet_rank AND
                                                     jmd.action_datediff = jupd.utilization_datediff
         LEFT JOIN employer.subscription_packet_job_active_agg spjaa
                   ON spjaa.database_source_id = jupd.database_source_id AND
                      spjaa.subscription_id = jupd.subscription_id AND spjaa.packet_rank = jupd.packet_rank
         LEFT JOIN employer.utilization_active_job_daily uajd ON uajd.database_source_id = jupd.database_source_id AND
                                                                 uajd.subscription_id = jupd.subscription_id AND
                                                                 uajd.packet_rank = jupd.packet_rank AND
                                                                 uajd.utilization_datediff = jupd.utilization_datediff
WHERE jupd.utilization_datediff < fn_get_date_diff(CURRENT_DATE::timestamp without time zone);

alter table employer.v_jcoin_utilization_daily
    owner to ypi;

grant select on employer.v_jcoin_utilization_daily to readonly;

grant select on employer.v_jcoin_utilization_daily to writeonly_product;

grant select on employer.v_jcoin_utilization_daily to readonly_ds;

