create or replace view employer.v_packet_job_profile_submission as
select pjps.database_source_id,
       pjps.country_id,
       pjps.employer_id,
       pjps.subscription_id,
       pjps.packet_rank,
       pjps.job_seeker_job_uid,
       pjps.employer_job_id,
       pjps.profile_id,
       pjps.submission_datediff,
       pjps.source_type_id,
       msp.packet_type_id,
       msp.packet_type_paid_result_id
from employer.packet_job_profile_submission pjps
left join employer.m_subscription_packet msp
on pjps.database_source_id = msp.database_source_id and
   pjps.subscription_id = msp.subscription_id and
   pjps.packet_rank = msp.packet_rank;

grant select on employer.v_packet_job_profile_submission to readonly;