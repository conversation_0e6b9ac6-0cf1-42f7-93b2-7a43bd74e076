create or replace view employer.v_jcoin_running_utilization_daily
as
select jud.database_source_id,
       e.country_code,
       jud.subscription_id,
       jud.packet_rank,
       jud.employer_id,
       case when subscription_paid.id_subscription is not null then 1 else 0 end as has_payment_subscription,
       ps.status as subscription_status,
       jud.utilization_datediff,
       jud.packet_jcoin_cnt,
       jud.packet_price,
       ps.order_date as subscription_order_datetime,
       sum(jud.jcoin_plan_cnt)
       over (partition by jud.employer_id, jud.subscription_id, jud.packet_rank ORDER BY jud.utilization_datediff) AS jcoin_running_plan_cnt,
       sum(jud.jcoin_utilized_cnt)
       over (partition by jud.employer_id, jud.subscription_id, jud.packet_rank ORDER BY jud.utilization_datediff) AS jcoin_running_utilized_cnt,
       sum(jud.job_active_cnt)
       over (partition by jud.employer_id, jud.subscription_id, jud.packet_rank ORDER BY jud.utilization_datediff) AS job_active_cnt,
       sum(jud.call_cnt)
       over (partition by jud.employer_id, jud.subscription_id, jud.packet_rank ORDER BY jud.utilization_datediff) AS call_cnt,
       sum(jud.call_answered_cnt)
       over (partition by jud.employer_id, jud.subscription_id, jud.packet_rank ORDER BY jud.utilization_datediff) AS call_answered_cnt,
       sum(jud.call_answered_30_sec_free_packet_cnt)
       over (partition by jud.employer_id, jud.subscription_id, jud.packet_rank ORDER BY jud.utilization_datediff) AS call_answered_30_sec_free_packet_cnt,
       sum(jud.call_answered_30_sec_paid_packet_cnt)
       over (partition by jud.employer_id, jud.subscription_id, jud.packet_rank ORDER BY jud.utilization_datediff) AS call_answered_30_sec_paid_packet_cnt,
       sum(jud.apply_cnt)
       over (partition by jud.employer_id, jud.subscription_id, jud.packet_rank ORDER BY jud.utilization_datediff) AS apply_cnt,
       sum(jud.apply_profile_viewed_cnt)
       over (partition by jud.employer_id, jud.subscription_id, jud.packet_rank ORDER BY jud.utilization_datediff) AS apply_profile_viewed_cnt,
       sum(jud.apply_profile_open_contact_free_packet_cnt)
       over (partition by jud.employer_id, jud.subscription_id, jud.packet_rank ORDER BY jud.utilization_datediff) AS apply_profile_open_contact_free_packet_cnt,
       sum(jud.apply_profile_open_contact_paid_packet_cnt)
       over (partition by jud.employer_id, jud.subscription_id, jud.packet_rank ORDER BY jud.utilization_datediff) AS apply_profile_open_contact_paid_packet_cnt,
       sum(jud.apply_profile_message_cnt)
       over (partition by jud.employer_id, jud.subscription_id, jud.packet_rank ORDER BY jud.utilization_datediff) AS apply_profile_message_cnt,
       sum(jud.profile_base_search_cnt)
       over (partition by jud.employer_id, jud.subscription_id, jud.packet_rank ORDER BY jud.utilization_datediff) AS profile_base_search_cnt,
       sum(jud.profile_base_profile_viewed_cnt)
       over (partition by jud.employer_id, jud.subscription_id, jud.packet_rank ORDER BY jud.utilization_datediff) AS profile_base_profile_viewed_cnt,
       sum(jud.profile_base_profile_open_contact_free_packet_cnt)
       over (partition by jud.employer_id, jud.subscription_id, jud.packet_rank ORDER BY jud.utilization_datediff) AS profile_base_profile_open_contact_free_packet_cnt,
       sum(jud.profile_base_profile_open_contact_paid_packet_cnt)
       over (partition by jud.employer_id, jud.subscription_id, jud.packet_rank ORDER BY jud.utilization_datediff) AS profile_base_profile_open_contact_paid_packet_cnt,
       sum(jud.profile_base_profile_message_cnt)
       over (partition by jud.employer_id, jud.subscription_id, jud.packet_rank ORDER BY jud.utilization_datediff) AS profile_base_profile_message_cnt,
       sum(jud.digital_recruiter_profile_cnt)
       over (partition by jud.employer_id, jud.subscription_id, jud.packet_rank ORDER BY jud.utilization_datediff) AS digital_recruiter_profile_cnt,
       sum(jud.digital_recruiter_profile_viewed_cnt)
       over (partition by jud.employer_id, jud.subscription_id, jud.packet_rank ORDER BY jud.utilization_datediff) AS digital_recruiter_profile_viewed_cnt,
       sum(jud.digital_recruiter_profile_open_contact_free_packet_cnt)
       over (partition by jud.employer_id, jud.subscription_id, jud.packet_rank ORDER BY jud.utilization_datediff) AS digital_recruiter_profile_open_contact_free_packet_cnt,
       sum(jud.digital_recruiter_profile_open_contact_paid_packet_cnt)
       over (partition by jud.employer_id, jud.subscription_id, jud.packet_rank ORDER BY jud.utilization_datediff) AS digital_recruiter_profile_open_contact_paid_packet_cnt,
       sum(jud.digital_recruiter_profile_message_cnt)
       over (partition by jud.employer_id, jud.subscription_id, jud.packet_rank ORDER BY jud.utilization_datediff) AS digital_recruiter_profile_message_cnt,
       jud.max_active_job_cnt,
       p.name as packet_name,
       ec.company_name
  from employer.jcoin_utilization_daily jud
       join imp_employer.packet_subscription ps
       on jud.database_source_id = ps.sources
          and jud.subscription_id = ps.id
       join imp_employer.packet p
       on jud.database_source_id = p.sources
          and ps.id_packet = p.id
       join imp_employer.employer e
       on jud.database_source_id = e.sources
          and jud.employer_id = e.id
       join imp_employer.employer_cdp ec
       on jud.database_source_id = ec.sources
          and e.id_cdp = ec.id
       left join (select distinct so.sources,
                                  so.id_subscription
                  from imp_employer.subscription_order so
                  where so.date_paid is not null
                    and so.date_refunded is null) subscription_paid
       on jud.database_source_id = subscription_paid.sources
          and jud.subscription_id = subscription_paid.id_subscription


alter table employer.v_jcoin_running_utilization_daily
    owner to ypi;

grant select on employer.v_jcoin_running_utilization_daily to readonly;
