SELECT pnl.employer_id,
       e.country_code,
       pnl.payment_datetime,
       pnl.payment_order_date,
       date(fn_get_timestamp_from_date_diff(pnl.revenue_datediff)) AS revenue_date,
       pnl.revenue_without_vat_uah,
       pnl.payment_id,
       ba.id_counterparty                                          AS counterparty_id,
       pnl.subscription_id,
       pnl.packet_id,
       so.id_agreement                                             AS agreement_id,
       ba.code                                                     AS agreement_code
FROM employer.jcoin_model_revenue_pnl pnl
         JOIN imp_employer.employer e ON pnl.database_source_id = e.sources AND pnl.employer_id = e.id
         JOIN imp_employer.employer_cdp ec ON pnl.database_source_id = ec.sources AND e.id_cdp = ec.id
         JOIN imp_employer.subscription_order so ON pnl.database_source_id = so.sources AND pnl.payment_id = so.id
         JOIN imp_employer.billing_agreement ba ON pnl.database_source_id = ba.sources AND so.id_agreement = ba.id
WHERE pnl.database_source_id = 1
