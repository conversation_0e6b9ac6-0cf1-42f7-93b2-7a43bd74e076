create or replace view employer.v_subscription_event
            (database_source_id, employer_id, subscription_id, event_datetime, event_type, event_rank,
             is_previous_subscription, is_first_event_of_subscription)
as
WITH a AS (
    SELECT ps.sources     AS database_source_id,
           ps.id_employer AS employer_id,
           ps.id          AS subscription_id,
           CASE
               WHEN ps.status <> 0 AND a_1.event_type = 1 THEN ps.order_date
               WHEN ps.status <> 0 AND a_1.event_type = 2 THEN ps.last_order_date
               ELSE NULL::timestamp without time zone
               END        AS event_datetime,
           a_1.event_type
    FROM imp_employer.packet_subscription ps
             JOIN (SELECT 1 AS event_type
                   UNION ALL
                   SELECT 2 AS event_type
                   UNION ALL
                   SELECT 5 AS event_type) a_1 ON
        CASE
            WHEN ps.status = 0 THEN a_1.event_type = 5
            ELSE a_1.event_type = ANY (ARRAY [1, 2])
            END
    WHERE NOT (a_1.event_type = 2 AND ps.order_date = ps.last_order_date)
    UNION ALL
    SELECT ps.sources     AS database_source_id,
           ps.id_employer AS employer_id,
           ps.id          AS subscription_id,
           CASE
               WHEN a_1.event_type = 3 THEN so.date_ordered
               WHEN a_1.event_type = 4 THEN so.date_paid
               ELSE NULL::timestamp without time zone
               END        AS event_datetime,
           a_1.event_type
    FROM imp_employer.packet_subscription ps
             JOIN imp_employer.subscription_order so
                  ON ps.sources = so.sources AND ps.id = so.id_subscription AND so.date_paid IS NOT NULL AND
                     so.date_refunded IS NULL
             CROSS JOIN (SELECT 3 AS event_type
                         UNION ALL
                         SELECT 4 AS event_type) a_1
    UNION ALL
    SELECT poc.sources         AS database_source_id,
           poc.id_employer     AS employer_id,
           poc.id_subscription AS subscription_id,
           poc.date            AS event_datetime,
           6                   AS event_type
    FROM imp_employer.profile_open_contact poc
    WHERE poc.date IS NOT NULL
    UNION ALL
    SELECT jc.sources         AS database_source_id,
           jc.id_employer     AS employer_id,
           jc.id_subscription AS subscription_id,
           jc.date            AS event_datetime,
           7                  AS event_type
    FROM imp_employer.job_call jc
             JOIN imp_employer.employer e
                  ON jc.id_employer = e.id
                      AND jc.sources = e.sources
    WHERE jc.status = 1
      AND (jc.flags & 1) = 1
      AND e.country_code = 'ua' /* обмежую запис таблиці job_call лише для UA, оскільки Рінгостат не працює на HU */
),
     a_ranked AS (
         SELECT a.database_source_id,
                a.employer_id,
                a.subscription_id,
                a.event_datetime,
                a.event_type,
                row_number()
                OVER (PARTITION BY a.database_source_id, a.employer_id ORDER BY a.event_datetime, a.event_type) AS event_rank,
                COALESCE(a.subscription_id = lag(a.subscription_id)
                                             OVER (PARTITION BY a.database_source_id, a.employer_id ORDER BY a.event_datetime),
                         false)                                                                                 AS is_previous_subscription
         FROM a
     )
SELECT a_ranked.database_source_id,
       a_ranked.employer_id,
       a_ranked.subscription_id,
       a_ranked.event_datetime,
       a_ranked.event_type,
       a_ranked.event_rank,
       a_ranked.is_previous_subscription,
       a_ranked.event_rank = min(a_ranked.event_rank)
                             OVER (PARTITION BY a_ranked.database_source_id, a_ranked.employer_id, a_ranked.subscription_id) AS is_first_event_of_subscription
FROM a_ranked;
