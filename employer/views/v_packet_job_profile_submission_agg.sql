create or replace view employer.v_packet_job_profile_submission_agg as
select  database_source_id,
           country_id,
           employer_id,
           subscription_id,
           packet_rank,
           count(distinct profile_id) as profile_submitted_cnt
from employer.packet_job_profile_submission
group by database_source_id,
           country_id,
           employer_id,
           subscription_id,
           packet_rank;

alter table employer.v_packet_job_profile_submission_agg
    owner to dap;

grant select on employer.v_packet_job_profile_submission_agg to readonly;