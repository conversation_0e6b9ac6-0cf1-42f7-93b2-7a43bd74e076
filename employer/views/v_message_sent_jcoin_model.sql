create or replace view employer.v_message_sent_jcoin_model
            (database_source_id, country_code, subscription_id, packet_rank, action_datediff, employer_id,
             apply_profile_message_cnt,
             digital_recruiter_profile_message_cnt, profile_base_profile_message_cnt)
as
select vsp.database_source_id,
       e.country_code,
       vsp.subscription_id,
       vsp.packet_rank,
       atcm.action_datediff as action_datediff,
       vsp.employer_id,
       count(distinct
             case
                 when atcm.feature_id = 1 then atcm.profile_id
                 else null::integer
                 end)       as apply_profile_message_cnt,
       count(distinct
             case
                 when atcm.feature_id = 2 then atcm.profile_id
                 else null::integer
                 end)       as digital_recruiter_profile_message_cnt,
       count(distinct
             case
                 when atcm.feature_id = 3 then atcm.profile_id
                 else null::integer
                 end)       as profile_base_profile_message_cnt
from profile.action_to_contact_message atcm
         join imp_employer.employer_account ea on ea.sources = 1 and atcm.employer_account_id = ea.id_account
         join imp_employer.employer e
              on e.sources = ea.sources and e.id = ea.id_employer
         join employer.m_subscription_cont_period vscp
              on vscp.database_source_id = ea.sources and vscp.employer_id = ea.id_employer and
                 atcm.action_datetime between vscp.period_start_datetime and vscp.period_end_datetime
         join employer.m_subscription_packet vsp
              on vsp.database_source_id = vscp.database_source_id and vsp.employer_id = vscp.employer_id
                  and vsp.subscription_id = vscp.subscription_id
                  and atcm.action_datetime between vsp.packet_start_datetime and vsp.packet_end_datetime
where atcm.country_id in (1, 10)
  and atcm.action_datediff >= 44407
  and atcm.platform_user_type_id = 2
group by vsp.database_source_id, e.country_code, atcm.action_datediff, vsp.employer_id, vsp.subscription_id,
         vsp.packet_rank;
