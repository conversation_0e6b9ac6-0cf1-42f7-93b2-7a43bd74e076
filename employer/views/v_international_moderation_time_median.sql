create or replace view employer.v_international_moderation_time_median(statistics_type, moderation_date, mod_time_min_median) as
with
    raw_data as (
        select
            'employer'                                                     as statistics_type,
            sem.action_type,
            sem.date_created at time zone 'utc' at time zone 'europe/kiev' as date_moderated,
            m.moderation_access,
            max(case
                    when sem.action_type = 0 and sem.moderation_status = 0
                        then sem.date_created at time zone 'utc' at time zone 'europe/kiev' end)
            over (partition by sem.sources, sem.id_employer order by sem.date_created
                range between unbounded preceding and current row)         as date_created,
            sem.sources,
            sem.id_employer                                                as id_entity
        from
            imp_employer.statistics_employer_moderation sem
            left join imp_employer.moderator m
                      on m.sources = sem.sources and m.email = sem.moderator_email

        union all

        select
            'job'                                                          as statistics_type,
            sjm.action_type,
            sjm.date_created at time zone 'utc' at time zone 'europe/kiev' as date_moderated,
            m.moderation_access,
            max(case
                    when sjm.action_type = 0 and sjm.moderation_status = 0
                        then sjm.date_created at time zone 'utc' at time zone 'europe/kiev' end)
            over (partition by sjm.sources, sjm.id_job order by sjm.date_created
                range between unbounded preceding and current row)         as date_created,
            sjm.sources,
            sjm.id_job                                                     as id_entity
        from
            imp_employer.statistics_job_moderation sjm
            left join imp_employer.moderator m
                      on m.sources = sjm.sources and m.email = sjm.moderator_email
    ),
    moderation_log as (
        select
            statistics_type,
            date_moderated::date as moderation_date,
            date_created,
            date_moderated,
            extract(epoch from date_moderated - date_created) / 60 as moderation_time_min,
            sources,
            id_entity
        from
            raw_data
        where
              date_moderated::date between '2022-11-01' and current_date - 1
          and action_type = 3
          and moderation_access = 2
    )
select
    statistics_type,
    moderation_date,
    percentile_cont(0.5) within group (order by moderation_time_min) as mod_time_min_median
from
    moderation_log
group by
    statistics_type,
    moderation_date;

grant select on employer.v_international_moderation_time_median to ypr, user_agg_team;
