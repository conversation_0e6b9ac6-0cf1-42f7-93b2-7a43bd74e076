with payments_1 as (
    select so.sources,
           so.id                                                                                     as payment_id,
           so.id_subscription,
           so.date_ordered,
           case
               when so.status = 2 and so.date_refunded is null then 1
               else 0
               end                                                                                   as is_payment_successful,
           lead(so.date_ordered)
           over (partition by so.sources, so.id_subscription order by so.date_ordered, so.date_paid) as next_date_ordered,
           ba.payment_method
    from imp_employer.subscription_order so
             left join imp_employer.billing_agreement ba
                       on ba.sources = so.sources and ba.id = so.id_agreement and ba.payment_method is not null
    where so.sources = 1
),
     payments as (
         select payments_1.sources,
                payments_1.payment_id,
                payments_1.id_subscription,
                payments_1.is_payment_successful,
                dense_rank()
                over (partition by payments_1.sources, payments_1.id_subscription order by payments_1.date_ordered) as payment_rank,
                payments_1.payment_method
         from payments_1
         where payments_1.is_payment_successful = 1
            or payments_1.is_payment_successful = 0 and payments_1.next_date_ordered is null
     ),
     vsp as (
         select vsp_1.database_source_id,
                vsp_1.employer_id,
                vsp_1.subscription_id,
                vsp_1.subscription_status,
                vsp_1.packet_price,
                vsp_1.packet_price_eur,
                vsp_1.subscription_month_cnt,
                vsp_1.jcoin_cnt,
                vsp_1.subscription_order_datetime,
                vsp_1.subscription_last_order_datetime,
                vsp_1.subscription_expiring_datetime,
                vsp_1.subscription_fact_expiring_datetime,
                vsp_1.subscription_plan_expiring_datetime,
                vsp_1.subscription_paid_period_end_datetime,
                vsp_1.packet_id,
                case
                    when vsp_1.packet_price > 0::numeric then 2
                    else 1
                    end as packet_type_id,
                so.payment_id,
                so.is_payment_successful,
                so.payment_rank,
                case
                    when so.is_payment_successful = 1 then vsp_1.subscription_order_datetime +
                                                           '1 mon'::interval * (so.payment_rank - 1)::double precision *
                                                           vsp_1.subscription_month_cnt::double precision
                    else null::timestamp without time zone
                    end as payment_start_datetime,
                a.packet_rank,
                case
                    when a.packet_rank = first_value(a.packet_rank)
                                         over (partition by vsp_1.database_source_id, vsp_1.subscription_id order by a.packet_rank desc)
                        then 1
                    else 0
                    end as is_last_packet,
                case
                    when date(vsp_1.subscription_order_datetime +
                              '1 mon'::interval * (a.packet_rank - 1)::double precision) =
                         date(vsp_1.subscription_paid_period_end_datetime) and
                         coalesce(so.is_payment_successful, 0) = 0 and
                         vsp_1.subscription_paid_period_end_datetime < vsp_1.subscription_fact_expiring_datetime
                        then vsp_1.subscription_paid_period_end_datetime
                    else vsp_1.subscription_order_datetime + '1 mon'::interval * (a.packet_rank - 1)::double precision
                    end as packet_start_datetime,
                vsp_1.has_payment_subscription,
                vsp_1.packet_name,
                vsp_1.payment_method
         from employer.v_subscription_packet_info vsp_1
                  left join (select generate_series(1, 12) as packet_rank) a
                            on
                                case
                                    when date(vsp_1.subscription_expiring_datetime) =
                                         date(vsp_1.subscription_order_datetime) and
                                         vsp_1.subscription_status <> 0 then a.packet_rank = 1
                                    else
                                        case
                                            when date(vsp_1.subscription_order_datetime +
                                                      '1 mon'::interval * (a.packet_rank - 1)::double precision) =
                                                 date(vsp_1.subscription_expiring_datetime) and
                                                 date(vsp_1.subscription_last_order_datetime) =
                                                 date(vsp_1.subscription_expiring_datetime) and
                                                 (vsp_1.subscription_expiring_datetime -
                                                  vsp_1.subscription_last_order_datetime) >
                                                 '00:01:00'::interval then
                                                    (vsp_1.subscription_order_datetime + '1 mon'::interval *
                                                                                         (a.packet_rank - 1)::double precision) <
                                                    vsp_1.subscription_expiring_datetime
                                            else date(vsp_1.subscription_order_datetime +
                                                      '1 mon'::interval * (a.packet_rank - 1)::double precision) <
                                                 date(vsp_1.subscription_expiring_datetime)
                                            end
                                    end
                  left join payments so
                            on vsp_1.database_source_id = so.sources and vsp_1.subscription_id = so.id_subscription and
                               so.payment_rank::integer =
                               ceil(a.packet_rank::double precision / vsp_1.subscription_month_cnt::double precision)::integer
         where vsp_1.subscription_status <> 0
           and vsp_1.database_source_id = 1
           and not (vsp_1.packet_price > 0::numeric and so.payment_id is null)
     )
select vsp.database_source_id,
       vsp.employer_id,
       vsp.subscription_id,
       vsp.subscription_status,
       vsp.packet_price,
       vsp.packet_id,
       vsp.subscription_month_cnt,
       vsp.jcoin_cnt                                                                            as packet_jcoin_cnt,
       vsp.subscription_order_datetime,
       vsp.subscription_last_order_datetime,
       vsp.packet_type_id,
       vsp.subscription_expiring_datetime,
       vsp.subscription_fact_expiring_datetime,
       vsp.subscription_plan_expiring_datetime,
       vsp.subscription_paid_period_end_datetime,
       vsp.payment_id,
       vsp.payment_rank,
       vsp.payment_start_datetime,
       case
           when vsp.is_payment_successful = 1 then coalesce(max(vsp.payment_start_datetime)
                                                            over (partition by vsp.database_source_id, vsp.subscription_id order by vsp.payment_rank groups between 1 following and 1 following) -
                                                            '00:00:00.001'::interval,
                                                            vsp.subscription_paid_period_end_datetime,
                                                            vsp.subscription_plan_expiring_datetime)
           else null::timestamp without time zone
           end                                                                                  as payment_end_datetime,
       vsp.packet_rank,
       case
           when vsp.subscription_status = 1 and vsp.is_last_packet = 1 then 3
           else
               case
                   when vsp.is_payment_successful = 1 and vsp.packet_price > 0::numeric then 1
                   else 2
                   end
           end                                                                                  as packet_type_paid_result_id,
       vsp.packet_start_datetime,
       lead(vsp.packet_start_datetime)
       over (partition by vsp.database_source_id, vsp.subscription_id order by vsp.packet_rank) as packet_next_start_datetime,
       coalesce(lead(vsp.packet_start_datetime)
                over (partition by vsp.database_source_id, vsp.subscription_id order by vsp.packet_rank) -
                '00:00:00.001'::interval, vsp.subscription_fact_expiring_datetime,
                vsp.packet_start_datetime + '1 mon'::interval - '00:00:00.001'::interval)       as packet_end_datetime,
       vsp.packet_start_datetime + '1 mon'::interval -
       '00:00:00.001'::interval                                                                 as packet_plan_end_datetime,
       null::timestamp without time zone                                                        as packet_cont_period_start_datetime,
       null::timestamp without time zone                                                        as packet_cont_period_end_datetime,
       null::integer                                                                            as packet_cont_period_rank,
       vsp.has_payment_subscription,
       vsp.packet_name,
       vsp.payment_method
from vsp
         left join employer.exception_v_subscription_packet excep
                   on vsp.database_source_id = excep.database_source_id and
                      vsp.subscription_id = excep.subscription_id and
                      vsp.packet_rank = excep.packet_rank
where excep.subscription_id is null
union all
select exception_v_subscription_packet.database_source_id,
       exception_v_subscription_packet.employer_id,
       exception_v_subscription_packet.subscription_id,
       exception_v_subscription_packet.subscription_status,
       exception_v_subscription_packet.packet_price,
       exception_v_subscription_packet.packet_id,
       exception_v_subscription_packet.subscription_month_cnt,
       exception_v_subscription_packet.packet_jcoin_cnt,
       exception_v_subscription_packet.subscription_order_datetime,
       exception_v_subscription_packet.subscription_last_order_datetime,
       exception_v_subscription_packet.packet_type_id,
       exception_v_subscription_packet.subscription_expiring_datetime,
       exception_v_subscription_packet.subscription_fact_expiring_datetime,
       exception_v_subscription_packet.subscription_plan_expiring_datetime,
       exception_v_subscription_packet.subscription_paid_period_end_datetime,
       exception_v_subscription_packet.payment_id,
       exception_v_subscription_packet.payment_rank,
       exception_v_subscription_packet.payment_start_datetime,
       exception_v_subscription_packet.payment_end_datetime,
       exception_v_subscription_packet.packet_rank,
       exception_v_subscription_packet.packet_type_paid_result_id,
       exception_v_subscription_packet.packet_start_datetime,
       exception_v_subscription_packet.packet_next_start_datetime,
       exception_v_subscription_packet.packet_end_datetime,
       exception_v_subscription_packet.packet_plan_end_datetime,
       exception_v_subscription_packet.packet_cont_period_start_datetime,
       exception_v_subscription_packet.packet_cont_period_end_datetime,
       exception_v_subscription_packet.packet_cont_period_rank,
       exception_v_subscription_packet.has_payment_subscription,
       exception_v_subscription_packet.packet_name,
       exception_v_subscription_packet.payment_method
from employer.exception_v_subscription_packet;


alter table employer.v_subscription_packet
    owner to pbi;
