create or replace view employer.v_employer_onboarding_activity
            (database_source_id, employer_account_id, employer_moderation_status, is_shadow, has_session_in_1_6_day, has_balance_in_0_6_day, employer_creation_datetime,
             session_in_7_13_day_cnt, atc_0_6_day_per_employer_account, atc_0_6_day_per_profile, employer_id)
as
WITH employer_data AS (
    SELECT e.sources              AS database_source_id,
           e.id                   AS employer_id,
           ea.id_account          AS employer_account_id,
           e.date_created         AS employer_creation_datetime,
           e.moderation_status    as employer_moderation_status
           CASE
               WHEN e.source <> 0 THEN 1
               ELSE 0
               END                AS is_shadow,
           max(
                   CASE
                       WHEN eas1.id IS NOT NULL THEN 1
                       ELSE 0
                       END)       AS has_session_in_1_6_day,
           count(DISTINCT eas.id) AS session_in_7_13_day_cnt
    FROM imp_employer.employer e
             JOIN imp_employer.employer_account ea ON ea.sources = e.sources AND ea.id_employer = e.id
             JOIN imp_employer.job j ON j.sources = e.sources AND j.id_account = ea.id_account /*AND j.apply_type <> 2*/ AND
                                        fn_get_date_diff(j.date_created) >= fn_get_date_diff(e.date_created) AND
                                        fn_get_date_diff(j.date_created) <= (fn_get_date_diff(e.date_created) + 6)
             LEFT JOIN imp_employer.employer_account_session eas1
                       ON eas1.sources = e.sources AND eas1.id_account = ea.id_account AND
                          eas1.date_diff >= (fn_get_date_diff(e.date_created) + 1) AND
                          eas1.date_diff <= (fn_get_date_diff(e.date_created) + 6) AND eas1.using_secret_key = false
             LEFT JOIN imp_employer.employer_account_session eas
                       ON eas.sources = e.sources AND eas.id_account = ea.id_account AND
                          eas.date_diff >= (fn_get_date_diff(e.date_created) + 7) AND
                          eas.date_diff <= (fn_get_date_diff(e.date_created) + 13) AND eas.using_secret_key = false
    WHERE e.country_code::text = 'ua'::text
      AND e.date_created >= '2021-01-01 00:00:00'::timestamp without time zone
    GROUP BY e.sources, e.source, e.id, ea.id_account, e.date_created, e.moderation_status
),
employer_balance as (
select database_source_id,
       employer_id,
       employer_creation_datetime,
       max(has_balance_in_0_6_day) as has_balance_in_0_6_day
  from (select e.sources                                          as database_source_id,
               e.id                                               as employer_id,
               e.date_created                                     as employer_creation_datetime,
               case when sum(value) is not null then 1 else 0 end as has_balance_in_0_6_day
        from imp_employer.employer e
                 left join imp_employer.employer_balance_history ebh
                           on ebh.sources = e.sources
                               and ebh.id_employer = e.id
                               and change_type in (1, 5, 7)
                               and fn_get_date_diff(ebh.date_created) >= fn_get_date_diff(e.date_created) and
                              fn_get_date_diff(ebh.date_created) <= (fn_get_date_diff(e.date_created) + 6)
        where e.sources = 1
          and e.country_code = 'ua'
          and e.date_created between '2021-01-01 00:00:00'::timestamp without time zone and '2021-07-31 23:59:59'::timestamp without time zone
        group by e.sources,
                 e.id,
                 e.date_created
        union
        select e.sources                                          as database_source_id,
               e.id                                               as employer_id,
               e.date_created,
               max(case when so.id is not null then 1 else 0 end) as has_balance_in_0_6_day
        from imp_employer.employer e
                 left join imp_employer.packet_subscription ps
                           on e.sources = ps.sources
                               and e.id = ps.id_employer
                               and ps.id_packet not in (1, 12)
                               and ps.status in (1, 2, 4)
                 left join imp_employer.subscription_order so
                           on ps.sources = so.sources
                               and ps.id = so.id_subscription
                               and so.date_paid is not null
                               and fn_get_date_diff(so.date_paid) >= fn_get_date_diff(e.date_created) and
                              fn_get_date_diff(so.date_paid) <= (fn_get_date_diff(e.date_created) + 6)
        where e.sources = 1
          and e.country_code = 'ua'
          and e.date_created >= '2021-07-26 00:00:00'::timestamp without time zone
        group by e.sources,
                 e.id,
                 e.date_created
       ) balance
 group by database_source_id,
         employer_id,
         employer_creation_datetime
)
SELECT ed.database_source_id,
       ed.employer_account_id,
       ed.employer_moderation_status,
       ed.is_shadow,
       ed.has_session_in_1_6_day as has_session_in_1_6_day,
       case when eb.balance_sum_in_0_6_day is not null then 1 else 0 end as has_balance_in_0_6_day,
       ed.employer_creation_datetime,
       ed.session_in_7_13_day_cnt as session_in_7_13_day_per_employer_account,
       sum(case when platform_user_type_id = 2 then atcs.action_to_contact_prob end) as atc_0_6_day_per_employer_account,
       sum(case when platform_user_type_id = 1 then atcs.action_to_contact_prob end) as atc_0_6_day_per_profile,
       ed.employer_id
FROM employer_data ed
         left join employer_balance eb
         on ed.database_source_id = eb.database_source_id
            and ed.employer_id = eb.employer_id
         LEFT JOIN profile.action_to_contact_structure atcs
                   ON atcs.country_id = 1 AND atcs.employer_account_id = ed.employer_account_id AND
                      atcs.action_datediff >= fn_get_date_diff(ed.employer_creation_datetime) AND
                      atcs.action_datediff <= (fn_get_date_diff(ed.employer_creation_datetime) + 6)
GROUP BY ed.database_source_id, ed.employer_id, ed.has_session_in_1_6_day, eb.balance_sum_in_0_6_day, ed.is_shadow, ed.employer_account_id,
         ed.employer_creation_datetime, ed.session_in_7_13_day_cnt, employer_moderation_status;

alter table employer.v_employer_onboarding_activity
    owner to ypi;

grant select on employer.v_employer_onboarding_activity to readonly;

grant select on employer.v_employer_onboarding_activity to readonly_ds;

