create or replace procedure employer.insert_employer_subscription_break()
    language plpgsql
as
$$
begin

	truncate table employer.subscription_break;

	insert into employer.subscription_break(database_source_id, employer_id, parent_subscription_id, parent_subscription_order_datetime,
	                                        parent_subscription_expiring_datetime, break_subscription_id, break_subscription_order_datetime, break_start_datetime, break_end_datetime)
    with
        vspi_inner as (
            select database_source_id,
                   employer_id,
                   subscription_id,
                   subscription_order_datetime,
                   packet_start_datetime as first_packet_start_datetime,
                   subscription_expiring_datetime
            from employer.v_subscription_packet vspi
            where packet_rank = 1
        )
    select vspi.database_source_id,
           vspi.employer_id,
           vspi.subscription_id                      as parent_subscription_id,
           vspi.subscription_order_datetime          as parent_subscription_order_datetime,
           vspi.subscription_expiring_datetime       as parent_subscription_expiring_datetime,
           vspi_inner.subscription_id                as break_subscription_id,
           vspi_inner.subscription_order_datetime    as break_subscription_order_datetime,
           vspi_inner.first_packet_start_datetime    as break_start_datetime,
           vspi_inner.subscription_expiring_datetime as break_end_datetime
    from employer.v_subscription_packet_info vspi
         join vspi_inner
         on vspi.database_source_id = vspi_inner.database_source_id
             and vspi.employer_id = vspi_inner.employer_id
             and vspi.subscription_id <> vspi_inner.subscription_id
             and vspi_inner.first_packet_start_datetime > vspi.subscription_order_datetime
             and vspi_inner.first_packet_start_datetime < vspi.subscription_expiring_datetime;



end;
$$;

alter procedure employer.insert_employer_subscription_break() owner to postgres;
