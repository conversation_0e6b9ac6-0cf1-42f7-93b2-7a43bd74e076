create or replace procedure employer.insert_employer_subscription_active_date()
    language plpgsql
as
$$
begin

        truncate table employer.subscription_active_date;

        insert into employer.subscription_active_date(database_source_id, employer_id, subscription_id, subscription_date, subscription_datediff)
        select distinct
               database_source_id,
               employer_id,
               subscription_id,
               ic.dt as subscription_date,
               ic.date_diff::int as subscription_datediff
        from employer.v_subscription_cont_period vscp
        left join dimension.info_calendar ic on ic.dt between date(vscp.period_start_datetime) and date(vscp.period_end_datetime);



end;


$$;

alter procedure employer.insert_employer_subscription_active_date() owner to rlu;
