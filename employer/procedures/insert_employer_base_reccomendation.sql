create or replace procedure employer.insert_employer_base_reccomendation()
	language plpgsql
as $$
begin

        truncate table employer.employer_base_reccomendation;

        insert into employer.employer_base_reccomendation (employer_id, employer_name, country_code, ea_created_date, industry,
                                                           register_source, account_type, has_employer_balance, balance, min_recommendation_date, database_source_id)
        with recommend_base_ea as (
        select j.id_employer,
               j.sources,
               min(cast(arg.date as date)) as min_date
        from imp_employer.apply_recommendation_group arg
        inner join imp_employer.job j on arg.sources=j.sources and j.id=arg.id_job
        group by j.id_employer,
                 j.sources)

        select distinct acc.employer_id,
                        acc.employer_name,
                        acc.country_code,
                        acc.ea_date_created,
                        acc.industry,
                        acc.register_source,
                        acc.account_type,
                        case when eb.id_employer is null then 0 else 1 end as has_eb,
                        balance::int as balance,
                        min_date, -- минимальная дата получения рекомендации
                        recommend_base_ea.sources
        from recommend_base_ea
        inner join retention_ea.stg_employer_related_account acc on recommend_base_ea.id_employer = acc.employer_id and recommend_base_ea.sources = acc.sources
        left join imp_employer.employer_balance eb on eb.sources=recommend_base_ea.sources and eb.id_employer=recommend_base_ea.id_employer;


end;


$$;

alter procedure employer.insert_employer_base_reccomendation() owner to rlu;
