create procedure employer.insert_employer_category()
    language plpgsql
as
    $$
    begin

        truncate table employer.employer_category;

        insert into employer.employer_category(database_source_id, country_code, employer_id, company_name, registration_date,
                                               first_subscription_order_datetime, first_paid_subscription_order_datetime,
                                               industry, max_active_job_cnt, active_job_group, registration_source, channel,
                                               blue_score_avg, blue_score_group, job_with_blue_score_cnt,
                                               max_subscription_month_cnt, max_utilization_velocity, utilization_group,
                                               avg_revenue_per_payment, revenue_total)
        with
            employer_jobs as (
                select ej.sources     as database_source_id,
                       ej.id_employer as employer_id,
                       max(ej.active) as max_active_job_cnt,
                       case
                           when max(ej.active) = 0               then 0
                           when max(ej.active) between 1 and 4   then 1
                           when max(ej.active) between 5 and 10  then 2
                           when max(ej.active) between 11 and 50 then 3
                           when max(ej.active) > 50              then 4
                       end            as active_job_group
                from imp_employer.employer_jobs ej
                group by ej.sources, ej.id_employer
            ),
            employer_reg_source as (
                select country_id,
                       employer_id,
                       source_medium                                                                                as registration_source,
                       channel_grouping                                                                             as channel,
                       row_number() over (partition by country_id, employer_id order by action_date, source_medium) as row_num
                from imp_api.ga_dte_ea_reg
            ),
            emloyer_blue_score as (
                select database_source_id,
                       employer_id,
                       avg(blue_score_avg)    as blue_score_avg,
                       count(distinct job_id) as job_with_blue_score_cnt
                from employer.job_blue_collar_score jbs
                group by database_source_id, employer_id
            ),
            employer_subscription as (
                select database_source_id,
                       employer_id,
                       max(case
                               when has_payment_subscription = 1 then subscription_month_cnt
                               else 0
                           end) as                              max_subscription_month_cnt,
                       min(vspi.subscription_order_datetime) as first_subscription_order_datetime,
                       min(case
                               when has_payment_subscription = 1 then vspi.subscription_order_datetime
                           end) as                              first_paid_subscription_order_datetime
                from employer.v_subscription_packet_info vspi
                group by database_source_id, employer_id
            ),
            employer_utilization_velocity as (
                select database_source_id,
                       employer_id,
                       max(utilization_velocity) as max_utilization_velocity
                from (
                         select database_source_id,
                                employer_id,
                                date_trunc('week', public.fn_get_timestamp_from_date_diff(utilization_datediff)) as utilization_week,
                                round(sum(jcoin_running_utilized_cnt)::numeric / sum(jcoin_running_plan_cnt),
                                      4)                                                                  as utilization_velocity
                         from employer.v_jcoin_running_utilization_daily rud
                         where date_trunc('week', public.fn_get_timestamp_from_date_diff(utilization_datediff)) <
                               date_trunc('week', current_date - 1) /* відкидаємо неповні тижні */
                           and has_payment_subscription = 1
                         group by database_source_id, employer_id,
                                  date_trunc('week', public.fn_get_timestamp_from_date_diff(utilization_datediff))
                     ) employer_utilization_week
                group by database_source_id, employer_id
            ),
            employer_revenue as (
                select database_source_id,
                       employer_id,
                       avg(payment_without_vat_uah) as avg_revenue_per_payment,
                       sum(payment_without_vat_uah) as revenue_total
                from employer.v_payment_revenue vpr
                where payment_status = 2
                  and refund_datetime is null
                group by database_source_id, employer_id
            )
        select e.sources                               as database_source_id,
               e.country_code,
               e.id                                    as employer_id,
               ecdp.company_name,
            /* дата реєстрації, першої підписки, першої оплаченої підписки */
               e.date_created                          as registration_date,
               es.first_subscription_order_datetime,
               es.first_paid_subscription_order_datetime,
            /* індустрія */
               ecdp.industry,
            /* по к-сті активних вакансій */
               ej.max_active_job_cnt,
               ej.active_job_group,
            /* по source приходу */
               ers.registration_source,
               ers.channel,
            /* blue score */
               ebs.blue_score_avg,
               case
                   when blue_score_avg >= 0.00 and blue_score_avg < 0.33 then 1
                   when blue_score_avg >= 0.33 and blue_score_avg < 0.66 then 2
                   when blue_score_avg >= 0.66 and blue_score_avg <= 1   then 3
               end                                     as blue_score_group,
               ebs.job_with_blue_score_cnt,
            /* максимальна тривалість купленої підписки */
               es.max_subscription_month_cnt,
            /* сегмент по утилізації */
               euv.max_utilization_velocity,
               case
                   when euv.max_utilization_velocity >= 0.00 and euv.max_utilization_velocity < 0.4 then 4
                   when euv.max_utilization_velocity >= 0.4 and euv.max_utilization_velocity < 0.8  then 3
                   when euv.max_utilization_velocity >= 0.8 and euv.max_utilization_velocity < 1.2  then 2
                   when euv.max_utilization_velocity >= 1.2                                         then 1
               end                                     as utilization_group,
            /* платежеспособность */
               coalesce(er.avg_revenue_per_payment, 0) as avg_revenue_per_payment,
               coalesce(er.revenue_total, 0)           as revenue_total
        from imp_employer.employer e
             left join imp_employer.employer_cdp ecdp
             on e.sources = ecdp.sources
                 and e.id_cdp = ecdp.id

             left join employer_jobs ej
             on e.sources = ej.database_source_id
                 and e.id = ej.employer_id

             left join employer_reg_source ers
             on e.sources = 1
                 and ers.country_id = 1
                 and e.id = ers.employer_id
                 and ers.row_num = 1 /* беремо лише найраніший запис із source*/

             left join emloyer_blue_score ebs
             on e.sources = ebs.database_source_id
                 and e.id = ebs.employer_id

             left join employer_subscription es
             on e.sources = es.database_source_id
                 and e.id = es.employer_id

             left join employer_utilization_velocity euv
             on e.sources = euv.database_source_id
                 and e.id = euv.employer_id

             left join employer_revenue er
             on e.sources = er.database_source_id
                 and e.id = er.employer_id
        where e.sources = 1
          and e.country_code = 'ua';

    end;
    $$;

