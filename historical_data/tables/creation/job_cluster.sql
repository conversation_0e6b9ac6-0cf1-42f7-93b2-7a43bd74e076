create table historical_data.job_cluster
(
    country_id smallint not null,
    id_job      bigint  not null,
    id_cluster  integer not null,
    cluster     varchar(2000),

    constraint pk_job_cluster
        primary key (country_id, id_job, id_cluster)

) partition by range (country_id)
  tablespace data4;


create table historical_data.job_cluster_pt_1 partition of historical_data.job_cluster
    for values from (1) to (2);

create table historical_data.job_cluster_pt_2 partition of historical_data.job_cluster
    for values from (2) to (3);

create table historical_data.job_cluster_pt_3 partition of historical_data.job_cluster
    for values from (3) to (4);

create table historical_data.job_cluster_pt_4 partition of historical_data.job_cluster
    for values from (4) to (5);

create table historical_data.job_cluster_pt_5 partition of historical_data.job_cluster
    for values from (5) to (6);

create table historical_data.job_cluster_pt_6 partition of historical_data.job_cluster
    for values from (6) to (7);

create table historical_data.job_cluster_pt_9 partition of historical_data.job_cluster
    for values from (9) to (10);

create table historical_data.job_cluster_pt_10 partition of historical_data.job_cluster
    for values from (10) to (11);

create table historical_data.job_cluster_pt_11 partition of historical_data.job_cluster
    for values from (11) to (12);

create table historical_data.job_cluster_pt_13 partition of historical_data.job_cluster
    for values from (13) to (14);

create table historical_data.job_cluster_pt_16 partition of historical_data.job_cluster
    for values from (16) to (17);


create table historical_data.job_cluster_pt_def partition of historical_data.job_cluster
    default;
