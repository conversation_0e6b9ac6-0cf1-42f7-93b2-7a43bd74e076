create view v_tableau_datasources
            (row_num, datasource_name, datasource_owner, is_published, datasource_id, datasource_url_id,
             datasource_project, datasource_project_url_id, last_refresh_datetime, last_incremental_update_datetime,
             last_update_time, workbook_url_id, workbook_name, workbook_owner, workbook_project,
             workbook_project_url_id, physical_table_id, is_custom_sql, physical_table_name, database_name,
             connection_type, database_host, is_in_dwh, database_table_name, database_table_schema, database_table_type,
             view_table_schema, view_table_name)
as
with
    dwh_tables as (
        select
            pg_tables.schemaname,
            pg_tables.tablename,
            'table'::text as type,
            1             as is_in_dwh,
            null::name    as view_table_schema,
            null::name    as view_table_name
        from
            pg_tables
        union all
        select
            v.schemaname,
            v.viewname,
            'view'::text    as type,
            1               as is_in_dwh,
            vt.table_schema as view_table_schema,
            vt.table_name   as view_table_name
        from
            pg_views v
            left join dc.view_table_usage_snapshot vt
                      on vt.view_schema::name = v.schemaname and vt.view_name::name = v.viewname
    )
select
    row_number() over (order by ds.datasource_name)                                as row_num,
    ds.datasource_name,
    ds.owner_name                                                                  as datasource_owner,
    ds.is_published,
    ds.id                                                                          as datasource_id,
    ds.url_id                                                                      as datasource_url_id,
    ds.project_name                                                                as datasource_project,
    ds.project_url_id                                                              as datasource_project_url_id,
    timezone('europe/kiev'::text, timezone('utc'::text, ds.last_refresh_datetime)) as last_refresh_datetime,
    timezone('europe/kiev'::text,
             timezone('utc'::text, ds.last_incremental_update_datetime))           as last_incremental_update_datetime,
    timezone('europe/kiev'::text, timezone('utc'::text, ds.last_update_time))      as last_update_time,
    wb.url_id                                                                      as workbook_url_id,
    wb.workbook_name,
    wb.owner_name                                                                  as workbook_owner,
    wb.project_name                                                                as workbook_project,
    wb.project_url_id                                                              as workbook_project_url_id,
    pt.id                                                                          as physical_table_id,
    pt.is_custom_sql,
    pt.table_name                                                                  as physical_table_name,
    pt.database_name,
    pt.connection_type,
    pt.database_host,
    pt.is_in_dwh,
    dbt.table_name                                                                 as database_table_name,
    dbt.table_schema                                                               as database_table_schema,
    dwh.type                                                                       as database_table_type,
    dwh.view_table_schema,
    dwh.view_table_name
from
    dc.tableau_datasource ds
    left join dc.tableau_workbook_datasource wbds
              on ds.id::text = wbds.datasource_id::text
    left join dc.tableau_workbook wb
              on wbds.workbook_id = wb.id
    left join dc.tableau_datasource_phys_table ds_pt
              on ds_pt.datasource_id = ds.id
    left join dc.tableau_physical_table pt
              on pt.id = ds_pt.physical_table_id
    left join dc.tableau_database_table dbt
              on dbt.physical_table_id::text = pt.id::text
    left join dwh_tables dwh
              on dwh.is_in_dwh = pt.is_in_dwh
                  and dwh.schemaname = dbt.table_schema::text
                  and dwh.tablename = dbt.table_name::text;

alter table v_tableau_datasources
    owner to postgres;

grant select on v_tableau_datasources to readonly;

