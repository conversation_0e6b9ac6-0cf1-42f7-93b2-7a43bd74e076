create or replace procedure dc.drop_old_table_catopus(_date_drop date)
    language plpgsql
as
$$

            declare _exec_text text;
            declare _row_start int;
            declare _row_finish int;

begin

            create temp table temp_drop as
            select row_number() over (order by table_name) as row_num, 'drop table ' || table_schema || '."' || table_name || '";' as scr_drop_table,
                   substring(replace(replace(table_name, '_', ''), '-', '') from (length(replace(replace(table_name, '_', ''), '-', '')) - 13) for 8)::date as create_date,
                   table_name
            from information_schema.tables
            where table_schema = 'catopus' and substring(replace(replace(table_name, '_', ''), '-', '') from (length(replace(replace(table_name, '_', ''), '-', '')) - 13) for 8)::date < _date_drop;


            _row_start := (select min(row_num) from temp_drop);
            _row_finish := (select max(row_num) from temp_drop);

            while _row_start <= _row_finish loop

                _exec_text := (select scr_drop_table from temp_drop where row_num = _row_start);

                begin

                    execute _exec_text;

                    insert into dwh_system.droped_tables(system_name, table_name, create_date, drop_date)
                    select 'catopus_schema', table_name, create_date, current_date from temp_drop where row_num = _row_start;

                end;



                _row_start := _row_start + 1;

            end loop;


            drop table temp_drop;


end;
$$;

alter procedure dc.drop_old_table_catopus(date) owner to postgres;
