create or replace procedure dc.insert_view_table_usage_snapshot()
    language plpgsql
as $$
begin
    truncate table dc.view_table_usage_snapshot;

    insert into dc.view_table_usage_snapshot(view_catalog, view_schema, view_name,
                                             table_catalog, table_schema, table_name)
    select view_catalog,
           view_schema,
           view_name,
           table_catalog,
           table_schema,
           table_name
    from information_schema.view_table_usage;

end;

$$
