create or replace function dc.send_notification_in_slack_analytics(_alert_message character varying) returns void
    language plpython3u
as
$$
from urllib import parse, request

msg = _alert_message
chat_minzdrav_url = "https://slack.com/api/chat.postMessage?"\
                    "token=*********************************************************&"\
                    "channel=analytics_alert&text={message}"
message_url = chat_minzdrav_url.format(message=parse.quote(msg))
request.urlopen(message_url)
$$;

alter function dc.send_notification_in_slack_analytics(varchar) owner to rlu;
