create function dc.check_action_to_contact_model(_check_datediff integer) returns void
	language plpgsql
as $$
declare _check_data_0 text;
declare _check_data_1 text;
declare _check_data_2 text;
declare _check_data_3 text;
declare _check_data_3_2 text;
declare _check_data_4 text;
declare _check_data_5 text;
declare _check_data_6 text;
declare _check_data_7 text;

begin
            _check_data_0 := (select '*Action to Contact Model за ' || public.fn_get_timestamp_from_date_diff(_check_datediff)::date || '* _(vs ' || public.fn_get_timestamp_from_date_diff(_check_datediff - 7)::date || ')_');

            create temp table temp_check_1 as
            select 1 as step_num,
                   'Total Action to contact' as type_action,
                   'action_to_contact_structure' as table_name,
                   public.fn_get_timestamp_from_date_diff(_check_datediff)::date as check_date,
                   coalesce(sum(action_to_contact_prob), 0) atc_value_now,
                   coalesce((select sum(action_to_contact_prob) atc_value_now
                             from profile.action_to_contact_structure
                             where action_datediff = _check_datediff - 7
                             group by action_datediff), 0) as atc_value_7_day
            from profile.action_to_contact_structure
            where action_datediff = _check_datediff
            union all
            select 2 as step_num,
                   'Job Seeker AtC' as type_action,
                   'job_seeker_action_to_contact' table_name,
                   public.fn_get_timestamp_from_date_diff(_check_datediff)::date as check_date,
                   coalesce((select sum(action_to_contact_prob)
                    from (select case when sum(action_to_contact_prob) > 1 then 1 else sum(action_to_contact_prob) end as action_to_contact_prob
                          from profile.job_seeker_action_to_contact
                          where action_datediff = _check_datediff
                          group by country_id,
                                   profile_id,
                                   action_datediff,
                                   action_datetime) as d), 0) atc_value_now,
                   coalesce((select sum(action_to_contact_prob)
                    from (select case when sum(action_to_contact_prob) > 1 then 1 else sum(action_to_contact_prob) end as action_to_contact_prob
                          from profile.job_seeker_action_to_contact
                          where action_datediff = _check_datediff - 7
                          group by country_id,
                                   profile_id,
                                   action_datediff,
                                   action_datetime) as d), 0) as atc_value_7_day
            union all
            select 3 as step_num,
                   'Employer AtC' as type_action,
                   'employer_action_to_contact' as table_name,
                   public.fn_get_timestamp_from_date_diff(_check_datediff)::date as check_date,
                   coalesce(sum(action_to_contact_prob), 0) atc_value_now,
                   coalesce((select sum(action_to_contact_prob) atc_prob_sum
                             from employer.employer_action_to_contact
                             where action_datediff = _check_datediff - 7
                             group by action_datediff), 0) as atc_value_7_day
            from employer.employer_action_to_contact
            where action_datediff = _check_datediff;

            _check_data_1 := (select string_agg(check_text, E'\n\n')
                              from (select case when step_num = 1 then (select case when round((atc_value_now - atc_value_7_day) / atc_value_7_day * 100, 0) > 9 then ':large_green_circle: '
                                                                                    when round((atc_value_now - atc_value_7_day) / atc_value_7_day * 100, 0) < -9 then ':red_circle: '
                                                                                    else ':large_blue_circle: ' end || '*Total Action to contact* _(' || table_name || ')_ ' ||
                                                                               round(atc_value_now, 0) || ' *(' || round((atc_value_now - atc_value_7_day) / atc_value_7_day * 100, 0) || '%)*.')
                                                when step_num in (2, 3) then (select '        ' || type_action || ' _(' || table_name || ')_: ' || round(atc_value_now, 0) || ' *(' ||
                                                                              round((atc_value_now - atc_value_7_day) / atc_value_7_day * 100, 0) || '%)*.')
                                            end as check_text
                                    from temp_check_1) as d);

            --perform * from dimension.send_notification_in_slack(_check_data_1);



            create temp table temp_check_3 as
            select 1 as step_num,
                   1 as substep_num,
                   'Apply AtC' as type_action,
                   null as table_name,
                   public.fn_get_timestamp_from_date_diff(_check_datediff)::date as check_date,
                   'значення `action_to_contact_prob` = ' as checking,
                   coalesce(sum(action_to_contact_prob), 0) atc_value_now,
                   coalesce((select sum(action_to_contact_prob) atc_prob_sum
                             from profile.action_to_contact_structure
                             where action_datediff = _check_datediff - 7 and platform_user_type_id = 1 and action_type_id = 2
                             group by action_datediff), 0) as atc_value_7_day
            from profile.action_to_contact_structure
            where action_datediff = _check_datediff and platform_user_type_id = 1 and action_type_id = 2/*apply*/
            union all
            select 1 as step_num,
                   2 as substep_num,
                   'Apply' as type_action,
                   'profile_apply' as table_name,
                   public.fn_get_timestamp_from_date_diff(_check_datediff)::date as check_date,
                   'кількість рядків = ' as checking,
                   count(*) as atc_value_now,
                   (select count(*)
                    from profile.profile_apply
                    where apply_datediff = _check_datediff - 7) as atc_value_7_day
            from profile.profile_apply
            where apply_datediff = _check_datediff
            union all
            select 1 as step_num,
                   3 as substep_num,
                   'Apply with Profile Submission' as type_action,
                   'job_seeker_apply_without_profile' table_name,
                   public.fn_get_timestamp_from_date_diff(_check_datediff)::date as check_date,
                   'кількість рядків = ' as checking,
                   count(*) as atc_value_now,
                   (select count(*)
                    from profile.job_seeker_apply_without_profile
                    where country_id =1 and apply_datediff = _check_datediff - 7) as atc_value_7_day
            from profile.job_seeker_apply_without_profile
            where country_id = 1 and apply_datediff = _check_datediff
            union all
            select 1 as step_num,
                   4 as substep_num,
                   'Apply Click' as type_action,
                   'profile_jdp_respond' table_name,
                   public.fn_get_timestamp_from_date_diff(_check_datediff)::date as check_date,
                   'кількість рядків = ' as checking,
                   count(*) as atc_value_now,
                   (select count(*)
                    from profile.profile_jdp_respond
                    where jdp_viewed_datediff = _check_datediff - 7 and (jdp_flag & 1) = 1 and is_apply_click = 1) as atc_value_7_day
            from profile.profile_jdp_respond
            where jdp_viewed_datediff = _check_datediff and (jdp_flag & 1) = 1 and is_apply_click = 1
            union all
            select 2 as step_num,
                   1 as substep_num,
                   'Call AtC' as type_action,
                   null as table_name,
                   public.fn_get_timestamp_from_date_diff(_check_datediff)::date as check_date,
                   'значення `action_to_contact_prob` = ' as checking,
                   coalesce(sum(action_to_contact_prob), 0) atc_value_now,
                   coalesce((select sum(action_to_contact_prob) atc_prob_sum
                             from profile.action_to_contact_structure
                             where action_datediff = _check_datediff - 7 and platform_user_type_id = 1 and action_type_id = 1
                             group by action_datediff), 0) as atc_value_7_day
            from profile.action_to_contact_structure
            where action_datediff = _check_datediff and platform_user_type_id = 1 and action_type_id = 1/*call*/
            union all
            select 2 as step_num,
                   2 as substep_num,
                   'DTE Call Click for authorized job seekers' as type_action,
                   'profile_jdp_respond' table_name,
                   public.fn_get_timestamp_from_date_diff(_check_datediff)::date as check_date,
                   'кількість рядків = ' as checking,
                   count(*) as atc_value_now,
                   (select count(*)
                    from profile.profile_jdp_respond
                    where jdp_viewed_datediff = _check_datediff - 7 and (jdp_flag & 1) = 1 and is_call_click = 1) as atc_value_7_day
            from profile.profile_jdp_respond
            where jdp_viewed_datediff = _check_datediff and (jdp_flag & 1) = 1 and is_call_click = 1
            union all
            select 2 as step_num,
                   3 as substep_num,
                   'DTE Jdp Views for authorized job seekers' as type_action,
                   'profile_jdp' table_name,
                   public.fn_get_timestamp_from_date_diff(_check_datediff)::date as check_date,
                   'кількість рядків = ' as checking,
                   count(*) as atc_value_now,
                   (select count(*)
                    from profile.profile_jdp
                    where jdp_viewed_datediff = _check_datediff - 7 and (jdp_flag & 1) = 1) as atc_value_7_day
            from profile.profile_jdp
            where jdp_viewed_datediff = _check_datediff and (jdp_flag & 1) = 1
            union all
            select 2 as step_num,
                   4 as substep_num,
                   'DTE Jdp Views' as type_action,
                   'session_jdp' table_name,
                   public.fn_get_timestamp_from_date_diff(_check_datediff)::date as check_date,
                   'кількість рядків = ' as checking,
                   count(id) as atc_value_now,
                   (select count(id)
                    from imp.session_jdp
                    where country = 1 and date_diff = _check_datediff - 7 and job_id_project = -1) as atc_value_7_day
            from imp.session_jdp
            where country = 1 and date_diff = _check_datediff and job_id_project = -1
            union all
            select 2 as step_num,
                   5 as substep_num,
                   'Session' as type_action,
                   'session' table_name,
                   public.fn_get_timestamp_from_date_diff(_check_datediff)::date as check_date,
                   'кількість рядків = ' as checking,
                   count(id) as atc_value_now,
                   (select count(id)
                    from imp.session
                    where country = 1 and date_diff = _check_datediff - 7 and is_bot = 0) as atc_value_7_day
            from imp.session
            where country = 1 and date_diff = _check_datediff and is_bot = 0
            union all
            select 3 as step_num,
                   1 as substep_num,
                   'Message AtC' as type_action,
                   null as table_name,
                   public.fn_get_timestamp_from_date_diff(_check_datediff)::date as check_date,
                   'значення `action_to_contact_prob` = ' as checking,
                   coalesce(sum(action_to_contact_prob), 0) atc_value_now,
                   coalesce((select sum(action_to_contact_prob) atc_prob_sum
                             from profile.action_to_contact_structure
                             where action_datediff = _check_datediff - 7 and platform_user_type_id = 1 and feature_id in (2, 3, 4)
                             group by action_datediff), 0) as atc_value_7_day
            from profile.action_to_contact_structure
            where action_datediff = _check_datediff and platform_user_type_id = 1 and feature_id in (2, 3, 4)
            union all
            select 4 as step_num,
                   1 as substep_num,
                   'Apply Answer AtC' as type_action,
                   null as table_name,
                   public.fn_get_timestamp_from_date_diff(_check_datediff)::date as check_date,
                   'значення `action_to_contact_prob` = ' as checking,
                   coalesce(sum(action_to_contact_prob), 0) atc_value_now,
                   coalesce((select sum(action_to_contact_prob) atc_prob_sum
                             from profile.action_to_contact_structure
                             where action_datediff = _check_datediff - 7 and platform_user_type_id = 2 and feature_id = 1
                             group by action_datediff), 0) as atc_value_7_day
            from profile.action_to_contact_structure
            where action_datediff = _check_datediff and platform_user_type_id = 2 and feature_id = 1
            union all
            select 4 as step_num,
                   2 as substep_num,
                   'Apply Intention to contact' as type_action,
                   'employer_intention_to_contact' as table_name,
                   public.fn_get_timestamp_from_date_diff(_check_datediff)::date as check_date,
                   'кількість рядків = ' as checking,
                   count(*) atc_value_now,
                   coalesce((select count(*) atc_prob_sum
                             from employer.employer_intention_to_contact
                             where action_datediff = _check_datediff - 7 and feature_id = 1
                             group by action_datediff), 0) as atc_value_7_day
            from employer.employer_intention_to_contact
            where action_datediff = _check_datediff and feature_id = 1
            union all
            select 4 as step_num,
                   3 as substep_num,
                   'Apply Seen' as type_action,
                   'job_apply' as table_name,
                   public.fn_get_timestamp_from_date_diff(_check_datediff)::date as check_date,
                   'кількість рядків = ' as checking,
                   count(*) atc_value_now,
                   coalesce((select count(*) atc_prob_sum
                             from imp_employer.job_apply ja
                             left join imp.session_apply sa on ja.dd_session_apply = sa.date_diff and ja.id_session_apply = sa.id
                             where ja.sources = 1 and sa.country = 1 and cast(ja.date_seen as date) = public.fn_get_timestamp_from_date_diff(_check_datediff - 7) and ja.flags & 1024 = 0
                             group by cast(ja.date_seen as date)), 0) as atc_value_7_day
            from imp_employer.job_apply ja
            left join imp.session_apply sa on ja.dd_session_apply = sa.date_diff and ja.id_session_apply = sa.id
            where ja.sources = 1 and sa.country = 1 and cast(ja.date_seen as date) = public.fn_get_timestamp_from_date_diff(_check_datediff) and ja.flags & 1024 = 0
            union all
            select 4 as step_num,
                   4 as substep_num,
                   'Apply Received' as type_action,
                   'job_apply' as table_name,
                   public.fn_get_timestamp_from_date_diff(_check_datediff)::date as check_date,
                   'кількість рядків = ' as checking,
                   count(*) atc_value_now,
                   coalesce((select count(*) atc_prob_sum
                             from imp_employer.job_apply ja
                             left join imp.session_apply sa on ja.dd_session_apply = sa.date_diff and ja.id_session_apply = sa.id
                             where ja.sources = 1 and sa.country = 1 and cast(ja.date as date) = public.fn_get_timestamp_from_date_diff(_check_datediff - 7)
                             group by cast(ja.date as date)), 0) as atc_value_7_day
            from imp_employer.job_apply ja
            left join imp.session_apply sa on ja.dd_session_apply = sa.date_diff and ja.id_session_apply = sa.id
            where ja.sources = 1 and sa.country = 1 and cast(ja.date as date) = public.fn_get_timestamp_from_date_diff(_check_datediff)
            union all
            select 5 as step_num,
                   1 as substep_num,
                   'Recommendation AtC' as type_action,
                   null as table_name,
                   public.fn_get_timestamp_from_date_diff(_check_datediff)::date as check_date,
                   'значення `action_to_contact_prob` = ' as checking,
                   coalesce(sum(action_to_contact_prob), 0) atc_value_now,
                   coalesce((select sum(action_to_contact_prob) atc_prob_sum
                             from profile.action_to_contact_structure
                             where action_datediff = _check_datediff - 7 and platform_user_type_id = 2 and feature_id = 2
                             group by action_datediff), 0) as atc_value_7_day
            from profile.action_to_contact_structure
            where action_datediff = _check_datediff and platform_user_type_id = 2 and feature_id = 2/*employer recommended profile*/
            union all
            select 5 as step_num,
                   2 as substep_num,
                   'Recommended Apply Intention to contact' as type_action,
                   'employer_intention_to_contact' as table_name,
                   public.fn_get_timestamp_from_date_diff(_check_datediff)::date as check_date,
                   'кількість рядків = ' as checking,
                   count(*) atc_value_now,
                   coalesce((select count(*) atc_prob_sum
                             from employer.employer_intention_to_contact
                             where action_datediff = _check_datediff - 7 and feature_id = 2
                             group by action_datediff), 0) as atc_value_7_day
            from employer.employer_intention_to_contact
            where action_datediff = _check_datediff and feature_id = 2
            union all
            select 5 as step_num,
                   3 as substep_num,
                   'Recommended Apply Seen' as type_action,
                   'job_apply' as table_name,
                   public.fn_get_timestamp_from_date_diff(_check_datediff)::date as check_date,
                   'кількість рядків = ' as checking,
                   count(distinct ja.id) atc_value_now,
                   coalesce((select count(distinct ja.id) atc_prob_sum
                             from imp_employer.job_apply ja
                             left join imp_employer.job j on ja.id_job = j.id
                             left join imp_employer.employer e on j.id_employer = e.id
                             where ja.sources = 1 and cast(ja.date_seen as date) = public.fn_get_timestamp_from_date_diff(_check_datediff - 7) and ja.flags & 1024 = 1024 and e.country_code = 'ua'
                             group by cast(ja.date_seen as date)), 0) as atc_value_7_day
            from imp_employer.job_apply ja
            left join imp_employer.job j on ja.id_job = j.id
            left join imp_employer.employer e on j.id_employer = e.id
            where ja.sources = 1 and cast(ja.date_seen as date) = public.fn_get_timestamp_from_date_diff(_check_datediff) and ja.flags & 1024 = 1024 and e.country_code = 'ua'
            union all
            select 6 as step_num,
                   1 as substep_num,
                   'Profile Base Action AtC' as type_action,
                   null as table_name,
                   public.fn_get_timestamp_from_date_diff(_check_datediff)::date as check_date,
                   'значення `action_to_contact_prob` = ' as checking,
                   coalesce(sum(action_to_contact_prob), 0) atc_value_now,
                   coalesce((select sum(action_to_contact_prob) atc_prob_sum
                             from profile.action_to_contact_structure
                             where action_datediff = _check_datediff - 7 and platform_user_type_id = 2 and feature_id = 3
                             group by action_datediff), 0) as atc_value_7_day
            from profile.action_to_contact_structure
            where action_datediff = _check_datediff and platform_user_type_id = 2 and feature_id = 3/*employer profile base*/
            union all
            select 6 as step_num,
                   2 as substep_num,
                   'Profile Intention to contact' as type_action,
                   'employer_intention_to_contact' as table_name,
                   public.fn_get_timestamp_from_date_diff(_check_datediff)::date as check_date,
                   'кількість рядків = ' as checking,
                   count(*) atc_value_now,
                   coalesce((select count(*) atc_prob_sum
                             from employer.employer_intention_to_contact
                             where action_datediff = _check_datediff - 7 and feature_id = 3
                             group by action_datediff), 0) as atc_value_7_day
            from employer.employer_intention_to_contact
            where action_datediff = _check_datediff and feature_id = 3
            union all
            select 6 as step_num,
                   3 as substep_num,
                   'Profile Seen' as type_action,
                   'profile_search_action' as table_name,
                   public.fn_get_timestamp_from_date_diff(_check_datediff)::date as check_date,
                   'кількість рядків = ' as checking,
                   count(*) atc_value_now,
                   coalesce((select count(*) atc_prob_sum
                             from imp_employer.profile_search_action
                             where sources = 1 and date_diff = _check_datediff - 7 and action = 0
                             group by date_diff), 0) as atc_value_7_day
            from imp_employer.profile_search_action
            where sources = 1 and date_diff = _check_datediff and action = 0
            union all
            select 6 as step_num,
                   4 as substep_num,
                   'Profile Search' as type_action,
                   'profile_search' as table_name,
                   public.fn_get_timestamp_from_date_diff(_check_datediff)::date as check_date,
                   'кількість рядків = ' as checking,
                   count(*) atc_value_now,
                   coalesce((select count(*) atc_prob_sum
                             from imp_employer.profile_search
                             where sources = 1 and date_diff = _check_datediff - 7
                             group by date_diff), 0) as atc_value_7_day
            from imp_employer.profile_search
            where sources = 1 and date_diff = _check_datediff;


            _check_data_3 := (select string_agg(check_text, E'\n\n')
                              from (select case when step_num = 1 and substep_num = 1 then ':one: '
                                                when step_num = 2 and substep_num = 1 then ':two: '
                                                when step_num = 3 and substep_num = 1 then ':three: '
                                                when step_num = 4 and substep_num = 1 then ':four: '
                                                when step_num = 5 and substep_num = 1 then ':five: '
                                                when step_num = 6 and substep_num = 1 then ':six: '
                                                when substep_num = 2 or (step_num = 1 and substep_num = 3) then '      :black_small_square:'
                                                when substep_num = 3 or (step_num = 2 and substep_num = 4) then '      :black_small_square::black_small_square:'
                                                when substep_num = 4 or (step_num = 2 and substep_num = 5) then '      :black_small_square::black_small_square::black_small_square:'
                                                end ||
                                           case when round((atc_value_now::numeric - atc_value_7_day::numeric) / atc_value_7_day::numeric * 100, 0) > 9 then ' :large_green_square: *'
                                                when round((atc_value_now::numeric - atc_value_7_day::numeric) / atc_value_7_day::numeric * 100, 0) < -9 then ' :large_red_square: *'
                                                else ' :large_blue_square: *' end ||
                                           case when (round((atc_value_now::numeric - atc_value_7_day::numeric) / atc_value_7_day::numeric * 100, 0)) > 0 then '+' else '' end ||
                                           round((atc_value_now::numeric - atc_value_7_day::numeric) / atc_value_7_day::numeric * 100, 0) || '%*.     *' ||
                                           type_action || '*' ||
                                           case when table_name is not null then ' _(' || table_name || ')_ ' else ' ' end ||
                                           round(atc_value_now, 0) as check_text
                                    from temp_check_3
                                    order by step_num, substep_num) as d);


            --perform * from dimension.send_notification_in_slack(_check_data_3);


            create temp table temp_check_4 as
            select 1 as step_num,
                   'Job Search Cycles' as type_action,
                   'job_seeker_cycle_start' table_name,
                   public.fn_get_timestamp_from_date_diff(_check_datediff)::date as check_date,
                   count(*) as atc_value_now,
                   (select count(*) as atc_value_now
                    from profile.job_seeker_cycle_start
                    where seeker_cycle_first_datediff = _check_datediff - 7) as atc_value_7_day
            from profile.job_seeker_cycle_start
            where seeker_cycle_first_datediff = _check_datediff;

            _check_data_4 := (select string_agg(check_text, E'\n\n')
                              from (select case when step_num = 1 then (select case when round((atc_value_now - atc_value_7_day) / atc_value_7_day * 100, 0) > 9 then ':large_green_circle: '
                                                                                    when round((atc_value_now - atc_value_7_day) / atc_value_7_day * 100, 0) < -9 then ':red_circle: '
                                                                                    else ':large_blue_circle: ' end || '*' || type_action || '* _(' || table_name || ')_ ' ||
                                                                               round(atc_value_now, 0) || ' *(' || round((atc_value_now - atc_value_7_day) / atc_value_7_day * 100, 0) || '%)*.')
                                                when step_num in (2, 3) then (select '        ' || type_action || ' _(' || table_name || ')_: ' || round(atc_value_now, 0) || ' *(' ||
                                            round((atc_value_now - atc_value_7_day) / atc_value_7_day * 100, 0) || '%)*.')
                                        end as check_text
                                from temp_check_4) as d);

            --perform * from dimension.send_notification_in_slack(' ');
            --perform * from dimension.send_notification_in_slack(_check_data_4);


            create temp table temp_check_5 as
            select 1 as step_num,
                   1 as substep_num,
                   'Profile Submission Start Cycle' as type_action,
                   'job_seeker_cycle_start' table_name,
                   public.fn_get_timestamp_from_date_diff(_check_datediff)::date as check_date,
                   'кількість профілів = ' as checking,
                   count(profile_id) as atc_value_now,
                   (select count(profile_id)
                    from profile.job_seeker_cycle_start
                    where seeker_cycle_first_datediff = _check_datediff - 7 and seeker_cycle_start_type_id = 1) as atc_value_7_day
            from profile.job_seeker_cycle_start
            where seeker_cycle_first_datediff = _check_datediff and seeker_cycle_start_type_id = 1
            union all
            select 1 as step_num,
                   2 as substep_num,
                   'Profile Submitted' as type_action,
                   'profile_submitted' table_name,
                   public.fn_get_timestamp_from_date_diff(_check_datediff)::date as check_date,
                   'кількість профілів = ' as checking,
                   count(id) as atc_value_now,
                   (select count(id)
                    from profile.profile_submitted
                    where submission_datediff = _check_datediff - 7) as atc_value_7_day
            from profile.profile_submitted
            where submission_datediff = _check_datediff
            union all
            select 2 as step_num,
                   1 as substep_num,
                   'Job Seeker AtC Start Cycle' as type_action,
                   'job_seeker_cycle_start' table_name,
                   public.fn_get_timestamp_from_date_diff(_check_datediff)::date as check_date,
                   'кількість профілів = ' as checking,
                   count(profile_id) as atc_value_now,
                   (select count(profile_id)
                    from profile.job_seeker_cycle_start
                    where seeker_cycle_first_datediff = _check_datediff - 7 and seeker_cycle_start_type_id = 2) as atc_value_7_day
            from profile.job_seeker_cycle_start
            where seeker_cycle_first_datediff = _check_datediff and seeker_cycle_start_type_id = 2
            union all
            select 3 as step_num,
                   1 as substep_num,
                   'Employer AtC Start Cycle' as type_action,
                   'job_seeker_cycle_start' table_name,
                   public.fn_get_timestamp_from_date_diff(_check_datediff)::date as check_date,
                   'кількість профілів = ' as checking,
                   count(profile_id) as atc_value_now,
                   (select count(profile_id)
                    from profile.job_seeker_cycle_start
                    where seeker_cycle_first_datediff = _check_datediff - 7 and seeker_cycle_start_type_id = 3) as atc_value_7_day
            from profile.job_seeker_cycle_start
            where seeker_cycle_first_datediff = _check_datediff and seeker_cycle_start_type_id = 3;


            _check_data_5 := (select string_agg(check_text, E'\n\n')
                              from (select case when step_num = 1 and substep_num = 1 then ':one: '
                                                when step_num = 2 and substep_num = 1 then ':two: '
                                                when step_num = 3 and substep_num = 1 then ':three: '
                                                when step_num = 4 and substep_num = 1 then ':four: '
                                                when step_num = 5 and substep_num = 1 then ':five: '
                                                when step_num = 6 and substep_num = 1 then ':six: '
                                                when substep_num = 2 then '      :black_small_square:'
                                                when substep_num = 3 then '      :black_small_square::black_small_square:'
                                                when substep_num = 4 then '      :black_small_square::black_small_square::black_small_square:'
                                                end ||
                                           case when round((atc_value_now::numeric - atc_value_7_day::numeric) / atc_value_7_day::numeric * 100, 0) > 9 then ' :large_green_square: *'
                                                when round((atc_value_now::numeric - atc_value_7_day::numeric) / atc_value_7_day::numeric * 100, 0) < -9 then ' :large_red_square: *'
                                                else ' :large_blue_square: *' end ||
                                           case when (round((atc_value_now::numeric - atc_value_7_day::numeric) / atc_value_7_day::numeric * 100, 0)) > 0 then '+' else '' end ||
                                           round((atc_value_now::numeric - atc_value_7_day::numeric) / atc_value_7_day::numeric * 100, 0) || '%*.     *' ||
                                           type_action || '*' ||
                                           case when table_name is not null then ' _(' || table_name || ')_ ' else ' ' end ||
                                           round(atc_value_now, 0) as check_text
                                    from temp_check_5
                                    order by step_num, substep_num) as d);


            perform * from dimension.send_notification_in_slack(_check_data_0);
            perform * from dimension.send_notification_in_slack(_check_data_1);
            --perform * from dimension.send_notification_in_slack(_check_data_2);
            perform * from dimension.send_notification_in_slack(_check_data_3);
            perform * from dimension.send_notification_in_slack(' ');
            perform * from dimension.send_notification_in_slack(_check_data_4);
            perform * from dimension.send_notification_in_slack(_check_data_5);


            drop table temp_check_1;
            --drop table temp_check_2;
            drop table temp_check_3;
            --drop table temp_check_3_2;
            drop table temp_check_4;
            drop table temp_check_5;
            --drop table temp_check_6;
            --drop table temp_check_7;
            --drop table temp_check_8;

end;
$$;

alter function dc.check_action_to_contact_model(integer) owner to rlu;
