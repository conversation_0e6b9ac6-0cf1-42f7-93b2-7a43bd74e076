create temp table api_data_tmp (country_code, token_type, token_key) as
    /* 0-web, 1-email*/
values ('de', 0, 'c9a80687-1c3a-4cc5-b4a9-df77899f39bf'),
       ('de', 1, '725cc4ac-07e7-41ed-a031-eb18263f16a7'),
       ('us', 0, 'eadf3932-7dce-4c73-98f0-a6ce5f608037'),
       ('us', 1, '9d60baf0-bdc2-4d8b-92ea-73f88c4ba846'),
       ('uk', 0, 'c29764aa-dad4-4322-b7fb-287586b4839f'),
       ('uk', 1, 'a0fff532-4d4a-4846-a80c-776dff63172a'),
       ('fr', 0, 'e2d7acd8-369f-4038-b73e-31122226ae3d'),
       ('fr', 1, 'd6ace596-f728-4f08-813d-ae9ceeeb0292'),
       ('ca', 0, 'a8371a19-b774-43f4-bc82-cd3d9e788edc'),
       ('ca', 1, 'eeaa1076-0d9c-4351-9873-670140cff694'),
       ('at', 0, '6b06ca38-d1d9-434b-ae8b-18dbc810d363'),
       ('at', 1, '9c9f7043-6719-4a17-8eb5-fa2ebd39f80c'),
       ('be', 0, '5e562144-c273-4f6e-a293-74d89d695f85'),
       ('be', 1, '1f28953c-1307-4012-8cb1-0ca3b2847009'),
       ('ch', 0, '0db8f8b0-57f5-41de-b720-412e2140705e'),
       ('ch', 1, 'e6432c61-5d5c-44d8-848a-b75114d67a9c'),
       ('nl', 0, '401fc757-6f7d-4127-bc1b-12517a41cda9'),
       ('nl', 1, 'fadf86ad-2133-4471-a673-c591f2acf131'),
       ('pl', 0, '26aca8dc-0625-406c-adb1-6c604b2fc02a'),
       ('pl', 1, 'a8bacaf1-82ea-4b62-9ac2-011fa7a82b6c'),
       ('it', 0, 'f15ef71a-28f0-47fe-a2a4-13f732bc2386'),
       ('it', 1, '532ece1c-1f7d-41f1-9d96-1b815084b5b7'),
       ('cz', 0, '01c29ae2-f562-4b28-bc56-1997736b5354'),
       ('cz', 1, '3563143f-657b-4b66-b061-98382069bcdd'),
       ('es', 0, 'd24aa90e-1b95-42dd-90bd-104fc7afb25e'),
       ('es', 1, 'f4cc0b73-4de5-4ca3-a4a6-2f6a7c8f4c5e'),
       ('se', 0, '3a563711-e57a-4d82-be0a-e94fe5ece12d'),
       ('se', 1, 'f1861a49-b13a-42b4-a456-abaf0cbca31a'),
       ('cl', 0, '6a885a8e-2aff-450c-a9e7-6784835d489f'),
       ('cl', 1, 'bd6b97fe-3f80-4de3-979f-49456900c2ae'),
       ('in', 0, 'cd124a56-e468-42dc-97d5-462f21b313c1'),
       ('in', 1, 'cd124a56-e468-42dc-97d5-462f21b313c1'),
       ('br', 0, '16184484-8f3d-43c3-a7be-ce1d1c65a4dc'),
       ('br', 1, 'c92729fe-09ee-46d3-946e-dceed375dda5'),
       ('mx', 0, 'e839b8db-031d-411b-bafe-e9cccd16cdc2'),
       ('mx', 1, '0bc426f7-a248-4c10-a96d-10d25aac202f'),
       ('id', 0, 'b9cfc22d-9aef-41c6-8472-f753cac3e19c'),
       ('id', 1, 'c0f29e02-b89d-48f1-9be1-17b755622413'),
       ('ae', 0, '644a816a-6c8f-4bb5-9bbe-e99b51d0570e'),
       ('ae', 1, '15066d10-17cd-490e-bb64-c460c6d6ef85'),
       ('ie', 0, '3965f6d6-5a57-4ccf-86e9-3595f86f4380'),
       ('ie', 1, '1a7991d7-7118-4c5b-aa88-62c558f90574'),
       ('dk', 0, 'c81d000d-9663-4af1-ab05-42178de7132f'),
       ('dk', 1, '4288f5f0-60e8-4df7-ba6e-648fc78c0865'),
       ('co', 0, 'eec51fe3-3dd2-4df6-90ec-a0febab622f7'),
       ('co', 1, '04256022-89bc-43d1-b48c-295e260c265d'),
       ('gr', 0, '76821c45-ff85-4e88-9934-65fb544e4634'),
       ('gr', 1, '9a6b5b68-939f-4eb6-8253-93e0730f69d8'),
       ('sk', 0, 'b84bfdd7-1b66-47d9-8a9c-66d585e57ada'),
       ('sk', 1, 'c6523310-743f-4be8-9d5a-400dc3e9c183'),
       ('sa', 0, 'fece709d-e99a-49a5-a531-4cb61b471569'),
       ('sa', 1, '69b90dae-6b9b-4771-a5fc-b0da42fe1a4c'),
       ('kz', 0, '076c0334-9afc-4703-9dd1-599b21bbe25e'),
       ('kz', 1, '9ea520c9-d883-4084-b84d-8c10230da401'),
       ('hu', 0, 'd3753c8e-439f-44f7-80e6-84ec60edfd34'),
       ('hu', 1, 'a4f3654d-8258-4e2a-bacd-b9c5189f14a9'),
       ('za', 0, 'f5cf5452-bd72-49fe-9787-01850968241f'),
       ('za', 1, 'a01f3f6e-c298-4f61-a58e-670898488934'),
       ('pt', 0, 'f50a633e-9e60-4865-a8d1-38dc195911ce'),
       ('pt', 1, 'e78464ee-be12-45fa-88dc-6986d6eb365f'),
       ('ar', 0, 'b915458b-9c49-4c16-86ff-81a622b91dc6'),
       ('ar', 1, 'f7c2fad3-f605-4e42-81a1-6b4e00351ccc'),
       ('pe', 0, '0f2e788b-9134-4cf0-bc09-bb83c250138d'),
       ('pe', 1, '32029a0f-2a8f-4e84-a398-2cdaa2e0d632'),
       ('no', 0, '3459d896-c8fa-4b29-a2bb-f2b789a943bd'),
       ('no', 1, 'eae71d7e-4667-45f8-a87a-309acf5c643e'),
       ('hk', 0, '17a9923a-f8dc-4256-acce-d6ebea57f20b'),
       ('hk', 1, 'ab103b59-5c3c-4d7d-b0cb-d8ea811167dc'),
       ('rs', 0, '7de9245c-9c5e-497b-8d43-7bceea74c75d'),
       ('rs', 1, 'c4fcb57e-b1f7-4525-ba41-be3db61e8754'),
       ('sg', 0, '55c915ee-5fba-4463-acc7-82959150353c'),
       ('sg', 1, '07dff462-42d7-434f-a62c-21f59f53790b'),
       ('fi', 0, '72ba7648-cb80-48d0-9452-cdbd3763cddf'),
       ('fi', 1, '95c41f0e-1381-4af1-af5c-53a081f6c43c'),
       ('th', 0, 'ae3d7f94-dbbb-46fb-a5d4-0d5407052be4'),
       ('th', 1, 'd2b24d15-91b3-476a-9e12-6ac706666cb5'),
       ('hr', 0, '3ce79b68-c86a-4d7e-bb6f-fbc13e02ca94'),
       ('hr', 1, '35087791-241f-4376-82b0-9aa32ad2bbcc'),
       ('bg', 0, '2b4d9f34-766e-431d-ac25-3f452ca20da4'),
       ('bg', 1, '6858b54f-ffa9-40eb-ad7c-a96ca855503'),
       ('ua', 0, '6b8eca59-c342-478e-bbee-e391298e34a2'),
       ('ua', 1, '57627562-d477-44e6-beaf-311ef17b268e'),
       ('qa', 0, '076b46b9-b43a-4978-a57a-69f1be25c8fd'),
       ('qa', 1, 'ec712deb-3679-44f2-a352-e17bb7593560'),
       ('tr', 0, '76701682-c767-45c7-a0cb-7ddcd364d855'),
       ('tr', 1, '56c17e25-cebf-4b72-9847-f155b3ffef90'),
       ('nz', 0, 'cdca2461-dcbc-44ba-9e15-6453ddb70081'),
       ('nz', 1, '91402c4f-af92-47bc-b3d6-d586ae306bd5'),
       ('do', 0, '9a6832e4-d4f4-49e1-a9b2-d2eb2ebcb5b2'),
       ('do', 1, 'debc28e9-b62c-473f-8204-fa03b05b375a'),
       ('ec', 0, 'd16c3a15-48ff-4744-a4a3-d8acb9691cfc'),
       ('ec', 1, '5b0962f3-f4ce-46cf-9984-6192bd9c2b57'),
       ('ph', 0, '1c37bcc8-4265-48b6-9500-740fe34c9155'),
       ('ph', 1, '322148ad-2b54-41c8-b157-01d05fbf3622'),
       ('my', 0, '6dec8e49-0b23-4e2e-a9a9-af1b8583abd0'),
       ('my', 1, '1c5af7da-b75e-4f2f-b1d0-2e18bafe873d'),
       ('uy', 0, '311e899b-0b26-460b-8633-c5f592b9cb85'),
       ('uy', 1, '62dfe7fc-6caf-47ae-a171-0c26bfaca9ac'),
       ('ng', 0, '85b95591-870c-46fd-8910-c6544157ee08'),
       ('ng', 1, '4eef3224-344f-4cde-9c86-93d1326b9208'),
       ('bh', 0, '6f798d82-059a-4bd3-91ca-8fe630b13434'),
       ('bh', 1, '7d3a0d72-1b9d-4601-879b-75be747cb739'),
       ('pr', 0, '317306e7-b257-482c-b1d3-f47cbcf3a256'),
       ('pr', 1, 'a365dd45-6bb3-4863-a846-dd4f1da648e8'),
       ('pk', 0, '76bf3305-bbd0-41ed-85f5-b240b8ac352f'),
       ('pk', 1, '4c80ca97-4875-4861-8aa9-48c088eb5d5a'),
       ('ba', 0, '9d65e857-e978-4664-a94c-5d1fd1b826b1'),
       ('ba', 1, '274bbbf0-0c10-412e-b111-8a9a15970ee4'),
       ('kw', 0, '280f3bab-c0f6-4709-93ef-62f6beeb7d7d'),
       ('kw', 1, '402139de-1b30-4636-b0f6-6deb868c089f'),
       ('jp', 0, '876fa5b5-ef2a-410c-b70f-17b11758a639'),
       ('jp', 1, 'd6963150-b9a5-49f4-88ab-ec88a84d4719'),
       ('cr', 0, '21affd1a-c94d-49e2-aced-a7393170f5c5'),
       ('cr', 1, '4d1a56ec-ccc4-499f-b4a3-34e62d35317f'),
       ('ro', 0, 'aa72ea3f-acb0-4ae2-b681-6a761011c0ad'),
       ('ro', 1, 'ec41c7a2-480a-4050-be1f-4a5c04a607ca'),
       ('uz', 0, 'b2577aff-86fd-4b22-a34d-88f9c0843ac9'),
       ('uz', 1, 'fac41313-052d-41b1-9091-4bc6920b8f39'),
       ('sv', 0, '8b7f87d7-4e99-448e-bbaa-67764a7e37df'),
       ('sv', 1, 'b4b48619-2e60-44a0-bb19-b2c39ed4e4c2'),
       ('kr', 0, '4b1fd0f1-2c2a-43d0-a92f-f5bc77183ef0'),
       ('kr', 1, '2bf7c484-4aac-4d17-9549-116ce9d08cc7'),
       ('ma', 0, 'dbf8288e-c4c0-4d66-b889-5245c8ff0c65'),
       ('ma', 1, 'e5a95c54-c816-41a6-b018-3886a8f32c7a'),
       ('tw', 0, 'bf8975dd-44cc-42c0-8114-c8f13cf74f2b'),
       ('tw', 1, '23f5f495-1e85-41a7-a211-da0631a5c13b'),
       ('az', 0, 'fad32613-7668-4c33-a398-376907a87091'),
       ('az', 1, '1560d303-4dc6-4fe4-ac9a-24a281b1027f'),
       ('eg', 0, '8935d039-19a2-47ea-8ab3-a6dd8f1b5911'),
       ('eg', 1, '127d21b0-d5a8-480c-a2c3-d3b91def96a4'),
       ('cn', 0, 'f216122f-6f2c-4b9e-b86d-b495f628ab60'),
       ('cn', 1, 'f9587383-6cde-4a81-a249-81186c657dea');

create temp table session_tmp as
select *
from link_dbo.session
where date_diff >= 45187 /*2023-09-20*/;

create temp table api_user_tmp as
select *
from link_dbo.api_user;

create temp table api_query_tmp as
select *
from link_dbo.api_query
where date(date) >= '2023-09-20';

create temp table session_click_no_serp_tmp as
select *
from link_dbo.session_click_no_serp
where date_diff > 45187 /*2023-09-20*/;

create temp table api_user_query_info_tmp as
select distinct aut.name                  as api_name,
                aut.website,
                aut.only_paid_jobs        as is_only_paid_jobs,
                ad.token_type,
                aqt.id                    as id_api_query,
                aqt.q_kw,
                aqt.q_region,
                an.fn_get_date_diff(date) as date_diff
from api_user_tmp aut
         join api_query_tmp aqt
              on aut.id = aqt.id_user
         join api_data_tmp ad
              on current_database() = ad.country_code
                  and aut.key = ad.token_key;

create temp table info_currency_history_tmp as
select an.fn_get_date_diff(date) as datediff,
       date(date)                as date,
       id_currency,
       avg(currency_rate)        as currency_rate,
       avg(value_to_usd)         as value_to_usd,
       avg(value_to_eur)         as value_to_eur
from link_dbo.info_currency_history
where date(date) >= '2023-09-20'
group by 1, 2, 3;

create temp table session_click_no_serp_revenue_tmp as
select auqi.id_api_query,
       auqi.q_kw,
       auqi.q_region,
       auqi.token_type,
       sum(scns.click_price * ic.value_to_usd::numeric) free_cv_revenue,
       count(distinct scns.id) as                       free_cv_away_cnt
from session_click_no_serp_tmp scns
         join info_currency_history_tmp ic
              on scns.date_diff = ic.datediff
                  and scns.id_currency = ic.id_currency
         join api_user_query_info_tmp auqi
              on scns.id_api_query = auqi.id_api_query
group by 1, 2, 3, 4;

select auqi.date_diff                                 as query_request_date_diff,
       auqi.token_type,
       auqi.q_kw,
       auqi.q_region,
       count(distinct auqi.id_api_query)              as free_cv_query_request_cnt,
       coalesce(count(distinct scns.id_api_query), 0) as free_cv_query_with_away_cnt,
       coalesce(sum(free_cv_revenue), 0)              as free_cv_revenue,
       coalesce(sum(free_cv_away_cnt), 0)             as free_cv_away_cnt
from api_user_query_info_tmp auqi
         left join session_click_no_serp_revenue_tmp scns
                   on scns.id_api_query = auqi.id_api_query
group by 1, 2, 3, 4;
