-- This function is at history DB
-- To call it we do -> 

-- select country_code,
--        year_month,
--        region_id,
--        title,
--        salary_val1,
--        salary_val2,
--        currency_id,
--        id_salary_rate,
--        top_num,
--        metric,
--        metric_type,
--        jobs_cnt,
--        unique_jobs_cnt
-- from public.fn_select_history_job_agg_vnov_new();

-- and then insert into vnov.vacancy_job_history_agg_second

create or replace function public.fn_select_history_job_agg_vnov_new()
    returns TABLE(country_code integer,
                  year_month text,
                  region_id integer,
                  title text,
                  salary_val1 double precision,
                  salary_val2 double precision,
                  currency_id smallint,
                  id_salary_rate smallint,
                  top_num bigint,
                  metric text,
                  metric_type text,
                  jobs_cnt bigint,
                  unique_jobs_cnt bigint)
    language plpgsql
as
$$
    declare _abroad_region int[] := ARRAY[19771,19772,19775,19777,19805,19808,19809,19824,19825,19826,19831,19833,
                            19835,19850,19851,19852,19853,19854,19855,19856,19857,19858,19859,19860,19861,19862,
                            19863,19864,19865,19866,19867,19868,19870,19871,19872,19873,19874,19875,19876,19877,
                            19879,19881,19883];

    /* for Germany DE
declare _abroad_region int[] := ARRAY[82444, 82445, 82446, 82447, 82448, 82449, 82450, 82453, 82454, 82455, 82456, 82457,
                            82458, 82459, 82460, 82462, 82463, 84454, 84455, 84456, 84457, 84458, 84459, 84460,
                            85454, 85455, 85457, 85458, 85459, 85460, 85461, 85462, 85463, 85464, 85831, 85832,
                            85833, 85834, 85835, 85836, 85837, 85838, 85839, 85840, 85841, 85842, 85843, 85844,
                            85845, 85846, 85847, 85848, 85849, 85850, 85851, 85852, 85853, 85854, 85855]; */

/* for France FR
declare _abroad_region int[] := ARRAY[43108,43109,43110,43111,43112,43113,44109,44110,44111,44112,44113,44114,
                            44115,44116,45121,45122,45123,45124,45126,45127,45128,45211,45214,45215,45216,45217,
                            45218,45219,45220,45221,45222,45223,45224,45225,45226,45227,45228,45229,45230,45231,
                            45232,45233,45234,45235,45236,45237,45238,45239,45240,45241,45242,45244,45245,45246,
                            45247,45248,45249,45250,45251,45252,45253,45254,45255]; */

/* for Austria AT
declare _abroad_region int[] := ARRAY[19771,19772,19775,19777,19805,19808,19809,19824,19825,19826,19831,19833,
                            19835,19850,19851,19852,19853,19854,19855,19856,19857,19858,19859,19860,19861,19862,
                            19863,19864,19865,19866,19867,19868,19870,19871,19872,19873,19874,19875,19876,19877,
                            19879,19881,19883]; */

/* for United Kingdom UK
declare _abroad_region int[] := ARRAY[40904,40864,40875,40876,40877,40878,40879,40880,40881,40882,40883,40884,40885,
                            40886,40887,40888,40889,40890,40891,40892,40893,40894,40895,40896,40897,40898,40899,40900,
                            40901,40902,40903,40905,40906,40907,41477,41478,41479,41480,41553,41554,41555,41556,41566,
                            41567,41616,41620,41627,41628,41629,41630,41631,41632,41633,41634,41635,41636,41637]; */

begin

    create temp table j_by_job_2022 as
  SELECT j.date_created,
         j.date_expired,
         j.id_region,
         j.title,
         j.job_type1,
         j.id,
         j.id_similar_group
FROM public.job j (nolock)
WHERE cast(j.date_created as date) between '2022-01-01' and '2023-06-30'
Group by j.date_created,
         j.date_expired,
         j.id_region,
         j.title,
         j.job_type1,
         j.id,
         j.id_similar_group
;

    create temp table j_by_job_2023 as
  SELECT j.date_created,
         j.date_expired,
         j.id_region,
         j.title,
         j.job_type1,
         j.id,
         j.id_similar_group,
         j.salary_val1,
         j.salary_val2,
         j.id_salary_rate,
         j.id_currency
FROM public.job j (nolock)
WHERE cast(j.date_created as date) between '2022-01-01' and '2023-06-30'
Group by j.date_created,
         j.date_expired,
         j.id_region,
         j.title,
         j.job_type1,
         j.id,
         j.id_similar_group,
         j.salary_val1,
         j.salary_val2,
         j.id_salary_rate,
         j.id_currency
;

create temp table j_by_yymm_abroad as
SELECT  to_char(j.date_created, 'YYYYMM')  as year_month,
        j.id_region,
        count(distinct j.id_similar_group) as unique_jobs_cnt,
        count(distinct j.id)               as jobs_cnt
FROM j_by_job_2022 j
WHERE cast(j.date_created as date) between '2023-01-01' and '2023-01-31'
        and (j.date_expired is null or cast(j.date_expired as date) > '2023-01-31')
        and j.id_region IN (SELECT unnest(_abroad_region))
Group by to_char(j.date_created, 'YYYYMM'),j.id_region
UNION ALL
SELECT  to_char(j.date_created, 'YYYYMM')  as year_month,
        j.id_region,
        count(distinct j.id_similar_group) as unique_jobs_cnt,
        count(distinct j.id) as jobs_cnt
FROM j_by_job_2022 j
WHERE cast(j.date_created as date) between '2023-02-01' and '2023-02-28'
        and (j.date_expired is null or cast(j.date_expired as date) > '2023-02-28')
        and j.id_region IN (SELECT unnest(_abroad_region))
Group by to_char(j.date_created, 'YYYYMM'),j.id_region
UNION ALL
SELECT  to_char(j.date_created, 'YYYYMM')  as year_month,
        j.id_region,
        count(distinct j.id_similar_group) as unique_jobs_cnt,
        count(distinct j.id) as jobs_cnt
FROM j_by_job_2022 j
WHERE cast(j.date_created as date) between '2023-03-01' and '2023-03-31'
        and (j.date_expired is null or cast(j.date_expired as date) > '2023-03-31')
        and j.id_region IN (SELECT unnest(_abroad_region))
Group by to_char(j.date_created, 'YYYYMM'),j.id_region
UNION ALL
SELECT  to_char(j.date_created, 'YYYYMM')  as year_month,
        j.id_region,
        count(distinct j.id_similar_group) as unique_jobs_cnt,
        count(distinct j.id) as jobs_cnt
FROM j_by_job_2022 j
WHERE cast(j.date_created as date) between '2023-04-01' and '2023-04-30'
        and (j.date_expired is null or cast(j.date_expired as date) > '2023-04-30')
        and j.id_region IN (SELECT unnest(_abroad_region))
Group by to_char(j.date_created, 'YYYYMM'),j.id_region
UNION ALL
SELECT  to_char(j.date_created, 'YYYYMM')  as year_month,
        j.id_region,
        count(distinct j.id_similar_group) as unique_jobs_cnt,
        count(distinct j.id) as jobs_cnt
FROM j_by_job_2022 j
WHERE cast(j.date_created as date) between '2023-05-01' and '2023-05-31'
        and (j.date_expired is null or cast(j.date_expired as date) > '2023-05-31')
        and j.id_region IN (SELECT unnest(_abroad_region))
Group by to_char(j.date_created, 'YYYYMM'),j.id_region
UNION ALL
SELECT  to_char(j.date_created, 'YYYYMM')  as year_month,
        j.id_region,
        count(distinct j.id_similar_group) as unique_jobs_cnt,
        count(distinct j.id) as jobs_cnt
FROM j_by_job_2022 j
WHERE cast(j.date_created as date) between '2023-06-01' and '2023-06-30'
        and (j.date_expired is null or cast(j.date_expired as date) > '2023-06-30')
        and j.id_region IN (SELECT unnest(_abroad_region))
Group by to_char(j.date_created, 'YYYYMM'),j.id_region
UNION ALL
SELECT  to_char(j.date_created, 'YYYYMM')  as year_month,
        j.id_region,
        count(distinct j.id_similar_group) as unique_jobs_cnt,
        count(distinct j.id)               as jobs_cnt
FROM j_by_job_2022 j
WHERE cast(j.date_created as date) between '2022-01-01' and '2022-01-31'
        and (j.date_expired is null or cast(j.date_expired as date) > '2022-01-31')
        and j.id_region IN (SELECT unnest(_abroad_region))
Group by to_char(j.date_created, 'YYYYMM'),j.id_region
UNION ALL
SELECT  to_char(j.date_created, 'YYYYMM')  as year_month,
        j.id_region,
        count(distinct j.id_similar_group) as unique_jobs_cnt,
        count(distinct j.id) as jobs_cnt
FROM j_by_job_2022 j
WHERE cast(j.date_created as date) between '2022-02-01' and '2022-02-28'
        and (j.date_expired is null or cast(j.date_expired as date) > '2022-02-28')
        and j.id_region IN (SELECT unnest(_abroad_region))
Group by to_char(j.date_created, 'YYYYMM'),j.id_region
UNION ALL
SELECT  to_char(j.date_created, 'YYYYMM')  as year_month,
        j.id_region,
        count(distinct j.id_similar_group) as unique_jobs_cnt,
        count(distinct j.id) as jobs_cnt
FROM j_by_job_2022 j
WHERE cast(j.date_created as date) between '2022-03-01' and '2022-03-31'
        and (j.date_expired is null or cast(j.date_expired as date) > '2022-03-31')
        and j.id_region IN (SELECT unnest(_abroad_region))
Group by to_char(j.date_created, 'YYYYMM'),j.id_region
UNION ALL
SELECT  to_char(j.date_created, 'YYYYMM')  as year_month,
        j.id_region,
        count(distinct j.id_similar_group) as unique_jobs_cnt,
        count(distinct j.id) as jobs_cnt
FROM j_by_job_2022 j
WHERE cast(j.date_created as date) between '2022-04-01' and '2022-04-30'
        and (j.date_expired is null or cast(j.date_expired as date) > '2022-04-30')
        and j.id_region IN (SELECT unnest(_abroad_region))
Group by to_char(j.date_created, 'YYYYMM'),j.id_region
UNION ALL
SELECT  to_char(j.date_created, 'YYYYMM')  as year_month,
        j.id_region,
        count(distinct j.id_similar_group) as unique_jobs_cnt,
        count(distinct j.id) as jobs_cnt
FROM j_by_job_2022 j
WHERE cast(j.date_created as date) between '2022-05-01' and '2022-05-31'
        and (j.date_expired is null or cast(j.date_expired as date) > '2022-05-31')
        and j.id_region IN (SELECT unnest(_abroad_region))
Group by to_char(j.date_created, 'YYYYMM'),j.id_region
UNION ALL
SELECT  to_char(j.date_created, 'YYYYMM')  as year_month,
        j.id_region,
        count(distinct j.id_similar_group) as unique_jobs_cnt,
        count(distinct j.id) as jobs_cnt
FROM j_by_job_2022 j
WHERE cast(j.date_created as date) between '2022-06-01' and '2022-06-30'
        and (j.date_expired is null or cast(j.date_expired as date) > '2022-06-30')
        and j.id_region IN (SELECT unnest(_abroad_region))
Group by to_char(j.date_created, 'YYYYMM'),j.id_region
UNION ALL
SELECT  to_char(j.date_created, 'YYYYMM')  as year_month,
        j.id_region,
        count(distinct j.id_similar_group) as unique_jobs_cnt,
        count(distinct j.id)               as jobs_cnt
FROM j_by_job_2022 j
WHERE cast(j.date_created as date) between '2022-07-01' and '2022-07-31'
        and (j.date_expired is null or cast(j.date_expired as date) > '2022-07-31')
        and j.id_region IN (SELECT unnest(_abroad_region))
Group by to_char(j.date_created, 'YYYYMM'),j.id_region
UNION ALL
SELECT  to_char(j.date_created, 'YYYYMM')  as year_month,
        j.id_region,
        count(distinct j.id_similar_group) as unique_jobs_cnt,
        count(distinct j.id) as jobs_cnt
FROM j_by_job_2022 j
WHERE cast(j.date_created as date) between '2022-08-01' and '2022-08-31'
        and (j.date_expired is null or cast(j.date_expired as date) > '2022-08-31')
        and j.id_region IN (SELECT unnest(_abroad_region))
Group by to_char(j.date_created, 'YYYYMM'),j.id_region
UNION ALL
SELECT  to_char(j.date_created, 'YYYYMM')  as year_month,
        j.id_region,
        count(distinct j.id_similar_group) as unique_jobs_cnt,
        count(distinct j.id) as jobs_cnt
FROM j_by_job_2022 j
WHERE cast(j.date_created as date) between '2022-09-01' and '2022-09-30'
        and (j.date_expired is null or cast(j.date_expired as date) > '2022-09-30')
        and j.id_region IN (SELECT unnest(_abroad_region))
Group by to_char(j.date_created, 'YYYYMM'),j.id_region
UNION ALL
SELECT  to_char(j.date_created, 'YYYYMM')  as year_month,
        j.id_region,
        count(distinct j.id_similar_group) as unique_jobs_cnt,
        count(distinct j.id) as jobs_cnt
FROM j_by_job_2022 j
WHERE cast(j.date_created as date) between '2022-10-01' and '2022-10-31'
        and (j.date_expired is null or cast(j.date_expired as date) > '2022-10-31')
        and j.id_region IN (SELECT unnest(_abroad_region))
Group by to_char(j.date_created, 'YYYYMM'),j.id_region
UNION ALL
SELECT  to_char(j.date_created, 'YYYYMM')  as year_month,
        j.id_region,
        count(distinct j.id_similar_group) as unique_jobs_cnt,
        count(distinct j.id) as jobs_cnt
FROM j_by_job_2022 j
WHERE cast(j.date_created as date) between '2022-11-01' and '2022-11-30'
        and (j.date_expired is null or cast(j.date_expired as date) > '2022-11-30')
        and j.id_region IN (SELECT unnest(_abroad_region))
Group by to_char(j.date_created, 'YYYYMM'),j.id_region
UNION ALL
SELECT  to_char(j.date_created, 'YYYYMM')  as year_month,
        j.id_region,
        count(distinct j.id_similar_group) as unique_jobs_cnt,
        count(distinct j.id) as jobs_cnt
FROM j_by_job_2022 j
WHERE cast(j.date_created as date) between '2022-12-01' and '2022-12-31'
        and (j.date_expired is null or cast(j.date_expired as date) > '2022-12-31')
        and j.id_region IN (SELECT unnest(_abroad_region))
Group by to_char(j.date_created, 'YYYYMM'),j.id_region
;

create temp table j_by_yymm_vacancy_top_abroad as
SELECT *, row_number() over ( order by a.unique_jobs_cnt desc ) AS TOPNo
FROM (
         SELECT to_char(j.date_created, 'YYYYMM')  as year_month,
                lower(j.title)                     as title,
                j.salary_val1,
                j.salary_val2,
                j.id_currency,
                j.id_salary_rate,
                count(distinct j.id)               as jobs_cnt,
                count(distinct j.id_similar_group) as unique_jobs_cnt
         FROM j_by_job_2023 j
         WHERE cast(j.date_created as date) between '2023-01-01' and '2023-01-31'
           and (j.date_expired is null or cast(j.date_expired as date) > '2023-01-31')
           and j.id_region IN (SELECT unnest(_abroad_region))
         Group by to_char(j.date_created, 'YYYYMM'), lower(j.title), j.salary_val1, j.salary_val2, j.id_currency, j.id_salary_rate
         Order by count(distinct j.id_similar_group) desc
         Limit 30
         )a
UNION ALL
SELECT *, row_number() over ( order by b.unique_jobs_cnt desc ) AS TOPNo
FROM (
SELECT to_char(j.date_created, 'YYYYMM')  as year_month,
       lower(j.title)                     as title,
       j.salary_val1,
       j.salary_val2,
       j.id_currency,
       j.id_salary_rate,
       count(distinct j.id)               as jobs_cnt,
       count(distinct j.id_similar_group) as unique_jobs_cnt
FROM j_by_job_2023 j
WHERE cast(j.date_created as date) between '2023-02-01' and '2023-02-28'
     and (j.date_expired is null or cast(j.date_expired as date) > '2023-02-28')
     and j.id_region IN (SELECT unnest(_abroad_region))
Group by to_char(j.date_created, 'YYYYMM'), lower(j.title), j.salary_val1, j.salary_val2, j.id_currency, j.id_salary_rate
Order by count(distinct j.id_similar_group) desc
Limit 30
    )b
UNION ALL
SELECT *, row_number() over ( order by c.unique_jobs_cnt desc ) AS TOPNo
FROM (
SELECT to_char(j.date_created, 'YYYYMM')  as year_month,
       lower(j.title)                     as title,
       j.salary_val1,
       j.salary_val2,
       j.id_currency,
       j.id_salary_rate,
       count(distinct j.id)               as jobs_cnt,
       count(distinct j.id_similar_group) as unique_jobs_cnt
FROM j_by_job_2023 j
WHERE cast(j.date_created as date) between '2023-03-01' and '2023-03-31'
        and (j.date_expired is null or cast(j.date_expired as date) > '2023-03-31')
       and j.id_region IN (SELECT unnest(_abroad_region))
Group by to_char(j.date_created, 'YYYYMM'), lower(j.title), j.salary_val1, j.salary_val2, j.id_currency, j.id_salary_rate
Order by count(distinct j.id_similar_group) desc
Limit 30
         )c
UNION ALL
SELECT *, row_number() over ( order by d.unique_jobs_cnt desc ) AS TOPNo
FROM (
SELECT to_char(j.date_created, 'YYYYMM')  as year_month,
       lower(j.title)                     as title,
       j.salary_val1,
       j.salary_val2,
       j.id_currency,
       j.id_salary_rate,
       count(distinct j.id)               as jobs_cnt,
       count(distinct j.id_similar_group) as unique_jobs_cnt
FROM j_by_job_2023 j
WHERE cast(j.date_created as date) between '2023-04-01' and '2023-04-30'
        and (j.date_expired is null or cast(j.date_expired as date) > '2023-04-30')
        and j.id_region IN (SELECT unnest(_abroad_region))
Group by to_char(j.date_created, 'YYYYMM'), lower(j.title), j.salary_val1, j.salary_val2, j.id_currency, j.id_salary_rate
Order by count(distinct j.id_similar_group) desc
Limit 30
         )d
UNION ALL
SELECT *, row_number() over ( order by dd.unique_jobs_cnt desc ) AS TOPNo
FROM (
SELECT to_char(j.date_created, 'YYYYMM')  as year_month,
       lower(j.title)                     as title,
       j.salary_val1,
       j.salary_val2,
       j.id_currency,
       j.id_salary_rate,
       count(distinct j.id)               as jobs_cnt,
       count(distinct j.id_similar_group) as unique_jobs_cnt
FROM j_by_job_2023 j
WHERE cast(j.date_created as date) between '2023-05-01' and '2023-05-31'
        and (j.date_expired is null or cast(j.date_expired as date) > '2023-05-31')
        and j.id_region IN (SELECT unnest(_abroad_region))
Group by to_char(j.date_created, 'YYYYMM'), lower(j.title), j.salary_val1, j.salary_val2, j.id_currency, j.id_salary_rate
Order by count(distinct j.id_similar_group) desc
Limit 30
         )dd
UNION ALL
SELECT *, row_number() over ( order by bd.unique_jobs_cnt desc ) AS TOPNo
FROM (
SELECT to_char(j.date_created, 'YYYYMM')  as year_month,
       lower(j.title)                     as title,
       j.salary_val1,
       j.salary_val2,
       j.id_currency,
       j.id_salary_rate,
       count(distinct j.id)               as jobs_cnt,
       count(distinct j.id_similar_group) as unique_jobs_cnt
FROM j_by_job_2023 j
WHERE cast(j.date_created as date) between '2023-06-01' and '2023-06-30'
        and (j.date_expired is null or cast(j.date_expired as date) > '2023-06-30')
        and j.id_region IN (SELECT unnest(_abroad_region))
Group by to_char(j.date_created, 'YYYYMM'), lower(j.title), j.salary_val1, j.salary_val2, j.id_currency, j.id_salary_rate
Order by count(distinct j.id_similar_group) desc
Limit 30
         )bd
;

-- final union with all segments
create temp table  final_union as
    Select
           top.year_month,
           top.id_region,
           null::text             as title,
           null::double precision as salary_val1,
           null::double precision as salary_val2,
           null::smallint         as id_currency,
           null::smallint         as id_salary_rate,
           null::smallint         as TOPNo,
           'jobs_by_region_top'   as metric,
           'abroad'               as metric_type,
           top.jobs_cnt,
           top.unique_jobs_cnt
    FROM j_by_yymm_abroad top
    union all
    Select
           j2.year_month,
           null::integer           as id_region,
           j2.title,
           j2.salary_val1,
           j2.salary_val2,
           j2.id_currency,
           j2.id_salary_rate,
           j2.TOPNo,
           'jobs_by_vacancy_top'   as metric,
           'abroad'                as metric_type,
           j2.jobs_cnt,
           j2.unique_jobs_cnt
    FROM j_by_yymm_vacancy_top_abroad j2
;

    return query
Select CAST(13 as integer) as country_code,  -- change number for the counrty you need
       CAST(u.year_month as text),
       CAST(u.id_region as integer) as region_id,
       CAST(u.title as text),
       CAST(u.salary_val1 as double precision),
       CAST(u.salary_val2 as double precision),
       CAST(u.id_currency as smallint) as currency_id,
       CAST(u.id_salary_rate as smallint),
       CAST(u.TOPNo as bigint) as top_num,
       CAST(u.metric as text),
       CAST(u.metric_type as text),
       CAST(u.jobs_cnt as bigint),
       CAST(u.unique_jobs_cnt as bigint)
FROM final_union u;

end;
$$;

alter function public.fn_select_history_job_agg_vnov_new() owner to svak;
