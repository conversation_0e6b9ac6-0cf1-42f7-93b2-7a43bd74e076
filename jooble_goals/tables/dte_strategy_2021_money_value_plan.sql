insert into jooble_goals.dte_strategy_2021_money_value_plan (month_first_date,  call_revenue_plan,  apply_revenue_plan,  profile_base_revenue_plan,  digital_recruiter_revenue_plan,  unutilized_revenue_plan)
values
('2021-04-01', 793000, 176000, 265000, 263500, 1497500),
('2021-05-01', 793000, 176000, 265000, 263500, 1497500),
('2021-06-01', 793000, 176000, 265000, 263500, 1497500),
('2021-07-01', 793000, 176000, 265000, 263500, 1497500),


('2021-08-01', 793000, 176000, 265000, 263500, 1497500),
('2021-09-01', 793000, 176000, 278000, 263500, 1497500),

('2021-10-01', 810000, 192000, 292000, 333000, 1627000),
('2021-11-01', 810000, 192000, 322000, 333000, 1657000),
('2021-12-01', 810000, 192000, 421000, 333000, 1756000),

('2022-01-01', 921000, 233000, 434000, 421500, 2009500),
('2022-02-01', 921000, 239000, 447000, 421500, 2028500),
('2022-03-01', 921000, 239000, 483000, 421500, 2064500),

('2022-04-01', 964000, 287000, 633000, 533000, 2417000),
('2022-05-01', 964000, 286000, 651000, 533000, 2434000),
('2022-06-01', 964000, 286000, 665000, 533000, 2448000),

('2022-07-01', 1123000, 366000, 712000, 674500, 2875500);

-- add cashflow_plan from strategy 2022 q1
alter table jooble_goals.dte_strategy_2021_money_value_plan
	add cashflow_plan int;

UPDATE jooble_goals.dte_strategy_2021_money_value_plan SET cashflow_plan = 3160000 WHERE month_first_date = '2022-01-01';
UPDATE jooble_goals.dte_strategy_2021_money_value_plan SET cashflow_plan = 1682000 WHERE month_first_date = '2022-02-01';
UPDATE jooble_goals.dte_strategy_2021_money_value_plan SET cashflow_plan = 1329000 WHERE month_first_date = '2022-03-01';
