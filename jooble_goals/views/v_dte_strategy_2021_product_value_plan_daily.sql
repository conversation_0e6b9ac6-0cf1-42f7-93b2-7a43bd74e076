create or replace view jooble_goals.v_dte_strategy_2021_product_value_plan_daily(session_traffic_source_group_id, is_session_with_profile_submitted, date, apply_plan_cnt, click_call_plan_cnt, profile_created_plan_cnt, session_plan_cnt) as
	SELECT pvp.session_traffic_source_group_id,
       pvp.is_session_with_profile_submitted,
       ic.dt             AS date,
       round(pvp.apply_plan_cnt::numeric / count(ic.dt)
                                           OVER (PARTITION BY pvp.session_traffic_source_group_id, pvp.is_session_with_profile_submitted, ic.dt_month)::numeric,
             0)::integer AS apply_plan_cnt,
       round(pvp.click_call_plan_cnt::numeric / count(ic.dt)
                                                OVER (PARTITION BY pvp.session_traffic_source_group_id, pvp.is_session_with_profile_submitted, ic.dt_month)::numeric,
             0)::integer AS click_call_plan_cnt,
       round(pvp.profile_created_plan_cnt::numeric / count(ic.dt)
                                                     OVER (PARTITION BY pvp.session_traffic_source_group_id, pvp.is_session_with_profile_submitted, ic.dt_month)::numeric,
             0)::integer AS profile_created_plan_cnt,
       round(pvp.session_plan_cnt::numeric / count(ic.dt)
                                             OVER (PARTITION BY pvp.session_traffic_source_group_id, pvp.is_session_with_profile_submitted, ic.dt_month)::numeric,
             0)::integer AS session_plan_cnt
FROM jooble_goals.dte_strategy_2021_product_value_plan pvp
         LEFT JOIN dimension.info_calendar ic
                   ON date_trunc('month'::text, ic.dt::timestamp with time zone) = pvp.month_first_date;

alter table jooble_goals.v_dte_strategy_2021_product_value_plan_daily owner to dap;

grant select on jooble_goals.v_dte_strategy_2021_product_value_plan_daily to readonly;

