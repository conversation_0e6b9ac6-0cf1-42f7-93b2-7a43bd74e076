create or replace view jooble_goals.v_dte_strategy_2021_product_value(month_first_date, session_traffic_source_group_id, is_session_with_profile_submitted, session_plan_cnt, click_call_plan_cnt, apply_plan_cnt, profile_created_plan_cnt, session_cnt, click_call_cnt, apply_cnt, profile_created_cnt, session_traffic_source_group_name) as
	SELECT pv_plan.month_first_date,
       pv_plan.session_traffic_source_group_id,
       pv_plan.is_session_with_profile_submitted,
       pv_plan.session_plan_cnt,
       pv_plan.click_call_plan_cnt,
       pv_plan.apply_plan_cnt,
       pv_plan.profile_created_plan_cnt,
       sum(sma.session_cnt)                                      AS session_cnt,
       sum(sma.click_call_cnt)                                   AS click_call_cnt,
       sum(sma.apply_cnt)                                        AS apply_cnt,
       sum(sma.profile_created_cnt)                              AS profile_created_cnt,
       (SELECT stsg.session_traffic_source_group_name
        FROM dimension.session_traffic_source_group stsg
        WHERE stsg.id = pv_plan.session_traffic_source_group_id) AS session_traffic_source_group_name
FROM jooble_goals.dte_strategy_2021_product_value_plan pv_plan
         LEFT JOIN traffic.session_metric_agg sma ON sma.country_id = 1 AND pv_plan.month_first_date =
                                                                            date_trunc('month'::text,
                                                                                       fn_get_timestamp_from_date_diff(sma.session_datediff)) AND
                                                     pv_plan.session_traffic_source_group_id =
                                                     sma.session_traffic_source_group_id AND
                                                     pv_plan.is_session_with_profile_submitted =
                                                     sma.is_session_with_profile_creation
GROUP BY pv_plan.month_first_date, pv_plan.session_traffic_source_group_id, pv_plan.is_session_with_profile_submitted,
         pv_plan.session_plan_cnt, pv_plan.click_call_plan_cnt, pv_plan.apply_plan_cnt,
         pv_plan.profile_created_plan_cnt;

alter table jooble_goals.v_dte_strategy_2021_product_value owner to dap;

grant select on jooble_goals.v_dte_strategy_2021_product_value to readonly;

grant select on jooble_goals.v_dte_strategy_2021_product_value to "pavlo.kvasnii";

