create or replace view jooble_goals.v_dte_strategy_2021_money_value_plan_daily(date, call_revenue_plan, apply_revenue_plan, profile_base_revenue_plan, digital_recruiter_revenue_plan, unutilized_revenue_plan, cashflow_plan) as
	SELECT ic.dt                                                                                                      AS date,
       round(cvp.call_revenue_plan::numeric / count(ic.dt) OVER (PARTITION BY ic.dt_month)::numeric,
             0)::integer                                                                                          AS call_revenue_plan,
       round(cvp.apply_revenue_plan::numeric / count(ic.dt) OVER (PARTITION BY ic.dt_month)::numeric,
             0)::integer                                                                                          AS apply_revenue_plan,
       round(cvp.profile_base_revenue_plan::numeric / count(ic.dt) OVER (PARTITION BY ic.dt_month)::numeric,
             0)::integer                                                                                          AS profile_base_revenue_plan,
       round(cvp.digital_recruiter_revenue_plan::numeric / count(ic.dt) OVER (PARTITION BY ic.dt_month)::numeric,
             0)::integer                                                                                          AS digital_recruiter_revenue_plan,
       round(cvp.unutilized_revenue_plan::numeric / count(ic.dt) OVER (PARTITION BY ic.dt_month)::numeric,
             0)::integer               AS unutilized_revenue_plan,
	              round(cvp.cashflow_plan::numeric / count(ic.dt) OVER (PARTITION BY ic.dt_month)::numeric,
             0)::integer  as cashflow_plan
FROM jooble_goals.dte_strategy_2021_money_value_plan cvp
         LEFT JOIN dimension.info_calendar ic
                   ON date_trunc('month'::text, ic.dt::timestamp with time zone) = cvp.month_first_date;

alter table jooble_goals.v_dte_strategy_2021_money_value_plan_daily owner to dap;
