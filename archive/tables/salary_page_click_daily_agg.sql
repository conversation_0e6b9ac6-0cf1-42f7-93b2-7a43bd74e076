SET NOCOUNT ON;

DECLARE @datediff_start int = :to_sqlcode_date_or_datediff_start,
        @datediff_end int = :to_sqlcode_date_or_datediff_end;


SELECT s.date_diff                           AS session_datediff,
       s.id_current_traf_source              AS traffic_source_id,
       SIGN(s.flags & 16)                    AS device_type_id,
       s.session_create_page_type,
       ss.search_source                      AS search_source_id,
       COUNT(DISTINCT ss.id)                 AS search_cnt,
       COUNT(DISTINCT sc.id)                 AS serp_click_cnt,
       COUNT(DISTINCT sa.id)                 AS away_cnt,
       SUM(sa.click_price * ic.value_to_usd) AS revenue_usd
FROM dbo.session s
         JOIN dbo.session_search ss
              ON s.date_diff = ss.date_diff AND
                 s.id = ss.id_session
         LEFT JOIN dbo.session_click sc
                   ON ss.id = sc.id_search AND
                      ss.date_diff = sc.date_diff
         LEFT JOIN dbo.info_currency ic
                   ON ic.id = sc.id_currency
         LEFT JOIN dbo.session_away sa
                   ON sc.date_diff = sa.date_diff
                       AND sc.id = sa.id_click
WHERE s.date_diff BETWEEN @datediff_start AND @datediff_end
  AND s.flags & 1 = 0
  AND s.flags & 64 = 0
  AND s.session_create_page_type = 8
GROUP BY ss.search_source,
         s.session_create_page_type,
         s.date_diff,
         s.id_current_traf_source,
         SIGN(s.flags & 16)

UNION

SELECT s.date_diff                           AS session_datediff,
       s.id_current_traf_source              AS traffic_source_id,
       SIGN(s.flags & 16)                    AS device_type_id,
       s.session_create_page_type,
       ss.search_source                      AS search_source_id,
       COUNT(DISTINCT ss.id)                 AS search_cnt,
       COUNT(DISTINCT sc.id)                 AS serp_click_cnt,
       COUNT(DISTINCT sa.id)                 AS away_cnt,
       SUM(sa.click_price * ic.value_to_usd) AS revenue_usd
FROM dbo.session s
         JOIN dbo.session_search ss
              ON s.date_diff = ss.date_diff AND
                 s.id = ss.id_session
         LEFT JOIN dbo.session_click sc
                   ON ss.id = sc.id_search AND
                      ss.date_diff = sc.date_diff
         LEFT JOIN dbo.info_currency ic
                   ON ic.id = sc.id_currency
         LEFT JOIN dbo.session_away sa
                   ON sc.date_diff = sa.date_diff
                       AND sc.id = sa.id_click
WHERE ss.search_source IN (118, 119, 134, 136, 137, 138, 139, 140, 141, 142, 143, 144)
  AND s.date_diff BETWEEN @datediff_start AND @datediff_end
  AND s.flags & 1 = 0
  AND s.flags & 64 = 0
  AND NOT s.session_create_page_type = 8
GROUP BY ss.search_source,
         s.session_create_page_type,
         s.date_diff,
         s.id_current_traf_source,
         SIGN(s.flags & 16)
;
