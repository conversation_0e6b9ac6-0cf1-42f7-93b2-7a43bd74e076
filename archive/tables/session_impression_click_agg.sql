select country_id = 1,
	   si.date as date_diff, si.id_session, si.id_search,
       count(distinct si.id) as impressions,
       count(distinct case when si.job_destination = 2 then si.id end) as agg,
       count(distinct case when j.id_project = -1 then si.id end) as dte,
       count(distinct case when si.visual_flags & 1 = 1 then si.id end) as new,
       count(distinct case when si.visual_flags & 4 = 4 then si.id end) as few_applies,
       count(distinct case when si.visual_flags & 64 = 64 then si.id end) as premium,
       count(distinct case when si.visual_flags & 8 = 8 then si.id end) as address,
       count(distinct case when si.visual_flags & 8 = 8 and si.job_destination = 3 then si.id end) as dte_address,
       count(distinct case when j.remote_type = 1 then si.id end) as remote,
       count(distinct case when isnull(j.job_type1, '') <> '' or isnull(j.job_type2, '') <> '' then si.id end) as job_type,
       count(distinct case when j.job_type1 = 1 or j.job_type2 = 1 then si.id end) as full_time,
       count(distinct case when j.job_type1 = 2 or j.job_type2 = 2 then si.id end) as temporary,
       count(distinct case when j.job_type1 = 3 or j.job_type2 = 3 then si.id end) as part_time,
       count(distinct case when j.job_type1 = 4 or j.job_type2 = 4 then si.id end) as internship
from session_impression si with (nolock)
join session_impression_on_screen sis with (nolock) on si.id = sis.id_impression and si.date = sis.date_diff
join job_region jr with(nolock) on jr.uid = si.uid_job
join job j with (nolock) on jr.id_job = j.id
join session_click sc with (nolock) on si.id = sc.id_impression and si.date = sc.date_diff
where si.date = @dt_start - 1
and si.id_search is not null
group by si.date, si.id_search, si.id_session
