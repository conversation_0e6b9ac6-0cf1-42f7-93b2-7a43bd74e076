create table employer.jcoin_utilization_employer_expectation as (
select database_source_id,
           fn_get_date_diff(utilization_date) as utilization_datediff,
           employer_id,
           subscription_id,
           jcoin_plan_cnt - coalesce(lag(jcoin_plan_cnt) over (partition by database_source_id, employer_id, subscription_id order by utilization_date),
                                     0) as employer_expectation_jcoin_cnt,
           jcoin_fact_cnt - coalesce(lag(jcoin_fact_cnt) over (partition by database_source_id, employer_id, subscription_id order by utilization_date),
                                     0) as employer_utilized_jcoin_cnt
    from employer.v_jcoin_utilization_daily
);

alter table employer.jcoin_utilization_employer_expectation
	add constraint jcoin_utilization_employer_expectation_pk
		primary key (database_source_id, date, employer_id,subscription_id);
