create table profile.job_seeker_action_to_contact_type
(
    country_id                smallint  not null,
    profile_id                integer   not null,
    jdp_viewed_datediff       integer,
    jdp_viewed_datetime       timestamp not null,
    job_uid                   bigint    not null,
    action_to_contact_type_id integer   not null,
    action_to_contact_prob    numeric(10, 4),
    constraint job_seeker_action_to_contact_type_pk
        primary key (country_id, profile_id, jdp_viewed_datetime, job_uid, action_to_contact_type_id)
);

alter table profile.job_seeker_action_to_contact_type
    owner to postgres;
