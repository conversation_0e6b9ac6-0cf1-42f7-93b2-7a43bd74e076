declare @date_diff int = ${dt_begin};
declare @country_id int = ${country_id};



select *
into #session_temp
from dbo.session
where date_diff between @date_diff - 6 and @date_diff;

select *
into #session_search_temp
from dbo.session_search
where date_diff between @date_diff - 6 and @date_diff;

select *
into #session_impression_on_screen_temp
from dbo.session_impression_on_screen
where date_diff between @date_diff - 6 and @date_diff;

select *
into #session_click_temp
from dbo.session_click
where date_diff between @date_diff - 6 and @date_diff;

select *
into #session_impression_temp
from dbo.session_impression
where date between @date_diff - 6 and @date_diff;



select *
into #job_region_temp
from dbo.job_region;

select *
into #job_temp
from dbo.job;



select distinct
    ss.kw_hash,
    count(distinct ss.id)                                                       as search_cnt,
    count(distinct iif(si.position between 1 and 10, si.id, null))              as impression_cnt_10,
    count(distinct iif(si.position between 11 and 20, si.id, null))             as impression_cnt_20,
    count(distinct iif(si.position between 21 and 30, si.id, null))             as impression_cnt_30,
    count(distinct iif(si.position between 31 and 40, si.id, null))             as impression_cnt_40,
    count(distinct iif(si.position between 41 and 50, si.id, null))             as impression_cnt_50,
    count(distinct iif(si.position between 1 and 10, sis.id_impression, null))  as impression_on_screen_cnt_10,
    count(distinct iif(si.position between 11 and 20, sis.id_impression, null)) as impression_on_screen_cnt_20,
    count(distinct iif(si.position between 21 and 30, sis.id_impression, null)) as impression_on_screen_cnt_30,
    count(distinct iif(si.position between 31 and 40, sis.id_impression, null)) as impression_on_screen_cnt_40,
    count(distinct iif(si.position between 41 and 50, sis.id_impression, null)) as impression_on_screen_cnt_50,
    count(distinct iif(si.position between 1 and 10, sc.id, null))              as click_cnt_10,
    count(distinct iif(si.position between 11 and 20, sc.id, null))             as click_cnt_20,
    count(distinct iif(si.position between 21 and 30, sc.id, null))             as click_cnt_30,
    count(distinct iif(si.position between 31 and 40, sc.id, null))             as click_cnt_40,
    count(distinct iif(si.position between 41 and 50, sc.id, null))             as click_cnt_50,
    avg(iif(si.position between 1 and 10, si.click_price, null))                as avg_cpc_usd_10,
    avg(iif(si.position between 11 and 20, si.click_price, null))               as avg_cpc_usd_20,
    avg(iif(si.position between 21 and 30, si.click_price, null))               as avg_cpc_usd_30,
    avg(iif(si.position between 31 and 40, si.click_price, null))               as avg_cpc_usd_40,
    avg(iif(si.position between 41 and 50, si.click_price, null))               as avg_cpc_usd_50
into #serp_kw_stat
from #session_search_temp ss with (nolock)
     join #session_temp s with (nolock)
          on s.date_diff = ss.date_diff and s.id = ss.id_session
     join #session_impression_temp si with (nolock)
          on si.date = ss.date_diff and si.id_search = ss.id
     left join #session_impression_on_screen_temp sis with (nolock)
               on sis.date_diff = si.date and sis.id_impression = si.id
     left join #session_click_temp sc with (nolock)
               on sc.date_diff = si.date and sc.id_impression = si.id
where ss.date_diff between @date_diff - 6 and @date_diff
  and si.position <= 50
  and s.flags & 1 = 0
group by
    ss.kw_hash;


select distinct
    j.id_project,
    kw.kw_hash64,
    kw.id_job,
    coalesce(jbi.billing_click_price_in_usd, 0) as job_cpc_usd
into #keyword_raw
from dbo.job_keyword kw with (nolock)
     join #job_temp j with (nolock)
          on j.id = kw.id_job
     left join #job_region_temp jr with (nolock)
               on jr.id_job = j.id
     left join dbo.vw_job_billing_info jbi with (nolock)
               on jbi.uid = jr.uid
where j.id_project < 100000;


with
    t as (
        select
            id_project,
            -1                        as job_cpc_usd,
            count(distinct kw_hash64) as keyword_cnt,
            count(distinct id_job)    as job_cnt
        from #keyword_raw
        group by id_project

        union all

        select
            id_project,
            job_cpc_usd,
            count(distinct kw_hash64) as keyword_cnt,
            count(distinct id_job)    as job_cnt
        from #keyword_raw
        group by id_project, job_cpc_usd
    )
select *
into #kw_jobs_agg
from t;



with
    job_kw_keys as (
        select distinct
            id_project,
            kw_hash64,
            job_cpc_usd
        from #keyword_raw

        union all

        select distinct
            id_project,
            kw_hash64,
            -1 as job_cpc_usd
        from #keyword_raw
    )
select *
into #job_kw_keys
from job_kw_keys;


select
    id_project,
    job_cpc_usd,
    sum(search_cnt)                                                         as search_cnt,
    sum(impression_cnt_10)                                                  as impression_cnt_10,
    sum(impression_cnt_20)                                                  as impression_cnt_20,
    sum(impression_cnt_30)                                                  as impression_cnt_30,
    sum(impression_cnt_40)                                                  as impression_cnt_40,
    sum(impression_cnt_50)                                                  as impression_cnt_50,
    sum(impression_on_screen_cnt_10)                                        as impression_on_screen_cnt_10,
    sum(impression_on_screen_cnt_20)                                        as impression_on_screen_cnt_20,
    sum(impression_on_screen_cnt_30)                                        as impression_on_screen_cnt_30,
    sum(impression_on_screen_cnt_40)                                        as impression_on_screen_cnt_40,
    sum(impression_on_screen_cnt_50)                                        as impression_on_screen_cnt_50,
    sum(click_cnt_10)                                                       as click_cnt_10,
    sum(click_cnt_20)                                                       as click_cnt_20,
    sum(click_cnt_30)                                                       as click_cnt_30,
    sum(click_cnt_40)                                                       as click_cnt_40,
    sum(click_cnt_50)                                                       as click_cnt_50,
    case
        when sum(click_cnt_10) > 0
            then sum(click_cnt_10 * avg_cpc_usd_10) / sum(click_cnt_10) end as avg_cpc_usd_10,
    case
        when sum(click_cnt_20) > 0
            then sum(click_cnt_20 * avg_cpc_usd_20) / sum(click_cnt_20) end as avg_cpc_usd_20,
    case
        when sum(click_cnt_30) > 0
            then sum(click_cnt_30 * avg_cpc_usd_30) / sum(click_cnt_30) end as avg_cpc_usd_30,
    case
        when sum(click_cnt_40) > 0
            then sum(click_cnt_40 * avg_cpc_usd_40) / sum(click_cnt_40) end as avg_cpc_usd_40,
    case
        when sum(click_cnt_50) > 0
            then sum(click_cnt_50 * avg_cpc_usd_50) / sum(click_cnt_50) end as avg_cpc_usd_50
into #kw_serp_agg
from #job_kw_keys
     left join #serp_kw_stat
               on kw_hash64 = kw_hash
group by
    id_project,
    job_cpc_usd;



select
    @country_id                                   as country_id,
    datediff(day, '1900-01-01', dbo.ex_getdate()) as date_diff,
    coalesce(j.id_project, s.id_project)          as id_project,
    coalesce(j.job_cpc_usd, s.job_cpc_usd)        as job_cpc_usd,
    job_cnt,
    keyword_cnt,
    search_cnt,
    impression_cnt_10,
    impression_cnt_20,
    impression_cnt_30,
    impression_cnt_40,
    impression_cnt_50,
    impression_on_screen_cnt_10,
    impression_on_screen_cnt_20,
    impression_on_screen_cnt_30,
    impression_on_screen_cnt_40,
    impression_on_screen_cnt_50,
    click_cnt_10,
    click_cnt_20,
    click_cnt_30,
    click_cnt_40,
    click_cnt_50,
    avg_cpc_usd_10,
    avg_cpc_usd_20,
    avg_cpc_usd_30,
    avg_cpc_usd_40,
    avg_cpc_usd_50
from #kw_jobs_agg j
     full join #kw_serp_agg s
               on j.id_project = s.id_project
                   and j.job_cpc_usd = s.job_cpc_usd;




drop table #job_kw_keys;
drop table #kw_jobs_agg;
drop table #kw_serp_agg;
drop table #serp_kw_stat;
drop table #keyword_raw;


drop table #session_temp;
drop table #session_search_temp;
drop table #session_impression_on_screen_temp;
drop table #session_click_temp;
drop table #session_impression_temp;
drop table #job_region_temp;
drop table #job_temp;
