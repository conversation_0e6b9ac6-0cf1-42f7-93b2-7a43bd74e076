WITH features_agg AS (
    SELECT
    
        date_diff,
        country_id,
        screen,
        type AS action_type,
        COUNT(id_session_feature) AS interactions
    FROM
        imp.session_feature_action
    WHERE
        screen IN (12000, 12001)
        AND type != 12
    GROUP BY
        date_diff,
        country_id,
        screen,
        type
),
description AS (
    SELECT
        action_type,
        action_type_name
    FROM
        aggregation.dic_action_type
    WHERE
        metric = 'feature_action'
)
SELECT
    date_diff,
    country_id,
    c.name_country_eng AS country_name,
    screen,
    CASE
        WHEN screen = 12000 THEN 'Home'
        ELSE 'Stars'
    END AS screen_name,
    features_agg.action_type,
    description.action_type_name,
    interactions
FROM
    features_agg
    LEFT JOIN description
        ON features_agg.action_type = description.action_type
    LEFT JOIN dimension.countries c
        ON features_agg.country_id = c.id;
