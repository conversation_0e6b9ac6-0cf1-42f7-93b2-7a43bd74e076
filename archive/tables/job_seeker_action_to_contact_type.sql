-- call clicks from job seeker
select country_id,
       profile_id,
       jdp_viewed_datediff as action_datediff,
       jdp_viewed_datetime as action_datetime,
       action_type_id,
       sum((select conversion
        from profile.job_seeker_action_to_contact_conversion jsatcc
        where jsatcc.intention_to_contact_type_id = atc_cc.action_type_id)) as action_to_contact_prob,
	feature_id,
        order_action_id
from profile.job_seeker_action_to_contact_call_click  atc_cc
where jdp_viewed_datediff = ${DT_NOW} - 1
and not exists (select 1 from profile.job_seeker_action_to_contact_apply atc_a
                where atc_a.country_id = atc_cc.country_id
                 and atc_a.profile_id = atc_cc.profile_id
                 and atc_a.job_uid = atc_cc.job_uid)
group by country_id,
       profile_id,
       jdp_viewed_datediff,
       jdp_viewed_datetime,
       action_type_id,
	   feature_id,
        order_action_id
union all
-- applies from job seeker
select country_id,
       profile_id,
       apply_datediff as action_datediff,
       apply_datetime as action_datetime,
       action_type_id,
       sum((select conversion
        from profile.job_seeker_action_to_contact_conversion jsatcc
        where jsatcc.intention_to_contact_type_id = atc_cc.action_type_id)) as action_to_contact_prob,
	    feature_id,
        order_action_id
from profile.job_seeker_action_to_contact_apply atc_a
where apply_datediff = ${DT_NOW} - 1
group by country_id,
       profile_id,
       jdp_viewed_datediff,
       jdp_viewed_datetime,
       action_type_id,
	   feature_id,
        order_action_id

union all
-- message from job_seeker
select country_id,
       profile_id,
       action_datediff as action_datediff,
       action_datetime as action_datetime,
       action_type_id,
       sum((select conversion
        from profile.job_seeker_action_to_contact_conversion jsatcc
        where jsatcc.intention_to_contact_type_id = action_type_id)) as action_to_contact_prob,
	    feature_id,
        order_action_id
from profile.action_to_contact_message
where action_datediff = ${DT_NOW} - 1
  and platform_user_type_id = 1 /*job_seeker*/
group by country_id,
       profile_id,
       action_datediff,
       action_datetime,
       action_type_id,
	   feature_id,
        order_action_id 
;
