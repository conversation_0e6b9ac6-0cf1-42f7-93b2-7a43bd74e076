create or replace view profile.profile_jcoin_2_day_predict as
with
    a as (
        select country_id,

               fn_get_date_from_date_diff(seeker_cycle_first_datediff)                   as seeker_cycle_date,
               seeker_cycle_start_type_id,
               date(date_trunc('week',
                               fn_get_date_from_date_diff(seeker_cycle_first_datediff))) as seeker_cycle_week,
               sum(jcoin_2_days_cnt - jcoin_1_days_cnt)                                                      as x
        from profile.v_jcoin_per_profile jpp
        group by country_id,
                 fn_get_date_from_date_diff(seeker_cycle_first_datediff),
                 seeker_cycle_start_type_id
    ),
    x_moving_average as (
        select country_id,
               seeker_cycle_week,
               seeker_cycle_date,
               seeker_cycle_start_type_id,
               seeker_cycle_date - seeker_cycle_week as day_of_week,

               x,
               coalesce(avg(x)
                        over (partition by country_id, seeker_cycle_start_type_id order by seeker_cycle_date rows between 7 preceding and 1 preceding)
                   , x)
                                                     as moving_avg_7_days
        from a
    ),
    x_moving_average_delta as (
        select country_id,
               seeker_cycle_week,
               seeker_cycle_date,
               seeker_cycle_start_type_id,
               day_of_week,

               x,
               moving_avg_7_days,
               x - moving_avg_7_days as delta,
               coalesce(lag(x - moving_avg_7_days)
                        over (partition by country_id, seeker_cycle_start_type_id order by seeker_cycle_date)
                   , 0)              as prev_delta
        from x_moving_average
    ),
    x_moving_average_delta_s as (
        select country_id,
               seeker_cycle_week,
               seeker_cycle_date,
               seeker_cycle_start_type_id,
               day_of_week,

               x,
               moving_avg_7_days,
               delta,
               prev_delta,
               (delta - prev_delta)::numeric / moving_avg_7_days                                    as s,

               avg((delta - prev_delta)::numeric / moving_avg_7_days)
               over (partition by country_id,seeker_cycle_start_type_id,
                   day_of_week order by seeker_cycle_week rows between 8 preceding and 1 preceding) as
                                                                                                       s_pred_last_8_weeks
        from x_moving_average_delta
        order by seeker_cycle_week desc, seeker_cycle_date desc
    )
select country_id,
       seeker_cycle_week,
       seeker_cycle_date,
       seeker_cycle_start_type_id,
       x,
       moving_avg_7_days,
       delta,
       prev_delta,
       day_of_week,

       s,
       s_pred_last_8_weeks                                                      as s_pred,
       moving_avg_7_days + prev_delta + s_pred_last_8_weeks * moving_avg_7_days as x_pred
from x_moving_average_delta_s;