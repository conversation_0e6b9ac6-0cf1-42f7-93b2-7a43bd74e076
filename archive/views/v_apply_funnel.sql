create view v_apply_funnel
            (country, dt, mobile, returned, no_cv_vacancy, apply_version, apply_subversion, is_auth, have_cv,
             no_cv_apply_flow, id_traf_source, rank_apply_click, f1_views, f2_clicks, f3_click_select, f3_click_upload,
             f3_auto_attach, f3_click_create, f3_without_cv, f4_selected, f4_uploaded, f4_auto_attached, f4_created,
             f4_without_cv, f5_apply_selected, f5_apply_uploaded, f5_apply_auto_attached, f5_apply_created,
             f5_apply_without_cv, f1_views_prc, f2_clicks_prc, f3_click_select_prc, f3_click_upload_prc,
             f3_auto_attach_prc, f3_click_create_prc, f3_without_cv_prc, f4_selected_prc, f4_uploaded_prc,
             f4_auto_attached_prc, f4_created_prc, f4_without_cv_prc, f5_apply_selected_prc, f5_apply_uploaded_prc,
             f5_apply_auto_attached_prc, f5_apply_created_prc, f5_apply_without_cv_prc)
as
SELECT m_apply_funnel.country,
       m_apply_funnel.dt,
       m_apply_funnel.mobile,
       m_apply_funnel.returned,
       m_apply_funnel.no_cv_vacancy,
       m_apply_funnel.apply_version,
       m_apply_funnel.apply_subversion,
       m_apply_funnel.is_auth,
       m_apply_funnel.have_cv,
       m_apply_funnel.no_cv_apply_flow,
       m_apply_funnel.id_traf_source,
       m_apply_funnel.rank_apply_click,
       sum(m_apply_funnel.cnt_jdp_with_easy_apply_form)                                                       AS f1_views,
       sum(m_apply_funnel.applies_click)                                                                      AS f2_clicks,
       sum(m_apply_funnel.cv_select_clicks)                                                                   AS f3_click_select,
       sum(m_apply_funnel.cv_upload_clicks)                                                                   AS f3_click_upload,
       sum(m_apply_funnel.cv_auto_attaches)                                                                   AS f3_auto_attach,
       sum(m_apply_funnel.cv_create_clicks)                                                                   AS f3_click_create,
       sum(m_apply_funnel.no_cv_clicks)                                                                       AS f3_without_cv,
       sum(m_apply_funnel.cv_selections)                                                                      AS f4_selected,
       sum(m_apply_funnel.cv_uploads)                                                                         AS f4_uploaded,
       sum(m_apply_funnel.cv_auto_attaches)                                                                   AS f4_auto_attached,
       sum(m_apply_funnel.cv_creations)                                                                       AS f4_created,
       sum(m_apply_funnel.no_cv_clicks)                                                                       AS f4_without_cv,
       sum(m_apply_funnel.applies_submit_with_cv_selected)                                                    AS f5_apply_selected,
       sum(m_apply_funnel.applies_submit_with_cv_uploaded)                                                    AS f5_apply_uploaded,
       sum(m_apply_funnel.applies_submit_with_cv_auto_attached)                                               AS f5_apply_auto_attached,
       sum(m_apply_funnel.applies_submit_with_cv_created)                                                     AS f5_apply_created,
       sum(GREATEST((m_apply_funnel.applies_submit - m_apply_funnel.applies_submit_with_cv_selected -
                     m_apply_funnel.applies_submit_with_cv_uploaded::numeric -
                     m_apply_funnel.applies_submit_with_cv_auto_attached::numeric)::double precision -
                    m_apply_funnel.applies_submit_with_cv_created,
                    0::double precision))                                                                     AS f5_apply_without_cv,
       sum(m_apply_funnel.cnt_jdp_with_easy_apply_form) /
       sum(m_apply_funnel.cnt_jdp_with_easy_apply_form)                                                       AS f1_views_prc,
       sum(m_apply_funnel.applies_click) /
       sum(m_apply_funnel.cnt_jdp_with_easy_apply_form)                                                       AS f2_clicks_prc,
       sum(m_apply_funnel.cv_select_clicks) /
       sum(m_apply_funnel.cnt_jdp_with_easy_apply_form)                                                       AS f3_click_select_prc,
       sum(m_apply_funnel.cv_upload_clicks)::numeric /
       sum(m_apply_funnel.cnt_jdp_with_easy_apply_form)                                                       AS f3_click_upload_prc,
       sum(m_apply_funnel.cv_auto_attaches)::numeric /
       sum(m_apply_funnel.cnt_jdp_with_easy_apply_form)                                                       AS f3_auto_attach_prc,
       sum(m_apply_funnel.cv_create_clicks) /
       sum(m_apply_funnel.cnt_jdp_with_easy_apply_form)                                                       AS f3_click_create_prc,
       sum(m_apply_funnel.no_cv_clicks) /
       sum(m_apply_funnel.cnt_jdp_with_easy_apply_form)                                                       AS f3_without_cv_prc,
       sum(m_apply_funnel.cv_selections) /
       sum(m_apply_funnel.cnt_jdp_with_easy_apply_form)                                                       AS f4_selected_prc,
       sum(m_apply_funnel.cv_uploads)::numeric /
       sum(m_apply_funnel.cnt_jdp_with_easy_apply_form)                                                       AS f4_uploaded_prc,
       sum(m_apply_funnel.cv_auto_attaches)::numeric /
       sum(m_apply_funnel.cnt_jdp_with_easy_apply_form)                                                       AS f4_auto_attached_prc,
       sum(m_apply_funnel.cv_creations) /
       sum(m_apply_funnel.cnt_jdp_with_easy_apply_form)::double precision                                     AS f4_created_prc,
       sum(m_apply_funnel.no_cv_clicks) /
       sum(m_apply_funnel.cnt_jdp_with_easy_apply_form)                                                       AS f4_without_cv_prc,
       sum(m_apply_funnel.applies_submit_with_cv_selected) /
       sum(m_apply_funnel.cnt_jdp_with_easy_apply_form)                                                       AS f5_apply_selected_prc,
       sum(m_apply_funnel.applies_submit_with_cv_uploaded)::numeric /
       sum(m_apply_funnel.cnt_jdp_with_easy_apply_form)                                                       AS f5_apply_uploaded_prc,
       sum(m_apply_funnel.applies_submit_with_cv_auto_attached)::numeric /
       sum(m_apply_funnel.cnt_jdp_with_easy_apply_form)                                                       AS f5_apply_auto_attached_prc,
       sum(m_apply_funnel.applies_submit_with_cv_created) /
       sum(m_apply_funnel.cnt_jdp_with_easy_apply_form)::double precision                                     AS f5_apply_created_prc,
       sum(GREATEST((m_apply_funnel.applies_submit - m_apply_funnel.applies_submit_with_cv_selected -
                     m_apply_funnel.applies_submit_with_cv_uploaded::numeric -
                     m_apply_funnel.applies_submit_with_cv_auto_attached::numeric)::double precision -
                    m_apply_funnel.applies_submit_with_cv_created, 0::double precision)) /
       sum(m_apply_funnel.cnt_jdp_with_easy_apply_form)::double precision                                     AS f5_apply_without_cv_prc
FROM product.m_apply_funnel
GROUP BY m_apply_funnel.country, m_apply_funnel.dt, m_apply_funnel.mobile, m_apply_funnel.returned,
         m_apply_funnel.no_cv_vacancy, m_apply_funnel.apply_version, m_apply_funnel.apply_subversion,
         m_apply_funnel.is_auth, m_apply_funnel.have_cv, m_apply_funnel.no_cv_apply_flow, m_apply_funnel.id_traf_source,
         m_apply_funnel.rank_apply_click;

alter table v_apply_funnel
    owner to rlu;
