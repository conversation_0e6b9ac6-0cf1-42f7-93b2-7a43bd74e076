create view archive.v_abandoned_profile_viber_messages
            (country, date_diff, date_sent_message, id_account, status, id, profile_id, has_link_visit,
             has_submit_profile) as
WITH abandoned_profile_messages AS (SELECT viber_message_sent.country,
                                           viber_message_sent.date_diff,
                                           viber_message_sent.date::date AS date_sent_message,
                                           viber_message_sent.id_account,
                                           viber_message_sent.status,
                                           viber_message_sent.id
                                    FROM imp.viber_message_sent
                                    WHERE viber_message_sent.country = 1
                                      AND viber_message_sent.message_type = 2)
SELECT DISTINCT apm.country,
                apm.date_diff,
                apm.date_sent_message,
                apm.id_account,
                apm.status,
                apm.id,
                pa.profile_id,
                CASE
                    WHEN sp.id_profile IS NOT NULL THEN 1
                    ELSE 0
                    END AS has_link_visit,
                CASE
                    WHEN spa.id_session IS NOT NULL THEN 1
                    ELSE 0
                    END AS has_submit_profile
FROM abandoned_profile_messages apm
LEFT JOIN profile.v_profile_account pa ON apm.country = pa.country_id AND apm.id_account = pa.account_id
LEFT JOIN imp.session_profile sp ON pa.country_id = sp.country AND pa.profile_id = sp.id_profile AND sp.source = 9
LEFT JOIN imp.session_profile_action spa
          ON sp.country = spa.country AND sp.date_diff = spa.date_diff AND sp.id_session = spa.id_session AND
             spa.type = 4 AND spa.screen = 2006;

alter table archive.v_abandoned_profile_viber_messages
    owner to nsh;

grant select on archive.v_abandoned_profile_viber_messages to readonly;

grant select on archive.v_abandoned_profile_viber_messages to math;

grant select on archive.v_abandoned_profile_viber_messages to writeonly_pyscripts;

grant select on archive.v_abandoned_profile_viber_messages to "pavlo.kvasnii";

