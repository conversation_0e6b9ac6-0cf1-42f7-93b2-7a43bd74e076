create view archive.v_profile_account_session
            (country_id, profile_id, session_id, session_datediff, sign_in_datetime, sign_out_datetime, account_id) as
SELECT ps.country_id,
       ps.profile_id,
       ps.session_id,
       ps.session_datediff,
       ps.sign_in_datetime,
       ps.sign_out_datetime,
       pa.account_id
FROM profile.profile_session ps
JOIN profile.v_profile_account pa ON pa.country_id = ps.country_id AND ps.profile_id = pa.profile_id
WHERE ps.session_datediff >= 44218;

alter table archive.v_profile_account_session
    owner to nsh;

grant select on archive.v_profile_account_session to readonly;

grant select on archive.v_profile_account_session to math;

grant select on archive.v_profile_account_session to writeonly_pyscripts;

grant select on archive.v_profile_account_session to "pavlo.kvasnii";

