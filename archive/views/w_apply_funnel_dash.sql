create view w_apply_funnel_dash
            (country, dt, mobile_id, mobile, returned_id, returned, no_cv_vacancy_id, no_cv_vacancy, apply_version,
             apply_subversion, rank_apply_click, is_auth, have_cv, id_traf_source, no_cv_apply_flow, funnel_stage_name,
             stage_number, stage_number_name, stage_value, views, clicks_apply, traf_source_name)
as
SELECT f.country,
       f.dt,
       f.mobile                                   AS mobile_id,
       (SELECT im.name
        FROM product.info_mobile im
        WHERE im.mobile = f.mobile)               AS mobile,
       f.returned                                 AS returned_id,
       (SELECT ir.name
        FROM product.info_returned ir
        WHERE ir.returned = f.returned)           AS returned,
       f.no_cv_vacancy                            AS no_cv_vacancy_id,
       (SELECT iv.name
        FROM product.info_no_cv_vacancy iv
        WHERE iv.no_cv_vacancy = f.no_cv_vacancy) AS no_cv_vacancy,
       f.apply_version,
       f.apply_subversion,
       f.rank_apply_click,
       f.is_auth,
       f.have_cv,
       f.id_traf_source,
       f.no_cv_apply_flow,
       i.funnel_stage_name,
       i.stage_number,
       isn.stage_number_name,
       sum(
               CASE
                   WHEN i.funnel_stage_name::text = 'view'::text THEN f.f1_views::double precision
                   WHEN i.funnel_stage_name::text = 'click apply'::text THEN f.f2_clicks::double precision
                   WHEN i.funnel_stage_name::text = 'click create'::text THEN f.f3_click_create::double precision
                   WHEN i.funnel_stage_name::text = 'click select'::text THEN f.f3_click_select::double precision
                   WHEN i.funnel_stage_name::text = 'click upload'::text THEN f.f3_click_upload::double precision
                   WHEN i.funnel_stage_name::text = 'auto-attach'::text THEN f.f3_auto_attach::double precision
                   WHEN i.funnel_stage_name::text = 'without cv flow'::text AND i.stage_number = 3
                       THEN f.f3_without_cv::double precision
                   WHEN i.funnel_stage_name::text = 'selected'::text THEN f.f4_selected::double precision
                   WHEN i.funnel_stage_name::text = 'uploaded'::text THEN f.f4_uploaded::double precision
                   WHEN i.funnel_stage_name::text = 'auto-attached'::text THEN f.f4_auto_attached::double precision
                   WHEN i.funnel_stage_name::text = 'created'::text THEN f.f4_created
                   WHEN i.funnel_stage_name::text = 'without cv flow'::text AND i.stage_number = 4
                       THEN f.f4_without_cv::double precision
                   WHEN i.funnel_stage_name::text = 'apply selected'::text THEN f.f5_apply_selected::double precision
                   WHEN i.funnel_stage_name::text = 'apply uploaded'::text THEN f.f5_apply_uploaded::double precision
                   WHEN i.funnel_stage_name::text = 'apply auto-attached'::text
                       THEN f.f5_apply_auto_attached::double precision
                   WHEN i.funnel_stage_name::text = 'apply created'::text THEN f.f5_apply_created
                   WHEN i.funnel_stage_name::text = 'apply without cv'::text THEN f.f5_apply_without_cv
                   ELSE NULL::double precision
                   END)                           AS stage_value,
       sum(f.f1_views)                            AS views,
       sum(f.f2_clicks)                           AS clicks_apply,
       (SELECT it.name
        FROM product.info_traffic_source it
        WHERE it.country::text = f.country::text
          AND it.id = f.id_traf_source)           AS traf_source_name
FROM archive.v_apply_funnel f
LEFT JOIN archive.info_funnel_stages i ON 1 = 1
LEFT JOIN archive.info_funnel_stages_number isn ON isn.stage_number = i.stage_number
GROUP BY f.country, f.dt, f.mobile, f.returned, f.no_cv_vacancy, f.apply_version, f.apply_subversion, f.is_auth,
         f.have_cv, f.no_cv_apply_flow, i.funnel_stage_name, i.stage_number, isn.stage_number_name, f.id_traf_source,
         f.rank_apply_click;

alter table w_apply_funnel_dash
    owner to rlu;
