create view v_ltv7_responds
            (country, first_day, client_id, id_traf_source, group319, clicks_calls, applies_click, aways,
             applies_submit) as
SELECT nu. country,
       nu. first_day,
       nu. client_id,
       s.id_traf_source,
       t."group"             AS group319,
       sum(s.clicks_calls)   AS clicks_calls,
       sum(s.applies_click)  AS applies_click,
       sum(s.aways)          AS aways,
       sum(s.applies_submit) AS applies_submit
FROM archive. ltv_new_users nu
JOIN archive. ltv_sessions s ON nu. client_id = s.client_id AND nu. country::text = s.country::text
JOIN archive. ltv_clients_tests t ON nu. client_id = t. client_id AND t. id_test = 319 AND nu. country::text = t. country::text
WHERE nu. first_day <= ((SELECT max(sd. dt) - '7 days'::interval
                        FROM archive. ltv_sessions sd))
  AND s.mobile = 1
GROUP BY nu. country, nu. first_day, nu. client_id, s.id_traf_source, t."group";

alter table v_ltv7_responds
    owner to dap;
