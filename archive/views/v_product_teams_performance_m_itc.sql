create view v_product_teams_performance_m_itc(dt_month, team_name, growth_bp, growth_itc) as
SELECT date_trunc('month'::text, ab. date_launch::timestamp without time zone)  AS dt_month,
       ab. team_name,
       exp(ln(sum(ab. growth_bp - 1::double precision) + 1::double precision))  AS growth_bp,
       exp(ln(sum(ab. growth_itc - 1::double precision) + 1::double precision)) AS growth_itc
FROM product. v_product_performance_ab_itc ab
WHERE ab. date_launch >= '2020-01-01'::date
GROUP BY (date_trunc('month'::text, ab. date_launch::timestamp without time zone)), ab. team_name;

alter table v_product_teams_performance_m_itc
    owner to dap;
