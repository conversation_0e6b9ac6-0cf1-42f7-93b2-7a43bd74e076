create view v_cv_apply_rate
            (device_type, had_one_apply, had_one_profile_apply, is_profile_apply, jdp_viewed_datediff, apply_click_cnt,
             apply_success_cnt)
as
SELECT j. device_type,
       CASE
           WHEN j. jdp_viewed_datetime > clla. first_apply_datetime THEN 1
           ELSE 0
           END                                    AS had_one_apply,
       CASE
           WHEN j. jdp_viewed_datetime > clfpa. first_apply_datetime THEN 1
           ELSE 0
           END                                    AS had_one_profile_apply,
       sign((j. apply_flag & 4)::double precision) AS is_profile_apply,
       j. jdp_viewed_datediff,
       sum(afc. apply_click)                       AS apply_click_cnt,
       sum(afc. is_success)                        AS apply_success_cnt
FROM dte. apply_jdp j
JOIN dte. application_form_conversion afc
     ON j. country_id = afc. country_id AND j. jdp_viewed_datediff = afc. jdp_viewed_datediff AND j. jdp_id = afc. jdp_id
LEFT JOIN archive. cookie_label_first_cv_apply clla
          ON clla. country_id = j. country_id AND clla. cookie_label = j. cookie_label
LEFT JOIN archive. cookie_label_first_profile_apply clfpa
          ON clfpa. country_id = j. country_id AND clfpa. cookie_label = j. cookie_label
WHERE j. country_id = 1
  AND (j. apply_flag & 1) = 1
  AND (j. jdp_flag & 1) = 1
GROUP BY j. device_type,
         (
             CASE
                 WHEN j. jdp_viewed_datetime > clla. first_apply_datetime THEN 1
                 ELSE 0
                 END),
         (
             CASE
                 WHEN j. jdp_viewed_datetime > clfpa. first_apply_datetime THEN 1
                 ELSE 0
                 END), (sign((j. apply_flag & 4)::double precision)), j. jdp_viewed_datediff;

alter table v_cv_apply_rate
    owner to dap;
