create view archive.v_subscription_offer_viber_messages_tsts
            (country, account_id, date_diff, date_sent_message, status, profile_id, has_link_visit, is_subscribe) as
WITH subscription_offer_messages AS (SELECT viber_message_sent.country,
                                            viber_message_sent.date_diff,
                                            viber_message_sent.date::date AS date_sent_message,
                                            viber_message_sent.id_account,
                                            viber_message_sent.status
                                     FROM imp.viber_message_sent
                                     WHERE viber_message_sent.country = 1
                                       AND viber_message_sent.message_type = 3
                                       AND (viber_message_sent.id_account = ANY
                                            (ARRAY [3876605::bigint, 4471426::bigint]))),
     subscribe_screen AS (SELECT DISTINCT session_action.id_session,
                                          session_action.date_diff,
                                          session_action.date::date AS date_action,
                                          CASE
                                              WHEN min(session_action.type)
                                                   OVER (PARTITION BY session_action.date_diff, session_action.id_session) =
                                                   1001 THEN 1
                                              ELSE 0
                                              END                   AS is_subscribe,
                                          session_action.country
                          FROM imp.session_action
                          WHERE (session_action.type = ANY (ARRAY [2000, 1001]))
                            AND session_action.screen = 3001
                            AND session_action.country = 1
                            AND session_action.date_diff >= 44219),
     all_a AS (SELECT som.country,
                      som.date_diff,
                      som.date_sent_message,
                      som.id_account,
                      som.status,
                      pas.profile_id,
                      max(
                              CASE
                                  WHEN ss.id_session IS NOT NULL THEN 1
                                  ELSE 0
                                  END)                  AS has_link_visit,
                      max(COALESCE(ss.is_subscribe, 0)) AS is_subscribe
               FROM subscription_offer_messages som
               LEFT JOIN archive.v_profile_account_session pas
                         ON som.country = pas.country_id AND som.id_account = pas.account_id
               LEFT JOIN subscribe_screen ss ON pas.country_id = ss.country AND pas.session_id = ss.id_session AND
                                                pas.session_datediff = ss.date_diff
               GROUP BY som.country, som.id_account, som.date_diff, som.date_sent_message, som.status, pas.profile_id)
SELECT DISTINCT all_a.country,
                all_a.id_account AS account_id,
                all_a.date_diff,
                all_a.date_sent_message,
                all_a.status,
                all_a.profile_id,
                all_a.has_link_visit,
                all_a.is_subscribe
FROM all_a;

alter table archive.v_subscription_offer_viber_messages_tsts
    owner to nsh;

grant select on archive.v_subscription_offer_viber_messages_tsts to readonly;

grant select on archive.v_subscription_offer_viber_messages_tsts to math;

grant select on archive.v_subscription_offer_viber_messages_tsts to writeonly_pyscripts;

grant select on archive.v_subscription_offer_viber_messages_tsts to "pavlo.kvasnii";

