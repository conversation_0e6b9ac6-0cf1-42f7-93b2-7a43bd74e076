create view v_ab_test_results_ds
            (test, test_type, dt_start, dt_end, country, team_id, team_name, group_in_prod, is_in_prod, date_launch,
             itc_growth, bp_growth, revenue_growth, itc_growth_significance, bp_growth_significance,
             revenue_growth_significance, itc_scalability, bp_scalability, revenue_scalability)
as
SELECT atrd. test,
       atrd. test_type,
       min(atrd. dt_start)            AS dt_start,
       max(atrd. dt_end)              AS dt_end,
       atrd. country,
       iatd. team_id,
       (SELECT ipt. name
        FROM product. info_product_teams ipt
        WHERE ipt. id = iatd. team_id) AS team_name,
       iatd. group_in_prod,
       iatd. is_in_prod,
       iatd. date_launch,
       min(
               CASE
                   WHEN atrd. metric::text = 'intention_to_contact_per_user'::text
                       THEN atrd. b_mean / atrd. a_mean - 1.00::double precision
                   ELSE NULL::double precision
                   END)              AS itc_growth,
       min(
               CASE
                   WHEN atrd. metric::text = 'EA_Business_Metric_2020_per_user'::text
                       THEN atrd. b_mean / atrd. a_mean - 1.00::double precision
                   ELSE NULL::double precision
                   END)              AS bp_growth,
       min(
               CASE
                   WHEN atrd. metric::text = 'revenue_per_user'::text
                       THEN atrd. b_mean / atrd. a_mean - 1.00::double precision
                   ELSE NULL::double precision
                   END)              AS revenue_growth,
       min(
               CASE
                   WHEN atrd. metric::text = 'intention_to_contact_per_user'::text THEN
                       CASE
                           WHEN atrd. b_ci_lower > atrd. a_ci_upper THEN 1
                           WHEN atrd. b_ci_upper < atrd. a_ci_lower THEN 1
                           WHEN atrd. p_value <= 0.05::double precision THEN 1
                           ELSE 0
                           END
                   ELSE NULL::integer
                   END)::boolean     AS itc_growth_significance,
       min(
               CASE
                   WHEN atrd. metric::text = 'EA_Business_Metric_2020_per_user'::text THEN
                       CASE
                           WHEN atrd. b_ci_lower > atrd. a_ci_upper THEN 1
                           WHEN atrd. b_ci_upper < atrd. a_ci_lower THEN 1
                           WHEN atrd. p_value <= 0.05::double precision THEN 1
                           ELSE 0
                           END
                   ELSE NULL::integer
                   END)::boolean     AS bp_growth_significance,
       min(
               CASE
                   WHEN atrd. metric::text = 'revenue_per_user'::text THEN
                       CASE
                           WHEN atrd. b_ci_lower > atrd. a_ci_upper THEN 1
                           WHEN atrd. b_ci_upper < atrd. a_ci_lower THEN 1
                           WHEN atrd. p_value <= 0.05::double precision THEN 1
                           ELSE 0
                           END
                   ELSE NULL::integer
                   END)::boolean     AS revenue_growth_significance,
       avg(atsd. scalability_itc)     AS itc_scalability,
       avg(atsd. scalability_bp)      AS bp_scalability,
       avg(atsd. scalability_revenue) AS revenue_scalability
FROM archive. ab_tests_results_ds atrd
LEFT JOIN archive. info_ab_tests_ds iatd ON iatd. test_type::text = atrd. test_type::text AND iatd. test = atrd. test AND
                                           iatd. country::text = atrd. country::text AND
                                           iatd. group_in_prod = "substring"(atrd. groups_vs::text, 6, 1)::integer
LEFT JOIN product. ab_tests_scalability_ds atsd
          ON atsd. test_type::text = atrd. test_type::text AND atsd. test_id = atrd. test AND
             atsd. country::text = atrd. country::text
GROUP BY atrd. test, atrd. test_type, atrd. country, iatd. team_id, iatd. group_in_prod, iatd. is_in_prod, iatd. date_launch;

alter table v_ab_test_results_ds
    owner to dap;
