create view email_vv_accounts
            (dt, alpha_2, source, source_name, device_type_id, type, users, verify_users, unsub_users) as
SELECT a.date_add::date     AS dt,
       c.alpha_2,
       a.source,
       aus.source_name,
       case
           when a.flags & 1 = 1 then 1 /*mobile*/
           when a.flags & 1 = 0 and a.source < 1000 then 0 /*desktop*/
           when a.source >= 1000 then 2 /*mobile app*/
           end              as device_type_id,
       CASE
           WHEN ac.type = 0 THEN 'email'::text
           WHEN ac.type = 1 THEN 'phone'::text
           ELSE NULL::text
           END              AS type,
       count(DISTINCT a.id) AS users,
       count(DISTINCT
             CASE
                 WHEN ac.verify_date IS NOT NULL THEN ac.id_account
                 ELSE NULL::integer
                 END)       AS verify_users,
       count(DISTINCT
             CASE
                 WHEN ai.unsub_date IS NOT NULL THEN ai.id_account
                 ELSE NULL::integer
                 END)       AS unsub_users
FROM imp.account a
         JOIN dimension.countries c ON c.id = a.country AND c.id <= 11
         JOIN archive.account_contact ac ON ac.id_account = a.id AND ac.country = a.country
         JOIN imp.account_info ai ON ai.id_account = a.id AND ai.country = a.country
         LEFT JOIN dimension.auth_source aus ON aus.id = a.source
WHERE a.date_add::date >= '2020-07-01'::date
  AND a.country <= 11
GROUP BY a.date_add::date,
         c.alpha_2,
         a.source,
         aus.source_name,
         case
             when a.flags & 1 = 1 then 1
             when a.flags & 1 = 0 and a.source < 1000 then 0
             when a.source >= 1000 then 2
             end,
         CASE
             WHEN ac.type = 0 THEN 'email'::text
             WHEN ac.type = 1 THEN 'phone'::text
             ELSE NULL::text
             END;

alter table email_vv_accounts
    owner to vni;

grant select on email_vv_accounts to npo;

grant select on email_vv_accounts to readonly;

grant delete, insert, select, update on email_vv_accounts to nsh;

grant select on email_vv_accounts to writeonly_product;

grant select on email_vv_accounts to writeonly_pyscripts;

grant select on email_vv_accounts to "pavlo.kvasnii";
