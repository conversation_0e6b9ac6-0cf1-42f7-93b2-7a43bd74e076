create view v_cv_quality_raw
            (country, cv_id, cvb_version, date_created, name, name_is, phone, phone_is, email, email_is, city, city_is,
             photo_is, position_is, expirience_title, expirience_is, education_name, education_is, skill_value,
             skills_is, age_is, employment_type_is, relocation_type_is, salary_is)
as
SELECT cv1.country,
       cv1.id                 AS cv_id,
       cv1.cvb_version,
       cv1.date_created::date AS date_created,
       cv1.name,
       CASE
           WHEN length(cv1.name) >= 3 THEN 1
           ELSE 0
           END                AS name_is,
       cv1.phone,
       CASE
           WHEN length(cv1.phone::text) >= 10 THEN 1
           ELSE 0
           END                AS phone_is,
       cv1.email,
       CASE
           WHEN length(cv1.email) >= 3 THEN 1
           ELSE 0
           END                AS email_is,
       cv1.city,
       CASE
           WHEN length(cv1.city) >= 3 THEN 1
           ELSE 0
           END                AS city_is,
       0                      AS photo_is,
       0                      AS position_is,
       cv1.expirience_title_1 AS expirience_title,
       CASE
           WHEN (COALESCE(length(cv1.expirience_title_1), 0) + COALESCE(length(cv1.expirience_name_1), 0) +
                 COALESCE(length(cv1.expirience_city_1), 0) + COALESCE(length(cv1.expirience_descr_1), 0) +
                 COALESCE(length(cv1.expirience_title_2), 0) + COALESCE(length(cv1.expirience_name_2), 0) +
                 COALESCE(length(cv1.expirience_city_2), 0) + COALESCE(length(cv1.expirience_descr_2), 0)) >= 40 THEN 1
           ELSE 0
           END                AS expirience_is,
       cv1.education_name_1   AS education_name,
       CASE
           WHEN (COALESCE(length(cv1.education_degree_1), 0) + COALESCE(length(cv1.education_name_1), 0) +
                 COALESCE(length(cv1.education_occupation_1), 0) + COALESCE(length(cv1.education_city_1), 0) +
                 COALESCE(length(cv1.education_degree_2), 0) + COALESCE(length(cv1.education_name_2), 0) +
                 COALESCE(length(cv1.education_occupation_2), 0) + COALESCE(length(cv1.education_city_2), 0)) >= 40
               THEN 1
           ELSE 0
           END                AS education_is,
       cv1.skills_value_1     AS skill_value,
       CASE
           WHEN (COALESCE(length(cv1.skills_value_1), 0) + COALESCE(length(cv1.skills_value_2), 0) +
                 COALESCE(length(cv1.skills_value_3), 0) + COALESCE(length(cv1.skills_value_4), 0) +
                 COALESCE(length(cv1.skills_value_5), 0) + COALESCE(length(cv1.skills_value_6), 0) +
                 COALESCE(length(cv1.skills_value_7), 0) + COALESCE(length(cv1.skills_value_8), 0)) >= 15 THEN 1
           ELSE 0
           END                AS skills_is,
       0                      AS age_is,
       0                      AS employment_type_is,
       0                      AS relocation_type_is,
       0                      AS salary_is
FROM archive. cv_parsed_1_0 cv1
UNION
SELECT cv2.country,
       cv2.id                 AS cv_id,
       cv2.cvb_version,
       cv2.date_created::date AS date_created,
       cv2.name,
       CASE
           WHEN length(cv2.name::text) >= 3 THEN 1
           ELSE 0
           END                AS name_is,
       cv2.phone,
       CASE
           WHEN length(cv2.phone::text) >= 10 THEN 1
           ELSE 0
           END                AS phone_is,
       cv2.email,
       CASE
           WHEN length(cv2.email::text) >= 3 THEN 1
           ELSE 0
           END                AS email_is,
       cv2.city,
       CASE
           WHEN length(cv2.city::text) >= 3 THEN 1
           ELSE 0
           END                AS city_is,
       cv2.has_photo          AS photo_is,
       CASE
           WHEN cv2."position" IS NOT NULL THEN 1
           ELSE 0
           END                AS position_is,
       cv2.expirience_title_1 AS expirience_title,
       CASE
           WHEN (COALESCE(length(cv2.expirience_title_1::text), 0) + COALESCE(length(cv2.expirience_name_1::text), 0) +
                 COALESCE(length(cv2.expirience_responsibilities_1), 0) +
                 COALESCE(length(cv2.expirience_title_2::text), 0) + COALESCE(length(cv2.expirience_name_2::text), 0) +
                 COALESCE(length(cv2.expirience_responsibilities_1), 0)) >= 40 THEN 1
           ELSE 0
           END                AS expirience_is,
       cv2.education_name_1   AS education_name,
       CASE
           WHEN (COALESCE(length(cv2.education_degree_id_1::text), 0) * 7 +
                 COALESCE(length(cv2.education_name_1::text), 0) +
                 COALESCE(length(cv2.education_occupation_1::text), 0) +
                 CASE
                     WHEN cv2.education_grad_year_1 IS NOT NULL THEN 4
                     ELSE 0
                     END + COALESCE(length(cv2.education_degree_id_2::text), 0) * 7 +
                 COALESCE(length(cv2.education_name_2::text), 0) +
                 COALESCE(length(cv2.education_occupation_2::text), 0) +
                 CASE
                     WHEN cv2.education_grad_year_2 IS NOT NULL THEN 4
                     ELSE 0
                     END) >= 40 THEN 1
           ELSE 0
           END                AS education_is,
       cv2.skills_value_1     AS skill_value,
       CASE
           WHEN COALESCE(length(cv2.skills_value_1), 0) >= 15 THEN 1
           ELSE 0
           END                AS skills_is,
       CASE
           WHEN
               CASE
                   WHEN cv2.yearofbirth IS NOT NULL THEN true
                   ELSE false
                   END THEN 1
           ELSE 0
           END                AS age_is,
       CASE
           WHEN cv2.employmenttypes_id_1 IS NOT NULL THEN 1
           ELSE 0
           END                AS employment_type_is,
       CASE
           WHEN cv2.relocation_type_id IS NOT NULL THEN 1
           ELSE 0
           END                AS relocation_type_is,
       CASE
           WHEN cv2.salary_amount IS NOT NULL THEN 1
           ELSE 0
           END                AS salary_is
FROM archive. cv_parsed_2_0 cv2;

alter table v_cv_quality_raw
    owner to dap;
