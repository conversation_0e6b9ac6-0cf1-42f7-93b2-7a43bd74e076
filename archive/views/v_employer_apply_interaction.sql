create view v_employer_apply_interaction
            (database_source_id, apply_id, subscription_id, packet_rank, packet_id, packet_type_paid_result_id,
             employer_id, cdp_id, job_id, is_abroad, profile_id, profile_link, recieved_datediff, recieved_datetime,
             viewed_datediff, viewed_datetime, open_contact_datediff, open_contact_datetime, company_name,
             packet_short_name, job_title)
as
SELECT eai. database_source_id,
       eai. apply_id,
       eai. subscription_id,
       eai. packet_rank,
       eai. packet_id,
       eai. packet_type_paid_result_id,
       eai. employer_id,
       eai. cdp_id,
       eai. job_id,
       eai. is_abroad,
       eai. profile_id,
       'https:// ua. jooble. org/ profilePreview/'::text || p. id_public::text AS profile_link,
       eai. recieved_datediff,
       eai. recieved_datetime,
       eai. viewed_datediff,
       eai. viewed_datetime,
       eai. open_contact_datediff,
       eai. open_contact_datetime,
       (SELECT ec. company_name
        FROM imp_employer. employer_cdp ec
        WHERE ec. sources = eai. database_source_id
          AND ec. id = eai. cdp_id)                                         AS company_name,
       (SELECT p_1.short_name
        FROM imp_employer. packet p_1
        WHERE p_1.sources = eai. database_source_id
          AND p_1.id = eai. packet_id)                                     AS packet_short_name,
       j. title                                                            AS job_title
FROM archive. employer_apply_interaction eai
JOIN imp. profiles p ON p. id = eai. profile_id
LEFT JOIN imp_employer. job j ON j. sources = eai. database_source_id AND j. id = eai. job_id;

alter table v_employer_apply_interaction
    owner to dap;
