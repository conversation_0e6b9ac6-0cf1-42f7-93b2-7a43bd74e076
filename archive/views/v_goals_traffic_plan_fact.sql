create view v_goals_traffic_plan_fact(dt_month, sessions, plan_fact_type) as
SELECT goals_traffic_plan.dt_month,
       goals_traffic_plan.sessions,
       'goal'::text AS plan_fact_type
FROM archive.goals_traffic_plan
UNION ALL
SELECT date_trunc('month'::text, goals_traffic.date::timestamp with time zone) AS dt_month,
       goals_traffic.sessions,
       'progress'::text                                                        AS plan_fact_type
FROM archive.goals_traffic;

alter table v_goals_traffic_plan_fact
    owner to dap;
