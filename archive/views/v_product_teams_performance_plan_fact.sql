create view v_product_teams_performance_plan_fact(dt_month, team_name, growth_bp, growth_itc, plan_fact_type) as
SELECT product_teams_performance_plan. dt_month,
       (SELECT ipt. name
        FROM product. info_product_teams ipt
        WHERE product_teams_performance_plan. team_id = ipt. id)   AS team_name,
       product_teams_performance_plan. growth_bp,
       product_teams_performance_plan. growth_itc,
       concat(product_teams_performance_plan. goal_type, ' goal') AS plan_fact_type
FROM archive. product_teams_performance_plan
UNION
SELECT v_product_teams_performance_m. dt_month,
       v_product_teams_performance_m. team_name,
       v_product_teams_performance_m. growth_bp,
       v_product_teams_performance_m. growth_itc,
       'progress'::text AS plan_fact_type
FROM product. v_product_teams_performance_m
UNION
SELECT '2020-01-01'::date                                      AS dt_month,
       (SELECT ipt. name
        FROM product. info_product_teams ipt
        WHERE product_teams_performance_plan. team_id = ipt. id) AS team_name,
       1                                                       AS growth_bp,
       1                                                       AS growth_itc,
       'progress'::text                                        AS plan_fact_type
FROM archive. product_teams_performance_plan
GROUP BY product_teams_performance_plan. team_id;

alter table v_product_teams_performance_plan_fact
    owner to dap;
