create view v_chat_bot_users_without_phone
            (date_diff, chat_bot_source_name, user_without_phone_cnt, user_without_phone_with_chat_bot_cnt) as
SELECT cb. date_diff,
       c.name                     AS chat_bot_source_name,
       count(DISTINCT cb. chat_id) AS user_without_phone_cnt,
       count(DISTINCT
             CASE
                 WHEN cb. is_deleted_bot = 0 THEN cb. chat_id
                 ELSE NULL::character varying
                 END)             AS user_without_phone_with_chat_bot_cnt
FROM archive. chat_bot_user_info cb
JOIN imp. channel c ON cb. channel_id = c.id
WHERE cb. phone IS NULL
GROUP BY cb. date_diff, c.name;

alter table v_chat_bot_users_without_phone
    owner to postgres;
