create view v_product_performance_ab
            (test, group_id, team_name, test_type, date_launch, scalability_bp, scalability_itc, growth_bp, growth_itc,
             task_link) as
SELECT i_ab_test. test,
       i_ab_test. group_in_prod                                               AS group_id,
       (SELECT ipt. name
        FROM product. info_product_teams ipt
        WHERE ipt. id = i_ab_test. team_id)                                    AS team_name,
       i_ab_test. test_type,
       COALESCE(i_ab_test. date_launch, max(res. dt_end))                      AS date_launch,
       avg(
               CASE
                   WHEN sca. country::text = 'UA'::text THEN sca. scalability_bp
                   ELSE NULL::double precision
                   END)                                                      AS scalability_bp,
       avg(
               CASE
                   WHEN sca. country::text = ANY (ARRAY ['UA'::character varying::text]) THEN sca. scalability_itc
                   ELSE NULL::double precision
                   END)                                                      AS scalability_itc,
       1::double precision + COALESCE(avg(
                                              CASE
                                                  WHEN res. metric::text = 'EA_Business_Metric_2020_per_user'::text THEN
                                                      CASE
                                                          WHEN res. b_ci_lower > res. a_ci_upper OR res. b_ci_upper < res. a_ci_lower
                                                              THEN res. b_mean / res. a_mean - 1::double precision
                                                          ELSE 0::double precision
                                                          END
                                                  ELSE NULL::double precision
                                                  END), 0::double precision) AS growth_bp,
       1::double precision + COALESCE(avg(
                                              CASE
                                                  WHEN res. metric::text = 'intention_to_contact_per_user'::text THEN
                                                      CASE
                                                          WHEN res. b_ci_lower > res. a_ci_upper OR res. b_ci_upper < res. a_ci_lower
                                                              THEN res. b_mean / res. a_mean - 1::double precision
                                                          ELSE 0::double precision
                                                          END
                                                  ELSE NULL::double precision
                                                  END), 0::double precision) AS growth_itc,
       i_ab_test. task_link
FROM product. info_ab_tests i_ab_test
LEFT JOIN product. ab_tests_scalability sca ON sca. test_id = i_ab_test. test AND sca. test_type::text = i_ab_test. test_type::text
LEFT JOIN product. ab_tests_results res
          ON res. test = i_ab_test. test AND res. test_type::text = i_ab_test. test_type::text AND
             "substring"(res. groups_vs::text, length(res. groups_vs::text) - 1, length(res. groups_vs::text))::integer =
             i_ab_test. group_in_prod::integer AND res. type_test::text = 'AB'::text AND
             res. devices::text = i_ab_test. device_types::text AND res. country::text = 'UA'::text
WHERE i_ab_test. is_in_prod = true
  AND i_ab_test. exclude_from_year_goal = 0
  AND res. dt_calc = ((SELECT max(dcalc. dt_calc) AS max
                      FROM product. ab_tests_results dcalc
                      WHERE res. test = dcalc. test
                        AND res. test_type::text = dcalc. test_type::text
                        AND res. groups_vs::text = dcalc. groups_vs::text
                        AND res. devices::text = dcalc. devices::text))
  AND (i_ab_test. team_id = ANY (ARRAY [1, 2, 3, 7]))
GROUP BY i_ab_test. test, i_ab_test. group_in_prod, i_ab_test. team_id, i_ab_test. test_type, i_ab_test. task_link
UNION
SELECT v_ab_test_results_ds. test,
       v_ab_test_results_ds. group_in_prod   AS group_id,
       v_ab_test_results_ds. team_name,
       v_ab_test_results_ds. test_type,
       v_ab_test_results_ds. date_launch,
       v_ab_test_results_ds. bp_scalability  AS scalability_bp,
       v_ab_test_results_ds. itc_scalability AS scalability_itc,
       CASE
           WHEN v_ab_test_results_ds. bp_growth_significance = true
               THEN v_ab_test_results_ds. bp_growth + 1::double precision
           ELSE 1::double precision
           END                              AS growth_bp,
       CASE
           WHEN v_ab_test_results_ds. itc_growth_significance = true
               THEN v_ab_test_results_ds. itc_growth + 1::double precision
           ELSE 1::double precision
           END                              AS growth_itc,
       NULL::character varying(300)         AS task_link
FROM product. v_ab_test_results_ds
WHERE v_ab_test_results_ds. country::text = 'UA'::text
  AND v_ab_test_results_ds. is_in_prod = true;

alter table v_product_performance_ab
    owner to dap;
