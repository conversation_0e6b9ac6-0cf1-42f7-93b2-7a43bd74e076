create view w_apply_cvb_funnel
            (country, dt, date_diff, mobile, returned, no_cv_vacancy, apply_version, apply_subversion, rank_apply_click,
             rank_apply_submit, is_auth, have_cv, view_1_step, view_2_step, view_3_step)
as
SELECT acf.country,
       af.dt_trunc                                                               AS dt,
       af.date_diff,
       af.mobile,
       af.returned,
       af.no_cv_vacancy,
       af.apply_version::character varying(100)                                  AS apply_version,
       af.apply_subversion::character varying(100)                               AS apply_subversion,
       af.rank_apply_click::integer                                              AS rank_apply_click,
       af.rank_apply_submit::integer                                             AS rank_apply_submit,
       sign(af.id_account::double precision)                                     AS is_auth,
       sign((af.n_account_cvs + COALESCE(af.n_client_cvs, 0))::double precision) AS have_cv,
       sum(acf.view_1_step)                                                      AS view_1_step,
       sum(acf.view_2_step)                                                      AS view_2_step,
       sum(acf.view_3_step)                                                      AS view_3_step
FROM archive.apply_cvb_funnel acf
JOIN archive.v_apply_funnel_raw af ON af.id_jdp = acf.id_jdp AND af.country::text = acf.country::text
GROUP BY acf.country, af.dt_trunc, af.date_diff, af.mobile, af.returned, af.no_cv_vacancy,
         (af.apply_version::character varying(100)), (af.apply_subversion::character varying(100)),
         (af.rank_apply_click::integer), (af.rank_apply_submit::integer), (sign(af.id_account::double precision)),
         (sign((af.n_account_cvs + COALESCE(af.n_client_cvs, 0))::double precision));

alter table w_apply_cvb_funnel
    owner to dap;
