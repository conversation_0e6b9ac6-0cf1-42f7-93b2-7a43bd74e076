create view v_apply_funnel_raw
            (country, dt, dt_trunc, date_diff, mobile, returned, group_t1, test_1, no_cv_vacancy, id_session, id_jdp,
             cookie_label, id_account, applies_click, applies_click_with_no_cv, applies_click_no_cv, applies_submit,
             applies_submit_no_cv, cv_create_clicks, cv_upload_clicks, cv_attaches, cv_uploaded, cv_selected, cv_auth,
             n_account_cvs, n_client_cvs, apply_version, apply_subversion, id_traf_source, rank_apply_click,
             rank_apply_submit, cv_creations)
as
SELECT j.country,
       j.dt,
       j.dt_trunc,
       j.date_diff,
       j.mobile,
       j.returned,
       j.group_t1,
       j.test_1,
       j.no_cv_vacancy,
       j.id_session,
       j.id_jdp,
       j.cookie_label,
       j.id_account,
       j.applies_click,
       j.applies_click_with_no_cv,
       j.applies_click_no_cv,
       j.applies_submit,
       j.applies_submit_no_cv,
       j.cv_create_clicks,
       j.cv_upload_clicks,
       j.cv_attaches,
       j.cv_uploaded,
       j.cv_selected,
       j.cv_auth,
       j.n_account_cvs,
       j.n_client_cvs,
       CASE
           WHEN j.mobile = 0 THEN 'apply desktop 1.0'::text
           WHEN j.test_1 = 330 AND j.group_t1 = 2 THEN 'apply 4.0'::text
           WHEN (j.test_2 = ANY (ARRAY [346, 350])) AND j.group_t2 = 2 THEN 'apply 4.0'::text
           WHEN (j.test_1 = 330 OR j.test_1 = 0) AND (j.group_t1 = ANY (ARRAY [0, 1])) AND j.date_diff < 43837
               THEN 'apply 3.0'::text
           WHEN (j.group_t1 = ANY (ARRAY [0, 1])) AND (j.group_t2 = ANY (ARRAY [0, 1])) AND j.date_diff >= 43837
               THEN 'apply 4.0'::text
           ELSE 'apply 4.0'::text
           END                                                                                                 AS apply_version,
       CASE
           WHEN j.group_t1 >= 2 OR j.group_t2 >= 2 OR j.group_t3 >= 2 OR j.group_t4 >= 2 OR j.group_t5 >= 2 THEN concat(
                   CASE
                       WHEN j.group_t1 >= 2 THEN concat(j.test_1, ' test ', j.group_t1, 'g')
                       ELSE NULL::text
                       END,
                   CASE
                       WHEN j.group_t1 >= 2 AND j.group_t2 >= 2 THEN ' & '::text
                       ELSE NULL::text
                       END,
                   CASE
                       WHEN j.group_t2 >= 2 THEN concat(j.test_2, ' test ', j.group_t2, 'g')
                       ELSE NULL::text
                       END,
                   CASE
                       WHEN (j.group_t1 >= 2 OR j.group_t2 >= 2) AND j.group_t3 >= 2 THEN ' & '::text
                       ELSE NULL::text
                       END,
                   CASE
                       WHEN j.group_t3 >= 2 THEN concat(j.test_3, ' test ', j.group_t3, 'g')
                       ELSE NULL::text
                       END,
                   CASE
                       WHEN (j.group_t1 >= 2 OR j.group_t2 >= 2 OR j.group_t3 >= 2) AND j.group_t4 >= 2 THEN ' & '::text
                       ELSE NULL::text
                       END,
                   CASE
                       WHEN j.group_t4 >= 2 THEN concat(j.test_4, ' test ', j.group_t4, 'g')
                       ELSE NULL::text
                       END,
                   CASE
                       WHEN (j.group_t1 >= 2 OR j.group_t2 >= 2 OR j.group_t3 >= 2 OR j.group_t4 >= 2) AND
                            j.group_t5 >= 2 THEN ' & '::text
                       ELSE NULL::text
                       END,
                   CASE
                       WHEN j.group_t5 >= 2 THEN concat(j.test_5, ' test ', j.group_t5, 'g')
                       ELSE NULL::text
                       END)
           ELSE 'basic'::text
           END                                                                                                 AS apply_subversion,
       COALESCE(j.id_traf_source::bigint, 0::bigint)                                                           AS id_traf_source,
       rank() OVER (PARTITION BY j.country, j.cookie_label, j.applies_click ORDER BY j.dt) *
       j.applies_click                                                                                         AS rank_apply_click,
       rank() OVER (PARTITION BY j.country, j.cookie_label, j.applies_submit ORDER BY j.dt) *
       j.applies_submit                                                                                        AS rank_apply_submit,
       j.cv_creations
FROM archive.apply_funnel_jdp j;

alter table v_apply_funnel_raw
    owner to dap;

grant select on v_apply_funnel_raw to npo;

grant select on v_apply_funnel_raw to readonly;

grant select on v_apply_funnel_raw to writeonly_product;

grant select on v_apply_funnel_raw to ksha;

grant select on v_apply_funnel_raw to writeonly_pyscripts;

grant select on v_apply_funnel_raw to "pavlo.kvasnii";

