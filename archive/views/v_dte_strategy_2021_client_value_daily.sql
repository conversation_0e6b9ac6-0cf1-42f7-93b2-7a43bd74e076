create view v_dte_strategy_2021_client_value_daily
            (country_id, date, call_plan_cnt, call_answered_30_sec_free_packet_plan_cnt,
             call_answered_30_sec_paid_packet_plan_cnt, apply_profile_viewed_plan_cnt,
             apply_profile_open_contact_free_packet_plan_cnt, apply_profile_open_contact_paid_packet_plan_cnt,
             profile_base_search_plan_cnt, profile_base_profile_viewed_plan_cnt,
             profile_base_profile_open_contact_free_packet_plan_cnt,
             profile_base_profile_open_contact_paid_packet_plan_cnt, digital_recruiter_profile_plan_cnt,
             digital_recruiter_profile_viewed_plan_cnt, digital_recruiter_profile_open_contact_free_packet_plan_cnt,
             digital_recruiter_profile_open_contact_paid_packet_plan_cnt, call_cnt,
             call_answered_30_sec_free_packet_cnt, call_answered_30_sec_paid_packet_cnt, late_call_cnt, apply_cnt,
             apply_profile_viewed_cnt, apply_profile_open_contact_free_packet_cnt,
             apply_profile_open_contact_paid_packet_cnt, apply_profile_message_cnt, profile_base_search_cnt,
             profile_base_profile_viewed_cnt, profile_base_profile_open_contact_free_packet_cnt,
             profile_base_profile_open_contact_paid_packet_cnt, profile_base_profile_message_cnt,
             digital_recruiter_profile_cnt, digital_recruiter_profile_viewed_cnt,
             digital_recruiter_profile_open_contact_free_packet_cnt,
             digital_recruiter_profile_open_contact_paid_packet_cnt, digital_recruiter_profile_message_cnt,
             jcoin_utilized_plan_cnt, jcoin_utilized_cnt, jcoin_utilized_plan_2022_q1_cnt)
as
WITH jcoin_utilization AS (SELECT jupd.database_source_id,
                                  jupd.utilization_datediff,
                                  sum(jupd.jcoin_plan_cnt) AS jcoin_plan_cnt
                           FROM employer.jcoin_utilization_plan_daily jupd
                           JOIN imp_employer.employer e
                                ON jupd.database_source_id = e.sources AND jupd.employer_id = e.id
                           JOIN employer.subscription_packet_job_active_agg spjaa
                                ON jupd.database_source_id = spjaa.database_source_id AND
                                   jupd.subscription_id = spjaa.subscription_id AND
                                   jupd.packet_rank = spjaa.packet_rank AND spjaa.max_active_job_cnt > 0
                           WHERE jupd.utilization_datediff < fn_get_date_diff(CURRENT_DATE::timestamp without time zone)
                             AND jupd.database_source_id = 1
                             AND e.country_code::text = 'ua'::text
                           GROUP BY jupd.database_source_id, jupd.utilization_datediff)
SELECT 1                                                                   AS country_id,
       cv_plan.date,
       cv_plan.call_plan_cnt,
       cv_plan.call_answered_30_sec_free_packet_plan_cnt,
       cv_plan.call_answered_30_sec_paid_packet_plan_cnt,
       cv_plan.apply_profile_viewed_plan_cnt,
       cv_plan.apply_profile_open_contact_free_packet_plan_cnt,
       cv_plan.apply_profile_open_contact_paid_packet_plan_cnt,
       cv_plan.profile_base_search_plan_cnt,
       cv_plan.profile_base_profile_viewed_plan_cnt,
       cv_plan.profile_base_profile_open_contact_free_packet_plan_cnt,
       cv_plan.profile_base_profile_open_contact_paid_packet_plan_cnt,
       cv_plan.digital_recruiter_profile_plan_cnt,
       cv_plan.digital_recruiter_profile_viewed_plan_cnt,
       cv_plan.digital_recruiter_profile_open_contact_free_packet_plan_cnt,
       cv_plan.digital_recruiter_profile_open_contact_paid_packet_plan_cnt,
       sum(cv_fact.call_cnt)                                               AS call_cnt,
       sum(cv_fact.call_answered_30_sec_free_packet_cnt)                   AS call_answered_30_sec_free_packet_cnt,
       sum(cv_fact.call_answered_30_sec_paid_packet_cnt)                   AS call_answered_30_sec_paid_packet_cnt,
       --lc.late_call_cnt::numeric                                           AS late_call_cnt,
       sum(cv_fact.apply_cnt)                                              AS apply_cnt,
       sum(cv_fact.apply_profile_viewed_cnt)                               AS apply_profile_viewed_cnt,
       sum(cv_fact.apply_profile_open_contact_free_packet_cnt)             AS apply_profile_open_contact_free_packet_cnt,
       sum(cv_fact.apply_profile_open_contact_paid_packet_cnt)             AS apply_profile_open_contact_paid_packet_cnt,
       sum(cv_fact.apply_profile_message_cnt)                              AS apply_profile_message_cnt,
       sum(cv_fact.profile_base_search_cnt)                                AS profile_base_search_cnt,
       sum(cv_fact.profile_base_profile_viewed_cnt)                        AS profile_base_profile_viewed_cnt,
       sum(cv_fact.profile_base_profile_open_contact_free_packet_cnt)      AS profile_base_profile_open_contact_free_packet_cnt,
       sum(cv_fact.profile_base_profile_open_contact_paid_packet_cnt)      AS profile_base_profile_open_contact_paid_packet_cnt,
       sum(cv_fact.profile_base_profile_message_cnt)                       AS profile_base_profile_message_cnt,
       sum(cv_fact.digital_recruiter_profile_cnt)                          AS digital_recruiter_profile_cnt,
       sum(cv_fact.digital_recruiter_profile_viewed_cnt)                   AS digital_recruiter_profile_viewed_cnt,
       sum(cv_fact.digital_recruiter_profile_open_contact_free_packet_cnt) AS digital_recruiter_profile_open_contact_free_packet_cnt,
       sum(cv_fact.digital_recruiter_profile_open_contact_paid_packet_cnt) AS digital_recruiter_profile_open_contact_paid_packet_cnt,
       sum(cv_fact.digital_recruiter_profile_message_cnt)                  AS digital_recruiter_profile_message_cnt,
       ju.jcoin_plan_cnt::numeric(16, 2)                                   AS jcoin_utilized_plan_cnt,
       sum(cv_fact.apply_profile_open_contact_paid_packet_cnt + cv_fact.apply_profile_open_contact_free_packet_cnt +
           cv_fact.digital_recruiter_profile_open_contact_paid_packet_cnt +
           cv_fact.digital_recruiter_profile_open_contact_free_packet_cnt +
           cv_fact.profile_base_profile_open_contact_paid_packet_cnt +
           cv_fact.profile_base_profile_open_contact_free_packet_cnt + cv_fact.call_answered_30_sec_free_packet_cnt +
           cv_fact.call_answered_30_sec_paid_packet_cnt)                   AS jcoin_utilized_cnt,
       cv_plan.jcoin_utilized_plan_cnt                                     AS jcoin_utilized_plan_2022_q1_cnt
FROM jooble_goals.v_dte_strategy_2021_client_value_plan_daily cv_plan
LEFT JOIN employer.jcoin_model_daily cv_fact
          ON cv_plan.date = fn_get_timestamp_from_date_diff(cv_fact.action_datediff) AND
             fn_get_timestamp_from_date_diff(cv_fact.action_datediff) < CURRENT_DATE AND
             cv_fact.country_code::text = 'ua'::text
LEFT JOIN jcoin_utilization ju
          ON ju.database_source_id = 1 AND fn_get_timestamp_from_date_diff(ju.utilization_datediff) = cv_plan.date
--LEFT JOIN archive.late_call_agg lc ON fn_get_timestamp_from_date_diff(lc.call_datediff) = cv_plan.date
WHERE cv_plan.date >= '2021-08-01'::date
GROUP BY cv_plan.date, cv_plan.call_plan_cnt, cv_plan.call_answered_30_sec_free_packet_plan_cnt,
         cv_plan.call_answered_30_sec_paid_packet_plan_cnt, cv_plan.apply_profile_viewed_plan_cnt,
         cv_plan.apply_profile_open_contact_free_packet_plan_cnt,
         cv_plan.apply_profile_open_contact_paid_packet_plan_cnt, cv_plan.profile_base_search_plan_cnt,
         cv_plan.profile_base_profile_viewed_plan_cnt, cv_plan.profile_base_profile_open_contact_free_packet_plan_cnt,
         cv_plan.profile_base_profile_open_contact_paid_packet_plan_cnt, cv_plan.digital_recruiter_profile_plan_cnt,
         cv_plan.digital_recruiter_profile_viewed_plan_cnt,
         cv_plan.digital_recruiter_profile_open_contact_free_packet_plan_cnt,
         cv_plan.digital_recruiter_profile_open_contact_paid_packet_plan_cnt, ju.jcoin_plan_cnt,
         cv_plan.jcoin_utilized_plan_cnt/*, (lc.late_call_cnt::numeric)*/;

alter table v_dte_strategy_2021_client_value_daily
    owner to rlu;

grant select on v_dte_strategy_2021_client_value_daily to readonly;

grant select on v_dte_strategy_2021_client_value_daily to math;

grant select on v_dte_strategy_2021_client_value_daily to writeonly_pyscripts;

grant select on v_dte_strategy_2021_client_value_daily to "pavlo.kvasnii";

