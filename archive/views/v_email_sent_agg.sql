create view v_email_sent_agg
            (country, dt, n_sent_letters, n_open_letters, n_click_letters, views_jdp, click_calls, applies_click, aways,
             applies_submit, applies_submit_no_cv, views_jdp_l, click_calls_l, applies_click_l, aways_l,
             applies_submit_l, applies_submit_no_cv_l, serp_aways, serp_aways_l, n_users, letter_type, letter_name, bp,
             intention_to_contact)
as
SELECT email_sent_agg. country,
       email_sent_agg. dt,
       email_sent_agg. n_sent_letters,
       email_sent_agg. n_open_letters,
       email_sent_agg. n_click_letters,
       email_sent_agg. views_jdp,
       email_sent_agg. click_calls,
       email_sent_agg. applies_click,
       email_sent_agg. aways,
       email_sent_agg. applies_submit,
       email_sent_agg. applies_submit_no_cv,
       email_sent_agg. views_jdp_l,
       email_sent_agg. click_calls_l,
       email_sent_agg. applies_click_l,
       email_sent_agg. aways_l,
       email_sent_agg. applies_submit_l,
       email_sent_agg. applies_submit_no_cv_l,
       email_sent_agg. serp_aways,
       email_sent_agg. serp_aways_l,
       email_sent_agg. n_users,
       email_sent_agg. letter_type,
       (SELECT i. name
        FROM archive. info_letter_type i
        WHERE i. id = email_sent_agg. letter_type) AS letter_name,
       email_sent_agg. bp,
       email_sent_agg. intention_to_contact
FROM archive. email_sent_agg;

alter table v_email_sent_agg
    owner to dap;
