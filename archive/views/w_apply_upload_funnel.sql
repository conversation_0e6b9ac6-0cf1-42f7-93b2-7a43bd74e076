create view w_apply_upload_funnel
            (country, dt, date_diff, mobile, returned, no_cv_vacancy, apply_version, apply_subversion, rank_apply_click,
             rank_apply_submit, is_auth, have_cv, upload_cv_click, upload_cv_success, upload_cv_error)
as
SELECT auf.country,
       af.dt_trunc                                                               AS dt,
       af.date_diff,
       af.mobile,
       af.returned,
       af.no_cv_vacancy,
       af.apply_version::character varying(100)                                  AS apply_version,
       af.apply_subversion::character varying(100)                               AS apply_subversion,
       af.rank_apply_click::integer                                              AS rank_apply_click,
       af.rank_apply_submit::integer                                             AS rank_apply_submit,
       sign(af.id_account::double precision)                                     AS is_auth,
       sign((af.n_account_cvs + COALESCE(af.n_client_cvs, 0))::double precision) AS have_cv,
       sum(auf.upload_cv_click)                                                  AS upload_cv_click,
       sum(auf.upload_cv_success)                                                AS upload_cv_success,
       sum(auf.upload_cv_error)                                                  AS upload_cv_error
FROM archive.apply_upload_funnel auf
JOIN archive.v_apply_funnel_raw af ON af.id_jdp = auf.id_jdp AND af.country::text = auf.country::text
GROUP BY auf.country, af.dt_trunc, af.date_diff, af.mobile, af.returned, af.no_cv_vacancy,
         (af.apply_version::character varying(100)), (af.apply_subversion::character varying(100)),
         (af.rank_apply_click::integer), (af.rank_apply_submit::integer), (sign(af.id_account::double precision)),
         (sign((af.n_account_cvs + COALESCE(af.n_client_cvs, 0))::double precision));

alter table w_apply_upload_funnel
    owner to dap;
