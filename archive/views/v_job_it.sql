create view v_job_it
            (country_id, country, job_id, job_created_datetime, job_title, company_name, job_url, job_description,
             industry_it_name, email)
as
SELECT ji.country_id,
       c.alpha_2                            AS country,
       ji.job_id,
       ji.job_created_datetime,
       ji.job_title,
       ji.company_name,
       ji.job_url,
       jid.job_description,
       ii.industry_it_name,
       archive.beautify_email(lower(
               CASE
                   WHEN "position"(ji.email_list::text, ';'::text) = 0 THEN ji.email_list::text
                   ELSE substr(ji.email_list::text, 1, "position"(ji.email_list::text, ';'::text) - 1)
                   END)::character varying) AS email
FROM archive.job_it ji
JOIN dimension.countries c ON c.id = ji.country_id
LEFT JOIN dimension.company_industry ci ON ci.company_name::text = ji.company_name::text
LEFT JOIN dimension.industry_it ii ON ii.industry_it_id = ci.industry_id
LEFT JOIN archive.job_it_description jid ON jid.country_id = ji.country_id AND jid.job_id = ji.job_id
WHERE ji.job_created_datetime > (CURRENT_DATE - 21)
  AND lower(ji.company_name::text) !~~ '%recruit%'::text
  AND lower(ji.company_name::text) !~~ '%confidential%'::text
  AND lower(ji.company_name::text) !~~ '%consult%'::text
  AND lower(ji.company_name::text) !~~ '%resourc%'::text
  AND (ci.industry_id <> 11 OR ci.industry_id IS NULL)
  AND ji.email_list IS NOT NULL;

alter table v_job_it
    owner to dap;
