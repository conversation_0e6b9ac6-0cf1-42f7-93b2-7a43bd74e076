create view v_cv_quality
            (country, cv_id, cvb_version, date_created, name_score, phone_score, email_score, city_score, photo_score,
             position_score, expirience_score, education_score, skills_score, age_score, employment_type_score,
             relocation_type_score, salary_score)
as
SELECT v_cv_quality_raw. country,
       v_cv_quality_raw. cv_id,
       v_cv_quality_raw. cvb_version,
       v_cv_quality_raw. date_created,
       v_cv_quality_raw. name_is::numeric * ((SELECT ps. score_value
                                             FROM archive. info_cv_fields i
                                             JOIN archive. cv_fields_poll_score ps
                                                  ON i. id = ps. id_field AND ps. n_poll = 1 AND ps. id_field = 1))                               AS name_score,
       v_cv_quality_raw. phone_is::numeric * ((SELECT ps. score_value
                                              FROM archive. info_cv_fields i
                                              JOIN archive. cv_fields_poll_score ps
                                                   ON i. id = ps. id_field AND ps. n_poll = 1 AND ps. id_field = 2))                              AS phone_score,
       v_cv_quality_raw. email_is::numeric * ((SELECT ps. score_value
                                              FROM archive. info_cv_fields i
                                              JOIN archive. cv_fields_poll_score ps
                                                   ON i. id = ps. id_field AND ps. n_poll = 1 AND ps. id_field = 3))                              AS email_score,
       v_cv_quality_raw. city_is::numeric * ((SELECT ps. score_value
                                             FROM archive. info_cv_fields i
                                             JOIN archive. cv_fields_poll_score ps
                                                  ON i. id = ps. id_field AND ps. n_poll = 1 AND ps. id_field = 4))                               AS city_score,
       COALESCE(v_cv_quality_raw. photo_is::bigint, 0::bigint)::numeric * ((SELECT ps. score_value
                                                                           FROM archive. info_cv_fields i
                                                                           JOIN archive. cv_fields_poll_score ps
                                                                                ON i. id = ps. id_field AND ps. n_poll = 1 AND ps. id_field = 5)) AS photo_score,
       v_cv_quality_raw. position_is::numeric * ((SELECT ps. score_value
                                                 FROM archive. info_cv_fields i
                                                 JOIN archive. cv_fields_poll_score ps
                                                      ON i. id = ps. id_field AND ps. n_poll = 1 AND ps. id_field = 6))                           AS position_score,
       v_cv_quality_raw. expirience_is::numeric * ((SELECT ps. score_value
                                                   FROM archive. info_cv_fields i
                                                   JOIN archive. cv_fields_poll_score ps
                                                        ON i. id = ps. id_field AND ps. n_poll = 1 AND ps. id_field = 7))                         AS expirience_score,
       v_cv_quality_raw. education_is::numeric * ((SELECT ps. score_value
                                                  FROM archive. info_cv_fields i
                                                  JOIN archive. cv_fields_poll_score ps
                                                       ON i. id = ps. id_field AND ps. n_poll = 1 AND ps. id_field = 8))                          AS education_score,
       v_cv_quality_raw. skills_is::numeric * ((SELECT ps. score_value
                                               FROM archive. info_cv_fields i
                                               JOIN archive. cv_fields_poll_score ps
                                                    ON i. id = ps. id_field AND ps. n_poll = 1 AND ps. id_field = 9))                             AS skills_score,
       v_cv_quality_raw. age_is::numeric * ((SELECT ps. score_value
                                            FROM archive. info_cv_fields i
                                            JOIN archive. cv_fields_poll_score ps
                                                 ON i. id = ps. id_field AND ps. n_poll = 1 AND ps. id_field = 10))                               AS age_score,
       v_cv_quality_raw. employment_type_is::numeric * ((SELECT ps. score_value
                                                        FROM archive. info_cv_fields i
                                                        JOIN archive. cv_fields_poll_score ps
                                                             ON i. id = ps. id_field AND ps. n_poll = 1 AND ps. id_field = 11))                   AS employment_type_score,
       v_cv_quality_raw. relocation_type_is::numeric * ((SELECT ps. score_value
                                                        FROM archive. info_cv_fields i
                                                        JOIN archive. cv_fields_poll_score ps
                                                             ON i. id = ps. id_field AND ps. n_poll = 1 AND ps. id_field = 12))                   AS relocation_type_score,
       v_cv_quality_raw. salary_is::numeric * ((SELECT ps. score_value
                                               FROM archive. info_cv_fields i
                                               JOIN archive. cv_fields_poll_score ps
                                                    ON i. id = ps. id_field AND ps. n_poll = 1 AND ps. id_field = 13))                            AS salary_score
FROM product. v_cv_quality_raw;

alter table v_cv_quality
    owner to dap;
