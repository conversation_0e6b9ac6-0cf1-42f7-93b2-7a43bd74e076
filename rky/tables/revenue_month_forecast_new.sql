Select dv_revenue_by_placement_and_src.date,
       case
           when dv_revenue_by_placement_and_src.channel in
                ('Organic Search', 'Paid Search', 'MobileApp', 'Affiliate', 'Email')
               then channel
           else 'Direct + Other' end as channel,
       sum(revenue_usd)              as income
from ono.dv_revenue_by_placement_and_src
where dv_revenue_by_placement_and_src.date between ('2024-04-06'::date) - 365 and ('2024-04-06'::date) - 1
group by dv_revenue_by_placement_and_src.date,
         case
             when dv_revenue_by_placement_and_src.channel in
                  ('Organic Search', 'Paid Search', 'MobileApp', 'Affiliate', 'Email')
                 then channel
             else 'Direct + Other' end
having sum(revenue_usd) > 0
;
