

begin




---------------------

create temp table session_t1 as --+
select * from dwh_test.session_tmp where date_diff = 44870 and country in (61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71);

create temp table session_jdp_t as --+
select * from dwh_test.session_jdp_tmp where date_diff = 44870 and country in (61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71);

create temp table session_away_t as
select * from dwh_test.session_away_for_email where date_diff = 44870 and country in (61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71);

create temp table session_away_message_t as
select * from imp.session_away_message where date_diff = 44870 and country in (61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71);

create temp table session_click_t as
select * from dwh_test.session_click_tmp where date_diff = 44870 and country in (61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71);

create temp table session_click_message_t as
select * from imp.session_click_message where date_diff = 44870 and country in (61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71);

create temp table session_alertview_message_t as
select * from dwh_test.session_alertview_message_tmp where date_diff = 44870 and country in (61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71);






--1
create temp table session_t as
Select *
from dwh_test.session_tmp
where flags & 1 = 0
  and flags & 4 = 0
  and date_diff = 44870
    and country in (61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71);


--2
create temp table email_sent_t as
Select *
from dwh_test.email_sent_tmp
where date_diff = 44870
    and country in (61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71);


--3
create temp table email_open_t as
Select email_open.id_message,
       email_open.date_diff,
       email_sent.id_account,
       email_open.country
from dwh_test.email_open_tmp email_open
         join email_sent_t email_sent
              on email_open.id_message = email_sent.id_message
where email_open.date_diff = 44870
    and email_open.country in (61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71);


--4
create temp table email_visit_t as
Select email_visit.id_message,
       email_visit.date_diff,
       email_sent.id_account,
       email_visit.country
from dwh_test.email_visit_tmp email_visit
         join email_sent_t email_sent
              on email_visit.id_message = email_sent.id_message
where email_visit.date_diff = 44870
    and email_visit.country in (61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71);

--5
create temp table yesterday_emails_t as
Select id_message,
       id_account,
       country
from email_sent_t email_sent
union
Select id_message,
       id_account,
       country
from email_open_t email_open
union
Select id_message,
       id_account,
       country
from email_visit_t email_visit;


--6
create temp table away_revenue_t as
select session_away.country                                                      as country_id,
       coalesce(coalesce(session_away_message.id_message, session_alertview_message.id_message),
                session_click_message.id_message)                                as id_message,
       max(coalesce(session_away_message.position, 0))                           as position,
       max(coalesce(session_click.position, 0))                                  as alert_position,
       count(session_away.id)                                                    as away_open_cnt,
       count(case when session_away.id_jdp is not null then session_away.id end) as away_jdp_cnt
from session_away_t session_away
         join session_t session
              on session_away.date_diff = session.date_diff and
                 session_away.id_session = session.id
         left join session_away_message_t session_away_message
                   on session_away.date_diff = session_away_message.date_diff and
                      session_away.id = session_away_message.id_away
         left join session_click_t session_click
                   on session_away.date_diff = session_click.date_diff and
                      session_away.id_click = session_click.id
         left join session_click_message_t session_click_message
                   on session_click_message.date_diff = session_click.date_diff and
                      session_click_message.id_click = session_click.id
         left join session_alertview_message_t session_alertview_message
                   on session_click.date_diff = session_alertview_message.date_diff and
                      session_click.id_alertview = session_alertview_message.id_alertview
where coalesce(session_away.flags, 0) & 2 = 0
group by session_away.country,
         coalesce(coalesce(session_away_message.id_message, session_alertview_message.id_message),
                  session_click_message.id_message);



--7
create temp table email_conversion_t as
select email_sent.country as country_id,
       email_sent.id_message,
       max(case when email_open.id_message is not null then 1 else 0 end)  as is_open,
       max(case when email_visit.id_message is not null then 1 else 0 end) as is_visit
from yesterday_emails_t email_sent
         left join email_open_t email_open
                   on email_sent.id_message = email_open.id_message
         left join email_visit_t email_visit
                   on email_sent.id_message = email_visit.id_message
group by email_sent.id_message,
         email_sent.country;



--8
create temp table alert_t as
select email_alert.country as country_id,
       email_alert.id_account,
       count(email_alert.id) as alerts
from imp.email_alert
where email_alert.id_type_unsub is null
    and country in (61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71)
group by email_alert.id_account,
         email_alert.country;


--9
create temp table alertviews_t as
select email_sent.country as country_id,
       email_sent.id_message,
       count(id_alertview) as alertviews
from dwh_test.session_alertview_message_tmp session_alertview_message
         join yesterday_emails_t email_sent
              on session_alertview_message.id_message = email_sent.id_message
where session_alertview_message.country in (61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71)
group by email_sent.id_message,
         email_sent.country;



-->>>
create temp table sam_t1 as
select sam.*
from yesterday_emails_t t
inner join dwh_test.session_alertview_message_tmp sam on sam.id_message = t.id_message
    and sam.country in (61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71);

create temp table sc_t1 as
select sc.*
from sam_t1
inner join dwh_test.session_click_tmp sc on sam_t1.id_alertview = sc.id_alertview
    and sc.country in (61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71);
-->>>
-->>>
create temp table scm_t1 as
select scm.*
from yesterday_emails_t t
inner join imp.session_click_message scm on scm.id_message = t.id_message
    and scm.country in (61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71);

create temp table sc_t2 as
select sc.*
from scm_t1
inner join dwh_test.session_click_tmp sc on scm_t1.id_click = sc.id
    and sc.country in (61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71);
-->>>


--10
create temp table email_open_jdp_t as
 SELECT country_id,
        message_id,
        sum(jdp_cnt)   as jdp_cnt,
        sum(apply_cnt) as apply_cnt
 FROM (
          select yesterday_emails.country as country_id,
                 yesterday_emails.id_message      as message_id,
                 count(distinct session_jdp.id)   as jdp_cnt,
                 count(distinct session_apply.id) as apply_cnt
          from yesterday_emails_t yesterday_emails
                   inner join sam_t1
                              on sam_t1.id_message = yesterday_emails.id_message
                   inner join sc_t1
                              on sam_t1.id_alertview = sc_t1.id_alertview
                   inner join dwh_test.session_jdp_tmp session_jdp
                              on session_jdp.id_click = sc_t1.id
                                and session_jdp.country in (61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71)
                   left join dwh_test.session_jdp_action_tmp session_jdp_action
                             on session_jdp.id = session_jdp_action.id_jdp
                                and session_jdp_action.country in (61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71)
                   left join imp.session_apply
                             on session_jdp_action.id = session_apply.id_src_jdp_action
                                and session_apply.country in (61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71)
          group by yesterday_emails.id_message,
                   yesterday_emails.country
          union all
          select yesterday_emails.country as country_id,
                 yesterday_emails.id_message      as message_id,
                 count(distinct session_jdp.id)   as open_jdp_cnt,
                 count(distinct session_apply.id) as apply_cnt
          from yesterday_emails_t yesterday_emails
                   inner join scm_t1
                              on scm_t1.id_message = yesterday_emails.id_message
                   inner join sc_t2
                              on scm_t1.id_click = sc_t2.id
                   inner join dwh_test.session_jdp_tmp session_jdp
                              on session_jdp.id_click = sc_t2.id
                                and session_jdp.country in (61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71)
                   left join dwh_test.session_jdp_action_tmp session_jdp_action
                             on session_jdp.id = session_jdp_action.id_jdp
                                and session_jdp_action.country in (61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71)
                   left join imp.session_apply
                             on session_jdp_action.id = session_apply.id_src_jdp_action
                                and session_apply.country in (61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71)
          group by yesterday_emails.id_message,
                   yesterday_emails.country) t
 GROUP BY message_id,
          country_id;



--11.1
--/Away temp
create temp table Away as
select
    sa.country                             as                        country_id,
    s.date_diff,
    s.id                                                             id_session,
    sa.click_price * coalesce(ic.value_to_usd, 0)                    click_price_usd,
    coalesce(sa.letter_type, sj.letter_type, email_sent.letter_type) as letter_type,
    coalesce(sc.id_alertview, scj.id_alertview)                      id_alertview,
    sa.id_jdp,
    coalesce(sa.id_account, sj.id_account) as                        id_account,
    s.flags,
    sa.id_project
from dwh_test.session_away_for_email sa
      inner join session_t1 s on sa.date_diff = s.date_diff
                                     and sa.id_session = s.id
      inner join dimension.info_currency ic on ic.id = sa.id_currency -- serp -> away
            and ic.country in (61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71)
      left join session_click_t sc on sc.date_diff = sa.date_diff
                                            and sc.id = sa.id_click -- serp -> jdp -> away
      left join session_jdp_t sj on sj.date_diff = sa.date_diff
                                          and sj.id = sa.id_jdp
      left join session_click_t scj on scj.date_diff = sj.date_diff
                                           and scj.id = sj.id_click
      left join session_alertview_message_t SAM on SC.date_diff = SAM.date_diff
                                                       and coalesce(SC.id_alertview, SCJ.id_alertview) = SAM.id_alertview
      left join dwh_test.email_sent_tmp email_sent on SAM.id_message = email_sent.id_message
            and email_sent.country in (61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71)
where sa.date_diff = 44870
    and sa.country in (61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71)
    and coalesce(s.flags, 0) & 1 = 0
    and coalesce(sa.flags, 0) & 2 = 0;


--11.2
--/letter_type_revenue_t temp
create temp table letter_type_revenue_t as
Select Away.country_id,
       Away.date_diff,
       letter_type,
       sum(click_price_usd) as total_revenue
from Away
     left join imp.session_account session_account
               on Away.id_session = session_account.id_session
                    and session_account.country in (61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71)
     left join dwh_test.session_alertview_tmp sa
               on sa.date_diff = 44870
                   and Away.id_alertview = sa.id
                   and sa.country in (61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71)
     left join imp.email_alert ea
                   on sa.sub_id_alert = ea.id
                   and ea.country in (61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71)
group by Away.date_diff,
         letter_type,
         Away.country_id;



-- first insert
insert into dwh_test.email_metric_daily_tmp
select email_sent.country                                                             as country_id,
       44870                                                                          as date_diff,
       cast(cast(email_sent.date as date) as timestamp)                               as sent_date,
       email_sent.letter_type,
       case
           when email_sent.calc_result_count < 11 then calc_result_count
           when email_sent.calc_result_count > 10 then 10
           else null
           end                                                                        as calc_result_count,
       away_revenue.position                                                          as max_away_position,
       away_revenue.alert_position,
       case
           when alert.alerts = 1 then '1'
           when alert.alerts = 2 then '2'
           when alert.alerts = 3 then '3'
           when alert.alerts = 4 then '4'
           when alert.alerts = 5 then '5+'
           else null
           end                                                                        as account_alerts,
       account.send_interval                                                          as account_send_interval,
       account_info.id_traf_src                                                       as account_traffic_source,
       (date_trunc('month', account.date_add))::timestamp                             as account_date,
       sum(alertviews.alertviews)                                                     as alertview_cnt,
       count(distinct account.id)                                                     as account_cnt,
       count(distinct case
                          when cast(account_info.unsub_date as date) = cast(email_sent.date as date)
                              then account.id end)                                    as account_unsub_cnt,
       count(distinct email_sent.id_message)                                          as sent_msg,
       count(distinct case
                          when email_conversion.is_open = 1 or alertviews.alertviews > 0 or
                               away_revenue.away_open_cnt > 0
                              then email_conversion.id_message end)                   as open_msg,
       count(distinct case
                          when email_conversion.is_visit = 1 or alertviews.alertviews > 0 or
                               away_revenue.away_open_cnt > 0
                              then email_conversion.id_message end)                   as visit_msg,

       sum(email_open_jdp.jdp_cnt)                                                    as jdp_cnt,
       sum(email_open_jdp.apply_cnt)                                                  as apply_cnt,
       sum(away_revenue.away_open_cnt)                                                as away_cnt,
       sum(away_jdp_cnt)                                                              as away_jdp_cnt,
       count(distinct away_revenue.id_message)                                        as message_with_away_cnt,
       count(distinct case
                          when alertviews.alertviews is not null
                              then email_sent.id_message end)                         as message_with_alertview_cnt,
       count(distinct
             case when email_open_jdp.jdp_cnt > 0 then email_open_jdp.message_id end) as message_with_jdp_cnt,
       count(distinct
             case when away_jdp_cnt > 0 then away_revenue.id_message end)             as message_with_jdp_away_cnt,
       null                                                                           as email_revenue
from yesterday_emails_t yesterday_emails
         inner join dwh_test.email_sent_tmp email_sent on yesterday_emails.id_message = email_sent.id_message
                and email_sent.country in (61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71)
         left join imp.account on email_sent.id_account = account.id
                and account.country in (61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71)
         left join imp.account_info on account.id = account_info.id_account
                and account_info.country in (61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71)
         left join email_open_jdp_t email_open_jdp on email_open_jdp.message_id = email_sent.id_message
         left join away_revenue_t away_revenue on away_revenue.id_message = email_sent.id_message
         left join email_conversion_t email_conversion on email_conversion.id_message = email_sent.id_message
         left join alert_t alert on email_sent.id_account = alert.id_account
         left join alertviews_t alertviews on email_sent.id_message = alertviews.id_message
group by cast(email_sent.date as date),
         email_sent.letter_type,
         case
             when email_sent.calc_result_count < 11 then calc_result_count
             when email_sent.calc_result_count > 10 then 10
             else null
             end,
         away_revenue.position,
         case
             when alert.alerts = 1 then '1'
             when alert.alerts = 2 then '2'
             when alert.alerts = 3 then '3'
             when alert.alerts = 4 then '4'
             when alert.alerts = 5 then '5+'
             end,
         account.send_interval,
         (date_trunc('month', account.date_add))::timestamp,
         away_revenue.alert_position,
         account_info.id_traf_src,
         email_sent.country;


--second insert
insert into dwh_test.email_metric_daily_tmp
select country_id   as country_id,
       44870    as date_diff,
       null          as sent_date,
       letter_type,
       null          as calc_result_count,
       null          as max_away_position,
       null          as alert_position,
       null          as account_alerts,
       null          as account_send_interval,
       null          as account_traffic_source,
       null          as account_date,
       null          as alertview_cnt,
       null          as account_cnt,
       null          as account_unsub_cnt,
       null          as sent_msg,
       null          as open_msg,
       null          as visit_msg,
       null          as jdp_cnt,
       null          as apply_cnt,
       null          as away_cnt,
       null          as away_jdp_cnt,
       null          as message_with_away_cnt,
       null          as message_with_alertview_cnt,
       null          as message_with_jdp_cnt,
       null          as message_with_jdp_away_cnt,
       total_revenue as email_revenue
from letter_type_revenue_t;

--##
drop table session_t;
drop table email_sent_t;
drop table email_open_t;
drop table email_visit_t;
drop table yesterday_emails_t;
drop table email_conversion_t;
drop table alert_t;
drop table alertviews_t;
drop table email_open_jdp_t;
drop table Away;
drop table away_revenue_t;
drop table letter_type_revenue_t;
--##
drop table session_t1;
drop table session_jdp_t;
drop table session_away_t;
drop table session_away_message_t;
drop table session_click_t;
drop table session_click_message_t;
drop table session_alertview_message_t;
--##
drop table sam_t1;
drop table sc_t1;
drop table scm_t1;
drop table sc_t2;






end;

