-- drop function an.project_conversions_daily;


create or replace function an.project_conversions_daily(_datediff integer)
    returns table (country_id int,
    metric text,
    session_date text,
    project_id  integer,
    project_name varchar,
    campaign_name  varchar,
    away_type text,
    campaign_id integer,
    is_mobile int,
    traffic_name varchar,
    traffic_is_paid smallint,
    channel varchar,
    ip_cc varchar,
    name varchar,
    away_revenue numeric,
    away_revenue_origin_currency numeric,
    aways bigint, --17
    conversions bigint,
    is_returned int,
    session_create_page_type smallint,
    id_current_traf_source integer)
    LANGUAGE 'plpgsql'

as $$

    declare start_datediff int = _datediff;
            country_id int = (select case when (select current_database()) = 'ua' then 1 when (select current_database()) = 'de'
                then 2 when (select current_database()) = 'uk' then 3 when (select current_database()) = 'fr' then 4 when (select current_database()) = 'ca'
                then 5 when (select current_database()) = 'us' then 6 when (select current_database()) = 'id' then 7 when (select current_database()) = 'ru'
                then 8 when (select current_database()) = 'pl' then 9 when (select current_database()) = 'hu' then 10 when (select current_database()) = 'ro'
                then 11 when (select current_database()) = 'es' then 12 when (select current_database()) = 'at' then 13 when (select current_database()) = 'be'
                then 14 when (select current_database()) = 'br' then 15 when (select current_database()) = 'ch' then 16 when (select current_database()) = 'cz'
                then 17 when (select current_database()) = 'in' then 18 when (select current_database()) = 'it' then 19 when (select current_database()) = 'nl'
                then 20 when (select current_database()) = 'tr' then 21 when (select current_database()) = 'by' then 22 when (select current_database()) = 'cl'
                then 23 when (select current_database()) = 'co' then 24 when (select current_database()) = 'gr' then 25 when (select current_database()) = 'sk'
                then 26 when (select current_database()) = 'th' then 27 when (select current_database()) = 'tw' then 28 when (select current_database()) = 've'
                then 29 when (select current_database()) = 'bg' then 30 when (select current_database()) = 'hr' then 31 when (select current_database()) = 'kz'
                then 32 when (select current_database()) = 'no' then 33 when (select current_database()) = 'rs' then 34 when (select current_database()) = 'se'
                then 35 when (select current_database()) = 'nz' then 36 when (select current_database()) = 'ng' then 37 when (select current_database()) = 'ar'
                then 38 when (select current_database()) = 'mx' then 39 when (select current_database()) = 'pe' then 40 when (select current_database()) = 'cn'
                then 41 when (select current_database()) = 'hk' then 42 when (select current_database()) = 'kr' then 43 when (select current_database()) = 'ph'
                then 44 when (select current_database()) = 'pk' then 45 when (select current_database()) = 'jp' then 46 when (select current_database()) = 'cu'
                then 47 when (select current_database()) = 'pr' then 48 when (select current_database()) = 'sv' then 49 when (select current_database()) = 'cr'
                then 50 when (select current_database()) = 'au' then 51 when (select current_database()) = 'do' then 52 when (select current_database()) = 'uy'
                then 53 when (select current_database()) = 'ec' then 54 when (select current_database()) = 'sg' then 55 when (select current_database()) = 'az'
                then 56 when (select current_database()) = 'fi' then 57 when (select current_database()) = 'ba' then 58 when (select current_database()) = 'pt'
                then 59 when (select current_database()) = 'dk' then 60 when (select current_database()) = 'ie' then 61 when (select current_database()) = 'my'
                then 62 when (select current_database()) = 'za' then 63 when (select current_database()) = 'ae' then 64 when (select current_database()) = 'qa'
                then 65 when (select current_database()) = 'sa' then 66 when (select current_database()) = 'kw' then 67 when (select current_database()) = 'bh'
                then 68 when (select current_database()) = 'eg' then 69 when (select current_database()) = 'ma' then 70 when (select current_database()) = 'uz'
                then 71 end);

begin
    return query
          SELECT country_id,
                 'aways'                                                              as metric,
                 to_char(('1900-01-01'::date + session_away.date_diff), 'yyyy-mm-dd') as session_date,
                 session_away.id_project                                              as project_iddd1,
                 info_project.name                                                    as project_name1,
                 campaign.name                                                        as campaign_name1,
                 case
                     when session_away.letter_type is not null then concat('Letter Type ', letter_type)
                     when session_away.id_click is not null then 'Click'
                     when session_away.id_jdp is not null then 'Jdp'
                     when session_away.id_click_no_serp is not null then 'No serp'
                     else 'Other' end                                                 as away_type1,
                 id_campaign,
                 case when session.flags & 16 = 16 then 1 else 0 end                  as is_mobile1,
                 u_traffic_source.name                                                as traffic_name1,
                 u_traffic_source.is_paid                                             as traffic_is_paid1,
                 u_traffic_source.channel                                             as channel1,
                 session.ip_cc                                                        as ip_cc1,
                 info_currency.name                                                   as name1,
                 0::numeric                                                           as away_revenue1,
                 0::numeric                                                           as away_revenue_origin_currency1,
                 count(session_away.id)                                               as aways,
                 count(conversion_away_connection.id_session_away)                    as conversions1,
                 session.id_current_traf_source as id_current_traf_source1,
                 session.session_create_page_type as session_create_page_type1,
                 case when session.flags & 2 = 2 then 1 else 0 end                    as is_returned1
          from public.session_away
                   left join link_dbo.info_project
                             on session_away.id_project = info_project.id
                   left join public.session
                             on session_away.id_session = session.id
                                 and session_away.date_diff = session.date_diff
                   left join link_dbo.u_traffic_source
                             on session.id_traf_source = u_traffic_source.id
                   left join
               (select distinct id_session_away
                from link_auction.conversion_away_connection
               ) conversion_away_connection
               on conversion_away_connection.id_session_away = session_away.id
                   left join link_auction.campaign
                             on session_away.id_campaign = campaign.id
                   left join link_dbo.info_currency
                             on session_away.id_currency = info_currency.id
          where session_away.date_diff = start_datediff
            and session.flags & 1 != 1
          group by country_id,
                   to_char(('1900-01-01'::date + session_away.date_diff), 'yyyy-mm-dd'),
                   info_project.name,
                   campaign.name,
                   case
                       when session_away.letter_type is not null
                           then concat('Letter Type ', letter_type)
                       when session_away.id_click is not null then 'Click'
                       when session_away.id_jdp is not null then 'Jdp'
                       when session_away.id_click_no_serp is not null then 'No serp'
                       else 'Other' end,
                   session_away.id_project,
                   u_traffic_source.name,
                   u_traffic_source.is_paid,
                   case when session.flags & 16 = 16 then 1 else 0 end,
                   u_traffic_source.channel,
                   session.ip_cc,
                   info_currency.name,
                   id_campaign,
                   session.id_current_traf_source,
                   session.session_create_page_type,
                   case when session.flags & 2 = 2 then 1 else 0 end

          UNION ALL

          select country_id,
                 'aways'                                                 as metric,
                 to_char(('1900-01-01'::date + date_diff), 'yyyy-mm-dd') as session_date,
                 project_iddd2,
                 project_name2,
                 campaign_name2,
                 away_type2,
                 id_campaign,
                 is_mobile2,
                 traffic_name2,
                 traffic_is_paid2,
                 channel                                                 as channel2,
                 ip_cc                                                   as ip_cc2,
                 name                                                    as name2,
                 (sum(away_revenue2))::numeric                           as away_revenue2,
                 (sum(away_revenue_origin_currency2))::numeric           as away_revenue_origin_currency2,
                 null                                                    as aways,
                 null                                                    as conversions2,
                 id_current_traf_source2 as id_current_traf_source2,
                 session_create_page_type2 as session_create_page_type2,
                 is_returned2

          from (
                   select sa.date_diff,
                          sa.id_project                                            as project_iddd2,
                          info_project.name                                        as project_name2,
                          campaign.name                                            as campaign_name2,
                          case
                              when sa.letter_type is not null
                                  then concat('Letter Type ', sa.letter_type)
                              when sa.id_click is not null then 'Click'
                              when sa.id_jdp is not null then 'Jdp'
                              when sa.id_click_no_serp is not null then 'No serp'
                              else 'Other' end                                     as away_type2,
                          sa.id_campaign,
                          case when s.flags & 16 = 16 then 1 else 0 end            as is_mobile2,
                          u_traffic_source.name                                    as traffic_name2,
                          u_traffic_source.is_paid                                 as traffic_is_paid2,
                          u_traffic_source.channel                                 as channel2,
                          s.ip_cc                                                  as ip_cc2,
                          ic.name                                                  as name2,
                          (coalesce(sa.click_price, 0) * ic.value_to_usd)::numeric as away_revenue2,
                          (coalesce(sa.click_price, 0))::numeric                   as away_revenue_origin_currency2,
                          s.id_current_traf_source as id_current_traf_source2,
                          s.session_create_page_type as session_create_page_type2,
                          case when s.flags & 2 = 2 then 1 else 0 end              as is_returned2,
                          ext.id                                                   as id_external

                   from public.session_away sa
                            inner join public.session s on sa.date_diff = s.date_diff
                       and sa.id_session = s.id
                            inner join link_dbo.info_currency ic on ic.id = sa.id_currency
                            left join link_auction.campaign ac on ac.id = sa.id_campaign
                            left join link_auction.site ast on ac.id_site = ast.id
                            left join link_auction.user au on au.id = ast.id_user
                       -- serp -> away
                            left join public.session_click sc on sc.date_diff = sa.date_diff
                       and sc.id = sa.id_click
                       -- serp -> jdp -> away
                            left join public.session_jdp sj on sj.date_diff = sa.date_diff
                       and sj.id = sa.id_jdp
                            left join public.session_click scj on scj.date_diff = sj.date_diff
                       and scj.id = sj.id_click
                            left join public.session_external ext on ext.date_diff = sa.date_diff
                       and ext.id_away = sa.id
                            left join link_dbo.info_project
                                      on sa.id_project = info_project.id
                            left join link_auction.campaign
                                      on sa.id_campaign = campaign.id
                            left join link_dbo.u_traffic_source
                                      on s.id_traf_source = u_traffic_source.id
                   where sa.date_diff = start_datediff
                     and coalesce(s.flags, 0) & 1 = 0
                     and (
                               sa.id_campaign = 0
                           or au.flags & 2 = 0
                       )
                     and coalesce(sa.flags, 0) & 2 = 0

                   union all

                   select sc.date_diff,
                          sc.id_project                                            as project_iddd2,
                          info_project.name                                        as project_name2,
                          campaign.name                                            as campaign_name2,
                          case
                              when coalesce(sa.letter_type, sj.letter_type) is not null
                                  then concat('Letter Type ', coalesce(sa.letter_type, sj.letter_type))
                              when sa.id_click is not null then 'Click'
                              when coalesce(sj.id, sa.id_jdp) is not null then 'Jdp'
                              when coalesce(sa.id_click_no_serp, sj.id_click_no_serp) is not null
                                  then 'No serp'
                              else 'Other' end                                     as away_type2,
                          sc.id_campaign,
                          case when s.flags & 16 = 16 then 1 else 0 end            as is_mobile2,
                          u_traffic_source.name                                    as traffic_name2,
                          u_traffic_source.is_paid                                 as traffic_is_paid2,
                          u_traffic_source.channel                                 as channel2,
                          s.ip_cc                                                  as ip_cc2,
                          ic.name                                                  as name2,
                          (coalesce(sc.click_price, 0) * ic.value_to_usd)::numeric as away_revenue2,
                          (coalesce(sc.click_price, 0))::numeric                   as away_revenue_origin_currency2,
                          s.id_current_traf_source as id_current_traf_source2,
                          s.session_create_page_type as session_create_page_type2,
                          case when s.flags & 2 = 2 then 1 else 0 end              as is_returned2,
                          ext.id                                                   as id_external

                   from public.session_click sc
                            inner join public.session s on sc.date_diff = s.date_diff
                       and sc.id_session = s.id
                            inner join link_dbo.info_currency ic on ic.id = sc.id_currency
                            left join link_auction.campaign ac on ac.id = sc.id_campaign
                            left join link_auction.site ast on ac.id_site = ast.id
                            left join link_auction.user au on au.id = ast.id_user
                            left join public.session_away sa on sc.date_diff = sa.date_diff
                       and sc.id = sa.id_click
                            left join public.session_jdp sj on sj.date_diff = sa.date_diff
                       and sj.id = sa.id_jdp
                            left join public.session_external ext on ext.date_diff = sc.date_diff
                       and
                                                                     (
                                                                                 ext.id_away = sa.id
                                                                             or ext.id_jdp = sj.id
                                                                         )
                            left join link_dbo.info_project
                                      on sc.id_project = info_project.id
                            left join link_auction.campaign
                                      on sc.id_campaign = campaign.id
                            left join link_dbo.u_traffic_source
                                      on s.id_traf_source = u_traffic_source.id
                   where sc.date_diff = start_datediff
                     and coalesce(s.flags, 0) & 1 = 0
                     and (
                               sc.id_campaign = 0
                           or au.flags & 2 = 2
                       )
                     and coalesce(sc.flags, 0) & 16 = 0

                   union all

                   select scns.date_diff,
                          scns.id_project                                            as project_iddd2,
                          info_project.name                                          as project_name2,
                          campaign.name                                              as campaign_name2,
                          case
                              when scns.letter_type is not null
                                  then concat('Letter Type ', scns.letter_type)
                              else 'No serp' end                                     as away_type2,
                          scns.id_campaign,
                          case when s.flags & 16 = 16 then 1 else 0 end              as is_mobile2,
                          u_traffic_source.name                                      as traffic_name2,
                          u_traffic_source.is_paid                                   as traffic_is_paid2,
                          u_traffic_source.channel                                   as channel2,
                          s.ip_cc                                                    as ip_cc2,
                          ic.name                                                    as name2,
                          (coalesce(scns.click_price, 0) * ic.value_to_usd)::numeric as away_revenue2,
                          (coalesce(scns.click_price, 0))::numeric                   as away_revenue_origin_currency2,
                          s.id_current_traf_source as id_current_traf_source2,
                          s.session_create_page_type as session_create_page_type2,
                          case when s.flags & 2 = 2 then 1 else 0 end                as is_returned2,
                          ext.id                                                     as id_external

                   from public.session_click_no_serp scns
                            inner join public.session s on scns.date_diff = s.date_diff
                       and scns.id_session = s.id
                            inner join link_dbo.info_currency ic on ic.id = scns.id_currency
                            inner join link_auction.campaign ac on ac.id = scns.id_campaign
                            inner join link_auction.site ast on ac.id_site = ast.id
                            inner join link_auction.user au on au.id = ast.id_user
                            left join link_dbo.info_project
                                      on scns.id_project = info_project.id
                            left join link_auction.campaign
                                      on scns.id_campaign = campaign.id
                            left join link_dbo.u_traffic_source
                                      on s.id_traf_source = u_traffic_source.id
                            left join public.session_jdp sj
                                      on sj.id_click_no_serp = scns.id and sj.date_diff = scns.date_diff
                            left join public.session_away sa
                                      on sa.id_click_no_serp = scns.id and sa.date_diff = scns.date_diff
                            left join public.session_external ext
                                      on ext.id_jdp = sj.id or ext.id_away = sa.id

                   where (scns.click_flags & 256 > 0 or scns.click_flags & 512 > 0)
                     and scns.date_diff = start_datediff
                     and coalesce(s.flags, 0) & 1 = 0
                     and au.flags & 2 = 2
                     and coalesce(scns.flags, 0) & 16 = 0
               ) as Revenue
          group by country_id,
                   to_char(('1900-01-01'::date + date_diff), 'yyyy-mm-dd'),
                   project_iddd2,
                   project_name2,
                   campaign_name2,
                   away_type2,
                   id_campaign,
                   is_mobile2,
                   traffic_name2,
                   traffic_is_paid2,
                   channel2,
                   ip_cc2,
                   name2,
                   id_current_traf_source2,
                   session_create_page_type2,
                   is_returned2

          union all

          SELECT country_id,
                 'applies'                                                  as metric,
                 to_char(('1900-01-01'::date + SJ.date_diff), 'yyyy-mm-dd') as session_date,
                 SJ.job_id_project                                          as project_iddd6,
                 info_project.name                                          as project_name3,
                 campaign.name                                              as campaign_name3,
                 case
                     when SJ.letter_type is not null then concat('Letter Type ', letter_type)
                     when SJ.id_click is not null then 'Click'
                     --when SJ.id_jdp is not null then 'Jdp'
                     when SJ.id_click_no_serp is not null then 'No serp'
                     else 'Other' end                                       as jdp_type,
                 id_campaign,
                 case when session.flags & 16 = 16 then 1 else 0 end        as is_mobile3,
                 u_traffic_source.name                                      as traffic_name3,
                 u_traffic_source.is_paid                                   as traffic_is_paid3,
                 u_traffic_source.channel                                   as channel3,
                 session.ip_cc                                              as ip_cc3,
                 info_currency.name                                         as name3,
                 0::numeric                                                 as revenue,
                 0::numeric                                                 as away_revenue_origin_currency3,
                 count(SJ.id)                                               as value,
                 count(SA.id)                                               as conversions3,
                 session.id_current_traf_source as id_current_traf_source3,
                 session.session_create_page_type as session_create_page_type3,
                 case when session.flags & 2 = 2 then 1 else 0 end          as is_returned3

          from public.session_jdp SJ
                   left join public.session_jdp_action SJA
                             on SJ.date_diff = SJA.date_diff
                                 and SJ.id = SJA.id_jdp
                   left join public.session_apply SA
                             on SA.date_diff = SJA.date_diff
                                 and SA.id_src_jdp_action = SJA.id
                   join public.session
                        on SJ.date_diff = session.date_diff
                            and SJ.id_session = session.id
                   left join public.session_click SC
                             on SJ.date_diff = SC.date_diff
                                 and SJ.id_click = SC.id
                   left join link_dbo.u_traffic_source
                             on session.id_traf_source = u_traffic_source.id
                   left join link_dbo.info_project
                             on SJ.job_id_project = info_project.id
                   left join link_auction.campaign
                             on SC.id_campaign = campaign.id
                   left join link_dbo.info_currency
                             on SC.id_currency = info_currency.id
          where SJ.date_diff = start_datediff
            and session.flags & 1 != 1
            and sj.flags & 4 = 4
          group by country_id,
                   to_char(('1900-01-01'::date + SJ.date_diff), 'yyyy-mm-dd'),
                   SJ.job_id_project,
                   info_project.name,
                   campaign.name,
                   case
                       when SJ.letter_type is not null then concat('Letter Type ', letter_type)
                       when SJ.id_click is not null then 'Click'
                       when SJ.id_click_no_serp is not null then 'No serp'
                       else 'Other' end,
                   u_traffic_source.name,
                   u_traffic_source.is_paid,
                   u_traffic_source.channel,
                   session.ip_cc,
                   info_currency.name,
                   id_campaign,
                   case when session.flags & 16 = 16 then 1 else 0 end,
                   session.id_current_traf_source,
                   session.session_create_page_type,
                   case when session.flags & 2 = 2 then 1 else 0 end;

end;
    $$;
