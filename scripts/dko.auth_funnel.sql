select distinct
    sa.id_session,
    case when t.name is null then '' else t.name end as traffic_source,
    saa.date as action_date,
    cast(s.start_date as date) as date,
    s.cookie_label,
    saa.data,
    sa.source,
    sign(s.flags & 16) as platform,
    sa.id as id_auth,
    saa.id as id_auth_action,
    saa.type,
    saa.flags as "action",
    p.id as id_profile,
    p.date_diff as date_diff_create_profile,
    p.date_created as date_created_profile,
    p.date_updated as date_update_profile,
    p.is_submitted,
    p.data as profile_data,
    p.phone as profile_phone,
    p.email as profile_email,
    so.source_name
from imp.session_auth sa
join imp.session s on sa.id_session = s.id and sa.date_diff = s.date_diff
join dimension.auth_source so on sa.source = so.id
left join dimension.u_traffic_source t on t.id = s.id_traf_source and s.country = t.country
left join imp.session_auth_action saa on sa.id = saa.id_auth and saa.type in (1,3,6,7,8,9,10) and sa.country = s.country
left join imp.profile_cookie_labels pc on s.cookie_label = pc.cookie_labels and s.country = pc.country
left join imp.profiles p on p.id = pc.id_profile and p.country = pc.country
where s.country = 1
and s.date_diff = ${DT_NOW} - 1
and sa.source <> 8
and sa.source <> 14;
