-- хотілося би отримати наступні інкременти:
-- 1. Воронка користувача на етапі онбордингу до підписки.
--     1.1. Розділення по OS, Traffic Source (Organic/Banner/Paid).
-- 2. Поведінка користувача після підписки (порівняння з вебом).
-- 3. Як результат поведінки - Revenue.
-- Ad Hocs: 4. Скільки підписок у користувача? Скільки він дивиться вакансій за 1 сесію?



-- таблиця з трафік сорсами, типом OS (івент пишеться під час кожного запуску)
--truncate dap.mobile_app_install_traffic_source;
insert into dap.mobile_app_install_traffic_source
select distinct sfa.country_id,
                (sfa.action_data::jsonb ->> 'installationId')::varchar as installation_id,
                sfa.date as first_launch_datetime,
                (sfa.action_data::jsonb ->> 'utmSource')::varchar      as traffic_source,
                (sfa.action_data::jsonb ->> 'utmMedium')::varchar      as traffic_medium,
                (sfa.action_data::jsonb ->> 'utmCampaign')::varchar    as traffic_campaign,
                (sfa.action_data::jsonb ->> 'platform')::varchar    as platform,
                (sfa.action_data::jsonb ->> 'countryCode')::varchar    as country_code
from imp.session_feature_action sfa
where type in (10, 11)
  and date_diff = fn_get_date_diff('2022-06-01')
;

-- проміжна таблиця проходження степів онбордингу: гранулярність кожен івент
drop  table temp_mobile_app_onboarding_funnel_action;
create temporary table temp_mobile_app_onboarding_funnel_action as
select sf.id_session                                               as session_id,
       s.cookie_label,
       (sfa.action_data::jsonb ->> 'selectedCountryCode')::varchar as country_code,
       s.ip_cc,
       c.id                                                        as country_id,
       sf.date                                                     as country_screen_show_datetime,
       sf1.type                                                    as feature_type_id,
       sf1.date                                                    as screen_show_datetime,
       sfa1.type                                                   as action_type_id,
       sfa1.action_data                                            as action_datetime,
       ac.id_account                                               as account_id
from imp.session_feature sf
         join imp.session s
              on s.country = sf.country_id and
                 s.id = sf.id_session
         left join imp.session_feature_action sfa
                   on sf.country_id = sfa.country_id and
                      sf.id = sfa.id_session_feature and
                      sfa.type in (7)
         left join dimension.countries c
                   on lower(c.alpha_2) = (sfa.action_data::jsonb ->> 'selectedCountryCode')::varchar
         left join dimension.countries cs on s.country = cs.id
         left join imp.session s1
                   on s1.country = c.id and
                      s1.cookie_label = s.cookie_label and
                      s1.flags & 64 = 64 /*only mobile app session*/ and
                      --s1.start_date between s.start_date - '15 minutes'::interval and s.start_date + '1 days'::interval
                      (s1.start_date - cs.deviation_utc_time) between (s.start_date - cs.deviation_utc_time) - '15 minutes'::interval and (s.start_date - cs.deviation_utc_time) + '1 days'::interval
                        /*rlu, потребує переводу до єдиного часового поясу!!!*/
         left join imp.session_feature sf1
                   on sf1.country_id = c.id and
                      sf1.id_session = s1.id and
                      sf1.type in (6, 7, 8, 9)
         left join imp.session_feature_action sfa1
                   on sfa1.country_id = sf1.country_id and
                      sfa1.id_session_feature = sf1.id
         left join imp.email_account_interactions eai
                   on eai.country = sf1.country_id and
                      eai.id_session = sf1.id_session and
                      eai.interaction_type = 0
          left join imp.account_contact ac
               on ac.country = eai.country and
                  ac.id_account = eai.id_account and
                  ac.type = 2 /*mobile app account*/
where sf.type in (5)
  and s.flags & 64 = 64
  and ((s.flags & 1 = 0) or
       (s.flags & 1 = 1 and s.flags & 128 = 128) or
       (s.flags & 1 = 1 and s.flags & 64 = 64)
    )
  and sf.date_diff >= 44711
    ;


-- проміжна таблиця проходження степів онбордингу: гранулярність - юзер, відповідає на запитання, чи був пройдений конкретний етап воронки
---- переписати таблицю, зробити обмеженою в часі
truncate dap.mobile_app_onboarding_funnel;
create table dap.mobile_app_onboarding_funnel as
with funnel_with_doubles as (
    select country_id                                                         as country_id,
           ip_cc                                                              as ip_cc,
           cookie_label,
           case when country_code is not null then 1 else 0 end               as has_country_selection,
           country_screen_show_datetime                                       as country_screen_show_datetime,
           /*identifying first session with country screen view*/
           min(country_screen_show_datetime) over (partition by cookie_label) as country_screen_show_first_datetime,
           screen_show_datetime,
           case when feature_type_id = 6 then 1 else 0 end                    as saw_job_page,
           case when feature_type_id = 7 then 1 else 0 end                    as saw_region_page,
           account_id                                                         as account_id
from temp_mobile_app_onboarding_funnel_action
)
select max(country_id) as country_id,
       max (ip_cc) as ip_country_code,
       cookie_label,
       max(has_country_selection) as has_country_selection,
       country_screen_show_first_datetime as country_screen_show_first_datetime,
       max(saw_job_page) as saw_job_page,
       max(saw_region_page) as saw_region_page,
       max(account_id) as account_id
from funnel_with_doubles
where country_screen_show_datetime = country_screen_show_first_datetime
  and country_screen_show_first_datetime >= '2022-06-01'
group by cookie_label,
         country_screen_show_first_datetime
;


-- таблиця усіх створених підписок-акаунтів з описом, чи маємо інфо по воронці, сорсом та платформою (якщо є)
--truncate dap.mobile_app_account_source;
insert into dap.mobile_app_account_source
select ac.country                                     as country_id,
       ac.id_account                                  as account_id,
       ac.verify_date                                 as account_verification_datetime,
       ac.contact                                     as installation_id,
       s.cookie_label,
       s.id                                           as session_id,
       s.date_diff                                    as session_datediff,
       s.ip_cc,
       --case when tle.account_id > 0 then 1 else 0 end as is_in_funnel,
       maifs.traffic_source,
       maifs.platform
from imp.account_contact ac
         join imp.email_account_interactions eai
              on eai.country = ac.country and
                 eai.id_account = ac.id_account and
                 eai.interaction_type = 0
         join imp.session s
              on s.country = eai.country and
                 s.id = eai.id_session
--         left join dap.mobile_app_onboarding_funnel tle
--                   on tle.country_id = eai.country and
--                      tle.account_id = eai.id_account
         left join dap.mobile_app_install_traffic_source maifs
                   on  lower(ac.contact) = lower(maifs.installation_id)
where ac.type = 2
  and ac.verify_date = '2022-06-01'
  and s.flags & 64 = 64
  and ((s.flags & 1 = 0) or
       (s.flags & 1 = 1
           and s.flags & 128 = 128)
    )
;


--  таблиця з гранулярністю до сесії , кількості переглянутих вакансій, кількістю вакансій з away-ем та revenue, яке ми отримали
do
$$

    declare _dd_start int = (select fn_get_date_diff('2022-06-01'));

    begin
            --truncate dap.mobile_app_session_account_agg;
            insert into dap.mobile_app_session_account_agg
            select s.country as country_id,
                   s.cookie_label,
                   s.date_diff as session_datediff,
                   --fn_get_date_diff(ac.verify_date) as account_creation_datediff,
                   s.id as session_id,
                   min(sa.id_account) as account_id,
                   count(distinct sion.id_impression) as impression_on_screen_cnt,
                   count(distinct sc.id) as serp_click_cnt,
                   count(distinct saw.id) as serp_away_cnt,
                   count(distinct sal.sub_id_alert) as subscription_cnt
            from imp.session s
            join imp.session_account sa
              on sa.country = s.country and
                 sa.date_diff = s.date_diff and
                 sa.id_session = s.id
            join imp.account_contact ac
                   on ac.country = sa.country and
                      ac.id_account = sa.id_account and
                      ac.type = 2 /*only mobile app accounts*/
            left join imp.session_alertview sal
                 on sal.country = s.country and
                    sal.date_diff = s.date_diff and
                    sal.id_session = s.id
            left join imp.session_impression si
                   on si.country = sal.country and
                      si.date = sal.date_diff and
                      si.id_session = sal.id_session and
                      si.id_alertview = sal.id
            left join imp.session_impression_on_screen sion
                  on sion.country = si.country and
                     sion.date_diff = si.date and
                     sion.id_impression = si.id
            left join imp.session_click sc
                   on sc.country = sion.country and
                      sc.date_diff = sion.date_diff and
                      sc.id_impression = sion.id_impression
            left join imp.session_away saw
                   on saw.country = sc.country and
                      saw.date_diff = sc.date_diff and
                      saw.id_click = sc.id
            where s.flags &64 = 64 /*mobile app*/ and
                  s.flags &1 = 0 /*not bots*/ and
                  s.date_diff >= _dd_start  -- 44711
            group by s.country,
                   s.cookie_label,
                   s.date_diff,
                   s.id
            ;
    end

$$
;

-- revenue
insert into dap.mobile_app_session_revenue
select  s.country as country_id,
        s.date_diff as session_datediff,
        s.id as session_id,
        sum(sa.click_price * ic.value_to_usd) as revenue_usd,
        1 /*internal statistics*/ as revenue_type_id
      from imp.session_away sa
      join imp.session s on sa.country = s.country and
                            sa.date_diff = s.date_diff and
                            sa.id_session = s.id
      join dimension.info_currency ic on ic.country = sa.country and
                                         ic.id = sa.id_currency
      left join imp.auction_campaign ac on ac.country_id = sa.country and
                                           ac.id = sa.id_campaign
      left join imp.site ast on ac.country_id = ast.country and
                                ac.id_site = ast.id
      left join imp.auction_user au on au.country = ast.country and
                                       au.id = ast.id_user
      -- serp -> away
      left join imp.session_click sc on sc.country = sa.country and
                                        sc.date_diff = sa.date_diff and
                                        sc.id = sa.id_click

      left join imp.session_external ext on ext.country = sa.country and
                                            ext.date_diff = sa.date_diff and
                                            ext.id_away = sa.id
      where sa.date_diff = 44711 and
            s.flags & 64 = 64 /*mobile app*/ and
            s.flags & 1 = 0 and
            (sa.id_campaign = 0  or au.flags & 2 = 0) and
            sa.flags & 2 = 0
group by s.id,
         s.country,
         s.date_diff

      union all

      select s.country as country_id,
        s.date_diff as session_datediff,
        s.id as session_id,
        sum(sc.click_price * ic.value_to_usd) as revenue_usd,
        2 /*external statistics*/ as revenue_type_id
      from imp.session_click sc
      join imp.session s on sc.country = s.country and
                            sc.date_diff = s.date_diff and
                            sc.id_session = s.id
      join dimension.info_currency ic on ic.country = sc.country and
                                         ic.id = sc.id_currency
      left join imp.auction_campaign ac on ac.country_id = sc.country and
                                           ac.id = sc.id_campaign
      left join imp.site ast on ac.country_id = ast.country and
                                ac.id_site = ast.id
      left join imp.auction_user au on au.country = ast.country and
                                       au.id = ast.id_user
      left join imp.session_away sa on sa.country = sc.country and
                                       sc.date_diff = sa.date_diff and
                                       sc.id = sa.id_click
      left join imp.session_external ext on ext.country = sc.country and
                                            ext.date_diff = sc.date_diff and
                                            ext.id_away = sa.id
      where sc.date_diff = 44711 and
            s.flags & 64 = 64 /*mobile app*/ and
            s.flags & 1 = 0 and
            (sc.id_campaign = 0 or au.flags & 2 = 2) and
            sc.flags & 16 = 0
group by s.id,
         s.country,
         s.date_diff
;

-- представлення, яке порівнює arpu між застосунком та вебом
drop view dap.v_mobile_app_cum_arpu;
create or replace view dap.v_mobile_app_cum_arpu as
select c.alpha_2 as country_name,
       count(distinct ac.id_account) as account_cnt,
       case when ac.type = 2 then 'mobile app'
            when ac.type = 0 then 'email'
        end as account_type_name,
       maas.traffic_source,
       maas.platform,
       fn_get_date_diff(ac.verify_date) as account_verification_datediff,
       sum(case when ar.date_diff between fn_get_date_diff(ac.verify_date)  and fn_get_date_diff(ac.verify_date)+2 then ar.away_clicks_free+ar.away_clicks_premium else 0 end) as cumulative_away_3_days_cnt,
       sum(case when ar.date_diff between fn_get_date_diff(ac.verify_date)  and fn_get_date_diff(ac.verify_date)+6 then ar.away_clicks_free+ar.away_clicks_premium else 0 end) as cumulative_away_7_days_cnt,
       sum(case when ar.date_diff between fn_get_date_diff(ac.verify_date)  and fn_get_date_diff(ac.verify_date)+13 then ar.away_clicks_free+ar.away_clicks_premium else 0 end) as cumulative_away_14_days_cnt,
       sum(coalesce(ar.away_clicks_free+ar.away_clicks_premium,0)) as cumulative_away_cnt,
       sum(case when ar.date_diff between fn_get_date_diff(ac.verify_date)  and fn_get_date_diff(ac.verify_date)+2 then ar.total_revenue else 0 end) as cumulative_revenue_3_days_usd,
       sum(case when ar.date_diff between fn_get_date_diff(ac.verify_date)  and fn_get_date_diff(ac.verify_date)+6 then ar.total_revenue else 0 end) as cumulative_revenue_7_days_usd,
       sum(case when ar.date_diff between fn_get_date_diff(ac.verify_date)  and fn_get_date_diff(ac.verify_date)+13 then ar.total_revenue else 0 end) as cumulative_revenue_14_days_usd,
       sum(coalesce(ar.total_revenue,0)) as cumulative_revenue_usd,
       sum(case when ar.date_diff between fn_get_date_diff(ac.verify_date)  and fn_get_date_diff(ac.verify_date)+2 then ar.away_clicks_premium else 0 end) as cumulative_paid_away_3_days_cnt,
       sum(case when ar.date_diff between fn_get_date_diff(ac.verify_date)  and fn_get_date_diff(ac.verify_date)+6 then ar.away_clicks_premium else 0 end) as cumulative_paid_away_7_days_cnt,
       sum(case when ar.date_diff between fn_get_date_diff(ac.verify_date)  and fn_get_date_diff(ac.verify_date)+13 then ar.away_clicks_premium else 0 end) as cumulative_paid_away_14_days_cnt
from imp.account_contact ac
join dimension.countries c
  on c.id = ac.country
left join imp.account_revenue ar
  on ar.country = ac.country and
     ar.id_account = ac.id_account and
     ar.date_diff >= fn_get_date_diff( ac.verify_date)
left join dap.mobile_app_account_source maas
      on maas.country_id = ac.country and
         maas.account_id = ac.id_account
where ac.verify_date >= '2022-06-01'
 and ac.type in (0,2)
group by ac.type, fn_get_date_diff(ac.verify_date), ac.country, maas.traffic_source,
       maas.platform,
         c.alpha_2
;

-- представлення, яке дає агреговану інфу по активності користувачів у застосунку після онбордингу
drop view dap.v_mobile_app_session_agg;
create or replace view dap.v_mobile_app_account_session_agg as
select c.alpha_2 as country_code,
       maas.platform,
       maas.traffic_source,
       maas.account_id,
       maas.account_verification_datetime::date as account_verification_date,
       case when masaa.session_datediff = maas.session_datediff then 1 /*session in onboarding day*/
            when masaa.session_datediff > maas.session_datediff then 2 /*session after onboarding day*/
            else 0 /*undefined*/ end  as session_retention_type_id,
       0/*undefined*/ as session_source_id,
       count(distinct masaa.session_id) as session_cnt,
       sum(masaa.impression_on_screen_cnt) as impression_on_screen_cnt,
       sum(masaa.serp_click_cnt) as serp_click_cnt
from dap.mobile_app_account_source maas
join dimension.countries c
  on maas.country_id = c.id
left join dap.mobile_app_session_account_agg masaa
     on masaa.account_id = maas.account_id and
        masaa.session_datediff between fn_get_date_diff(maas.account_verification_datetime) and fn_get_date_diff(maas.account_verification_datetime)+2
group by c.alpha_2,
       maas.platform,
       maas.traffic_source,
       maas.account_verification_datetime::date,
         maas.account_id,
         case when masaa.session_datediff = maas.session_datediff then 1 /*session in onboarding day*/
            when masaa.session_datediff > maas.session_datediff then 2 /*session after onboarding day*/
            else 0 /*undefined*/ end;

create or replace view dap.v_mobile_app_session_agg as
with session_from_push as (
    select country,
           id_session,
           min(fn_get_date_diff(date_add)) as push_sent_datediff
    from imp.push_status ps
    where status_type in (31,32)
    group by country,
           id_session
),
     mobile_app_session_revenue as (select country_id,
                                           session_datediff,
                                           session_id,
                                           sum(revenue_usd) as revenue_usd
         from dap.mobile_app_session_revenue
         group by country_id,
                  session_datediff,
                  session_id)


select c.alpha_2 as country_code,
       maas.platform,
       maas.traffic_source,
       --masaa.account_id,
       maas.account_verification_datetime::date as account_verification_date,
       masaa.session_datediff,
       case when masaa.session_datediff = maas.session_datediff then 1 /*session in onboarding day*/
            when masaa.session_datediff > maas.session_datediff then 2 /*session after onboarding day*/
            else 0 /*undefined*/ end  as session_retention_type_id,
       case when sfp.id_session is not null then 1 /*push*/ else 0/*undefined*/ end as session_source_id,
       count(distinct masaa.session_id) as session_cnt,
       sum(masaa.impression_on_screen_cnt) as impression_on_screen_cnt,
       sum(masaa.serp_click_cnt) as serp_click_cnt,
       sum(masr.revenue_usd) as revenue_usd
from dap.mobile_app_session_account_agg masaa
left join dap.mobile_app_account_source maas
      on masaa.country_id = maas.country_id and
         masaa.account_id = maas.account_id
left join session_from_push sfp
       on sfp.country = masaa.country_id and
          sfp.id_session = masaa.session_id
left join mobile_app_session_revenue masr
    on masr.country_id = masaa.country_id and
       masr.session_id = masaa.session_id
join dimension.countries c
  on masaa.country_id = c.id

group by c.alpha_2,
       maas.platform,
       maas.traffic_source,
       maas.account_verification_datetime::date,
         masaa.session_datediff,
         --masaa.account_id,
         case when masaa.session_datediff = maas.session_datediff then 1 /*session in onboarding day*/
            when masaa.session_datediff > maas.session_datediff then 2 /*session after onboarding day*/
            else 0 /*undefined*/ end,
         case when sfp.id_session is not null then 1 /*push*/ else 0/*undefined*/ end;



create or replace view dap.v_mobile_app_session as
with session_from_push as (
    select country,
           id_session,
           min(fn_get_date_diff(date_add)) as push_sent_datediff
    from imp.push_status ps
    where status_type in (31,32)
    group by country,
           id_session
),
         mobile_app_session_revenue as (select country_id,
                                           session_datediff,
                                           session_id,
                                           sum(revenue_usd) as revenue_usd
         from dap.mobile_app_session_revenue
         group by country_id,
                  session_datediff,
                  session_id)

select c.alpha_2 as country_code,
       maas.platform,
       maas.traffic_source,
       masaa.account_id,
       maas.account_verification_datetime::date as account_verification_date,
       masaa.session_datediff,
       case when masaa.session_datediff = maas.session_datediff then 1 /*session in onboarding day*/
            when masaa.session_datediff > maas.session_datediff then 2 /*session after onboarding day*/
            else 0 /*undefined*/ end as session_retention_type_id,
       case when sfp.id_session is not null then 1 /*push*/ else 0/*undefined*/ end as session_source_id,
       masaa.session_id as session_id,
       sum(masaa.impression_on_screen_cnt) as impression_on_screen_cnt,
       sum(masaa.serp_click_cnt) as serp_click_cnt,
       sum(masr.revenue_usd) as revenue_usd
from dap.mobile_app_session_account_agg masaa
left join dap.mobile_app_account_source maas
      on masaa.country_id = maas.country_id and
         masaa.account_id = maas.account_id
left join session_from_push sfp
       on sfp.country = masaa.country_id and
          sfp.id_session = masaa.session_id
join dimension.countries c
  on masaa.country_id = c.id
left join mobile_app_session_revenue masr
    on masr.country_id = masaa.country_id and
       masr.session_id = masaa.session_id
group by c.alpha_2,
         maas.platform,
         maas.traffic_source,
         maas.account_verification_datetime::date,
         masaa.session_datediff,
         masaa.account_id,
         masaa.session_id,
         case when masaa.session_datediff = maas.session_datediff then 1 /*session in onboarding day*/
              when masaa.session_datediff > maas.session_datediff then 2 /*session after onboarding day*/
              else 0 /*undefined*/ end,
         case when sfp.id_session is not null then 1 /*push*/ else 0/*undefined*/ end
;



-- Retention of web vs app users on Job_XX MS SQL
-- table mobile_app_retention_vs_web

declare @country_id int = ${country_id};

select         --@country as country_id,
               case when ac.type = 0 then 1 /*email user from web*/
                    when ac.type = 2 then 2 /*mobile app user*/
                end as user_type_id,
               CONVERT(INT, ac.verify_date)            as verify_date_diff,
               count( distinct ac.id_account )        as user_cnt,
               count( distinct case when s.date_diff - CONVERT(INT, ac.verify_date)  between 1 and 7 then ac.id_account  end) as week_retention_user_cnt,
               count( distinct case when s.date_diff - CONVERT(INT, ac.verify_date)  between 8 and 14 then ac.id_account  end) as second_week_retention_user_cnt,
               count( distinct case when s.date_diff - CONVERT(INT, ac.verify_date)  between 15 and 21 then ac.id_account  end) as third_week_retention_user_cnt,
               count( distinct case when s.date_diff - CONVERT(INT, ac.verify_date)  between 22 and 30 then ac.id_account  end) as fourth_week_retention_user_cnt,
               count( distinct case when s.date_diff - CONVERT(INT, ac.verify_date)  between 1 and 7 then s.id  end) as first_week_session_cnt,
               count( distinct case when s.date_diff - CONVERT(INT, ac.verify_date)  between 8 and 14 then s.id  end) as second_week_session_cnt,
               count( distinct case when s.date_diff - CONVERT(INT, ac.verify_date)  between 15 and 21 then s.id  end) as third_week_session_cnt,
               count( distinct case when s.date_diff - CONVERT(INT, ac.verify_date)  between 22 and 30 then s.id  end) as fourth_week_session_cnt,
               count( distinct case when s.date_diff - CONVERT(INT, ac.verify_date)  between 1 and 7 then sc.id  end) as first_week_serp_click_cnt,
               count( distinct case when s.date_diff - CONVERT(INT, ac.verify_date)  between 8 and 14 then sc.id  end) as second_week_serp_click_cnt,
               count( distinct case when s.date_diff - CONVERT(INT, ac.verify_date)  between 15 and 21 then sc.id  end) as third_week_serp_click_cnt,
               count( distinct case when s.date_diff - CONVERT(INT, ac.verify_date)  between 22 and 30 then sc.id  end) as fourth_week_serp_click_cnt
        from account_contact ac with (nolock)
        left join dbo.session_account sa with (nolock)
                on sa.id_account = ac.id_account and
                   sa.date_diff between  CONVERT(INT, ac.verify_date) and CONVERT(INT, ac.verify_date) +30
        left join dbo.session s with (nolock)
          on sa.date_diff = s.date_diff and
             sa.id_session = s.id and
             s.flags & 1 = 0
        left join dbo.session_click sc with (nolock)
              on sc.date_diff = s.date_diff and
                 sc.id_session = s.id
        where  ac.verify_date between '2022-05-15' and '2022-07-10' and
              ac.type in (0,2)
group by       CONVERT(INT, ac.verify_date),
         case when ac.type = 0 then 1 /*email user from web*/
                    when ac.type = 2 then 2 /*mobile app user*/
                end;

drop view dap.v_mobile_app_retention_vs_web;
create or replace view dap.v_mobile_app_retention_vs_web as
select c.alpha_2 as country_code,
       marvw.user_type_id,
       verify_date_diff,
       user_cnt,
       week_retention_user_cnt,
       second_week_retention_user_cnt,
       third_week_retention_user_cnt,
       fourth_week_retention_user_cnt,
first_week_session_cnt,
second_week_session_cnt,
third_week_session_cnt,
       fourth_week_session_cnt,
       first_week_serp_click_cnt,
       second_week_serp_click_cnt,

       third_week_serp_click_cnt,
       fourth_week_serp_click_cnt
from dap.mobile_app_retention_vs_web marvw
join dimension.countries c
  on c.id = marvw.country_id;



--create or replace view dap.v_mobile_app_cum_arpu as
select c.alpha_2 as country_name,
       count(distinct ac.id_account) as account_cnt,
       case when ac.type = 2 then 'mobile app'
            when ac.type = 0 then 'email'
        end as account_type_name,
       maas.traffic_source,
       st.group_num as test_group,
       fn_get_date_diff(ac.verify_date) as account_verification_datediff,
       sum(case when ar.date_diff between fn_get_date_diff(ac.verify_date)  and fn_get_date_diff(ac.verify_date)+13 then ar.away_clicks_free+ar.away_clicks_premium else 0 end) as cumulative_away_14_days_cnt,
       sum(case when ar.date_diff between fn_get_date_diff(ac.verify_date)  and fn_get_date_diff(ac.verify_date)+6 then ar.away_clicks_free+ar.away_clicks_premium else 0 end) as cumulative_away_7_days_cnt,
       sum(coalesce(ar.away_clicks_free+ar.away_clicks_premium,0)) as cumulative_away_cnt,
       sum(case when ar.date_diff between fn_get_date_diff(ac.verify_date)  and fn_get_date_diff(ac.verify_date)+13 then ar.total_revenue else 0 end) as cumulative_revenue_14_days_usd,
       sum(case when ar.date_diff between fn_get_date_diff(ac.verify_date)  and fn_get_date_diff(ac.verify_date)+6 then ar.total_revenue else 0 end) as cumulative_revenue_7_days_usd,
       sum(coalesce(ar.total_revenue,0)) as cumulative_revenue_usd
from imp.account_contact ac
join dimension.countries c
  on c.id = ac.country
left join imp.account_revenue ar
  on ar.country = ac.country and
     ar.id_account = ac.id_account and
     ar.date_diff >= fn_get_date_diff( ac.verify_date)
left join dap.mobile_app_account_source maas
      on maas.country_id = ac.country and
         maas.account_id = ac.id_account
left join imp.email_account_interactions eai
      on eai.country = ac.country and
         eai.id_account = ac.id_account
left join dap.test_interaction st
       on st.test_datediff = eai.date_diff and
          st.session_id = eai.id_session
where ac.verify_date >= '2022-06-01'
 and ac.type in (0,2)
group by ac.type, fn_get_date_diff(ac.verify_date), ac.country, maas.traffic_source,
         c.alpha_2,st.group_num;
