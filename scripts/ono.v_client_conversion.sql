create or replace view ono.v_client_conversion
            (country, session_date, id_project, project_name, campaign_name, away_type, is_mobile, traffic_name,
             traffic_is_paid, away_revenue, aways, conversions, conversion_type)
as
WITH country AS (SELECT conversion_away_connection.country
                 FROM imp.conversion_away_connection
                 WHERE conversion_away_connection.country <> 8
                 GROUP BY conversion_away_connection.country
                 HAVING count(*) > 10
                 UNION
                 SELECT countries.id
                 FROM imp_statistic.conversions
                 JOIN dimension.countries ON conversions.country::text = countries.alpha_2::text),
     row_aways AS (SELECT countries.name_country_eng                     AS country,
                          session_away.date                              AS session_date,
                          session_away.id_project,
                          info_project.name                              AS project_name,
                          campaign.name                                  AS campaign_name,
                          CASE
                              WHEN session_away.letter_type IS NOT NULL
                                  THEN concat('Letter Type ', session_away.letter_type)
                              WHEN session_away.id_click IS NOT NULL THEN 'Click'::text
                              WHEN session_away.id_jdp IS NOT NULL THEN 'Jdp'::text
                              WHEN session_away.id_click_no_serp IS NOT NULL THEN 'No serp'::text
                              ELSE 'Other'::text
                              END                                        AS away_type,
                          session_away.id_campaign,
                          CASE
                              WHEN (session_away.session_flags & 16) = 16 THEN 1
                              ELSE 0
                              END                                        AS is_mobile,
                          u_traffic_source.name                          AS traffic_name,
                          u_traffic_source.is_paid                       AS traffic_is_paid,
                          COALESCE(session_away.click_price, 0::numeric) AS click_price,
                          info_currency.value_to_usd,
                          session_away.session_away_id,
                          conversion_away_connection.id_session_away
                   FROM imp.session_attached_session_away session_away
                   LEFT JOIN dimension.info_project ON session_away.session_away_country = info_project.country AND
                                                       session_away.id_project = info_project.id
                   LEFT JOIN dimension.u_traffic_source
                             ON COALESCE(session_away.id_current_traf_source, session_away.id_traf_source) =
                                u_traffic_source.id
                   LEFT JOIN (SELECT DISTINCT conversion_away_connection_1.country,
                                              conversion_away_connection_1.id_session_away
                              FROM imp.conversion_away_connection conversion_away_connection_1) conversion_away_connection
                             ON conversion_away_connection.country = session_away.session_away_country AND
                                conversion_away_connection.id_session_away = session_away.session_away_id
                   LEFT JOIN imp.campaign ON session_away.session_away_country = campaign.country AND
                                             session_away.id_campaign = campaign.id
                   LEFT JOIN dimension.info_currency ON session_away.session_away_country = info_currency.country AND
                                                        session_away.id_currency = info_currency.id
                   LEFT JOIN dimension.countries ON session_away.session_away_country = countries.id
                   JOIN country ON session_away.session_away_country = country.country
                   WHERE (session_away.session_away_flags & 2) = 0
                     AND session_away.session_away_date_diff = 44376),
     aways AS (SELECT row_aways.country,
                      row_aways.session_date,
                      row_aways.id_project,
                      row_aways.project_name,
                      row_aways.id_campaign,
                      row_aways.campaign_name,
                      row_aways.away_type,
                      row_aways.is_mobile,
                      row_aways.traffic_name,
                      row_aways.traffic_is_paid,
                      sum(COALESCE(row_aways.click_price, 0::numeric) * row_aways.value_to_usd) AS away_revenue,
                      count(row_aways.session_away_id)                                          AS aways,
                      count(row_aways.id_session_away)                                          AS conversions
               FROM row_aways
               GROUP BY row_aways.country, row_aways.session_date, row_aways.id_project, row_aways.project_name,
                        row_aways.id_campaign, row_aways.campaign_name, row_aways.away_type, row_aways.is_mobile,
                        row_aways.traffic_name, row_aways.traffic_is_paid),
     daily_aways AS (SELECT aways.country,
                            aways.session_date,
                            aways.project_name,
                            'Daily_Manual'::text         AS conversion_type,
                            sum(aways.away_revenue)      AS away_revenue,
                            sum(aways.aways)             AS aways,
                            sum(conversions.conversions) AS conversions
                     FROM aways
                     JOIN imp_statistic.conversions ON aways.country::text = conversions.country::text AND
                                                       aways.id_project = conversions.id_project AND
                                                       aways.session_date = conversions.date AND
                                                       conversions.id_date_period = 1
                     GROUP BY aways.country, aways.session_date, aways.project_name),
     monthy_aways AS (SELECT aways.country,
                             aways.session_date,
                             aways.project_name,
                             aways.conversion_type,
                             aways.away_revenue,
                             aways.aways,
                             conversions.conversions
                      FROM (SELECT aways_1.country,
                                   make_date(date_part('year'::text, aways_1.session_date)::integer,
                                             date_part('month'::text, aways_1.session_date)::integer,
                                             1)              AS session_date,
                                   aways_1.id_project,
                                   aways_1.project_name,
                                   'Monthly_Manual'::text    AS conversion_type,
                                   sum(aways_1.away_revenue) AS away_revenue,
                                   sum(aways_1.aways)        AS aways
                            FROM aways aways_1
                            GROUP BY aways_1.country,
                                     (make_date(date_part('year'::text, aways_1.session_date)::integer,
                                                date_part('month'::text, aways_1.session_date)::integer, 1)),
                                     aways_1.project_name, aways_1.id_project) aways
                      JOIN imp_statistic.conversions ON aways.country::text = conversions.country::text AND
                                                        aways.id_project = conversions.id_project AND
                                                        aways.session_date = conversions.date AND
                                                        conversions.id_date_period = 2)
SELECT aways.country,
       aways.session_date,
       aways.id_project,
       aways.project_name,
       aways.campaign_name,
       aways.away_type,
       aways.is_mobile,
       aways.traffic_name,
       aways.traffic_is_paid,
       aways.away_revenue,
       aways.aways,
       aways.conversions,
       'PostBack'::text AS conversion_type
FROM aways
UNION ALL
SELECT daily_aways.country,
       daily_aways.session_date,
       NULL::integer           AS id_project,
       daily_aways.project_name,
       NULL::character varying AS campaign_name,
       NULL::text              AS away_type,
       NULL::integer           AS is_mobile,
       NULL::character varying AS traffic_name,
       NULL::boolean           AS traffic_is_paid,
       daily_aways.away_revenue,
       daily_aways.aways,
       daily_aways.conversions,
       'Daily Manual'::text    AS conversion_type
FROM daily_aways
UNION ALL
SELECT monthy_aways.country,
       monthy_aways.session_date,
       NULL::integer           AS id_project,
       monthy_aways.project_name,
       NULL::character varying AS campaign_name,
       NULL::text              AS away_type,
       NULL::integer           AS is_mobile,
       NULL::character varying AS traffic_name,
       NULL::boolean           AS traffic_is_paid,
       monthy_aways.away_revenue,
       monthy_aways.aways,
       monthy_aways.conversions,
       'Monthly Manual'::text  AS conversion_type
FROM monthy_aways;

alter table ono.v_client_conversion
    owner to ono;
