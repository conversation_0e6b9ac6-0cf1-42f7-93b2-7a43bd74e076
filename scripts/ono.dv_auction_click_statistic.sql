create view dv_auction_click_statistic
            (country_id, country, date, action_date, id_project, site, contractor, id_campaign, currency, campaign,
             click_count, session_count, ip_count, to_jdp_count, from_jdp_count, total_value, id_user, test_count,
             budget, organic_count, paid_overflow_count, manager, impressions_count, user_currency_total_value,
             contractor_size, job_count, is_job_ad_exchange_project, duplicated_count, total_value_discount,
             external_click_cnt, external_revenue, away_count)
as
WITH auction AS (SELECT auction_click_statistic_analytics.country_id,
                        countries.alpha_2                            AS country,
                        auction_click_statistic_analytics.date::date AS date,
                        auction_click_statistic_analytics.id_project,
                        auction_click_statistic_analytics.site,
                        auction_click_statistic_analytics.contractor,
                        auction_click_statistic_analytics.id_campaign,
                        auction_click_statistic_analytics.campaign,
                        auction_click_statistic_analytics.currency,
                        auction_click_statistic_analytics.click_count,
                        auction_click_statistic_analytics.session_count,
                        auction_click_statistic_analytics.ip_count,
                        auction_click_statistic_analytics.total_value + COALESCE(
                                - (auction_click_statistic_analytics.total_value -
                                   (auction_click_statistic_analytics.click_count -
                                    COALESCE(auction_click_statistic_analytics.test_count, 0) - COALESCE(
                                            CASE
                                                WHEN auction_click_statistic_analytics.test_count > 0 THEN 0
                                                ELSE auction_click_statistic_analytics.duplicated_count
                                                END, 0))::numeric * (auction_click_statistic_analytics.total_value /
                                                                     COALESCE(auction_click_statistic_analytics.click_count, 1)::numeric)),
                                0::numeric)                          AS total_value,
                        auction_click_statistic_analytics.id_user,
                        auction_click_statistic_analytics.test_count,
                        auction_click_statistic_analytics.organic_count,
                        auction_click_statistic_analytics.paid_overflow_count,
                        auction_click_statistic_analytics.duplicated_count,
                        auction_click_statistic_analytics.total_value_discount,
                        auction_click_statistic_analytics.away_count
                 FROM aggregation.auction_click_statistic_analytics
                 JOIN dimension.countries ON auction_click_statistic_analytics.country_id = countries.id),
     acs_1 AS (SELECT countries.id                                    AS country_id,
                      acs_1.country,
                      acs_1.date::timestamp without time zone         AS date,
                      acs_1.date::timestamp without time zone         AS action_date,
                      acs_1.id_project,
                      acs_1.site,
                      acs_1.contractor,
                      acs_1.id_campaign,
                      acs_1.campaign,
                      acs_1.currency,
                      sum(acs_1.click_count - acs_1.duplicated_count) AS click_count,
                      sum(acs_1.session_count)                        AS session_count,
                      sum(acs_1.ip_count)                             AS ip_count,
                      0                                               AS to_jdp_count,
                      0                                               AS from_jdp_count,
                      sum(acs_1.total_value)                          AS total_value,
                      acs_1.id_user,
                      sum(acs_1.test_count)                           AS test_count,
                      0                                               AS budget,
                      sum(acs_1.organic_count)                        AS organic_count,
                      sum(acs_1.paid_overflow_count)                  AS paid_overflow_count,
                      v_sale_manager.sale_manager                     AS manager,
                      0                                               AS job_count,
                      sum(acs_1.duplicated_count)                     AS duplicated_count,
                      sum(acs_1.total_value_discount)                 AS total_value_discount,
                      sum(acs_1.away_count)                           AS away_count
               FROM auction acs_1
               LEFT JOIN dimension.info_currency ic_1
                         ON acs_1.country_id = ic_1.country AND acs_1.currency::text = ic_1.name::text
               LEFT JOIN dimension.countries ON acs_1.country::text = countries.alpha_2::text
               LEFT JOIN aggregation.v_sale_manager
                         ON countries.id = v_sale_manager.country AND acs_1.id_project = v_sale_manager.id_project
               GROUP BY acs_1.country, acs_1.date, acs_1.id_project, acs_1.site, acs_1.contractor, acs_1.id_campaign,
                        acs_1.campaign, acs_1.id_user, v_sale_manager.sale_manager, countries.id, acs_1.currency),
     acs AS (SELECT acs_1.country_id,
                    acs_1.country,
                    acs_1.date,
                    acs_1.action_date,
                    acs_1.id_project,
                    acs_1.site,
                    acs_1.contractor,
                    acs_1.id_campaign,
                    acs_1.campaign,
                    acs_1.currency,
                    acs_1.click_count,
                    acs_1.session_count,
                    acs_1.ip_count,
                    acs_1.to_jdp_count,
                    acs_1.from_jdp_count,
                    acs_1.total_value,
                    acs_1.id_user,
                    acs_1.test_count,
                    acs_1.budget,
                    acs_1.organic_count,
                    acs_1.paid_overflow_count,
                    acs_1.manager,
                    first_value(acs_1.currency)
                    OVER (PARTITION BY acs_1.country, acs_1.id_user ORDER BY acs_1.date DESC)    AS user_currency,
                    row_number() OVER (PARTITION BY acs_1.country, acs_1.date, acs_1.id_project) AS campaign_number,
                    acs_1.job_count,
                    acs_1.duplicated_count,
                    acs_1.total_value_discount,
                    acs_1.away_count
             FROM acs_1),
     external_stat AS (SELECT app.id_project,
                              app.country,
                              app.datetime::date  AS date,
                              count(app.id_click) AS external_click_cnt,
                              sum(app.price)      AS external_revenue,
                              'appcast'::text     AS type
                       FROM imp_statistic.conversions_appcast app
                       WHERE app.action_type = 'click'::text
                       GROUP BY app.id_project, (app.datetime::date), app.country
                       UNION ALL
                       SELECT ind.id_project,
                              ind.country,
                              ind.date         AS dates,
                              sum(ind.clicks)  AS count_clic,
                              sum(ind.revenue) AS external_revenue,
                              'indeed'::text   AS type
                       FROM imp_statistic.conversions_indeed ind
                       GROUP BY ind.id_project, ind.country, ind.date)
SELECT acs.country_id,
       acs.country::character varying(10)                AS country,
       acs.date,
       acs.action_date,
       acs.id_project,
       acs.site,
       acs.contractor,
       acs.id_campaign,
       acs.user_currency                                 AS currency,
       acs.campaign,
       acs.click_count,
       acs.session_count,
       acs.ip_count,
       acs.to_jdp_count::bigint                          AS to_jdp_count,
       acs.from_jdp_count::bigint                        AS from_jdp_count,
       acs.total_value,
       acs.id_user,
       acs.test_count,
       acs.budget::numeric                               AS budget,
       acs.organic_count,
       acs.paid_overflow_count,
       acs.manager,
       ims.impressions_count,
       acs.total_value / COALESCE((SELECT ich.value_to_usd
                                   FROM dimension.info_currency_history ich
                                   WHERE acs.country_id = ich.country
                                     AND ich.date <= acs.date
                                     AND ich.id_currency = ic.id
                                     AND ich.value_to_usd IS NOT NULL
                                     AND ich.value_to_usd <> 0::numeric
                                   ORDER BY ich.date DESC
                                   LIMIT 1), 1::numeric) AS user_currency_total_value,
       '0'::text                                         AS contractor_size,
       acs.job_count::bigint                             AS job_count,
       CASE
           WHEN ip.hide_in_search THEN 1
           ELSE 0
           END                                           AS is_job_ad_exchange_project,
       acs.duplicated_count,
       acs.total_value_discount,
       CASE
           WHEN acs.campaign_number = 1 THEN external_stat.external_click_cnt
           ELSE NULL::bigint
           END                                           AS external_click_cnt,
       CASE
           WHEN acs.campaign_number = 1 THEN external_stat.external_revenue
           ELSE NULL::bigint::double precision
           END                                           AS external_revenue,
       acs.away_count
FROM acs
LEFT JOIN imp_statistic.impression_statistic ims
          ON ims.country = acs.country_id AND ims.id_campaign = acs.id_campaign AND
             fn_get_date_diff(acs.date::date::timestamp without time zone) = ims.date_diff
LEFT JOIN dimension.info_currency ic ON acs.country_id = ic.country AND acs.user_currency::text = ic.name::text
LEFT JOIN dimension.info_project ip ON ip.country = acs.country_id AND ip.id = acs.id_project
LEFT JOIN external_stat
          ON acs.country::text = external_stat.country::text AND acs.id_project = external_stat.id_project AND
             acs.date = external_stat.date;

alter table dv_auction_click_statistic
    owner to ono;
