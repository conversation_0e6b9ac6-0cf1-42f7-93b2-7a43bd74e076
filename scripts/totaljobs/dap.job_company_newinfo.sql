select j.title as job_title,
	   jr.url as job_url,
	   region_from_source.value as location,
	   country_from_source.value as country,
	   jobtype_from_source.value as employment_type,
	   round(1.0 * jsr.val1 * irc.factor * ic.value_to_usd / 12,0) as salary_monthly_usd,
	   cast(cast (getdate() as date) as datetime) as snapshot_date,
	   cast(cast(j.date_created as date) as datetime) as creation_date,
	   j.company_name as company_website
from job j with(nolock)
inner join job_region jr with(nolock) on jr.id_job = j.id
left join field_from_source region_from_source with(nolock) on j.id = region_from_source.id_job and region_from_source.name = 'region_from_source'
left join field_from_source country_from_source with(nolock) on j.id = country_from_source.id_job and country_from_source.name = 'country_from_source'
left join field_from_source jobtype_from_source with(nolock) on j.id = jobtype_from_source.id_job and jobtype_from_source.name = 'jobtype_from_source'
left join field_from_source id_from_source with(nolock) on j.id = id_from_source.id_job and id_from_source.name = 'id_from_source'
left join job_salary_raw jsr on jsr.id_job = j.id
left join job_apply_url jau on jau.id_job = j.id
left join job_phone_raw job_phone on job_phone.id_job = j.id
left join info_rate_config irc on jsr.id_rate = irc.id
left join info_currency ic with (nolock) on ic.id = irc.id_currency
