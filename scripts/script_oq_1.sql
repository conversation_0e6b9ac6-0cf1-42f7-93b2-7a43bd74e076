/*пример как использовать линк на дургой сервер к большим таблицам, в таком случае он переносит только результат запроса, когда в другом варианте он тянет себе всю таблицу и только потом добавляет условие*/
SELECT * FROM OPENQUERY([dwh], 'select * from dimension.countries order by id')

-- считаю первый сабмит профиля на текущем куки
SELECT *
into #cookie_profile
FROM OPENQUERY([dwh],
						'select pcl.cookie_labels,
								min(ps.submission_datediff) as min_submission_datediff						
						from imp.profile_cookie_labels pcl
						join profile.profile_submitted ps
							on ps.id = pcl.id_profile
						group by pcl.cookie_labels');

ALTER TABLE #cookie_profile ADD PRIMARY KEY   (cookie_labels);

-- считаю сессии, в которых был заход с письма или вайбера
select distinct s.id as session_id, s.date_diff
into #session_from_message
from dbo.session s with (nolock)
     left join dbo.session_click_message scm with (nolock)
     on scm.date_diff = s.date_diff
     and scm.id_session = s.id
left join dbo.session_jdp_message sjm with (nolock)
    on sjm.date_diff = s.date_diff
    and sjm.id_session = s.id
left join dbo.session_alertview sa with (nolock)
     on sa.date_diff = s.date_diff
     and sa.id_session = s.id
left join dbo.session_profile sp with (nolock)
    on sp.date_diff = s.date_diff
   and sp.source in (5, 6, 9, 10) /*viber messages*/
and sp.id_session = s.id
left join dbo.session_jdp sj with (nolock)
  on sj.id_session = s.id
 and sj.date_diff = s.date_diff
where sj.letter_type > 0 or sj.flags & 8=8 or sp.id is not null or sa.id is not null or scm.id_message is not null or sjm.id_message is not null
  and s.date_diff between 44247 and 44267;
ALTER TABLE #session_from_message ADD PRIMARY KEY   (session_id, date_diff);


-- таблица с ботами, которых не исключили на проде
SELECT *
into #bot_session
FROM OPENQUERY([dwh],
						'select s_dwh.id as session_id,
								s_dwh.date_diff
						from imp.session s_dwh
						where s_dwh.country = 1
						  and s_dwh.is_bot = 1
						  and s_dwh.date_diff between 44247 and 44267');

ALTER TABLE #bot_session ADD PRIMARY KEY   (session_id, date_diff);


SELECT *
into #session_hot
FROM OPENQUERY([dwh],
					'select distinct
						   ps.session_id,
						   ps.session_datediff
					from profile.profile_session ps
					where country_id = 1
					  and session_datediff between 44247 and 44267
					and exists (select 1
								from profile.profile_session ps_lag
								where ps_lag.country_id = ps.country_id
								 and ps_lag.profile_id = ps.profile_id
								 and ps_lag.session_id <> ps.session_id
								 and ps_lag.sign_in_datetime < ps.sign_in_datetime
								 and ps_lag.sign_in_datetime >= ps.sign_in_datetime - interval ''7 days''
								 and ps_lag.session_datediff between 44247-7 and 44267)');

ALTER TABLE #session_hot ADD PRIMARY KEY   (session_id, session_datediff);


-- считаю сессии и отклики по дням для 8 сегментов пользователей: с-профилем/без-профиля и сессия-из-месседжа/сессия-не-из-месседжа, тёплая сессия - холодная (был в течении 7 дней - не был)
select s.date_diff,
       case when cp.cookie_labels is not null then 1 else 0 end as has_profile,
       case when sfm.session_id is not null then 1 else 0 end as is_from_message,
       case when sh.session_id is not null then 1 else 0 end as is_hot, 
       count(distinct s.id) as session_cnt,
       count(distinct case when sja.type = 2 then sja.id_jdp end) as apply_cnt,
       count(distinct case when sja.type in (13, 19, 21, 34)  then sja.id_jdp end)*0.27 as call_cnt
--into #temp_res
from dbo.session s with (nolock)
left join  #session_hot sh 
       on sh.session_id = s.id and sh.session_datediff = s.date_diff
left join #cookie_profile cp
    on cp.cookie_labels= s.cookie_label
   and s.date_diff > min_submission_datediff
left join #session_from_message sfm
     on sfm.date_diff = s.date_diff
    and sfm.session_id = s.id
left join dbo.session_jdp sj with (nolock)
  on sj.id_session = s.id
 and sj.date_diff = s.date_diff
left join dbo.session_jdp_action sja with (nolock)
     on sja.date_diff = sj.date_diff
    and sja.id_jdp = sj.id
    and sj.job_id_project = -1
    and ((sja.type = 2 and sja.flags & 1 = 1)
or sja.type in (13, 19, 21, 34))
left join #bot_session s_dwh on s_dwh.session_id = s.id and s.date_diff = s_dwh.date_diff
where s.date_diff between 44247 and 44267 and s.flags&1 = 0 and s.flags &4 = 0 /*kill bots*/
and s_dwh.session_id is null
group by s.date_diff,
       case when cp.cookie_labels is not null then 1 else 0 end,
       case when sfm.session_id is not null then 1 else 0 end,
       case when sh.session_id is not null then 1 else 0 end
;
