-- Запрос на вставку данных с DataGrip №2
-- delete from job_seeker.user_apply where jdp_viewed_datediff = 44225;

explain insert into job_seeker.user_apply (country_id, jdp_viewed_datediff, jdp_id, account_id, phone, email, profile_id, user_id,
                                   user_type_id, session_id, job_uid, apply_flag, cookie_label, apply_id, jdp_viewed_datetime, device_id)
select v.country_id,
       v.jdp_viewed_datediff as jdp_viewed_datediff,
       v.jdp_id,
       v.account_id,
       ja.phone,
       ja.email,
       (select  case when count(distinct pa.id_profile) > 1 then -1 else max(pa.id_profile) end
           from imp.profile_accounts pa
       where pa.id_accounts = v.account_id and pa.country = v.country_id and pa.id_accounts != 0
           ) as id_profile,
       coalesce(
                       coalesce(case
                                     when substr(replace(ja.phone, ' ', ''), 1, 1) = '0'
                                         then '38' || substr(replace(ja.phone, ' ', ''), 1, 10)
                                     when substr(replace(ja.phone, ' ', ''), 1, 1) = '+'
                                         then substr(replace(ja.phone, ' ', ''), 2, 13)
                                     else substr(replace(ja.phone, ' ', ''), 1, 12)
                           end::varchar(30)
                           , ja.email
                           )::varchar(30),
           v.cookie_label::varchar(30)) as user_id,
               coalesce(case when ja.phone is not null then 1 else null end,
               case when ja.email is not null then 3
                    when v.cookie_label is not null then 4 else null end) as user_type_id,   --create table user_type
       v.session_id as session_id,
       v.job_uid as job_uid,
       v.apply_flag,
       v.cookie_label,
       af.apply_id as apply_id,
       v.jdp_viewed_datetime as jdp_viewed_datetime,
       v.device_type
from apply.jdp v
join apply.application_form_conversion af on af.jdp_viewed_datediff = v.jdp_viewed_datediff and af.jdp_id = v.jdp_id and af.country_id = v.country_id
left join employer.job_apply ja on ja.id_session_apply = af.apply_id and ja.sources = 1
where v.country_id = 1
  and af.apply_id is not null
  and v.jdp_flag in (1,3)
  and v.jdp_viewed_datediff = 44225;

/*
date_diff - 44225 (44225 - 44232, !!!!!!44220!!!!!!);
rows - 3013;
time - 10 s 298 ms;
cost - 25539.60..395348.90;
*/
