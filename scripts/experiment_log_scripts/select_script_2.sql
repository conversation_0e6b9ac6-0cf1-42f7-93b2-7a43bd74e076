-- Запрос на выборку данных №2:

explain select v.country_id,
       v.jdp_viewed_datediff as jdp_viewed_datediff,
       v.jdp_id,
       v.account_id,
       ja.phone,
       ja.email,
       (select  case when count(distinct pa.id_profile) > 1 then -1 else max(pa.id_profile) end
           from imp.profile_accounts pa
       where pa.id_accounts = v.account_id and pa.country = v.country_id and pa.id_accounts != 0
           ) as id_profile,
       coalesce(
                       coalesce(case
                                     when substr(replace(ja.phone, ' ', ''), 1, 1) = '0'
                                         then '38' || substr(replace(ja.phone, ' ', ''), 1, 10)
                                     when substr(replace(ja.phone, ' ', ''), 1, 1) = '+'
                                         then substr(replace(ja.phone, ' ', ''), 2, 13)
                                     else substr(replace(ja.phone, ' ', ''), 1, 12)
                           end::varchar(30)
                           , ja.email
                           )::varchar(30),
           v.cookie_label::varchar(30)) as user_id,
               coalesce(case when ja.phone is not null then 1 else null end,
               case when ja.email is not null then 3
                    when v.cookie_label is not null then 4 else null end) as user_type_id,   --create table user_type
       v.session_id as session_id,
       v.job_uid as job_uid,
       v.apply_flag,
       v.cookie_label,
       af.apply_id as apply_id,
       v.jdp_viewed_datetime as jdp_viewed_datetime,
       v.device_type
from apply.jdp v
join apply.application_form_conversion af on af.jdp_viewed_datediff = v.jdp_viewed_datediff and af.jdp_id = v.jdp_id and af.country_id = v.country_id
left join employer.job_apply ja on ja.id_session_apply = af.apply_id and ja.sources = 1
where v.country_id = 1
  and af.apply_id is not null
  and v.jdp_flag in (1,3)
  and v.jdp_viewed_datediff = 44230;

/*
date_diff - 44230;
rows - 2751;
time - 6 m 22 s 321 ms (execution: 6 m 22 s 191 ms, fetching: 130 ms);
cost - 1057.62..148194.16;
*/
