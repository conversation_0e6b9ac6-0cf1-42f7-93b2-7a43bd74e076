-- Запрос на вставку данных с DataGrip №3
truncate table job_seeker.user_lifetime_framework;
alter sequence job_seeker.user_lifetime_framework_id_seq RESTART with 1;

explain insert into job_seeker.user_lifetime_framework (user_id, user_type_id, blue_collar_score, jdp_viewed_datediff, next_apply_datediff, prev_apply_datediff, user_returned_type_id, user_churn_type_id, apply_cnt, country_id)
select a.user_id,
       a.user_type_id,
       a.score::float as blue_collar_score,
       a.jdp_viewed_datediff,
       a.next_apply_datediff,
       a.prev_apply_datediff,
       case
           when a.prev_apply_datediff is null and a.jdp_viewed_datediff >= (43707 + 98) then 1
           when (a.jdp_viewed_datediff - a.prev_apply_datediff) > 98 and a.jdp_viewed_datediff > (43707 + 98) then 2
           when (a.jdp_viewed_datediff - a.prev_apply_datediff) <= 98 and a.jdp_viewed_datediff > (43707 + 98) then 3
           else 0   --'undefined'::text
           end as user_returned_type_id,
       case
           when a.next_apply_datediff is null and
                (public.fn_get_date_diff(current_date::timestamp without time zone) - 1 - a.jdp_viewed_datediff) > 98 then 1
           when a.next_apply_datediff is null and (a.next_apply_datediff - a.jdp_viewed_datediff) <= 98 then 2
           else 0
           end as user_churn_type_id,
           apply_cnt,
	   1 as country_id
from (
        select lead(b.jdp_viewed_datediff) over (partition by b.user_id, b.user_type_id order by b.jdp_viewed_datediff) as next_apply_datediff,
               lag(b.jdp_viewed_datediff) over (partition by b.user_id, b.user_type_id order by b.jdp_viewed_datediff) as prev_apply_datediff,
               b.user_id,
               b.user_type_id,
               b.jdp_viewed_datediff,
               b.score,
               b.apply_cnt
        from (select ua.user_id,
                     ua.user_type_id,
                     ua.jdp_viewed_datediff,
                     s.score,
                     count(distinct ua.apply_id) as apply_cnt
              from job_seeker.user_apply ua
              left join job_seeker.user_active_blue_collar_score s on ua.user_id = s.user_id
              group by ua.user_id,
                       ua.user_type_id,
                       ua.jdp_viewed_datediff,
                       s.score
             ) b
    ) a;

/*
date_diff - all;
rows - 815 154;
time - 24 s 222 ms;
cost - 209405.68..629871.43;
*/
