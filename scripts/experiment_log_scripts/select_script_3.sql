-- Запрос на выборку данных №3:

explain select  country,
		dt,
		mobile,
		returned,
		coalesce(group_t1,0) as group_t1,
		coalesce(test_1,0) as test_1,
		coalesce(group_t2,0) as group_t2,
		coalesce(test_2,0) as test_2,
		coalesce(group_t3,0) as group_t3,
		coalesce(test_3,0) as test_3,
		coalesce(group_t4,0) as group_t4,
		coalesce(test_4,0) as test_4,
		coalesce(group_t5,0) as group_t5,
		coalesce(test_5,0) as test_5,
		no_cv_vacancy,
		id_session,
		id_jdp,
		cookie_label,
		id_account,
		applies_click,
		applies_click_no_cv,
		applies_submit,
		applies_submit_no_cv,
		applies_click_with_no_cv,
		cv_create_clicks,
		cv_upload_clicks,
		cv_creations,
		cv_attaches,
		cv_auth,
		cv_uploaded,
		cv_selected,
		n_account_cvs,
		n_client_cvs,
		id_traf_source,
		dt_trunc,
		date_diff
from (
select dim_c.alpha_2 as country,
       cast(max(sjd.date) as timestamp) as dt,
	   sjd.flags  & 1 as mobile,
	   sign(s.flags & 2)  as returned,
	   t1."group" as group_t1,
	   t1.id_test as test_1,
	   t2."group" as group_t2,
	   t2.id_test as test_2,
	   t3."group" as group_t3,
	   t3.id_test as test_3,
	   t4."group" as group_t4,
	   t4.id_test as test_4,
	   t5."group" as group_t5,
	   t5.id_test as test_5,
	   sign(sjd.flags & 1024) as no_cv_vacancy,
	   s.id as id_session,
	   sjd.id as id_jdp,
	   s.cookie_label,
	   coalesce(sjd.id_account,0) as id_account,
	   sign(sum(case when sja.type in (1, 29) then 1 else 0 end)) as applies_click,
	   sign(sum(case when sja.type in (29) then 1 else 0 end)) as applies_click_no_cv,
	   sign(sum(case when sja.type in (30, 2, 15)  and sja.flags & 1 = 1 then 1 else 0 end)) as applies_submit,
	   sign(sum(case when (sja.type in (30)  or (sja.type in (2, 15) and sjd.flags & 1024 = 1024) )  and sja.flags & 1 = 1 then 1 else 0 end))
			- sign(sum(case when (sja.type in (2, 15) and sjd.flags & 1024 = 1024 and ( ( saa.type in (4, 6, 24) and saa.flags = 0) )) and sja.flags & 1 = 1 then 1 else 0 end)) as applies_submit_no_cv,
	   sign(sum(case when sja.type in (1, 29) and sjd.flags & 1024 = 1024 then 1 else 0 end))
			- sign(sum(case when sja.type in (1, 29)  and sjd.flags & 1024 = 1024 and  saa.type in (4, 6, 24, 5) then 1 else 0 end)) as applies_click_with_no_cv,
	   sign(sum(case when saa.type = 5 and saa.flags = 0 then 1 else 0 end)) as cv_create_clicks,
	   sign(sum(case when saa.type in (4) then 1 else 0 end)) as cv_upload_clicks,
	   count(distinct cba.id_jdp) as cv_creations,
	   sign(sum(case when saa.type in (24) and saa.flags = 0 then 1 else 0 end)) as cv_attaches,
	   sign(sum(case when saa.type in (13) and saa.flags = 0 then 1 else 0 end)) as cv_auth,
	   sign(sum(case when saa.type in (28) then 1 else 0 end)) as cv_uploaded,
	   sign(sum(case when saa.type in (6) and saa.flags = 0 then 1 else 0 end)) as cv_selected,
	   count(distinct cva.id) as n_account_cvs,
	   count(distinct case when sjd.id_account is null then cva_cookie.id end) as n_client_cvs,
	   max(s.id_traf_source) as id_traf_source,
	   '1900-01-01'::timestamp + sjd.date_diff * interval '1 days'  as dt_trunc,
	   sjd.date_diff as date_diff,
	   ROW_NUMBER () OVER (Partition by sjd.id,  sjd.date_diff, t2.id_test,t3.id_test, t4.id_test, t5.id_test  ORDER BY t1.id_test desc) as t1_n,
	   ROW_NUMBER () OVER (Partition by sjd.id, sjd.date_diff, t1.id_test,t3.id_test, t4.id_test, t5.id_test   ORDER BY t2.id_test desc) as t2_n,
	   ROW_NUMBER () OVER (Partition by sjd.id, sjd.date_diff, t1.id_test,t2.id_test, t4.id_test, t5.id_test   ORDER BY t3.id_test desc) as t3_n,
	   ROW_NUMBER () OVER (Partition by sjd.id, sjd.date_diff, t1.id_test,t2.id_test, t3.id_test, t5.id_test   ORDER BY t4.id_test desc) as t4_n,
	   ROW_NUMBER () OVER (Partition by sjd.id, sjd.date_diff, t1.id_test,t2.id_test, t3.id_test, t4.id_test   ORDER BY t5.id_test desc) as t5_n
from imp.session s
left join imp.session_test t1 on t1.date_diff = s.date_diff and t1.id_session = s.id and t1.id_test in (select id_test from dimension.session_test_number where id_t = 1) and s.country = t1.country
left join imp.session_test t2 on t2.date_diff = s.date_diff and t2.id_session = s.id and t2.id_test in (select id_test from dimension.session_test_number where id_t = 2) and s.country = t2.country
left join imp.session_test t3 on t3.date_diff = s.date_diff and t3.id_session = s.id and t3.id_test in (select id_test from dimension.session_test_number where id_t = 3) and s.country = t3.country
left join imp.session_test t4 on t4.date_diff = s.date_diff and t4.id_session = s.id and t4.id_test in (select id_test from dimension.session_test_number where id_t = 4) and s.country = t4.country
left join imp.session_test t5 on t5.date_diff = s.date_diff and t5.id_session = s.id and t5.id_test in (select id_test from dimension.session_test_number where id_t = 5) and s.country = t5.country
inner join imp.session_jdp sjd on sjd.date_diff = s.date_diff and sjd.id_session = s.id and sjd.flags & 4 = 4 and s.country = sjd.country
left join imp.session_jdp_action sja on sjd.date_diff = sja.date_diff and sjd.id = sja.id_jdp and sjd.country = sja.country and sja.type in (1, 29, 30, 2, 15)
left join imp.session_apply_action saa on saa.id_jdp = sjd.id and saa.date_diff = sjd.date_diff and saa.country = sjd.country
/*блок резюме авторизированного клиента*/
left join imp.account_cv cva
       on cva.id_account = sjd.id_account
	  and cva.is_deleted = false
	  and cva.date_created <=s.start_date
       and cva.country = sjd.country

/*блок резюме неавторизированного клиента*/
left join imp.account_cv cva_cookie
       on cva_cookie.date_created < s.start_date
      and cva_cookie.cookie_label = s.cookie_label
      and cva_cookie.country = sjd.country

    /*созданные на jdp резюме*/
left join imp.cv_build_action cba
       on cba.id_jdp = sjd.id
      and cba.date_diff = sjd.date_diff
      and cba.country = sjd.country
      and cba.type = 1
      and cba.flags  & 2 = 2

left join dimension.countries dim_c on s.country = dim_c.id
where s.country = 1 and s.date_diff between ${DT_NOW} - 2 and ${DT_NOW} - 1
  and s.flags & 1 = 0
  and s.flags & 4 = 0
group by  dim_c.alpha_2,
          sjd.flags  & 1,
		  sign(s.flags & 2),
		   sign(sjd.flags & 1024),
		   s.id,
		   sjd.id,
		   s.cookie_label,
		   coalesce(sjd.id_account,0),
		   s.id_traf_source,
		   sjd.date_diff,
		   t1."group",
		   t1.id_test,
		   t2."group",
		   t2.id_test,
		   t3."group",
		   t3.id_test,
		   t4."group",
		   t4.id_test,
		   t5."group",
		   t5.id_test
) a
where t1_n = 1 and t2_n = 1 and t3_n = 1 and t4_n = 1 and t5_n = 1;

/*
date_diff - 44230;
rows - 591 326;
time - 1 in 4 m 3 s 425 ms (execution: 3 m 38 s 781 ms, fetching: 24 s 644 ms);
cost - 3793173.10..3793173.25;
*/
