-- Запрос на выборку данных №5:

explain SELECT pdj.country_id,
       pdj.profile_id,
       avg(pbs.blue_score) AS blue_score,
       CASE
           WHEN avg(pbs.blue_score)::double precision >= 0::double precision AND
                avg(pbs.blue_score)::double precision <= 0.34::double precision THEN 1
           WHEN avg(pbs.blue_score)::double precision >= 0.35::double precision AND
                avg(pbs.blue_score)::double precision <= 0.64::double precision THEN 2
           WHEN avg(pbs.blue_score)::double precision >= 0.65::double precision AND
                avg(pbs.blue_score)::double precision <= 1::double precision THEN 3
           ELSE 0
           END::smallint   AS profile_blue_collar_type_id
FROM profile.profile_profession pdj
         LEFT JOIN dimension.info_profession ip ON ip.id = pdj.profession_id AND ip.country = pdj.country_id
         LEFT JOIN dimension.professions_blue_score pbs ON pbs.profession::text = ip.name::text
WHERE pdj.country_id = 1
GROUP BY pdj.country_id, pdj.profile_id;

/*
date_diff - ____;
rows - 344 135;
time - 27 s 490 ms (execution: 25 s 380 ms, fetching: 2 s 110 ms);
cost -  69276.85..171525.73;
*/

explain select country_id, jdp_viewed_datediff, device_type, is_dte, is_premium, is_cv_apply, is_add_quest_apply, is_apply_success,
       is_apply_viewed, is_in_offer_status, is_in_rejected_status, is_add_quest_shown, is_add_quest_submited, blue_collar_score, jdp_view_cnt
from apply.w_additional_questions_funnel
where jdp_viewed_datediff between ${DT_NOW} - 8 and ${DT_NOW} - 1;

/*
date_diff - 44231;
rows - 2300;
time - 1 m 19 s 840 ms (execution: 1 m 19 s 769 ms, fetching: 71 ms);
cost - 4957681.25..4957681.57;
*/

explain WITH jdp_user AS (
    SELECT j_1.country_id,
           j_1.jdp_viewed_datediff,
           j_1.device_type,
           j_1.jdp_flag & 1                                       AS is_dte,
           sign((j_1.jdp_flag & 2)::smallint::double precision)   AS is_premium,
           j_1.apply_flag & 1                                     AS is_cv_apply,
           sign((j_1.apply_flag & 2)::smallint::double precision) AS is_add_quest_apply,
           afc.is_success                                         AS is_apply_success,
           CASE
               WHEN COALESCE(ubcs_a.score, ubcs_j.score) >= 0::double precision AND
                    COALESCE(ubcs_a.score, ubcs_j.score) <= 0.34::double precision
                   THEN 'white collars'::character varying(255)
               WHEN COALESCE(ubcs_a.score, ubcs_j.score) >= 0.35::double precision AND
                    COALESCE(ubcs_a.score, ubcs_j.score) <= 0.64::double precision
                   THEN 'white-blue collars'::character varying(255)
               WHEN COALESCE(ubcs_a.score, ubcs_j.score) >= 0.65::double precision AND
                    COALESCE(ubcs_a.score, ubcs_j.score) <= 1::double precision
                   THEN 'blue collars'::character varying(255)
               ELSE 'undefined'::character varying(255)
               END                                                AS blue_collar_score,
           j_1.jdp_id,
           afc.apply_id
    FROM apply.jdp j_1
             JOIN apply.application_form_conversion afc
                  ON afc.country_id = j_1.country_id AND afc.jdp_viewed_datediff = j_1.jdp_viewed_datediff AND
                     afc.jdp_id = j_1.jdp_id
             JOIN job_seeker.user_jdp uj
                  ON j_1.jdp_viewed_datediff = uj.jdp_viewed_datediff AND j_1.country_id = uj.country_id AND
                     j_1.jdp_id = uj.jdp_id
             LEFT JOIN job_seeker.user_active_blue_collar_score ubcs_a
                       ON ubcs_a.user_type_id = uj.user_type_id AND ubcs_a.user_id::text = uj.user_id::text
             LEFT JOIN job_seeker.user_jdp_blue_collar_score ubcs_j
                       ON ubcs_j.user_type_id = uj.user_type_id AND ubcs_j.user_id::text = uj.user_id::text
    WHERE j_1.jdp_viewed_datediff >= 44018
      AND (j_1.jdp_flag & 1) = 1
    GROUP BY j_1.country_id, j_1.jdp_viewed_datediff, (j_1.jdp_flag & 1),
             (sign((j_1.jdp_flag & 2)::smallint::double precision)), (j_1.apply_flag & 1),
             (sign((j_1.apply_flag & 2)::smallint::double precision)), j_1.device_type, afc.is_success,
             (
                 CASE
                     WHEN COALESCE(ubcs_a.score, ubcs_j.score) >= 0::double precision AND
                          COALESCE(ubcs_a.score, ubcs_j.score) <= 0.34::double precision
                         THEN 'white collars'::character varying(255)
                     WHEN COALESCE(ubcs_a.score, ubcs_j.score) >= 0.35::double precision AND
                          COALESCE(ubcs_a.score, ubcs_j.score) <= 0.64::double precision
                         THEN 'white-blue collars'::character varying(255)
                     WHEN COALESCE(ubcs_a.score, ubcs_j.score) >= 0.65::double precision AND
                          COALESCE(ubcs_a.score, ubcs_j.score) <= 1::double precision
                         THEN 'blue collars'::character varying(255)
                     ELSE 'undefined'::character varying(255)
                     END), j_1.jdp_id, afc.apply_id
)
SELECT j.country_id,
       j.jdp_viewed_datediff,
       j.device_type,
       j.is_dte,
       j.is_premium,
       j.is_cv_apply,
       j.is_add_quest_apply,
       j.is_apply_success,
       CASE
           WHEN ja.date_seen <= fn_get_timestamp_from_date_diff(j.jdp_viewed_datediff + 7)
               THEN sign((ja.flags & 2)::smallint::double precision)
           ELSE 0::double precision
           END                  AS is_apply_viewed,
       CASE
           WHEN ja.status = 3 THEN 1
           ELSE 0
           END                  AS is_in_offer_status,
       CASE
           WHEN ja.status = ANY (ARRAY [4, 100]) THEN 1
           ELSE 0
           END                  AS is_in_rejected_status,
       CASE
           WHEN af_aq_c.jdp_id IS NOT NULL THEN 1
           ELSE 0
           END                  AS is_add_quest_shown,
       CASE
           WHEN af_aq_c.is_success = 1 THEN 1
           ELSE 0
           END                  AS is_add_quest_submited,
       j.blue_collar_score,
       count(DISTINCT j.jdp_id) AS jdp_view_cnt
FROM jdp_user j
         LEFT JOIN apply.application_form_add_quest_conversion af_aq_c
                   ON af_aq_c.jdp_viewed_datediff = j.jdp_viewed_datediff AND af_aq_c.jdp_id = j.jdp_id AND
                      af_aq_c.country_id = j.country_id
         LEFT JOIN employer.job_apply ja ON ja.sources = 1 AND ja.id_session_apply = j.apply_id AND
                                            ja.dd_session_apply = j.jdp_viewed_datediff
where j.jdp_viewed_datediff between ${DT_NOW} - 8 and ${DT_NOW} - 1
GROUP BY j.country_id, j.jdp_viewed_datediff, j.device_type, j.is_dte, j.is_premium, j.is_cv_apply,
         j.is_add_quest_apply, j.is_apply_success,
         (
             CASE
                 WHEN ja.date_seen <= fn_get_timestamp_from_date_diff(j.jdp_viewed_datediff + 7)
                     THEN sign((ja.flags & 2)::smallint::double precision)
                 ELSE 0::double precision
                 END),
         (
             CASE
                 WHEN ja.status = 3 THEN 1
                 ELSE 0
                 END),
         (
             CASE
                 WHEN ja.status = ANY (ARRAY [4, 100]) THEN 1
                 ELSE 0
                 END),
         (
             CASE
                 WHEN af_aq_c.jdp_id IS NOT NULL THEN 1
                 ELSE 0
                 END),
         (
             CASE
                 WHEN af_aq_c.is_success = 1 THEN 1
                 ELSE 0
                 END), j.blue_collar_score;

/*
date_diff - 44231;
rows - 2300;
time - 39 s 94 ms (execution: 39 s 24 ms, fetching: 70 ms);
cost - 4957681.25..4957681.57;
*/
