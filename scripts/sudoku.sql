 insert into profile.sudoku_profile(initial_datediff, profile_id, create_profile_datediff, submission_datediff, is_deleted, has_sudoku_call, sudoku_call_datetime, call_status_name)
        select date_first_json_update as initial_datediff,
               id as profile_id,
               date_diff as create_profile_datediff,
               date_submitted,
               is_deleted,
               max(case when cjsc.call_datetime is not null then 1 else 0 end) as has_sudoku_call,
               min(cjsc.call_datetime) as sudoku_call_datetime,
               call_status_name
        from imp.profiles p
        left join imp_statistic.crm_job_seeker_call cjsc on cjsc.country_id = p.country and cjsc.profile_id = p.id
        where country = 1 and date_first_json_update = fn_get_date_diff(current_date)-1 --between 44293 and fn_get_date_diff(current_date)-1
              and (date_deleted is null or date_deleted > date_first_json_update) and (date_submitted is null or date_submitted > date_first_json_update)
        group by date_first_json_update,
                 id, date_diff,
                 date_submitted,
                 is_deleted,
                 call_status_name;


        truncate table profile.sudoku_action_structure;

        insert into profile.sudoku_action_structure(initial_datediff, profile_id, submission_datediff, has_sudoku_call,
                                                    sudoku_call_datetime, atc_total_1_7_day, atc_employer_1_7_day, atc_job_seeker_1_7_day,
                                                    atc_total_1_14_day, atc_employer_1_14_day, atc_job_seeker_1_14_day, atc_total_1_28_day,
                                                    atc_employer_1_28_day, atc_job_seeker_1_28_day)
        select initial_datediff,
               p.profile_id as profile_id,
               p.submission_datediff,
               p.has_sudoku_call,
               p.sudoku_call_datetime,
              --7 day action count
               (select sum(atcs.action_to_contact_prob)
                   from profile.action_to_contact_structure atcs
                   where atcs.country_id = 1
                   and atcs.profile_id = p.profile_id
                    and atcs.action_datediff between p.initial_datediff and p.initial_datediff+6) as atc_1_7_days,
                --7 day action emp
               (select sum(atcs.action_to_contact_prob)
                   from profile.action_to_contact_structure atcs
                   where atcs.country_id = 1 and atcs.platform_user_type_id = 2
                   and atcs.profile_id = p.profile_id
                    and atcs.action_datediff between p.initial_datediff and p.initial_datediff+6) as atc_emp_1_7_days,
              -- action by js kinda 7day
               (select sum(atcs.action_to_contact_prob)
                   from profile.action_to_contact_structure atcs
                   where atcs.country_id = 1 and atcs.platform_user_type_id = 1
                   and atcs.profile_id = p.profile_id
                    and atcs.action_datediff between p.initial_datediff and p.initial_datediff+6) as atc_js_1_7_days,
               --14 day:
               --js+emp
               (select sum(atcs.action_to_contact_prob)
                   from profile.action_to_contact_structure atcs
                   where atcs.country_id = 1
                   and atcs.profile_id = p.profile_id
                    and atcs.action_datediff between p.initial_datediff and p.initial_datediff+13) as atc_1_14_days,
               --emp
               (select sum(atcs.action_to_contact_prob)
                   from profile.action_to_contact_structure atcs
                   where atcs.country_id = 1 and atcs.platform_user_type_id = 2
                   and atcs.profile_id = p.profile_id
                    and atcs.action_datediff between p.initial_datediff and p.initial_datediff+13) as atc_emp_1_14_days,
                -- js
                (select sum(atcs.action_to_contact_prob)
                   from profile.action_to_contact_structure atcs
                   where atcs.country_id = 1 and atcs.platform_user_type_id = 1
                   and atcs.profile_id = p.profile_id
                    and atcs.action_datediff between p.initial_datediff and p.initial_datediff+13) as atc_js_1_14_days,
                --28 days actions:
               -- total actions
                (select sum(atcs.action_to_contact_prob)
                   from profile.action_to_contact_structure atcs
                   where atcs.country_id = 1
                   and atcs.profile_id = p.profile_id
                    and atcs.action_datediff between p.initial_datediff and p.initial_datediff+27) as atc_1_28_days,
                --emp actions
                 (select sum(atcs.action_to_contact_prob)
                   from profile.action_to_contact_structure atcs
                   where atcs.country_id = 1 and atcs.platform_user_type_id = 2
                   and atcs.profile_id = p.profile_id
                    and atcs.action_datediff between p.initial_datediff and p.initial_datediff+27) as atc_emp_1_28_days,
               -- js actions
                 (select sum(atcs.action_to_contact_prob)
                   from profile.action_to_contact_structure atcs
                   where atcs.country_id = 1 and atcs.platform_user_type_id = 1
                   and atcs.profile_id = p.profile_id
                    and atcs.action_datediff between p.initial_datediff and p.initial_datediff+27)
                     --and initial_datediff < fn_get_date_diff(current_date)-27)
                     as atc_js_1_28_days
        from profile.sudoku_profile p
        where initial_datediff between 44296 and fn_get_date_diff(current_date)-8 and initial_datediff not in (44320, 44321, 44322);
