select sapc.sources as database_id,
       (select id from dimension.countries where lower(alpha_2) = e.country_code) as country_id,
       sapc.id as id_action,
       1 as action_type_id,        --'phone'::text
       sapc.id_apply as apply_id,
       sapc.date as action_datetime,
       public.fn_get_date_diff(sapc.date) as action_datediff,
       sapc.id_account as employer_account_id
from imp_employer.statistics_apply_phone_click sapc
left join imp_employer.job_apply ja on sapc.sources = ja.sources and sapc.id_apply = ja.id
left join imp_employer.job j on sapc.sources = j.sources and ja.id_job = j.id
left join imp_employer.employer e on j.sources = e.sources and j.id_employer = e.id
where sapc.date::date < '2021-08-01' and sapc.date::date = (current_date - 1)::date
union
select poc.sources as database_id,
       (select id from dimension.countries where lower(alpha_2) = e.country_code) as country_id,
       poc.id as id_action,
       9 as action_type_id,        --'open contact'::text
       poc.id_apply as apply_id,
       poc.date as action_datetime,
       public.fn_get_date_diff(poc.date) as action_datediff,
       j.id_account as employer_account_id
from imp_employer.profile_open_contact poc
join imp_employer.job_apply ja on poc.sources = ja.sources and poc.id_apply = ja.id
join imp_employer.job j on poc.sources = j.sources and ja.id_job = j.id
left join imp_employer.employer e on poc.sources = e.sources and poc.id_employer = e.id
where poc.date::date = (current_date - 1)::date and id_apply is not null
union
select javc.sources as database_id,
       (select id from dimension.countries where lower(alpha_2) = e.country_code) as country_id,
       javc.id as id_action,
       3 as action_type_id,     --'viber'::text
       javc.id_apply as apply_id,
       javc.date_created as action_datetime,
       public.fn_get_date_diff(javc.date_created) as action_datediff,
       javc.id_account as employer_account_id
from imp_employer.job_apply_viber_click javc
left join imp_employer.job_apply ja on javc.sources = ja.sources and javc.id_apply = ja.id
left join imp_employer.job j on javc.sources = j.sources and ja.id_job = j.id
left join imp_employer.employer e on j.sources = e.sources and j.id_employer = e.id
where javc.date_created::date < '2021-08-01' and javc.date_created::date = (current_date - 1)::date
union
select jam.sources as database_id,
       (select id from dimension.countries where lower(alpha_2) = e.country_code) as country_id,
       min(jam.id) as action_id,
       case
            when jam.type = 0 or jam.type is null then 4      --'question'::text
            when jam.type = 1 then 101      --'reject'::text
            when jam.type = 2 then 102      -- 'invitation'::text
       end as action_type_id,
       jam.id_apply as apply_id,
       min(jam.date_created) as action_datetime,
       public.fn_get_date_diff(jam.date_created) as action_datediff,
       jam.id_account_author as employer_account_id
from imp_employer.job_apply_message jam
left join imp_employer.job_apply ja on jam.sources = ja.sources and jam.id_apply = ja.id
left join imp_employer.job j on jam.sources = j.sources and ja.id_job = j.id
left join imp_employer.employer e on j.sources = e.sources and j.id_employer = e.id
where jam.date_created::date = (current_date - 1)::date and jam.author_type = 0 /*Employer*/
group by jam.sources,
         e.country_code,
         case
              when jam.type = 0 or jam.type is null then 4      --'question'::text
              when jam.type = 1 then 101      --'reject'::text
              when jam.type = 2 then 102      -- 'invitation'::text
         end,
         jam.id_apply,
         jam.id_account_author,
         public.fn_get_date_diff(jam.date_created);
