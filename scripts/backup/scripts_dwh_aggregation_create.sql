create database backup_dwh tablespace data_old;

create schema apply;

set search_path = "apply";

create table application_form_cv_funnel
(
	id serial not null
		constraint pk_apply_cv_id
			primary key,
	country_id smallint not null,
	jdp_id bigint not null,
	jdp_viewed_datediff integer not null,
	cv_auto_attached integer,
	cv_upload_click integer,
	cv_uploaded integer,
	cv_create_click integer,
	cv_created integer,
	click_select integer,
	cv_selected integer,
	userrec varchar(15),
	daterec timestamp,
	usermod varchar(15),
	datemod timestamp
);

alter table application_form_cv_funnel owner to postgres;

create index ind_apply_cv_dc
	on application_form_cv_funnel (jdp_viewed_datediff, country_id);

create index ind_apply_cv_ij
	on application_form_cv_funnel (jdp_id);


create table jdp
(
	id serial not null,
	country_id smallint not null,
	jdp_id bigint not null,
	jdp_viewed_datediff integer not null,
	job_uid bigint,
	jdp_flag integer,
	jdp_viewed_datetime timestamp,
	account_id integer,
	apply_flag integer,
	session_id bigint,
	cookie_label bigint,
	device_type smallint,
	is_returned smallint,
	traf_source_id integer,
	userrec varchar(15),
	daterec timestamp,
	usermod varchar(15),
	datemod timestamp,
	constraint pk_apply_jdp_id
		primary key (country_id, jdp_id, jdp_viewed_datediff)
);

alter table jdp owner to postgres;

create index ind_views_jdp_uid
	on jdp (job_uid);

create index ind_views_jdp_acc
	on jdp (account_id);

create index ind_jdp_cl
	on jdp (cookie_label);


create table application_form_conversion
(
	id serial not null
		constraint pk_apply_flow_id
			primary key,
	country_id smallint not null,
	jdp_id bigint not null,
	jdp_viewed_datediff integer not null,
	apply_click integer,
	is_success integer,
	apply_id bigint,
	userrec varchar(15),
	daterec timestamp,
	usermod varchar(15),
	datemod timestamp
);

alter table application_form_conversion owner to postgres;

create index ind_apply_flow_dc
	on application_form_conversion (jdp_viewed_datediff, country_id);

create index ind_apply_flow_ij
	on application_form_conversion (jdp_id);

create index ind_apply_flow_uid
	on application_form_conversion (apply_id);


create table application_form_add_quest_conversion
(
	id serial not null
		constraint pk_apply_additional_questions_id
			primary key,
	country_id smallint not null,
	jdp_id bigint,
	jdp_viewed_datediff integer not null,
	is_success integer,
	userrec varchar(15),
	daterec timestamp,
	usermod varchar(15),
	datemod timestamp
);

alter table application_form_add_quest_conversion owner to postgres;

create index ind_apply_additional_questions_dc
	on application_form_add_quest_conversion (jdp_viewed_datediff, country_id);

create index ind_apply_additional_questions_ij
	on application_form_add_quest_conversion (jdp_id);


create table application_form_cv_funnel_datetime
(
	id serial not null
		constraint pk_date_apply_cv_id
			primary key,
	country_id smallint not null,
	jdp_id bigint not null,
	jdp_viewed_datediff integer not null,
	cv_auto_attached_datetime timestamp,
	cv_upload_click_datetime timestamp,
	cv_uploaded_datetime timestamp,
	cv_create_click_datetime timestamp,
	cv_created_datetime timestamp,
	click_select_datetime timestamp,
	cv_selected_datetime timestamp,
	userrec varchar(15),
	daterec timestamp,
	usermod varchar(15),
	datemod timestamp
);

alter table application_form_cv_funnel_datetime owner to postgres;

create index ind_date_apply_cv_dc
	on application_form_cv_funnel_datetime (jdp_viewed_datediff, country_id);

create index ind_date_apply_cv_ij
	on application_form_cv_funnel_datetime (jdp_id);

create table m_additional_questions_funnel
(
	id serial not null
		constraint m_additional_questions_funnel_pkey
			primary key,
	country_id smallint not null,
	jdp_viewed_datediff integer not null,
	device_type integer,
	is_dte integer,
	is_premium integer,
	is_cv_apply integer,
	is_add_quest_apply integer,
	is_apply_success integer,
	is_apply_viewed integer,
	is_in_offer_status integer,
	is_in_rejected_status integer,
	is_add_quest_shown integer,
	is_add_quest_submited integer,
	blue_collar_score varchar(255),
	jdp_view_cnt bigint
);

alter table m_additional_questions_funnel owner to postgres;

create index ind_m_additional_questions_funnel_d
	on m_additional_questions_funnel (jdp_viewed_datediff);

create table m_profile_apply_funnel
(
	id serial not null
		constraint m_profile_apply_funnel_pkey
			primary key,
	country_id smallint not null,
	jdp_viewed_datediff integer not null,
	device_type integer,
	is_dte integer,
	is_premium integer,
	is_cv_apply integer,
	is_add_quest_apply integer,
	is_profile_apply integer,
	is_apply_success integer,
	is_apply_viewed integer,
	is_in_offer_status integer,
	is_in_rejected_status integer,
	blue_collar_score varchar(100),
	jdp_view_cnt integer
);

alter table m_profile_apply_funnel owner to postgres;

create index ind_m_profile_apply_funnel_d
	on m_profile_apply_funnel (country_id, jdp_viewed_datediff);


create schema company;

set search_path = "company";


create table company.away_feedback_agg
(
	country varchar(2) default ''::character varying not null,
	answer bigint default '0'::bigint not null,
	project_name varchar(100) default ''::character varying not null,
	cnt_responds bigint,
	constraint idx_90121_primary
		primary key (country, answer, project_name)
);

alter table company.away_feedback_agg owner to postgres;

create table company.clients_monthly
(
	country varchar(2) default ''::character varying not null,
	dt_month date not null,
	clients bigint,
	constraint idx_90127_primary
		primary key (country, dt_month)
);

alter table company.clients_monthly owner to postgres;


create table company.goals_bp_plan
(
	country varchar(2) default ''::character varying not null,
	dt_month date not null,
	business_points numeric(41,3),
	constraint idx_90233_primary
		primary key (country, dt_month)
);

alter table company.goals_bp_plan owner to postgres;


create table company.goals_itc_plan
(
	dt_month date not null
		constraint idx_90237_primary
			primary key,
	ab_growth numeric(10,5)
);

alter table company.goals_itc_plan owner to postgres;


create table company.goals_revenue_dte
(
	country varchar(2) default ''::character varying not null,
	date date not null,
	product varchar(3) default ''::character varying not null,
	revenue numeric(38,6),
	constraint idx_90240_primary
		primary key (country, date, product)
);

alter table company.goals_revenue_dte owner to postgres;


create table company.goals_revenue_dte_plan
(
	country varchar(10) default ''::character varying not null,
	dt_month date not null,
	product varchar(20) default ''::character varying not null,
	revenue numeric(38,2),
	constraint idx_90245_primary
		primary key (country, dt_month, product)
);

alter table company.goals_revenue_dte_plan owner to postgres;


create table company.goals_revenue_non_dte
(
	product varchar(20) default ''::character varying not null,
	date date not null,
	revenue numeric(19,4),
	constraint idx_90250_primary
		primary key (product, date)
);

alter table company.goals_revenue_non_dte owner to postgres;


create table company.goals_revenue_non_dte_plan
(
	country varchar(10) default ''::character varying not null,
	dt_month date not null,
	product varchar(20) default ''::character varying not null,
	revenue numeric(38,2),
	constraint idx_90254_primary
		primary key (country, dt_month, product)
);

alter table company.goals_revenue_non_dte_plan owner to postgres;


create table company.goals_traffic
(
	date date not null
		constraint idx_90259_primary
			primary key,
	users bigint,
	sessions bigint
);

alter table company.goals_traffic owner to postgres;


create table company.goals_traffic_plan
(
	country varchar(10) not null,
	dt_month date not null,
	sessions bigint,
	constraint idx_90262_primary
		primary key (country, dt_month)
);

alter table company.goals_traffic_plan owner to postgres;


create table company.itc_jdp
(
	country varchar(2) default ''::character varying not null,
	dt date not null,
	device integer default 0 not null,
	id_traf_source bigint default '0'::bigint not null,
	is_returned integer default 0 not null,
	no_cv_vacancy integer default 0 not null,
	is_premium integer default 0 not null,
	is_ea integer default 0 not null,
	id_project bigint default '0'::bigint not null,
	jdp_response_type_id integer default 0 not null,
	views bigint,
	away_clicks bigint,
	call_clicks bigint,
	apply_clicks bigint,
	applies bigint,
	intention_to_contact bigint,
	constraint idx_90302_primary
		primary key (country, dt, device, id_traf_source, is_returned, no_cv_vacancy, is_premium, is_ea, jdp_response_type_id, id_project)
);

alter table company.itc_jdp owner to postgres;


create table company.itc_jdp_samples
(
	country varchar(2) default ''::character varying not null,
	dt date not null,
	device integer default 0 not null,
	id_traf_source bigint default '0'::bigint not null,
	is_returned integer default 0 not null,
	no_cv_vacancy integer default 0 not null,
	is_premium integer default 0 not null,
	is_ea integer default 0 not null,
	id_project bigint default '0'::bigint not null,
	jdp_response_type_id integer default 0 not null,
	views bigint,
	away_clicks bigint,
	call_clicks bigint,
	apply_clicks bigint,
	applies bigint,
	intention_to_contact bigint,
	constraint idx_90314_primary
		primary key (country, dt, device, id_traf_source, is_returned, no_cv_vacancy, is_premium, is_ea, jdp_response_type_id, id_project)
);

alter table company.itc_jdp_samples owner to postgres;


create table company.itc_serp
(
	country varchar(2) default ''::character varying not null,
	dt date not null,
	device integer default 0 not null,
	id_traf_source bigint default '0'::bigint not null,
	is_returned integer default 0 not null,
	id_project bigint default '0'::bigint not null,
	click_aways_serp bigint,
	sum_click_price_eur double precision,
	constraint idx_90326_primary
		primary key (country, dt, id_project, device, id_traf_source, is_returned)
);

alter table company.itc_serp owner to postgres;


create table company.m_itc
(
	country varchar(2) default ''::character varying not null,
	dt date not null,
	source_type varchar(20) default ''::character varying not null,
	device bigint default '0'::bigint not null,
	id_traf_source bigint default '0'::bigint not null,
	is_returned bigint default '0'::bigint not null,
	no_cv_vacancy bigint default '0'::bigint not null,
	is_premium bigint default '0'::bigint not null,
	is_ea bigint default '0'::bigint not null,
	id_project bigint default '0'::bigint not null,
	jdp_response_type_id bigint default '0'::bigint not null,
	views bigint,
	intention_to_contact numeric(65,5),
	constraint idx_90395_primary
		primary key (country, dt, source_type, device, id_traf_source, is_returned, no_cv_vacancy, is_premium, is_ea, id_project, jdp_response_type_id)
);

alter table company.m_itc owner to postgres;


create table company.m_itc_jdp_away_projects
(
	country varchar(2) default ''::character varying not null,
	dt date not null,
	views numeric(32),
	intention_to_contact numeric(32),
	constraint idx_90408_primary
		primary key (country, dt)
);

alter table company.m_itc_jdp_away_projects owner to postgres;


create table company.session_agg
(
	country varchar(2) default ''::character varying not null,
	dt date not null,
	device smallint default '-1'::smallint not null,
	is_returned smallint default '0'::smallint not null,
	id_traf_source bigint default '0'::bigint not null,
	cnt_sessions bigint,
	constraint idx_90460_primary
		primary key (country, dt, device, is_returned, id_traf_source)
);

alter table company.session_agg owner to postgres;


create table company.m_itc_jdp_samples_for_ab
(
	country varchar(2) not null,
	id_project bigint not null,
	itc_per_view double precision,
	constraint m_itc_jdp_samples_for_ab_pkey
		primary key (country, id_project)
);

alter table company.m_itc_jdp_samples_for_ab owner to postgres;


create table company.m_itc_jdp_away_projects_for_ab
(
	country varchar(2) not null
		constraint m_itc_jdp_away_projects_for_ab_pkey
			primary key,
	itc_per_view double precision
);

alter table company.m_itc_jdp_away_projects_for_ab owner to postgres;


create table company.itc_projects_for_sample
(
	dt_month date not null,
	country varchar(2) not null,
	project_id integer not null,
	min_date timestamp,
	max_date timestamp,
	click_aways_serp integer,
	click_serp_no_away integer,
	avg_click_price_eur numeric(47,9),
	constraint itc_projects_for_sample_pkey
		primary key (dt_month, country, project_id)
);

alter table company.itc_projects_for_sample owner to postgres;


create table company.info_projects_short
(
	country varchar(2) not null,
	id integer not null,
	name varchar(255),
	is_active smallint,
	constraint info_projects_short_pkey
		primary key (country, id)
);

alter table company.info_projects_short owner to postgres;


create table company.itc_sample_project_pool
(
	dt_month date not null,
	country varchar(2) not null
		constraint itc_sample_project_pool_country_check
			check (upper((country)::text) = (country)::text),
	project_id integer not null,
	every_n_click integer not null,
	month_limit integer,
	constraint itc_sample_project_pool_pkey
		primary key (dt_month, country, project_id)
);

alter table company.itc_sample_project_pool owner to postgres;

create schema email;

set search_path = "email";

create table email.alertview_message_stat
(
	id serial not null
		constraint pk_alertview_message_stat_id
			primary key,
	country varchar(2) not null,
	dt date,
	letter_type_name varchar(15),
	flags integer,
	sent_msg integer,
	open_msg integer,
	vist_msg integer,
	open_jdp integer,
	open_away integer,
	calls integer,
	applies integer,
	bp numeric(18,18),
	ith numeric(18,18)
);

alter table email.alertview_message_stat owner to postgres;


create table email.account_desc_flag
(
	flags integer,
	desc_flag text,
	is_mobile integer
);

alter table email.account_desc_flag owner to vni;

create table email.email_recommend_test
(
	country smallint not null,
	id_test integer not null,
	id_account integer not null,
	id_group integer,
	date_diff integer not null,
	flags integer,
	constraint pk_email_recommend_test_id
		primary key (country, id_account, id_test)
);

alter table email.email_recommend_test owner to postgres;

create table email.email_reg_lt_stat
(
	sent_date date,
	country smallint,
	desc_flag text,
	letter_type integer,
	letter_type_desc text,
	sent_msg numeric,
	open_msg numeric,
	vst_msg integer,
	open_jdp numeric,
	open_away numeric,
	call_clicks numeric,
	applies numeric,
	bp numeric,
	ith double precision,
	message_with_open_jdp_cnt integer,
	message_with_applies_cnt integer,
	has_submited_profile integer,
	open_away_revenue numeric(18,4)
);

alter table email.email_reg_lt_stat owner to rlu;

create table email.email_open_jdp
(
	country_id smallint not null,
	message_id varchar(100) not null,
	jdp_open_cnt integer,
	sent_datediff integer not null,
	constraint pk_email_open_jdp_id
		primary key (country_id, sent_datediff, message_id)
);

alter table email.email_open_jdp owner to postgres;

create table email.email_open_away
(
	country_id smallint not null,
	message_id varchar(100) not null,
	away_open_cnt integer,
	sent_datediff integer not null,
	open_away_revenue numeric(18,4),
	constraint pk_email_open_away_id
		primary key (country_id, sent_datediff, message_id)
);

alter table email.email_open_away owner to postgres;

create table email.email_user_action
(
	country_id smallint not null,
	message_id varchar(100) not null,
	is_call bigint,
	is_apply bigint,
	bp bigint,
	ith_jdp integer,
	sent_datediff integer not null,
	constraint pk_email_user_action_id
		primary key (country_id, sent_datediff, message_id)
);

alter table email.email_user_action owner to postgres;

create table email.email_serp_ith
(
	country_id smallint not null,
	message_id varchar(100) not null,
	serp_ith double precision,
	sent_datediff integer not null,
	constraint pk_email_serp_ith_id
		primary key (country_id, sent_datediff, message_id)
);

alter table email.email_serp_ith owner to postgres;

create table email.email_test
(
	country_code text,
	id_test bigint,
	is_active bigint,
	letter_type text,
	date_diff bigint
);

alter table email.email_test owner to vni;

create table email.email_test_interval_old
(
	date_diff bigint,
	cohort_period bigint,
	country_code text,
	id_test bigint,
	id_group bigint,
	metric_name text,
	low_bound double precision,
	avg_value double precision,
	up_bound double precision
);

alter table email.email_test_interval_old owner to vni;

create table email.email_test_compare_interval_old
(
	day_test bigint,
	metric text,
	country_code text,
	id_test bigint,
	cont_group bigint,
	test_group bigint,
	delta double precision,
	low_bound double precision,
	up_bound double precision
);

alter table email.email_test_compare_interval_old owner to vni;

create table email.email_test_metric_old
(
	country_code text,
	id_test bigint,
	id_group bigint,
	avg_or double precision,
	avg_vr double precision,
	avg_bp double precision,
	avg_ith double precision,
	day_0_or double precision,
	day_7_or double precision,
	day_14_or double precision,
	day_0_vr double precision,
	day_7_vr double precision,
	day_14_vr double precision,
	day_0_bp double precision,
	day_7_bp double precision,
	day_14_bp double precision,
	day_0_ith double precision,
	day_7_ith double precision,
	day_14_ith double precision,
	unsub_rate double precision,
	avg_applies double precision,
	sent_msg bigint,
	avg_call_clicks double precision,
	avg_open_away double precision,
	avg_open_jdp double precision,
	day_0_applies double precision,
	day_7_applies double precision,
	day_14_applies double precision,
	day_0_call_clicks double precision,
	day_7_call_clicks double precision,
	day_14_call_clicks double precision,
	day_0_open_away double precision,
	day_7_open_away double precision,
	day_14_open_away double precision,
	day_0_open_jdp double precision,
	day_7_open_jdp double precision,
	day_14_open_jdp double precision
);

alter table email.email_test_metric_old owner to vni;

create table email.et_intervals
(
	day_test integer,
	country_code text,
	id_test integer,
	id_group text,
	metric text,
	low_border double precision,
	avg_value double precision,
	up_border double precision
);

alter table email.et_intervals owner to vni;

create table email.email_test_metric
(
	country_code text,
	id_test bigint,
	"window" bigint,
	id_group text,
	metric text,
	avg_value double precision
);

alter table email.email_test_metric owner to vni;

create table email.email_metrics
(
	country_id smallint not null,
	sent_datediff integer not null,
	message_id varchar(100) not null,
	is_open integer,
	is_visit integer,
	constraint pk_email_metrics_id
		primary key (country_id, sent_datediff, message_id)
);

alter table email.email_metrics owner to postgres;

create table email.subscription_offer_viber_messages
(
	country_id smallint,
	sent_message_datediff integer,
	sent_message_date date,
	account_id bigint,
	status integer,
	has_link_visit integer,
	is_subscribe integer
);

alter table email.subscription_offer_viber_messages owner to postgres;

create table email.agg_subscription_offer_viber_messages
(
	sent_message_datediff integer,
	sent_message_date date,
	message_sent_daily_cnt integer,
	message_delivered_daily_cnt integer,
	has_link_visit_cnt integer,
	is_subscribe_cnt integer
);

alter table email.agg_subscription_offer_viber_messages owner to postgres;

create table email.abandoned_profile_viber_messages
(
	country_id smallint,
	sent_message_datediff integer,
	sent_message_date date,
	account_id bigint,
	status integer,
	profile_id integer,
	has_link_visit integer,
	has_submit_profile integer
);

alter table email.abandoned_profile_viber_messages owner to postgres;

create table email.agg_abandoned_profile_viber_messages
(
	sent_message_datediff integer,
	sent_message_date date,
	message_sent_daily_cnt integer,
	message_delivered_daily_cnt integer,
	has_link_visit_cnt integer,
	has_submit_profile_cnt integer
);

alter table email.agg_abandoned_profile_viber_messages owner to postgres;

create table email.abandoned_apply_viber_messages
(
	country_id smallint,
	sent_message_datediff integer,
	sent_message_date date,
	account_id bigint,
	status integer,
	job_uid bigint,
	session_id bigint,
	jdp_id bigint,
	has_link_on_jdp integer,
	has_apply_on_the_same_jdp integer,
	has_call_on_the_same_jdp integer
);

alter table email.abandoned_apply_viber_messages owner to postgres;

create table email.agg_abandoned_apply_viber_messages
(
	sent_message_datediff integer,
	sent_message_date date,
	message_sent_daily_cnt integer,
	message_delivered_daily_cnt integer,
	has_link_on_jdp_cnt integer,
	has_apply_on_the_same_jdp_cnt integer,
	has_call_on_the_same_jdp_cnt integer
);

alter table email.agg_abandoned_apply_viber_messages owner to postgres;


create schema employer;

set search_path = "employer";


create table employer.suggest_candidates_views
(
	country varchar(2) not null,
	employer_id integer not null,
	job_id bigint not null,
	date_job_created timestamp,
	date_job_end timestamp,
	date_suggest_created timestamp,
	date_suggest_end timestamp,
	date_sess_first timestamp,
	date_sess_last timestamp,
	suggest_date_seen_first timestamp,
	suggest_date_seen_last timestamp,
	potential_candidates_created integer,
	potential_candidates_seen integer,
	active_sessions integer,
	constraint suggest_candidates_views_pkey
		primary key (country, employer_id, job_id)
);

alter table employer.suggest_candidates_views owner to rlu;



create table employer.account_service_roles
(
	id integer not null
		constraint pk_account_service_roles_id
			primary key,
	name varchar(50) not null,
	value integer not null
);

alter table employer.account_service_roles owner to postgres;


create table employer.job_blocking_reason
(
	id_job bigint not null
		constraint job_blocking_reason_pkey
			primary key,
	reason text not null
);

alter table employer.job_blocking_reason owner to postgres;



create table employer.ea_add_question
(
	department text,
	expected text,
	id text,
	question text,
	required boolean,
	type text,
	language text,
	"locationName" text,
	certificate text,
	uid_job double precision,
	uid_question text,
	date_update date,
	id_job bigint,
	country_code varchar(2),
	sources integer
);

alter table employer.ea_add_question owner to nsh;


create table employer.active_session_ea_3m
(
	id serial not null
		constraint pk_active_session_ea_3m_id
			primary key,
	id_employer integer,
	country_code varchar(2),
	id_account integer,
	industry varchar(1024),
	company_name varchar(2048),
	exist_session integer,
	date_last_session date,
	cnt_session integer,
	time_key date,
	sources smallint
);

alter table employer.active_session_ea_3m owner to postgres;

create index ind_active_session_ea_3m_em
	on employer.active_session_ea_3m (id_employer);

create index ind_active_session_ea_3m_ac
	on employer.active_session_ea_3m (id_account);



create table employer.employer_apply_status
(
	id serial not null
		constraint pk_empoyer_apply_status_id
			primary key,
	id_employer integer,
	sources smallint,
	country_code varchar(2),
	company_name varchar(2048),
	industry varchar(1024),
	id_account integer,
	id_job bigint,
	id_apply bigint,
	have_sess_after_apply integer,
	date_reciev_apply date,
	is_seen integer,
	date_seen date,
	is_drop integer,
	change_st_user integer,
	current_status integer,
	time_key date
);

alter table employer.employer_apply_status owner to postgres;

create index ind_empoyer_apply_status_em
	on employer.employer_apply_status (id_employer);



create table employer.employer_apply_status_flow
(
	id serial not null
		constraint pk_employer_apply_status_flow_id
			primary key,
	country_code varchar(2),
	sources smallint,
	id_employer integer,
	id_apply bigint,
	current_status integer,
	old_status integer,
	new_status integer,
	date_change date,
	time_key date
);

alter table employer.employer_apply_status_flow owner to postgres;

create index ind_employer_apply_status_flow_em
	on employer.employer_apply_status_flow (id_employer);

create index ind_employer_apply_status_flow_ap
	on employer.employer_apply_status_flow (id_apply);


create table employer.apply_intention_to_contact
(
	database_source_id integer not null,
	action_id integer not null,
	action_type_id integer not null,
	apply_id integer,
	action_datetime timestamp,
	action_datediff integer,
	employer_account_id integer,
	constraint pk_apply_intention_to_contact_id
		primary key (database_source_id, action_id, action_type_id)
);

alter table employer.apply_intention_to_contact owner to postgres;


create table employer.profile_intention_to_contact
(
	database_source_id integer not null,
	action_id bigint not null,
	action_type_id integer not null,
	action_datediff integer,
	action_datetime timestamp,
	profile_id integer,
	employer_account_id integer,
	constraint pk_profile_intention_to_contact_id
		primary key (database_source_id, action_id, action_type_id)
);

alter table employer.profile_intention_to_contact owner to postgres;



create table employer.action_to_contact_conversion
(
	intention_to_contact_type_id integer not null,
	feature_id integer not null,
	conversion numeric(10,4) not null,
	constraint action_to_contact_conversion_pk
		primary key (intention_to_contact_type_id, feature_id)
);

alter table employer.action_to_contact_conversion owner to postgres;


create table employer.employer_intention_to_contact
(
	country_id smallint not null,
	profile_id integer,
	employer_account_id integer,
	order_action_id integer,
	feature_id integer,
	action_datediff integer,
	action_datetime timestamp,
	action_type_id integer,
	action_cnt bigint
);

alter table employer.employer_intention_to_contact owner to postgres;


create table employer.employer_action_to_contact
(
	country_id smallint not null,
	profile_id integer,
	action_datediff integer,
	employer_account_id integer,
	action_to_contact_prob numeric,
	order_action_id smallint
);

alter table employer.employer_action_to_contact owner to postgres;



create table employer.ea_payment_history
(
	employer_id integer,
	create_date date,
	repl_sum numeric,
	action_all_cnt bigint,
	action_by_day_cnt bigint,
	database_source_id smallint,
	day_lag_paym integer,
	is_last_payment integer,
	currency varchar(255),
	repl_usd_sum numeric,
	employer_name varchar(2048),
	ea_create_dated date,
	country_code varchar(2),
	country_name varchar,
	account_type integer,
	business_type varchar(100),
	industry varchar(100),
	staff_size varchar(100),
	register_source integer,
	source_name varchar(100),
	moderation_status_ea integer,
	acc_by_employer_ctn integer,
	current_balance_state numeric,
	curr_balance_usd numeric
);

alter table employer.ea_payment_history owner to postgres;



create table employer.apply_and_phone_respond
(
	job_id bigint,
	apply_cnt bigint,
	vacancy_type integer,
	reciev_apply_date date,
	apply_group integer,
	employer_id integer,
	country_code varchar(2),
	account_type integer,
	employer_name varchar(2048),
	industry varchar(100),
	database_source_id smallint
);

alter table employer.apply_and_phone_respond owner to postgres;



create table employer.rfm_active_job_by_employer_monthly
(
	employer_id integer,
	job_total_cnt bigint,
	premium_job_cnt bigint,
	job_total_daily_cnt bigint,
	premium_job_daily_cnt bigint,
	date_diff integer,
	database_source_id smallint
);

alter table employer.rfm_active_job_by_employer_monthly owner to postgres;



create table employer.apply_funnel_job
(
	employer_id integer,
	job_id bigint,
	receiv_apply_date date,
	is_premium integer,
	is_seen integer,
	day_on_seen integer,
	is_reccomendation integer,
	has_interaction integer,
	apply_cnt integer,
	database_source_id smallint
);

alter table employer.apply_funnel_job owner to postgres;



create table employer.employer_job_recommendation
(
	job_id bigint,
	job_title_tag varchar(4096),
	job_region varchar(1024),
	title varchar(2048),
	is_new_job integer,
	recommendation_date date,
	employer_id integer,
	is_premium integer,
	profile_by_job_cnt integer,
	apply_before_recommendation integer,
	database_source_id smallint
);

alter table employer.employer_job_recommendation owner to postgres;



create table employer.employer_recommendation_cover_active_job_before_realese
(
	recommendation_datediff integer,
	country_code varchar(2),
	employer_id integer,
	job_id bigint,
	active_state integer,
	has_recommendation integer,
	database_source_id smallint
);

alter table employer.employer_recommendation_cover_active_job_before_realese owner to postgres;


create table employer.employer_recommendation_cover_active_job
(
	recommendation_datediff integer,
	country_code varchar(2),
	employer_id integer,
	job_id bigint,
	active_state integer,
	has_recommendation integer,
	database_source_id smallint
);

alter table employer.employer_recommendation_cover_active_job owner to postgres;



create table employer.applies_receive_view_7_day
(
	country_code varchar(2),
	country_name varchar(50),
	apply_date date,
	applies_seen_in_7_days integer,
	applies integer,
	is_reccomendation integer,
	database_source_id smallint
);

alter table employer.applies_receive_view_7_day owner to postgres;


create table employer.employer_base_reccomendation
(
	employer_id integer,
	employer_name varchar(2048),
	country_code varchar(2),
	ea_created_date date,
	industry varchar(100),
	register_source integer,
	account_type integer,
	has_employer_balance integer,
	balance integer,
	min_recommendation_date date,
	database_source_id smallint
);

alter table employer.employer_base_reccomendation owner to postgres;

create schema job;

set search_path = "job";


create table job.job_profession
(
	job_uid bigint not null,
	profession varchar(2048),
	id_project integer,
	job_title varchar,
	id_job_employer bigint
);

alter table job.job_profession owner to postgres;

create index ind_job_profession_uid
	on job.job_profession (job_uid);

create index ind_job_profession_proff
	on job.job_profession (profession);



create table job.profession_by_text
(
	country_id smallint not null,
	uid_job bigint not null,
	profession varchar,
	date_updated timestamp not null,
	constraint pk_profession_by_text_id
		primary key (country_id, uid_job)
);

alter table job.profession_by_text owner to postgres;

create index ind_profession_by_text_pr
	on job.profession_by_text (profession);


create table job.profession_by_text_except_common
(
	country_id smallint not null,
	uid_job bigint not null,
	profession varchar,
	date_updated timestamp not null,
	constraint pk_profession_by_text_except_common_id
		primary key (country_id, uid_job)
);

alter table job.profession_by_text_except_common owner to postgres;

create index ind_profession_by_text_except_common_pr
	on job.profession_by_text_except_common (profession);



create table job.profession_by_title
(
	country_id smallint not null,
	uid_job bigint not null,
	profession varchar,
	date_updated timestamp not null,
	constraint pk_profession_by_title_id
		primary key (country_id, uid_job)
);

alter table job.profession_by_title owner to postgres;

create index ind_profession_by_title_pr
	on job.profession_by_title (profession);



create table job.profession_by_title_and_text
(
	country_id smallint not null,
	uid_job bigint not null,
	profession varchar,
	date_updated timestamp not null,
	constraint pk_profession_by_title_and_text_id
		primary key (country_id, uid_job)
);

alter table job.profession_by_title_and_text owner to postgres;

create index ind_profession_by_title_and_text_pr
	on job.profession_by_title_and_text (profession);



create table job.profession_by_title_and_text_except_common
(
	country_id smallint not null,
	uid_job bigint not null,
	profession varchar,
	date_updated timestamp not null,
	constraint pk_profession_by_title_and_text_except_common_id
		primary key (country_id, uid_job)
);

alter table job.profession_by_title_and_text_except_common owner to postgres;

create index ind_profession_by_title_and_text_except_common_pr
	on job.profession_by_title_and_text_except_common (profession);


create table job.profession_by_title_except_common
(
	country_id smallint not null,
	uid_job bigint not null,
	profession varchar,
	date_updated timestamp not null,
	constraint pk_profession_by_title_except_common_id
		primary key (country_id, uid_job)
);

alter table job.profession_by_title_except_common owner to postgres;

create index ind_profession_by_title_except_common_pr
	on job.profession_by_title_except_common (profession);


create schema job_seeker;

set search_path = "job_seeker";


create table job_seeker.ltv_clients_tests
(
	country varchar(2) default ''::character varying not null,
	client_id numeric default '0'::numeric not null,
	id_test bigint default '0'::bigint not null,
	"group" bigint,
	constraint idx_90352_primary
		primary key (country, client_id, id_test)
);

alter table job_seeker.ltv_clients_tests owner to postgres;

create table job_seeker.ltv_new_users
(
	country varchar(2) default ''::character varying not null,
	client_id numeric default '0'::numeric not null,
	first_day date,
	constraint idx_90361_primary
		primary key (country, client_id)
);

alter table job_seeker.ltv_new_users owner to postgres;

create unique index idx_90361_ltv_new_users_main_indx
	on job_seeker.ltv_new_users (first_day, client_id, country);

create table job_seeker.m_ltv7_responds
(
	country varchar(2) not null,
	first_day date not null,
	client_id numeric default '0'::numeric not null,
	id_traf_source bigint not null,
	group319 integer not null,
	clicks_calls integer,
	applies_click integer,
	aways integer,
	applies_submit integer,
	constraint idx_90412_primary
		primary key (country, first_day, client_id, id_traf_source, group319)
);

alter table job_seeker.m_ltv7_responds owner to postgres;

create table job_seeker.m_ltv7_responds_agg
(
	first_day date not null,
	id_traf_source bigint not null,
	group319 integer not null,
	responds integer,
	clients numeric default '0'::numeric not null,
	constraint idx_90419_primary
		primary key (first_day, id_traf_source, group319)
);

alter table job_seeker.m_ltv7_responds_agg owner to postgres;

create table job_seeker.users_actions
(
	dt date,
	is_mobile bigint,
	cnt_users bigint,
	cnt_sessions bigint,
	cnt_searches bigint,
	cnt_nonea_jdp_views bigint,
	cnt_premium_ea_jdp_views bigint,
	cnt_free_ea_jdp_views bigint,
	cnt_nonpaid_easy_apply_jdp_views bigint,
	cnt_paid_nonea_jdp_away bigint,
	cnt_nonpaid_nonea_jdp_away bigint,
	cnt_nonea_jdp_call bigint,
	cnt_premium_ea_jdp_call bigint,
	cnt_free_ea_jdp_call bigint,
	cnt_premium_ea_jdp_apply_cv_click bigint,
	cnt_free_ea_jdp_apply_cv_click bigint,
	cnt_premium_ea_jdp_apply_nocv_click bigint,
	cnt_free_ea_jdp_apply_nocv_click bigint,
	cnt_premium_ea_jdp_apply_cv bigint,
	cnt_free_ea_jdp_apply_cv bigint,
	cnt_premium_ea_jdp_apply_nocv bigint,
	cnt_free_ea_jdp_apply_nocv bigint,
	cnt_nonpaid_easy_apply_jdp_apply bigint,
	cnt_nonpaid_easy_apply_jdp_apply_click bigint,
	revenue_eur numeric(38,9)
);

alter table job_seeker.users_actions owner to postgres;

create table job_seeker.user_apply
(
	id serial not null,
	country_id smallint not null,
	jdp_viewed_datediff integer not null,
	jdp_id bigint not null,
	account_id integer,
	phone varchar(255),
	email varchar(255),
	profile_id integer,
	user_id varchar(255),
	user_type_id smallint,
	session_id bigint,
	job_uid bigint,
	apply_flag integer,
	cookie_label bigint,
	apply_id bigint,
	jdp_viewed_datetime timestamp,
	device_id smallint,
	constraint pk_user_applies_id
		primary key (country_id, jdp_id, jdp_viewed_datediff)
);

alter table job_seeker.user_apply owner to postgres;

create index ind_user_applies_a
	on job_seeker.user_apply (account_id);

create index ind_user_applies_cl
	on job_seeker.user_apply (cookie_label);

create index ind_user_applies_u
	on job_seeker.user_apply (user_id, user_type_id);

create table job_seeker.user_active_blue_collar_score
(
	id serial not null
		constraint pk_user_blue_collar_score_id
			primary key,
	user_id varchar(255),
	user_type_id smallint,
	score double precision,
	activation_datediff integer,
	activation_datetime timestamp,
	country_id integer
);

alter table job_seeker.user_active_blue_collar_score owner to postgres;

create index ind_user_blue_collar_score_d
	on job_seeker.user_active_blue_collar_score (activation_datediff);

create index ind_user_blue_collar_score_u
	on job_seeker.user_active_blue_collar_score (user_id, user_type_id);

create table job_seeker.user_cookie
(
	id serial not null
		constraint pk_user_cookie_id
			primary key,
	cookie_label bigint,
	user_id varchar(255),
	user_type_id smallint,
	user_per_cookie_cnt integer,
	country_id integer
);

alter table job_seeker.user_cookie owner to postgres;

create index ind_user_cookie_cl
	on job_seeker.user_cookie (cookie_label);

create index user_cookie_idx
	on job_seeker.user_cookie (cookie_label, country_id);

create table job_seeker.user_account
(
	id serial not null,
	account_id bigint,
	user_id varchar(255),
	user_type_id smallint,
	user_per_account_cnt integer,
	country_id integer
);

alter table job_seeker.user_account owner to postgres;

create index ind_user_account_cl
	on job_seeker.user_account (account_id);

create index user_account_idx
	on job_seeker.user_account (account_id, country_id);

create table job_seeker.user_retention
(
	id serial not null
		constraint pk_user_retention_id
			primary key,
	score double precision,
	user_type_id smallint,
	user_id varchar(255),
	activation_datediff integer,
	activation_datetime timestamp,
	is_retained_1_7days integer,
	is_retained_1_28days integer,
	is_retained_29_98days integer,
	country_id integer,
	is_retained_1day integer
);

alter table job_seeker.user_retention owner to postgres;

create index ind_user_retention_u
	on job_seeker.user_retention (user_id, user_type_id);

create table job_seeker.user_additional_questions
(
	id serial not null
		constraint pk_user_additional_questions_id
			primary key,
	user_id varchar(255),
	user_type_id smallint,
	usage_first_datediff integer,
	usage_first_datetime timestamp,
	usage_cnt integer,
	country_id integer
);

alter table job_seeker.user_additional_questions owner to postgres;

create index ind_user_additional_questions_u
	on job_seeker.user_additional_questions (user_id, user_type_id);

create index ind_user_additional_questions_dd
	on job_seeker.user_additional_questions (usage_first_datediff);

create table job_seeker.user_address_show_on_map
(
	id serial not null
		constraint pk_user_address_show_on_map_id
			primary key,
	user_id varchar(255),
	user_type_id smallint,
	usage_first_datediff integer,
	usage_first_datetime timestamp,
	usage_cnt integer,
	country_id integer
);

alter table job_seeker.user_address_show_on_map owner to postgres;

create index ind_user_address_show_on_map_u
	on job_seeker.user_address_show_on_map (user_id, user_type_id);

create table job_seeker.user_lifetime_framework
(
	id serial not null
		constraint pk_user_lifetime_framework_id
			primary key,
	user_id varchar(255),
	user_type_id smallint,
	blue_collar_score double precision,
	jdp_viewed_datediff integer,
	next_apply_datediff integer,
	prev_apply_datediff integer,
	user_returned_type_id smallint,
	user_churn_type_id smallint,
	apply_cnt integer,
	country_id integer
);

alter table job_seeker.user_lifetime_framework owner to postgres;

create index ind_user_lifetime_framework_dd
	on job_seeker.user_lifetime_framework (jdp_viewed_datediff);

create table job_seeker.user_retained_traffic
(
	id serial not null
		constraint pk_user_retained_traffic_id
			primary key,
	blue_collar_score double precision,
	user_type_id smallint,
	user_id varchar(255),
	activation_datediff integer,
	traf_source_id bigint,
	is_paid_traf boolean,
	retention_day_name varchar(100),
	apply_cnt bigint,
	country_id integer
);

alter table job_seeker.user_retained_traffic owner to postgres;

create table job_seeker.user_jdp
(
	id serial not null,
	country_id integer not null,
	jdp_id bigint not null,
	jdp_viewed_datediff integer not null,
	user_id varchar(255),
	user_type_id smallint,
	jdp_viewed_datetime timestamp,
	job_uid bigint,
	constraint pk_user_jdp_id
		primary key (country_id, jdp_id, jdp_viewed_datediff)
);

alter table job_seeker.user_jdp owner to postgres;

create index ind_user_jdp_uid
	on job_seeker.user_jdp (job_uid);

create index ind_user_jdp_usid
	on job_seeker.user_jdp (user_id);

create table job_seeker.user_jdp_blue_collar_score
(
	country_id integer,
	user_id varchar(255),
	user_type_id smallint,
	score double precision,
	jdp_view_first_datediff integer,
	jdp_view_first_datetime timestamp
);

alter table job_seeker.user_jdp_blue_collar_score owner to postgres;

create table job_seeker.user_profile_apply
(
	user_id varchar(255) not null,
	user_type_id smallint not null,
	usage_first_datediff integer not null,
	usage_first_datetime timestamp,
	usage_cnt integer,
	country_id smallint not null,
	constraint pk_user_profile_apply_id
		primary key (country_id, user_id, user_type_id)
);

alter table job_seeker.user_profile_apply owner to postgres;

create table job_seeker.user_profile_created
(
	id serial not null
		constraint pk_user_profile_created_id
			primary key,
	country_id smallint not null,
	user_id varchar(255),
	user_type_id smallint,
	usage_first_datediff integer,
	usage_first_datetime timestamp,
	usage_cnt integer
);

alter table job_seeker.user_profile_created owner to postgres;

create index ind_user_profile_created_u
	on job_seeker.user_profile_created (user_id, user_type_id);

create table job_seeker.user_call_click
(
	country_id smallint not null,
	jdp_viewed_datediff integer not null,
	jdp_id bigint not null,
	account_id integer,
	session_id bigint,
	cookie_label bigint,
	job_uid bigint,
	jdp_flag integer,
	jdp_viewed_datetime timestamp,
	device_type integer,
	constraint user_call_click_pk
		primary key (country_id, jdp_viewed_datediff, jdp_id)
);

alter table job_seeker.user_call_click owner to postgres;

create table job_seeker.user_away
(
	country_id smallint not null,
	jdp_viewed_datediff integer not null,
	jdp_id bigint not null,
	account_id integer,
	session_id bigint,
	cookie_label bigint,
	job_uid bigint,
	jdp_flag integer,
	jdp_viewed_datetime timestamp,
	device_type integer,
	constraint user_away_pk
		primary key (country_id, jdp_viewed_datediff, jdp_id)
);

alter table job_seeker.user_away owner to postgres;

create table job_seeker.user_jdp_from_search
(
	country_id smallint,
	session_datediff integer,
	cookie_label bigint,
	session_id bigint,
	search_id bigint,
	jdp_id bigint,
	jdp_flag integer
);

alter table job_seeker.user_jdp_from_search owner to postgres;

create table job_seeker.cookie_label_first_apply
(
	country_id smallint not null,
	cookie_label bigint not null,
	first_apply_datediff integer,
	first_apply_datetime timestamp,
	constraint cookie_label_first_apply_pk
		primary key (country_id, cookie_label)
);

alter table job_seeker.cookie_label_first_apply owner to postgres;


create table job_seeker.cookie_label_first_profile_apply
(
	country_id smallint not null,
	cookie_label bigint not null,
	first_apply_datediff integer,
	first_apply_datetime timestamp,
	constraint cookie_label_first_profile_apply_pk
		primary key (country_id, cookie_label)
);

alter table job_seeker.cookie_label_first_profile_apply owner to postgres;

create table job_seeker.cookie_label_first_cv_apply
(
	country_id smallint not null,
	cookie_label bigint not null,
	first_apply_datediff integer,
	first_apply_datetime timestamp,
	constraint cookie_label_first_cv_apply_pk
		primary key (country_id, cookie_label)
);

alter table job_seeker.cookie_label_first_cv_apply owner to postgres;


create schema job_seeker_feature;

set search_path = "job_seeker_feature";

create table job_seeker_feature.show_on_map_usage
(
	country_id smallint,
	jdp_viewed_datediff integer,
	jdp_with_apply_cnt bigint,
	jdp_with_show_on_map_cnt bigint,
	jdp_with_show_on_map_click_cnt bigint
);

alter table job_seeker_feature.show_on_map_usage owner to postgres;


create table job_seeker_feature.show_on_map_apply_rate
(
	country_id smallint,
	jdp_viewed_datediff integer,
	has_show_on_map_click integer,
	apply_click_cnt bigint,
	apply_cnt bigint
);

alter table job_seeker_feature.show_on_map_apply_rate owner to postgres;

create schema jooble_goals;

set search_path = "jooble_goals";

create table jooble_goals.revenue_product_aggregator_plan_2021_monthly
(
	month_first_date date not null
		constraint pk_revenue_product_aggregator_plan_2021_monthly
			primary key,
	revenue_usd numeric(38)
);

alter table jooble_goals.revenue_product_aggregator_plan_2021_monthly owner to postgres;

create table jooble_goals.info_revenue_source_type
(
	id integer not null
		constraint pk_info_revenue_source_type
			primary key,
	name varchar(30)
);

alter table jooble_goals.info_revenue_source_type owner to postgres;

create table jooble_goals.info_revenue_source_subtype
(
	id integer not null
		constraint pk_info_revenue_source_subtype
			primary key,
	name varchar(30)
);

alter table jooble_goals.info_revenue_source_subtype owner to postgres;


create schema product;

set search_path = "product";


create table product.ab_email_tests_raw_data_account
(
	country varchar(2) default ''::character varying not null,
	date_diff bigint default '0'::bigint not null,
	dt date not null,
	id_account bigint default '0'::bigint not null,
	id_test bigint default '0'::bigint not null,
	"group" bigint default '0'::bigint not null,
	n_sent_letters bigint,
	n_open_letters bigint,
	n_click_letters bigint,
	views_jdp bigint,
	click_calls bigint,
	applies_click bigint,
	aways bigint,
	applies_submit bigint,
	applies_submit_no_cv bigint,
	views_jdp_l bigint,
	click_calls_l bigint,
	applies_click_l bigint,
	aways_l bigint,
	applies_submit_l bigint,
	applies_submit_no_cv_l bigint,
	serp_aways bigint,
	serp_aways_l bigint,
	clicks_alertview bigint,
	constraint idx_90055_primary
		primary key (country, date_diff, dt, id_account, id_test, "group")
);

alter table product.ab_email_tests_raw_data_account owner to postgres;

create table product.ab_tests_boot_data
(
	id bigserial not null,
	test bigint not null,
	groups bigint not null,
	metric varchar(100) not null,
	metric_value double precision not null,
	dt_start date not null,
	dt_end date not null,
	dt_calc timestamp not null,
	devices varchar(10) not null,
	test_type varchar(20) not null,
	country varchar(10) not null,
	constraint idx_90065_primary
		primary key (id, devices, test_type, country)
);

alter table product.ab_tests_boot_data owner to postgres;

create table product.ab_tests_extra_metrics
(
	dt date not null,
	test bigint not null,
	groups bigint not null,
	mobile smallint not null,
	returned smallint not null,
	metric varchar(100) not null,
	metric_value double precision,
	constraint idx_90069_primary
		primary key (dt, test, groups, mobile, metric, returned)
);

alter table product.ab_tests_extra_metrics owner to postgres;

create table product.ab_tests_raw_apply_data
(
	session_id numeric not null,
	jdp_id numeric not null,
	dt date not null,
	id_test smallint not null,
	group_id smallint not null,
	mobile smallint not null,
	returned smallint not null,
	clicks_calls double precision,
	applies_click double precision,
	applies_submit double precision,
	applies_submit_no_cv double precision,
	applies_submit_cv_builder double precision,
	constraint idx_90072_primary
		primary key (session_id, jdp_id, id_test)
);

alter table product.ab_tests_raw_apply_data owner to postgres;

create unique index idx_90072_idx_ab_tests_raw_app_data_unique
	on product.ab_tests_raw_apply_data (session_id, jdp_id, dt, id_test, group_id);

create table product.ab_tests_raw_data
(
	session_id numeric not null,
	dt date not null,
	id_test smallint not null,
	group_id smallint not null,
	mobile smallint not null,
	returned smallint not null,
	searches double precision,
	clicks_serp double precision,
	views_jdp double precision,
	clicks_calls double precision,
	applies_click double precision,
	aways double precision,
	applies_submit double precision,
	applies_submit_no_cv double precision,
	applies_submit_cv_builder double precision,
	constraint idx_90078_primary
		primary key (session_id, id_test)
);

alter table product.ab_tests_raw_data owner to postgres;

create unique index idx_90078_idx_ab_tests_raw_data_unique
	on product.ab_tests_raw_data (session_id, dt, id_test, group_id);

create table product.ab_tests_results
(
	test bigint not null,
	metric varchar(100) not null,
	type_test varchar(20) not null,
	groups_vs varchar(20) not null,
	a_mean double precision not null,
	b_mean double precision not null,
	delta_ab_mean_prc double precision not null,
	p_value double precision not null,
	significance smallint not null,
	dt_start date not null,
	dt_end date not null,
	dt_calc timestamp not null,
	a_ci_lower double precision,
	a_ci_upper double precision,
	b_ci_lower double precision,
	b_ci_upper double precision,
	devices varchar(10) default ''::character varying not null,
	test_type varchar(20) not null,
	country varchar(10) not null,
	constraint idx_90084_primary
		primary key (test, metric, groups_vs, dt_calc, devices, test_type, country)
);

alter table product.ab_tests_results owner to postgres;

create table product.ab_tests_scalability
(
	test_id bigint not null,
	test_type varchar(20) not null,
	country varchar(3) default ''::character varying not null,
	scalability_bp double precision default '1'::double precision,
	scalability_itc double precision,
	group_in_prod integer,
	constraint ab_tests_scalability_pk
		primary key (test_id, test_type, country)
);

alter table product.ab_tests_scalability owner to postgres;

create table product.cv_fields_poll_score
(
	id_field bigint not null,
	date_poll date not null,
	n_poll integer default 0 not null,
	score_value numeric(5,4) default 0.0000 not null,
	constraint idx_90131_primary
		primary key (id_field, n_poll)
);

alter table product.cv_fields_poll_score owner to postgres;

create table product.cv_parsed_1_0
(
	country varchar(2) default ''::character varying not null,
	id bigint default '0'::bigint not null,
	date_created timestamp with time zone,
	score bigint,
	cvb_version varchar(3),
	city text,
	name text,
	phone varchar(50),
	email text,
	expirience_title_1 text,
	expirience_name_1 text,
	expirience_city_1 text,
	expirience_stillactive_1 boolean,
	expirience_fromdate_1 timestamp with time zone,
	expirience_todate_1 timestamp with time zone,
	expirience_descr_1 text,
	expirience_title_2 text,
	expirience_name_2 text,
	expirience_city_2 text,
	expirience_stillactive_2 boolean,
	expirience_fromdate_2 timestamp with time zone,
	expirience_todate_2 timestamp with time zone,
	expirience_descr_2 text,
	expirience_title_3 text,
	expirience_name_3 text,
	expirience_city_3 text,
	expirience_stillactive_3 boolean,
	expirience_fromdate_3 timestamp with time zone,
	expirience_todate_3 timestamp with time zone,
	expirience_descr_3 text,
	education_degree_1 text,
	education_name_1 text,
	education_occupation_1 text,
	education_city_1 text,
	education_ischecked_1 boolean,
	education_stillactivedate_1 boolean,
	education_fromdate_1 timestamp with time zone,
	education_todate_1 timestamp with time zone,
	education_degree_2 text,
	education_name_2 text,
	education_occupation_2 text,
	education_city_2 text,
	education_ischecked_2 boolean,
	education_stillactivedate_2 boolean,
	education_fromdate_2 timestamp with time zone,
	education_todate_2 timestamp with time zone,
	skills_value_1 text,
	skills_value_2 text,
	skills_value_3 text,
	skills_value_4 text,
	skills_value_5 text,
	skills_value_6 text,
	skills_value_7 text,
	skills_value_8 text,
	skills_value_9 text,
	skills_value_10 text,
	skills_value_11 text,
	skills_value_12 text,
	skills_value_13 text,
	skills_value_14 text,
	skills_value_15 text,
	noexperience boolean,
	noeducation boolean,
	constraint idx_90136_primary
		primary key (country, id)
);

alter table product.cv_parsed_1_0 owner to postgres;

create table product.cv_parsed_2_0
(
	country varchar(2) default ''::character varying not null,
	id bigint default '0'::numeric not null,
	id_session bigint,
	id_jdp bigint,
	date_created timestamp with time zone,
	has_photo smallint,
	city varchar(800),
	name varchar(500),
	phone varchar(50),
	email varchar(500),
	expirience_title_1 varchar(500),
	expirience_name_1 varchar(500),
	expirience_period_id_1 smallint,
	expirience_responsibilities_1 text,
	expirience_title_2 varchar(500),
	expirience_name_2 varchar(500),
	expirience_period_id_2 smallint,
	expirience_responsibilities_2 text,
	expirience_title_3 varchar(500),
	expirience_name_3 varchar(500),
	expirience_period_id_3 smallint,
	expirience_responsibilities_3 text,
	education_degree_id_1 varchar(500),
	education_name_1 varchar(500),
	education_occupation_1 varchar(500),
	education_grad_year_1 smallint,
	education_degree_id_2 varchar(500),
	education_name_2 varchar(500),
	education_occupation_2 varchar(500),
	education_grad_year_2 smallint,
	education_degree_id_3 varchar(500),
	education_name_3 varchar(500),
	education_occupation_3 varchar(500),
	education_grad_year_3 smallint,
	skills_value_1 text,
	hasexperience boolean,
	position boolean,
	yearofbirth smallint,
	relocation_type_id smallint,
	salary_amount integer,
	salary_currency_id smallint,
	salary_period_id smallint,
	employmenttypes_id_1 smallint,
	employmenttypes_id_2 smallint,
	employmenttypes_id_3 smallint,
	employmenttypes_id_4 smallint,
	cvb_version varchar(10) default '2.0'::character varying,
	constraint idx_90144_primary
		primary key (country, id)
);

alter table product.cv_parsed_2_0 owner to postgres;

create table product.email_sent_funnel_raw_d
(
	country varchar(10) not null,
	date_diff bigint not null,
	dt date not null,
	id_account bigint not null,
	n_sent_letters bigint,
	n_open_letters bigint,
	n_click_letters bigint,
	session_id numeric,
	views_jdp bigint,
	click_calls bigint,
	applies_click bigint,
	aways bigint,
	applies_submit bigint,
	applies_submit_no_cv bigint,
	views_jdp_l bigint,
	click_calls_l bigint,
	applies_click_l bigint,
	aways_l bigint,
	applies_submit_l bigint,
	applies_submit_no_cv_l bigint,
	constraint idx_90220_primary
		primary key (country, date_diff, id_account)
);

alter table product.email_sent_funnel_raw_d owner to postgres;

create table product.e_funnel_apply_cvb_rate
(
	apply_version varchar(15) not null,
	country varchar(3) not null,
	cvb_rate double precision,
	cvb_rate_cv_vacancy double precision,
	cvb_rate_nocv_vacancy double precision,
	constraint idx_90230_primary
		primary key (apply_version, country)
);

alter table product.e_funnel_apply_cvb_rate owner to postgres;

create table product.info_ab_tests
(
	test bigint not null,
	test_type varchar(20) not null,
	team_id smallint,
	exclude_from_year_goal integer default 0,
	device_types varchar(20),
	user_types varchar(20),
	is_in_prod boolean default false not null,
	date_launch date,
	group_in_prod smallint,
	task_link varchar(300),
	constraint idx_90265_primary
		primary key (test, test_type)
);

alter table product.info_ab_tests owner to postgres;

create table product.info_cv_fields
(
	id bigint not null
		constraint idx_90269_primary
			primary key,
	name varchar(100)
);

alter table product.info_cv_fields owner to postgres;

create table product.info_funnel_stages
(
	funnel_stage_id bigserial not null
		constraint idx_90274_primary
			primary key,
	funnel_stage_name varchar(45),
	stage_number bigint
);

alter table product.info_funnel_stages owner to postgres;

create table product.info_funnel_stages_number
(
	stage_number integer default 0 not null
		constraint idx_90278_primary
			primary key,
	stage_number_name varchar(100)
);

alter table product.info_funnel_stages_number owner to postgres;

create table product.info_letter_type
(
	id bigint not null
		constraint idx_90282_primary
			primary key,
	name varchar(45)
);

alter table product.info_letter_type owner to postgres;

create table product.info_mobile
(
	mobile bigint not null
		constraint idx_90285_primary
			primary key,
	name varchar(45) not null
);

alter table product.info_mobile owner to postgres;

create table product.info_no_cv_vacancy
(
	no_cv_vacancy bigint not null
		constraint idx_90288_primary
			primary key,
	name varchar(45) not null
);

alter table product.info_no_cv_vacancy owner to postgres;

create table product.info_returned
(
	returned bigint not null
		constraint idx_90291_primary
			primary key,
	name varchar(45) not null
);

alter table product.info_returned owner to postgres;

create table product.apply_funnel_jdp
(
	country varchar(3) not null,
	dt timestamp not null,
	mobile smallint default 0 not null,
	returned smallint default 0 not null,
	group_t1 smallint default 0 not null,
	test_1 smallint default 0 not null,
	group_t2 smallint default 0 not null,
	test_2 smallint default 0 not null,
	no_cv_vacancy smallint default 0 not null,
	id_session bigint default '0'::bigint not null,
	id_jdp bigint default '0'::bigint not null,
	cookie_label bigint default '0'::bigint not null,
	id_account integer default '0'::bigint not null,
	applies_click smallint,
	applies_click_with_no_cv smallint,
	applies_click_no_cv smallint,
	applies_submit smallint,
	applies_submit_no_cv smallint,
	cv_create_clicks smallint,
	cv_upload_clicks smallint,
	cv_attaches smallint,
	cv_uploaded smallint,
	cv_selected smallint,
	cv_auth integer,
	n_account_cvs integer,
	n_client_cvs integer,
	cv_creations smallint,
	id_traf_source integer,
	dt_trunc date not null,
	test_3 smallint default 0 not null,
	group_t3 smallint default 0 not null,
	test_4 smallint default 0,
	group_t4 smallint default 0,
	test_5 smallint default 0,
	group_t5 smallint default 0,
	date_diff integer not null,
	constraint apply_funnel_jdp_pk
		primary key (country, id_jdp, date_diff)
);

alter table product.apply_funnel_jdp owner to postgres;

create index apply_funnel_jdp_bot_delete_idx
	on product.apply_funnel_jdp (country, date_diff, id_session);

create index apply_funnel_jdp_cl_idx
	on product.apply_funnel_jdp (country, cookie_label, applies_submit, dt);

create index apply_funnel_jdp_cls_idx
	on product.apply_funnel_jdp (country, cookie_label, applies_click, dt);

create table product.apply_metrics
(
	country varchar(3) default 'UA'::character varying not null,
	dt date not null,
	mobile smallint not null,
	returned smallint not null,
	group_308 smallint default '0'::smallint not null,
	test_308 bigint default '0'::bigint not null,
	group_319 smallint default '0'::smallint not null,
	test_319 smallint default '0'::smallint not null,
	group_330 smallint default '0'::smallint not null,
	test_330 smallint default '0'::smallint not null,
	group_346 smallint default '0'::smallint not null,
	test_346 smallint default '0'::smallint not null,
	cnt_sess double precision,
	cnt_jdp_with_easy_apply_form double precision,
	applies_click double precision,
	applies_submit double precision,
	applies_submit_no_cv double precision,
	clients double precision,
	cv_create_clicks double precision,
	cv_creations double precision,
	cv_attaches double precision,
	phone_clicks double precision,
	constraint idx_90109_primary
		primary key (country, dt, mobile, returned, group_308, test_308, group_319, test_319, group_330, test_330, group_346, test_346)
);

alter table product.apply_metrics owner to postgres;

create table product.info_traffic_source
(
	country varchar(2) default ''::character varying not null,
	id bigint default '0'::bigint not null,
	name varchar(255),
	utm varchar(255),
	adv_flag bigint,
	is_paid boolean,
	country_id integer,
	constraint idx_90294_primary
		primary key (country, id)
);

alter table product.info_traffic_source owner to postgres;

create table product.jdp_interaction
(
	country varchar(2) default ''::character varying not null,
	dt date not null,
	device bigint default '0'::bigint not null,
	id_traf_source bigint default '0'::bigint not null,
	is_returned bigint default '0'::bigint not null,
	no_cv_vacancy bigint default '0'::bigint not null,
	is_premium bigint default '0'::bigint not null,
	is_ea bigint default '0'::bigint not null,
	jdp_response_type_id bigint default '0'::bigint not null,
	sessions bigint,
	clients bigint,
	serp_clicks_premium bigint,
	serp_clicks bigint,
	views bigint,
	aways bigint,
	call_clicks bigint,
	apply_clicks bigint,
	applies bigint,
	responds bigint,
	constraint idx_90334_primary
		primary key (country, dt, device, id_traf_source, is_returned, no_cv_vacancy, is_premium, is_ea, jdp_response_type_id)
);

alter table product.jdp_interaction owner to postgres;

create index idx_90334_country_dt_indx
	on product.jdp_interaction (country, dt);

create table product.jobs_daily
(
	country varchar(3) default ''::character varying not null,
	dt date not null,
	jobs_premium numeric,
	jobs_not_premium numeric,
	jobs_all numeric,
	jobs_ea numeric,
	constraint idx_90345_primary
		primary key (country, dt)
);

alter table product.jobs_daily owner to postgres;

create table product.ltv_sessions
(
	country varchar(2) default ''::character varying not null,
	session_id numeric default '0'::numeric not null,
	dt timestamp with time zone,
	mobile bigint,
	returned bigint,
	client_id numeric,
	id_traf_source bigint,
	searches bigint,
	clicks_serp bigint,
	views_jdp bigint,
	views_jdp_with_apply bigint,
	clicks_calls bigint,
	applies_click bigint,
	aways bigint,
	applies_submit bigint,
	applies_submit_no_cv bigint,
	constraint idx_90369_primary
		primary key (country, session_id)
);

alter table product.ltv_sessions owner to postgres;

create index idx_90369_ltv7_indx
	on product.ltv_sessions (mobile, dt, client_id, country, id_traf_source);

create index idx_90369_client_indx
	on product.ltv_sessions (client_id, dt);

create table product.m_apply_funnel
(
	country varchar(3) default 'xx'::character varying not null,
	dt date not null,
	mobile smallint default 0 not null,
	returned smallint default 0 not null,
	no_cv_vacancy smallint default 0 not null,
	apply_version varchar(30) not null,
	apply_subversion varchar(100) default ''::character varying not null,
	is_auth numeric default '0'::numeric not null,
	have_cv numeric not null,
	no_cv_apply_flow smallint not null,
	n_clients numeric default '0'::numeric not null,
	cnt_sess numeric default '0'::numeric not null,
	cnt_jdp_with_easy_apply_form numeric default '0'::numeric not null,
	applies_click numeric(32),
	applies_submit numeric(32),
	applies_submit_with_cv_created double precision,
	applies_submit_with_cv_selected numeric(33),
	cv_create_clicks numeric(32),
	no_cv_clicks numeric(32),
	cv_select_clicks numeric(23),
	cv_creations double precision,
	cv_selections numeric(23),
	id_traf_source integer default 0 not null,
	rank_apply_click smallint default 0 not null,
	rank_apply_submit smallint default 0 not null,
	cv_upload_clicks integer,
	cv_uploads integer,
	cv_auto_attaches integer,
	applies_submit_with_cv_auto_attached integer,
	applies_submit_with_cv_uploaded integer,
	constraint m_apply_funnel_pk
		primary key (country, dt, mobile, returned, no_cv_vacancy, apply_version, apply_subversion, is_auth, have_cv, no_cv_apply_flow, id_traf_source, rank_apply_click, rank_apply_submit)
);

alter table product.m_apply_funnel owner to postgres;

create table product.product_teams_performance_plan
(
	dt_month date not null,
	team_id smallint not null,
	goal_type varchar(45) default ''::character varying not null,
	growth_bp double precision,
	growth_itc double precision,
	constraint idx_90426_primary
		primary key (dt_month, team_id, goal_type)
);

alter table product.product_teams_performance_plan owner to postgres;

create table product.recommend_query
(
	country varchar(5) not null,
	dt date not null,
	mobile smallint not null,
	returned smallint not null,
	session_id numeric not null,
	group319 smallint,
	test319 bigint,
	q_kw varchar(175) not null,
	recommended_job_views bigint,
	recommended_job_clicks bigint,
	click_calls bigint,
	applies_click bigint,
	aways bigint,
	applies_submit bigint,
	applies_submit_no_cv bigint,
	constraint idx_90430_primary
		primary key (country, dt, mobile, returned, session_id, q_kw)
);

alter table product.recommend_query owner to postgres;


create table product.info_product_teams
(
	id serial not null
		constraint info_product_teams_pk
			primary key,
	name varchar(100) not null
);

alter table product.info_product_teams owner to dap;

create table product.session_test
(
	country varchar(2) not null,
	id_session bigint not null,
	dt date not null,
	id_test smallint not null,
	"group" smallint,
	constraint session_test_pkey
		primary key (country, id_session, dt, id_test)
);

alter table product.session_test owner to dap;

create table product.cv_build_action
(
	country varchar(2) not null,
	id bigint not null,
	dt timestamp not null,
	date timestamp,
	id_account integer,
	id_session bigint,
	id_session_build bigint,
	id_image bigint,
	json text,
	id_jdp bigint,
	type smallint,
	flags integer,
	step smallint,
	data varchar(512),
	constraint cv_build_action_pkey
		primary key (country, id, dt)
);

alter table product.cv_build_action owner to dap;

create table product.ab_tests_results_ds
(
	test integer not null,
	metric varchar(100) not null,
	type_test varchar(20) not null
		constraint ab_tests_results_ds_type_test_check
			check ((type_test)::text = ANY ((ARRAY['AB'::character varying, 'AA'::character varying])::text[])),
	groups_vs varchar(20) not null,
	a_mean double precision not null,
	b_mean double precision not null,
	dt_start date not null,
	dt_end date not null,
	dt_calc timestamp default CURRENT_TIMESTAMP not null,
	a_ci_lower double precision,
	a_ci_upper double precision,
	b_ci_lower double precision,
	b_ci_upper double precision,
	devices varchar(10) not null
		constraint ab_tests_results_ds_devices_check
			check ((devices)::text = ANY ((ARRAY['m'::character varying, 'd'::character varying, 'm+d'::character varying])::text[])),
	test_type varchar(20) not null
		constraint ab_tests_results_ds_test_type_check
			check ((test_type)::text = ANY ((ARRAY['recommend'::character varying, 'search'::character varying])::text[])),
	country varchar(2) not null
		constraint ab_tests_results_ds_country_check
			check (upper((country)::text) = (country)::text),
	p_value double precision,
	constraint pk_ab_tests_results_ds
		primary key (test, metric, groups_vs, devices, test_type, country)
);

alter table product.ab_tests_results_ds owner to postgres;

create table product.user_actions_daily_agg
(
	country varchar(2) default ''::character varying not null,
	session_day timestamp,
	returned_user integer,
	session_count integer,
	serp_clicks_away_mob_premium_count integer,
	serp_clicks_away_desktop_premium_count integer,
	serp_clicks_jdp_mob_dte_premium_count integer,
	serp_clicks_jdp_desktop_dte_premium_count integer,
	serp_clicks_jdp_mob_notdte_premium_count integer,
	serp_clicks_jdp_desktop_notdte_premium_count integer,
	serp_clicks_away_mob_notpremium_count integer,
	serp_clicks_away_desktop_notpremium_count integer,
	serp_clicks_jdp_mob_dte_notpremium_count integer,
	serp_clicks_jdp_desktop_dte_notpremium_count integer,
	serp_clicks_jdp_mob_notdte_notpremium_count integer,
	serp_clicks_jdp_desktop_notdte_notpremium_count integer,
	jdp_mob_serp_dte_premium_count integer,
	jdp_desktop_serp_dte_premium_count integer,
	jdp_mob_notserp_dte_premium_count integer,
	jdp_desktop_notserp_dte_premium_count integer,
	jdp_mob_serp_dte_notpremium_count integer,
	jdp_desktop_serp_dte_notpremium_count integer,
	jdp_mob_notserp_dte_notpremium_count integer,
	jdp_desktop_notserp_dte_notpremium_count integer,
	jdp_mob_serp_notdte_count integer,
	jdp_desktop_serp_notdte_count integer,
	jdp_mob_notserp_notdte_count integer,
	jdp_desktop_notserp_notdte_count integer,
	itc_mob_serp_dte_premium integer,
	itc_desktop_serp_dte_premium integer,
	itc_mob_notserp_dte_premium integer,
	itc_desktop_notserp_dte_premium integer,
	itc_mob_serp_dte_notpremium integer,
	itc_desktop_serp_dte_notpremium integer,
	itc_mob_notserp_dte_notpremium integer,
	itc_desktop_notserp_dte_notpremium integer,
	itc_mob_serp_notdte integer,
	itc_desktop_serp_notdte integer,
	itc_mob_notserp_notdte integer,
	itc_desktop_notserp_notdte integer,
	calls_mob_serp_dte_premium integer,
	calls_desktop_serp_dte_premium integer,
	calls_mob_notserp_dte_premium integer,
	calls_desktop_notserp_dte_premium integer,
	calls_mob_serp_dte_notpremium integer,
	calls_desktop_serp_dte_notpremium integer,
	calls_mob_notserp_dte_notpremium integer,
	calls_desktop_notserp_dte_notpremium integer,
	calls_mob_serp_notdte integer,
	calls_desktop_serp_notdte integer,
	calls_mob_notserp_notdte integer,
	calls_desktop_notserp_notdte integer,
	apply_submit_mob_serp_dte_premium integer,
	apply_submit_desktop_serp_dte_premium integer,
	apply_submit_mob_notserp_dte_premium integer,
	apply_submit_desktop_notserp_dte_premium integer,
	apply_submit_mob_serp_dte_notpremium integer,
	apply_submit_desktop_serp_dte_notpremium integer,
	apply_submit_mob_notserp_dte_notpremium integer,
	apply_submit_desktop_notserp_dte_notpremium integer,
	apply_submit_mob_serp_notdte integer,
	apply_submit_desktop_serp_notdte integer,
	apply_submit_mob_notserp_notdte integer,
	apply_submit_desktop_notserp_notdte integer,
	click_away_mob_serp_dte_premium integer,
	click_away_desktop_serp_dte_premium integer,
	click_away_mob_notserp_dte_premium integer,
	click_away_desktop_notserp_dte_premium integer,
	click_away_mob_serp_dte_notpremium integer,
	click_away_desktop_serp_dte_notpremium integer,
	click_away_mob_notserp_dte_notpremium integer,
	click_away_desktop_notserp_dte_notpremium integer,
	click_away_mob_serp_notdte integer,
	click_away_desktop_serp_notdte integer,
	click_away_mob_notserp_notdte integer,
	click_away_desktop_notserp_notdte integer,
	click_apply_mob_serp_dte_premium integer,
	click_apply_desktop_serp_dte_premium integer,
	click_apply_mob_notserp_dte_premium integer,
	click_apply_desktop_notserp_dte_premium integer,
	click_apply_mob_serp_dte_notpremium integer,
	click_apply_desktop_serp_dte_notpremium integer,
	click_apply_mob_notserp_dte_notpremium integer,
	click_apply_desktop_notserp_dte_notpremium integer,
	click_apply_mob_serp_notdte integer,
	click_apply_desktop_serp_notdte integer,
	click_apply_mob_notserp_notdte integer,
	click_apply_desktop_notserp_notdte integer,
	users integer
);

alter table product.user_actions_daily_agg owner to postgres;

create index idx_user_actions_daily_agg_lookup
	on product.user_actions_daily_agg (country, session_day, returned_user, session_count, serp_clicks_away_mob_premium_count, serp_clicks_away_desktop_premium_count, serp_clicks_jdp_mob_dte_premium_count, serp_clicks_jdp_desktop_dte_premium_count, serp_clicks_jdp_mob_notdte_premium_count, serp_clicks_jdp_desktop_notdte_premium_count, serp_clicks_away_mob_notpremium_count, serp_clicks_away_desktop_notpremium_count, serp_clicks_jdp_mob_dte_notpremium_count, serp_clicks_jdp_desktop_dte_notpremium_count, serp_clicks_jdp_mob_notdte_notpremium_count, serp_clicks_jdp_desktop_notdte_notpremium_count, jdp_mob_serp_dte_premium_count, jdp_desktop_serp_dte_premium_count, jdp_mob_notserp_dte_premium_count, jdp_desktop_notserp_dte_premium_count, jdp_mob_serp_dte_notpremium_count, jdp_desktop_serp_dte_notpremium_count, jdp_mob_notserp_dte_notpremium_count, jdp_desktop_notserp_dte_notpremium_count, jdp_mob_serp_notdte_count, jdp_desktop_serp_notdte_count, jdp_mob_notserp_notdte_count, jdp_desktop_notserp_notdte_count, itc_mob_serp_dte_premium, itc_desktop_serp_dte_premium, itc_mob_notserp_dte_premium, itc_desktop_notserp_dte_premium);

create table product.info_ab_tests_ds
(
	test bigint not null,
	test_type varchar(20) not null,
	team_id smallint,
	exclude_from_year_goal integer,
	device_types varchar(20),
	user_types varchar(20),
	is_in_prod boolean,
	date_launch date,
	group_in_prod smallint,
	task_link varchar(300),
	country varchar(2) default 'XX'::character varying not null,
	constraint info_ab_tests_ds_pk
		primary key (test_type, test, country)
);

alter table product.info_ab_tests_ds owner to dap;

create table product.ab_tests_scalability_ds
(
	test_id bigint not null,
	test_type varchar(20) not null,
	country varchar(3) not null,
	scalability_bp double precision,
	scalability_itc double precision,
	scalability_revenue double precision,
	constraint ab_tests_scalability_ds_pk
		primary key (test_type, test_id, country)
);

alter table product.ab_tests_scalability_ds owner to dap;

create table product.user_mailing_interaction
(
	country varchar(2) not null,
	date_diff integer not null,
	users integer,
	subscribe integer,
	verify integer,
	sents integer,
	opens integer,
	returns integer,
	responds integer,
	constraint pk_user_mailing_interaction
		primary key (country, date_diff)
);

alter table product.user_mailing_interaction owner to postgres;

create table product.apply_cvb_funnel
(
	country varchar(2) not null,
	date_diff integer not null,
	id_jdp bigint not null,
	date_click_create timestamp,
	view_1_step smallint,
	click_next_from_1_step smallint,
	click_back_from_1_step smallint,
	view_2_step smallint,
	click_next_from_2_step smallint,
	click_back_from_2_step smallint,
	view_3_step smallint,
	click_back_from_3_step smallint,
	cv_created smallint,
	date_created timestamp,
	constraint pk_apply_cvb_funnel_id
		primary key (country, date_diff, id_jdp)
);

alter table product.apply_cvb_funnel owner to postgres;

create table product.apply_upload_funnel
(
	country varchar(2) not null,
	date_diff integer not null,
	id_jdp bigint not null,
	upload_cv_click integer,
	upload_cv_success integer,
	upload_cv_error integer,
	constraint pk_apply_upload_funnel_id
		primary key (country, date_diff, id_jdp)
);

alter table product.apply_upload_funnel owner to postgres;

create table product.apply_cv_upload_errors
(
	country varchar(2) not null,
	id_jdp bigint not null,
	date_diff integer not null,
	date timestamp,
	flags integer
);

alter table product.apply_cv_upload_errors owner to postgres;

create index ind_apply_cv_upload_errors_id
	on product.apply_cv_upload_errors (country, id_jdp, date_diff);

create table product.metrics_data
(
	country varchar(2) not null,
	test smallint not null,
	"group" smallint not null,
	iteration smallint not null,
	platform varchar(50) not null,
	user_type varchar(50) not null,
	sessions integer,
	users integer,
	sessions_with_search integer,
	users_with_search integer,
	searches integer,
	no_result_searches integer,
	sessions_with_click integer,
	users_with_click integer,
	searches_with_click integer,
	clicks integer,
	jobs_click integer,
	serp_away_click integer,
	jdp_away_click integer,
	jdp_apply_click integer,
	premium_click integer,
	ads_click integer,
	users_with_jdp_view integer,
	sessions_with_jdp_view integer,
	jdp_count integer,
	premium_jdp integer,
	dte_jdp integer,
	agg_jdp integer,
	users_with_jdp_action integer,
	users_with_respond integer,
	users_with_click_apply integer,
	users_with_apply_submit_click integer,
	users_with_apply_submit_success integer,
	sessions_with_jdp_action integer,
	sessions_with_respond integer,
	sessions_with_click_apply integer,
	sessions_with_apply_submit_click integer,
	sessions_with_apply_submit_success integer,
	jdp_with_action integer,
	jdp_with_respond integer,
	jdp_with_click_apply integer,
	jdp_with_apply_submit_click integer,
	jdp_with_apply_submit_success integer,
	itc integer,
	bp double precision,
	jdp_count_from_swipe integer,
	jdp_count_from_arrow_click integer,
	actions integer,
	users_2_jdp integer,
	users_5_jdp integer,
	users_10_jdp integer,
	users_with_submit_success_2 integer,
	users_with_submit_success_3 integer,
	users_with_submit_success_4 integer,
	date_diff integer not null,
	weekly smallint not null,
	constraint pk_metrics_data_id
		primary key (country, test, "group", iteration, platform, user_type, date_diff, weekly)
);

alter table product.metrics_data owner to postgres;

create table product.apply_upload_funnel_agg
(
	country varchar(2) not null,
	dt_trunc date not null,
	date_diff integer not null,
	mobile smallint,
	returned smallint,
	no_cv_vacancy smallint,
	apply_version varchar(100),
	apply_subversion varchar(100),
	rank_apply_click integer,
	rank_apply_submit integer,
	is_auth double precision,
	have_cv double precision,
	upload_cv_click integer,
	upload_cv_success integer,
	upload_cv_error integer
);

alter table product.apply_upload_funnel_agg owner to postgres;

create index ind_apply_upload_funnel_agg_d
	on product.apply_upload_funnel_agg (country, dt_trunc);

create table product.apply_cvb_funnel_agg
(
	country varchar(2) not null,
	dt_trunc date not null,
	date_diff integer not null,
	mobile smallint,
	returned smallint,
	no_cv_vacancy smallint,
	apply_version varchar(100),
	apply_subversion varchar(100),
	rank_apply_click integer,
	rank_apply_submit integer,
	is_auth double precision,
	have_cv double precision,
	view_1_step integer,
	view_2_step integer,
	view_3_step integer
);

alter table product.apply_cvb_funnel_agg owner to postgres;

create index ind_apply_cvb_funnel_agg_d
	on product.apply_cvb_funnel_agg (country, dt_trunc);

create table product.metrics_action_data
(
	country varchar(2) not null,
	test smallint not null,
	"group" smallint not null,
	iteration smallint not null,
	platform varchar(50) not null,
	user_type varchar(50) not null,
	job_type varchar(50) not null,
	dte_job_type varchar(50) not null,
	job_age varchar(50) not null,
	apply_type varchar(50) not null,
	jdp_branded varchar(50) not null,
	jdp_recommend varchar(50) not null,
	label_for_new varchar(50) not null,
	employer_widget varchar(50) not null,
	jdps integer,
	respond_click integer,
	respond_apply_click integer,
	respond_away_click integer,
	apply_submit_click integer,
	apply_success integer,
	save_click integer,
	swipe_next integer,
	swipe_previous integer,
	arrow_next integer,
	arrow_previous integer,
	save_mobile_click integer,
	back_to_serp_bottom_click integer,
	back_to_serp_top_click integer,
	click_to_call integer,
	company_info_click integer,
	show_phone_number integer,
	view_employee_number integer,
	view_multiply_employee_number integer,
	show_on_map integer,
	link_for_create_cv integer,
	apply_form_summary_show integer,
	jdp_view_action integer,
	jdp_view_jdp integer,
	jdp_view_user integer,
	respond_click_user integer,
	respond_apply_click_user integer,
	respond_away_click_user integer,
	apply_submit_click_user integer,
	apply_success_user integer,
	swipe_user integer,
	arrow_click_user integer,
	call_action_user integer,
	action_to_contact_user integer,
	action_to_contact_jdp integer,
	date_diff integer not null,
	weekly smallint not null,
	constraint pk_metrics_action_data_id
		primary key (country, test, "group", iteration, platform, user_type, job_type, dte_job_type, job_age, apply_type, jdp_branded, jdp_recommend, label_for_new, employer_widget, date_diff, weekly)
);

alter table product.metrics_action_data owner to postgres;

create table product.apply_funnel_dash
(
	id serial not null
		constraint pk_apply_funnel_dash_id
			primary key,
	country smallint not null,
	date_diff integer not null,
	apply_flow_type integer,
	job_profession integer,
	is_auth integer,
	device integer,
	returned integer,
	id_traf_source integer,
	have_cv integer,
	rank_apply_click integer,
	rank_apply_submit integer,
	funnel_stage_name integer,
	stage_number integer,
	stage_number_name integer,
	stage_value integer,
	views integer,
	clicks_apply integer,
	applies integer,
	userrec varchar(15),
	daterec timestamp,
	usermod varchar(15),
	datemod timestamp
);

alter table product.apply_funnel_dash owner to postgres;

create index ind_apply_funnel_dash
	on product.apply_funnel_dash (date_diff, country);

create table product.apply_funnel_dash_test
(
	id serial not null
		constraint pk_apply_funnel_dash_test_id
			primary key,
	country smallint not null,
	date_diff integer not null,
	apply_flow_type integer,
	test_id integer,
	"group" integer,
	iteration integer,
	is_auth integer,
	device integer,
	returned integer,
	id_traf_source integer,
	have_cv integer,
	rank_apply_click integer,
	rank_apply_submit integer,
	funnel_stage_name integer,
	stage_number integer,
	stage_number_name integer,
	stage_value integer,
	views integer,
	clicks_apply integer,
	applies integer,
	userrec varchar(15),
	daterec timestamp,
	usermod varchar(15),
	datemod timestamp
);

alter table product.apply_funnel_dash_test owner to postgres;

create index ind_apply_funnel_dash_test
	on product.apply_funnel_dash_test (date_diff, country);

create table product.profile_parse
(
	country smallint not null,
	id integer not null,
	aboutme varchar,
	telephone varchar,
	desiredjobs varchar,
	driverlicenses varchar,
	workexperience varchar,
	education varchar,
	salary varchar,
	currency varchar,
	additionalquestionsanswers varchar,
	photo_id bigint,
	is_add_photo smallint,
	constraint pk_profile_parse_id
		primary key (country, id)
);

alter table product.profile_parse owner to postgres;

create table product.ab_test_et_126
(
	seeker_cycle_first_datediff integer,
	profile_id integer,
	account_id integer,
	account_test_start_datediff integer,
	id_group integer,
	action_to_contact_7_days_cnt numeric
);

alter table product.ab_test_et_126 owner to dap;


create schema profile;

set search_path = "profile";

create table profile.profile_adoption
(
	id serial not null
	    constraint pk_adoption_id
			primary key,
	session_datediff integer not null,
	session_cnt integer,
	session_with_profile_cnt integer,
	apply_cnt integer,
	apply_with_profile_cnt integer
);

alter table profile.profile_adoption owner to postgres;

create index ind_adoption_dd
	on profile.profile_adoption (session_datediff);

create table profile.profile_alive_daily
(
	id serial not null
	    constraint pk_alive_daily_id
			primary key,
	report_datediff integer not null,
	profile_cnt integer,
	profile_with_session_last_7_days_cnt integer
);

alter table profile.profile_alive_daily owner to postgres;

create index ind_alive_daily_dd
	on profile.profile_alive_daily (report_datediff);

create table profile.profile_profession
(
	id serial not null
		constraint pk_profile_desired_job_id
			primary key,
	country_id integer not null,
	profile_id integer not null,
	profession_id integer,
	userrec varchar(10),
	daterec timestamp,
	usermod varchar(10),
	datemod timestamp
);

alter table profile.profile_profession owner to postgres;

create index ind_profile_profession_all
	on profile.profile_profession (country_id, profile_id, profession_id);

create table profile.profile_region
(
	id serial not null
		constraint pk_specified_profile_region_id
			primary key,
	country_id smallint not null,
	profile_id integer,
	region_id integer
);

alter table profile.profile_region owner to postgres;

create index ind_specified_profile_region_prof
	on profile.profile_region (profile_id);

create index ind_specified_profile_region_reg
	on profile.profile_region (region_id);

create table profile.alive_daily_new_version
(
	id serial not null
		constraint pk_alive_daily_new_version_id
			primary key,
	report_datediff integer,
	profile_cnt integer,
	alive_profile_cnt integer,
	only_created_profile_cnt integer,
	only_returned_profile_cnt integer,
	only_activated_profile_cnt integer,
	only_activated_and_returned_profile_cnt integer,
	apply_returned_profile_cnt integer
);

alter table profile.alive_daily_new_version owner to postgres;

create index ind_alive_daily_new_version_dd
	on profile.alive_daily_new_version (report_datediff);

create table profile.profile_registration_source
(
	id serial not null
		constraint pk_registration_source_id
			primary key,
	country_id smallint not null,
	profile_id integer,
	profile_registration_datediff integer not null,
	registration_source_id integer,
	profile_registration_datetime timestamp
);

alter table profile.profile_registration_source owner to rlu;

create index ind_profile_registration_source_f
	on profile.profile_registration_source (country_id, profile_id, profile_registration_datediff);

create table profile.profile_liveliness_level_daily
(
	id serial not null,
	report_datediff integer not null,
	registration_source_id integer not null,
	profile_blue_collar_type_id integer not null,
	profile_cnt integer,
	alive_profile_cnt integer,
	only_created_profile_cnt integer,
	only_returned_profile_cnt integer,
	only_activated_profile_cnt integer,
	only_activated_and_returned_profile_cnt integer,
	apply_returned_profile_cnt integer,
	country_id smallint not null,
	submission_datediff integer not null,
	creation_datediff integer not null,
	constraint pk_liveliness_level_daily_id
		primary key (country_id, report_datediff, registration_source_id, profile_blue_collar_type_id, submission_datediff, creation_datediff)
);

alter table profile.profile_liveliness_level_daily owner to postgres;

create table profile.profile_liveliness_level_daily_detailed
(
	id serial not null
		constraint pk_liveliness_level_daily_detaileds_id
			primary key,
	country_id smallint not null,
	report_datediff integer not null,
	profile_id integer not null,
	registration_source_id integer not null,
	profile_blue_collar_type_id integer,
	is_created_last_7_days integer,
	is_session_after_creation_last_7_days integer,
	is_activated_last_7_days integer,
	is_session_after_activation_last_7_days integer,
	is_returned_apply_last_7_days integer
);

alter table profile.profile_liveliness_level_daily_detailed owner to postgres;

create table profile.profile_general_info
(
	id serial not null
		constraint pk_profile_genera_info_id
			primary key,
	country_id smallint not null,
	profile_id integer,
	is_submitted boolean,
	gender smallint,
	region varchar(500),
	regions varchar(1000),
	yearofbirth integer,
	last_name varchar(255),
	first_name varchar(255),
	isreadyforrelocate boolean,
	isvisibleforemployers boolean
);

alter table profile.profile_general_info owner to postgres;

create index ind_profile_genera_info_region_prof
	on profile.profile_general_info (profile_id);

create table profile.profile_session_and_view_lt7
(
	country_id smallint not null,
	profile_id integer not null,
	creation_datediff integer not null,
	lt7_session_cnt integer,
	lt7_dte_jdp_view_cnt integer,
	constraint pk_profile_session_and_view_lt7_id
		primary key (country_id, profile_id, creation_datediff)
);

alter table profile.profile_session_and_view_lt7 owner to postgres;

create table profile.profile_apply
(
	country_id smallint,
	employer_apply_id bigint,
	job_seeker_apply_id bigint,
	apply_datediff integer,
	apply_datetime timestamp,
	profile_id integer,
	employer_account_id integer,
	job_uid bigint
);

alter table profile.profile_apply owner to postgres;

create index ind_profile_apply_dd
	on profile.profile_apply (apply_datediff);

create index ind_profile_apply_ea
	on profile.profile_apply (employer_apply_id);

create index ind_profile_apply_p
	on profile.profile_apply (profile_id);

create table profile.profile_interaction
(
	id serial not null,
	country_id smallint not null,
	apply_datediff integer not null,
	apply_cnt integer,
	interaction_cnt integer,
	constraint pk_profile_interaction_id
		primary key (country_id, apply_datediff)
);

alter table profile.profile_interaction owner to postgres;

create table profile.profile_jdp_respond
(
	country_id smallint not null,
	profile_id integer not null,
	jdp_id bigint not null,
	jdp_viewed_datediff integer not null,
	jdp_flag integer,
	is_apply_click integer,
	is_away_click integer,
	is_call_click integer,
	constraint profile_jdp_respond_pk
		primary key (country_id, profile_id, jdp_id, jdp_viewed_datediff)
);

alter table profile.profile_jdp_respond owner to postgres;

create table profile.job_seeker_action_to_contact_conversion
(
	intention_to_contact_type_id integer not null
		constraint job_seeker_action_to_contact_conversion_pk
			primary key,
	conversion numeric(10,4) not null
);

alter table profile.job_seeker_action_to_contact_conversion owner to postgres;

create table profile.temp_profile_submitted
(
	country_id smallint,
	id integer,
	email varchar,
	phone varchar,
	creation_datediff integer,
	data varchar,
	date_created date,
	date_updated date,
	submission_datediff integer,
	first_input_datediff integer
);

alter table profile.temp_profile_submitted owner to rlu;

create table profile.profile_submitted
(
	country_id smallint not null,
	id integer not null,
	email varchar,
	phone varchar,
	creation_datediff integer,
	data varchar,
	date_created date,
	date_updated date,
	submission_datediff integer,
	first_input_datediff integer,
	constraint pk_profile_submitted_id
		primary key (country_id, id)
);

alter table profile.profile_submitted owner to postgres;

create table profile.profile_session
(
	country_id smallint not null,
	profile_id integer not null,
	session_id bigint not null,
	session_datediff integer not null,
	sign_in_datetime timestamp not null,
	sign_out_datetime timestamp,
	constraint pk_table_idc_id
		primary key (country_id, profile_id, session_id, session_datediff, sign_in_datetime)
);

alter table profile.profile_session owner to postgres;

create table profile.profile_search
(
	country_id smallint not null,
	profile_id integer,
	search_id bigint not null,
	search_datediff integer not null,
	session_id bigint,
	account_id integer,
	search_datetime timestamp,
	constraint pk_profile_search_id
		primary key (country_id, search_id, search_datediff)
);

alter table profile.profile_search owner to postgres;

create index ind_profile_search_p
	on profile.profile_search (profile_id);

create index ind_profile_search_ses
	on profile.profile_search (session_id);

create table profile.profile_jdp
(
	country_id smallint not null,
	profile_id integer not null,
	session_id bigint,
	jdp_id bigint not null,
	jdp_viewed_datediff integer not null,
	jdp_viewed_datetime timestamp,
	account_id integer,
	jdp_flag integer,
	constraint pk_profile_jdp_id
		primary key (country_id, profile_id, jdp_id, jdp_viewed_datediff)
);

alter table profile.profile_jdp owner to postgres;

create index ind_profile_jdp_sea
	on profile.profile_jdp (account_id);

create index ind_profile_jdp_ses
	on profile.profile_jdp (session_id);

create table profile.job_seeker_cycle_start
(
	country_id smallint not null,
	profile_id integer not null,
	seeker_cycle_first_datediff integer not null,
	seeker_cycle_start_type_id integer,
	constraint job_seeker_cycle_start_pk
		primary key (country_id, profile_id, seeker_cycle_first_datediff)
);

alter table profile.job_seeker_cycle_start owner to dap;

create table profile.action_to_contact
(
	country_id integer not null,
	profile_id integer not null,
	platform_user_type_id integer not null,
	action_datediff integer not null,
	action_to_contact_prob numeric,
	order_action_id smallint not null,
	constraint action_to_contact_pk
		primary key (country_id, profile_id, platform_user_type_id, action_datediff, order_action_id)
);

alter table profile.action_to_contact owner to dap;

create table profile.job_seeker_action_to_contact_apply
(
	country_id smallint not null,
	employer_apply_id bigint,
	job_seeker_apply_id bigint,
	apply_datediff integer,
	apply_datetime timestamp,
	profile_id integer,
	employer_account_id integer,
	job_uid bigint,
	registration_source_id integer,
	first_apply_datetime timestamp,
	feature_id integer,
	order_action_id smallint,
	action_type_id smallint
);

alter table profile.job_seeker_action_to_contact_apply owner to postgres;

create table profile.job_seeker_action_to_contact_call_click
(
	country_id smallint not null,
	profile_id integer,
	jdp_viewed_datediff integer,
	job_uid bigint,
	jdp_viewed_datetime timestamp,
	feature_id integer,
	order_action_id smallint,
	action_type_id smallint
);

alter table profile.job_seeker_action_to_contact_call_click owner to postgres;

create table profile.job_seeker_action_to_contact
(
	country_id smallint not null,
	profile_id integer not null,
	action_datediff integer not null,
	action_to_contact_prob numeric,
	action_datetime timestamp not null,
	action_type_id smallint not null,
	feature_id smallint not null,
	order_action_id smallint not null,
	constraint job_seeker_action_to_contact_pk
		primary key (country_id, profile_id, action_datediff, action_datetime, action_type_id, feature_id, order_action_id)
);

alter table profile.job_seeker_action_to_contact owner to postgres;

create table profile.action_to_contact_structure
(
	country_id smallint not null,
	profile_id integer,
	platform_user_type_id integer,
	feature_id integer,
	action_type_id integer,
	action_datediff integer,
	action_datetime timestamp,
	action_to_contact_prob numeric,
	order_action_id smallint
);

alter table profile.action_to_contact_structure owner to postgres;

create index pk_action_to_contact_structure
	on profile.action_to_contact_structure (country_id, profile_id, platform_user_type_id, feature_id, action_type_id, action_datediff);

create table profile.profile_blue_score
(
	country_id smallint not null,
	profile_id integer not null,
	blue_score numeric,
	profile_blue_collar_type_id smallint,
	constraint profile_blue_score_pk
		primary key (country_id, profile_id)
);

alter table profile.profile_blue_score owner to postgres;

create table profile.action_to_contact_sequence
(
	country_id smallint not null,
	profile_id integer,
	seeker_cycle_first_datediff integer,
	action_sequence text,
	seeker_cycle_start_type_id integer,
	action_cnt integer
);

alter table profile.action_to_contact_sequence owner to postgres;

create table profile.profile_action_to_contact_day_distribution
(
	country_id smallint not null,
	profile_id integer,
	seeker_cycle_first_datediff integer,
	action_from_seeker_cycle_start_day_interval integer,
	action_to_contact_28_days_cnt numeric,
	seeker_cycle_start_type_id integer
);

alter table profile.profile_action_to_contact_day_distribution owner to postgres;

create table profile.profile_first_apply
(
	country_id smallint not null,
	profile_id integer not null,
	first_apply_datetime timestamp,
	constraint profile_first_apply_pk
		primary key (profile_id, country_id)
);

alter table profile.profile_first_apply owner to dap;

create table profile.action_to_contact_message
(
	country_id integer,
	profile_id integer,
	employer_account_id integer,
	platform_user_type_id integer,
	action_type_id integer,
	action_datediff integer,
	action_datetime timestamp,
	feature_id integer,
	order_action_id bigint
);

alter table profile.action_to_contact_message owner to dap;

create table profile.job_seeker_apply_without_profile
(
	country_id smallint,
	employer_apply_id bigint,
	job_seeker_apply_id bigint,
	apply_datediff integer,
	apply_datetime timestamp,
	profile_id integer,
	submission_datediff integer,
	source_flag integer default 0 not null
);

alter table profile.job_seeker_apply_without_profile owner to postgres;

create table profile.m_action_to_contact_per_profile
(
	country_id smallint,
	profile_id integer,
	seeker_cycle_first_datediff integer,
	action_to_contact_28_days_cnt numeric,
	action_to_contact_call_28_days_cnt numeric,
	action_to_contact_apply_28_days_cnt numeric,
	action_to_contact_employer_28_days_cnt numeric,
	action_to_contact_14_days_cnt numeric,
	action_to_contact_call_14_days_cnt numeric,
	action_to_contact_apply_14_days_cnt numeric,
	action_to_contact_employer_14_days_cnt numeric,
	action_to_contact_7_days_cnt numeric,
	action_to_contact_call_7_days_cnt numeric,
	action_to_contact_apply_7_days_cnt numeric,
	action_to_contact_employer_7_days_cnt numeric,
	action_to_contact_1_days_cnt numeric,
	action_to_contact_call_1_days_cnt numeric,
	action_to_contact_apply_1_days_cnt numeric,
	action_to_contact_employer_1_days_cnt numeric,
	blue_collar_type_name varchar(35),
	seeker_cycle_start_type_name varchar(50),
	initiator_name varchar(20)
);

alter table profile.m_action_to_contact_per_profile owner to postgres;


create schema profile_base;

set search_path = "profile_base";


create table profile_base.profile_search_parse
(
	id serial not null
		constraint pk_profile_search_parse_id
			primary key,
	account_id integer,
	search_id bigint,
	employment_types varchar,
	age_to integer,
	age_from integer,
	gender smallint,
	salary_from double precision,
	salary_to double precision,
	currency_id varchar(5),
	rate integer,
	sources smallint
);

alter table profile_base.profile_search_parse owner to rlu;

create table profile_base.profile_search_employment_type
(
	profile_search_parse_id integer,
	employment_type integer
);

alter table profile_base.profile_search_employment_type owner to rlu;

create table profile_base.profile_search_parse_pivot
(
	search_id bigint not null,
	sources smallint not null,
	category_name varchar(100),
	filter_value varchar
);

alter table profile_base.profile_search_parse_pivot owner to rlu;

create table profile_base.profile_base_itc_detailed
(
	country_code varchar(5) not null,
	employer_id integer,
	account_id integer,
	profile_id integer,
	is_view smallint,
	view_date date,
	interact_date date,
	is_intention_to_contact smallint,
	intention_to_contact_type smallint
);

alter table profile_base.profile_base_itc_detailed owner to postgres;

create table profile_base.profile_base_itc_agg
(
	action_min_date date,
	is_view integer,
	is_intention_to_contact integer,
	profile_cnt integer
);

alter table profile_base.profile_base_itc_agg owner to postgres;

create table profile_base.profile_base_ea_session
(
	employer_id integer,
	account_id integer,
	session_date date,
	database_source_id smallint,
	account_type integer,
	is_active_in_profile_base smallint
);

alter table profile_base.profile_base_ea_session owner to postgres;

create table profile_base.profile_base_search_conversion
(
	account_id integer,
	id_session bigint,
	search_trigger smallint,
	query_hash bigint,
	search_date date,
	search_id bigint,
	view_in_search smallint,
	profile_view_cnt integer
);

alter table profile_base.profile_base_search_conversion owner to postgres;

create table profile_base.employer_profile_search
(
	country_code varchar(2),
	account_type integer,
	employer_id integer,
	ea_created_date date,
	account_id integer,
	session_id bigint,
	search_by_session_cnt integer,
	device_type smallint,
	search_id bigint,
	search_date timestamp,
	type_query smallint,
	keyword text,
	region_id integer,
	additional_filters_cnt smallint,
	additional_filters_json text,
	results_total integer,
	database_source_id smallint,
	display_name varchar(510),
	region_name varchar(510),
	view_by_search_cnt integer
);

alter table profile_base.employer_profile_search owner to postgres;


create schema retention_ea;

set search_path = "retention_ea";

create table retention_ea.ea_session_history
(
	id serial not null
		constraint pk_ea_session_history_new_id
			primary key,
	country_code varchar(2),
	employer_id integer,
	moderation_status_ea smallint,
	account_id integer,
	is_joobler integer,
	session_date date,
	cnt_sess_by_day bigint,
	sources smallint,
	time_key date
);

alter table retention_ea.ea_session_history owner to postgres;

create table retention_ea.stg_employer_related_account
(
	employer_id integer,
	employer_name varchar(2048),
	country_code varchar(2),
	industry varchar(100),
	staff_size varchar(100),
	type_business varchar(100),
	ea_date_created date,
	moderation_status integer,
	account_type integer,
	name_source varchar(100),
	register_source integer,
	counterparty_id integer,
	cnt_acc_by_employer integer,
	rank_created_account integer,
	account_id integer,
	roles integer,
	is_account_creator integer,
	acc_date_created date,
	is_verified boolean,
	is_delete smallint,
	sources smallint,
	time_key date not null
);

alter table retention_ea.stg_employer_related_account owner to postgres;

create table retention_ea.retention_registration
(
	country_code varchar(2),
	country_name varchar(50),
	employer_id integer,
	industry varchar(255),
	staff_size varchar(50),
	type_business varchar(50),
	moder_ok smallint,
	account_type integer,
	register_source integer,
	account_id integer,
	is_verified boolean,
	first_day_of_the_week_acc date,
	first_day_of_the_week_ea date,
	agent varchar(10),
	weeks_from_reg_acc integer,
	weeks_from_reg_ea integer,
	date_last_closed_week date,
	time_key date
);

alter table retention_ea.retention_registration owner to postgres;


create schema sales;

set search_path = "sales";


create table sales.sales_current_base
(
	country_code text,
	employer_with_balance numeric,
	employer_with_premium_jobs numeric,
	total_premium_jobs numeric,
	average_premium_jobs double precision,
	pay_2_or_more numeric,
	pay_once numeric,
	to_second_payment double precision,
	lost_clients numeric,
	client_retention double precision
);

alter table sales.sales_current_base owner to postgres;

create table sales.sales_funnel
(
	country_code text,
	yymm numeric,
	registration numeric,
	moderation numeric,
	reg_to_mod double precision,
	payers numeric,
	mod_to_pay double precision,
	payers_7 numeric,
	payers_7_28 double precision,
	payers_28 double precision,
	money_7 double precision,
	money_7_28 double precision,
	money_28 double precision,
	new_payers numeric,
	lost_payers double precision,
	retention double precision,
	current_clients numeric,
	total_revenue double precision,
	value_of_registration double precision
);

alter table sales.sales_funnel owner to postgres;

create table sales.sales_payment_and_debt
(
	country_code text,
	yymm numeric,
	payers_total numeric,
	count_of_pays numeric,
	payers_new numeric,
	lost_clients numeric,
	current_clients numeric,
	core_clients double precision,
	total_sales double precision,
	new_sales double precision,
	service double precision,
	debt double precision
);

alter table sales.sales_payment_and_debt owner to postgres;

create table sales.sales_premium_clicks
(
	country_code text,
	yymm numeric,
	is_premium numeric,
	clicks double precision,
	responds double precision,
	job_count numeric,
	employer_count numeric,
	count_per_employer double precision,
	clicks_per_day double precision,
	responds_per_day double precision,
	average_days double precision,
	clicks_per_job double precision
);

alter table sales.sales_premium_clicks owner to postgres;


create schema traffic;

set search_path = "traffic";


create table traffic.cost_by_source
(
	cost_source_datetime timestamp,
	source_name varchar(10),
	cost_usd double precision,
	src varchar(100),
	jooble_id integer
);

alter table traffic.cost_by_source owner to postgres;

create table traffic.traffic_cost
(
	traffic_datediff integer,
	country_id smallint,
	traffic_cost_usd double precision,
	traffic_source_id integer
);

alter table traffic.traffic_cost owner to postgres;
