create view dv_revenue_by_placement_and_src
            (date, placement, name, country, revenue_usd, continent_name, region_name, alpha_2, channel,
             traffic_is_paid, total_session_cnt, bot_session_cnt, internal_sessions, local_session_nobot_cnt,
             mobile_session_nobot_cnt, returned_session_nobot_cnt, last_date, table_name, jooble_goal,
             organic_revenue_goal, returning_revenue_goal, other_revenue_goal, cost_type, cost_usd, ga_sessions,
             revenue_usd_discount, forecast, net_forecast)
as
WITH xs AS (SELECT contractors.date,
                   sum(contractors.plus)                  AS revenue_usd,
                   count(DISTINCT contractors.contractor) AS count
            FROM (SELECT make_date(rows.year, rows.month, 1)                             AS date,
                         rows.contractor,
                         (rows.min_rate - rows.spended) * (200::numeric / rows.min_rate) AS plus
                  FROM (SELECT invoice_row.year,
                               invoice_row.month,
                               invoice_row.currency,
                               btrim(invoice_row.contractor::text) AS contractor,
                               sum(invoice_row.spent)              AS spended,
                               max(invoice_row.min_rate)           AS min_rate
                        FROM aggregation.invoice_row
                        WHERE invoice_row.min_rate IS NOT NULL
                          AND invoice_row.year >= 2023
                        GROUP BY invoice_row.year, invoice_row.month, invoice_row.currency,
                                 (btrim(invoice_row.contractor::text))) rows
                  WHERE rows.min_rate > rows.spended) contractors
            GROUP BY contractors.date),
     google_analytics AS (SELECT ga_general.action_date   AS date,
                                 countries.alpha_2,
                                 ga_general.channel_grouping,
                                 sum(ga_general.sessions) AS sessions
                          FROM imp_api.ga_general
                          JOIN dimension.countries ON ga_general.country_id = countries.id
                          WHERE ga_general.action_date >= '2023-01-01'::date
                          GROUP BY ga_general.action_date, countries.alpha_2, ga_general.channel_grouping),
     adsense AS (SELECT adsense_afs.action_date                                          AS create_date,
                        sum(adsense_afs.estimated_earnings_usd)                          AS estimated_income_usd,
                        COALESCE(countries.name_country_eng, 'Other'::character varying) AS country,
                        max(adsense_afs.action_date) OVER ()                             AS last_date,
                        'adsense_afs'::text                                              AS table_name
                 FROM imp_api.adsense_afs
                 LEFT JOIN ono.dic_countries_adsense
                           ON adsense_afs.country_name::text = dic_countries_adsense.country_as_in_reports
                 LEFT JOIN dimension.countries
                           ON lower(dic_countries_adsense.two_digits) = lower(countries.alpha_2::text)
                 WHERE adsense_afs.action_date >= '2023-01-01'::date
                 GROUP BY adsense_afs.action_date, (COALESCE(countries.name_country_eng, 'Other'::character varying))
                 UNION ALL
                 SELECT adsense_afc.action_date                                          AS create_date,
                        sum(adsense_afc.estimated_earnings_usd)                          AS estimated_income_usd,
                        COALESCE(countries.name_country_eng, 'Other'::character varying) AS "coalesce",
                        max(adsense_afc.action_date) OVER ()                             AS last_date,
                        'adsense_afc'::text                                              AS table_name
                 FROM imp_api.adsense_afc
                 LEFT JOIN ono.dic_countries_adsense ON "left"(
                                                                CASE
                                                                    WHEN adsense_afc.domain_code::text = 'jooble.org'::text
                                                                        THEN 'us.jooble.org'::character varying
                                                                    WHEN adsense_afc.domain_code::text = 'ja.jooble.org'::text
                                                                        THEN 'jp.jooble.org'::character varying
                                                                    ELSE adsense_afc.domain_code
                                                                    END::text, 2) =
                                                        dic_countries_adsense.country_as_in_reports
                 LEFT JOIN dimension.countries
                           ON lower(dic_countries_adsense.two_digits) = lower(countries.alpha_2::text)
                 WHERE adsense_afc.action_date >= '2023-01-01'::date
                 GROUP BY adsense_afc.action_date, (COALESCE(countries.name_country_eng, 'Other'::character varying))),
     revenue AS (SELECT withoutregion.date,
                        withoutregion.placement,
                        withoutregion.name,
                        withoutregion.country,
                        withoutregion.revenue_usd,
                        COALESCE(v_country_info.continent_name, 'Other'::character varying) AS continent_name,
                        COALESCE(v_country_info.region_name, 'Other'::character varying)    AS region_name,
                        v_country_info.alpha_2,
                        COALESCE(withoutregion.channel, 'Undefined'::character varying)     AS channel,
                        COALESCE(withoutregion.is_paid, false)                              AS traffic_is_paid,
                        0                                                                   AS total_session_cnt,
                        0                                                                   AS bot_session_cnt,
                        0                                                                   AS internal_sessions,
                        0                                                                   AS local_session_nobot_cnt,
                        0                                                                   AS mobile_session_nobot_cnt,
                        0                                                                   AS returned_session_nobot_cnt,
                        withoutregion.last_date,
                        withoutregion.table_name,
                        0                                                                   AS jooble_goal,
                        0                                                                   AS organic_revenue_goal,
                        0                                                                   AS returning_revenue_goal,
                        0                                                                   AS other_revenue_goal,
                        withoutregion.revenue_usd_discount
                 FROM (SELECT fn_get_timestamp_from_date_diff(adv_revenue_by_placement_and_src.date_diff) AS date,
                              adv_revenue_by_placement_and_src.placement::text                            AS placement,
                              u_traffic_source.name,
                              countries.name_country_eng                                                  AS country,
                              adv_revenue_by_placement_and_src.revenue_usd,
                              max(fn_get_timestamp_from_date_diff(adv_revenue_by_placement_and_src.date_diff))
                              OVER ()                                                                     AS last_date,
                              'adv_revenue_by_placement_and_src'::text                                    AS table_name,
                              u_traffic_source.channel,
                              u_traffic_source.is_paid,
                              adv_revenue_by_placement_and_src.revenue_usd_discount
                       FROM aggregation.adv_revenue_by_placement_and_src_analytics adv_revenue_by_placement_and_src
                       LEFT JOIN dimension.u_traffic_source
                                 ON adv_revenue_by_placement_and_src.country_id = u_traffic_source.country AND
                                    adv_revenue_by_placement_and_src.id_current_traf_source = u_traffic_source.id
                       LEFT JOIN dimension.countries ON adv_revenue_by_placement_and_src.country_id = countries.id
                       WHERE adv_revenue_by_placement_and_src.date_diff >= 44925
                       UNION ALL
                       SELECT xs.date,
                              'xs'::text                        AS placement,
                              NULL::character varying           AS name,
                              NULL::character varying           AS name_country_eng,
                              xs.revenue_usd,
                              NULL::timestamp without time zone AS last_date,
                              NULL::text                        AS table_name,
                              NULL::text                        AS channel,
                              NULL::boolean                     AS is_paid,
                              0                                 AS revenue_usd_discount
                       FROM xs
                       UNION ALL
                       SELECT adsense.create_date               AS date,
                              'adsense'::text                   AS placement,
                              NULL::character varying           AS name,
                              adsense.country,
                              sum(adsense.estimated_income_usd) AS revenue_usd,
                              max(adsense.last_date)            AS last_date,
                              adsense.table_name,
                              NULL::character varying           AS channel,
                              NULL::boolean                     AS is_paid,
                              0                                 AS revenue_usd_discount
                       FROM adsense
                       GROUP BY adsense.create_date, adsense.country, adsense.table_name) withoutregion
                 LEFT JOIN dimension.v_country_info ON withoutregion.country::text = v_country_info.country_name::text
                 UNION ALL
                 SELECT fn_get_timestamp_from_date_diff(session_daily_agg.action_datediff)                   AS date,
                        NULL::text                                                                           AS placement,
                        u_traffic_source.name,
                        v_country_info.country_name                                                          AS country,
                        0                                                                                    AS revenue_usd,
                        COALESCE(v_country_info.continent_name, 'Other'::character varying)                  AS continent_name,
                        COALESCE(v_country_info.region_name, 'Other'::character varying)                     AS region_name,
                        v_country_info.alpha_2,
                        COALESCE(u_traffic_source.channel, 'Undefined'::text::character varying)             AS channel,
                        COALESCE(u_traffic_source.is_paid, false)                                            AS traffic_is_paid,
                        session_daily_agg.total_session_cnt,
                        session_daily_agg.bot_session_cnt,
                        session_daily_agg.total_session_cnt -
                        COALESCE(session_daily_agg.bot_session_cnt, 0)                                       AS internal_sessions,
                        session_daily_agg.local_session_nobot_cnt,
                        session_daily_agg.mobile_session_nobot_cnt,
                        session_daily_agg.returned_session_nobot_cnt,
                        max(fn_get_timestamp_from_date_diff(session_daily_agg.action_datediff))
                        OVER ()                                                                              AS last_date,
                        'session_daily_agg'::text                                                            AS table_name,
                        0,
                        0,
                        0,
                        0,
                        0
                 FROM aggregation.session_daily_agg
                 LEFT JOIN dimension.u_traffic_source ON session_daily_agg.country_id = u_traffic_source.country AND
                                                         session_daily_agg.id_traf_source = u_traffic_source.id
                 LEFT JOIN dimension.v_country_info ON session_daily_agg.country_id = v_country_info.country_id
                 WHERE session_daily_agg.action_datediff >= 44925
                 UNION ALL
                 SELECT dic_revenue_goals."Date",
                        NULL::text                        AS text,
                        NULL::character varying           AS "varchar",
                        NULL::character varying           AS "varchar",
                        NULL::numeric                     AS "numeric",
                        NULL::character varying           AS "varchar",
                        NULL::character varying           AS "varchar",
                        NULL::character varying           AS "varchar",
                        NULL::character varying           AS "varchar",
                        NULL::boolean                     AS bool,
                        NULL::integer                     AS int4,
                        NULL::integer                     AS int4,
                        NULL::integer                     AS int4,
                        NULL::integer                     AS int4,
                        NULL::integer                     AS int4,
                        NULL::integer                     AS int4,
                        NULL::timestamp without time zone AS "timestamp",
                        NULL::text                        AS text,
                        dic_revenue_goals."Goal",
                        dic_revenue_goals."Organic_Revenue",
                        dic_revenue_goals."Returning_Revenue",
                        dic_revenue_goals."Other_Revenue",
                        0
                 FROM aggregation.dic_revenue_goals
                 WHERE dic_revenue_goals."Date" >= '2023-01-01'::date),
     cost AS (SELECT adwords.day                                                                 AS date,
                     'Adwords'::text                                                             AS cost_type,
                     adwords.cost::numeric / COALESCE(currency_source.to_usd, avg_to_usd.to_usd) AS cost,
                     max(adwords.day) OVER ()                                                    AS last_date,
                     'adwords'::text                                                             AS table_name
              FROM imp_statistic.adwords
              LEFT JOIN imp_statistic.currency_source
                        ON adwords.day = currency_source.date AND currency_source.currency = 'COP'::bpchar
              LEFT JOIN (SELECT date_part('year'::text, currency_source_1.date)  AS year,
                                date_part('month'::text, currency_source_1.date) AS month,
                                avg(currency_source_1.to_usd)                    AS to_usd
                         FROM imp_statistic.currency_source currency_source_1
                         WHERE currency_source_1.currency = 'COP'::bpchar
                         GROUP BY (date_part('year'::text, currency_source_1.date)),
                                  (date_part('month'::text, currency_source_1.date))) avg_to_usd
                        ON date_part('year'::text, adwords.day) = avg_to_usd.year AND
                           date_part('month'::text, adwords.day) = avg_to_usd.month
              WHERE adwords.day >= '2023-01-01'::date AND adwords.day <= '2023-01-31'::date
                 OR adwords.day >= '2023-02-01'::date AND adwords.campaign::text !~~ 'HU_%'::text AND
                    adwords.campaign::text !~~ 'RO_%'::text AND adwords.campaign::text !~~ 'UA_%'::text
              UNION ALL
              SELECT facebook_2018.date_start                                                           AS date,
                     'Facebook'::text                                                                   AS cost_type,
                     facebook_2018.spend::numeric / COALESCE(currency_source.to_usd, avg_to_usd.to_usd) AS cost,
                     max(facebook_2018.date_start) OVER ()                                              AS last_date,
                     'facebook_2018'::text                                                              AS table_name
              FROM imp_statistic.facebook_2018
              LEFT JOIN imp_statistic.currency_source
                        ON facebook_2018.date_start = currency_source.date AND currency_source.currency = 'COP'::bpchar
              LEFT JOIN (SELECT date_part('year'::text, currency_source_1.date)  AS year,
                                date_part('month'::text, currency_source_1.date) AS month,
                                avg(currency_source_1.to_usd)                    AS to_usd
                         FROM imp_statistic.currency_source currency_source_1
                         WHERE currency_source_1.currency = 'COP'::bpchar
                         GROUP BY (date_part('year'::text, currency_source_1.date)),
                                  (date_part('month'::text, currency_source_1.date))) avg_to_usd
                        ON date_part('year'::text, facebook_2018.date_start) = avg_to_usd.year AND
                           date_part('month'::text, facebook_2018.date_start) = avg_to_usd.month
              WHERE facebook_2018.date_start >= '2023-01-01'::date AND facebook_2018.date_start <= '2023-01-31'::date
                 OR facebook_2018.date_start >= '2023-02-01'::date AND
                    facebook_2018.campaign_name::text !~~ 'HU_%'::text AND
                    facebook_2018.campaign_name::text !~~ 'RO_%'::text AND facebook_2018.campaign_name::text !~~ 'UA_%'::text
              UNION ALL
              SELECT fn_get_timestamp_from_date_diff(statistics_daily_agg.date_diff)              AS date,
                     'Affiliate'::text                                                            AS text,
                     CASE
                         WHEN statistics_daily_agg.is_bot = 0 AND statistics_daily_agg.is_duplicated = 0 AND
                              statistics_daily_agg.is_billable = 1 THEN statistics_daily_agg.feed_cpc_usd *
                                                                        statistics_daily_agg.external_click_cnt::numeric
                         ELSE NULL::numeric
                         END                                                                      AS cost,
                     max(fn_get_timestamp_from_date_diff(statistics_daily_agg.date_diff)) OVER () AS last_date,
                     'affiliate_stat_daily_agg'::text                                             AS table_name
              FROM affiliate.statistics_daily_agg
              WHERE statistics_daily_agg.date_diff >= 44925)
SELECT revenue.date,
       revenue.placement,
       revenue.name,
       revenue.country,
       revenue.revenue_usd,
       revenue.continent_name,
       revenue.region_name,
       revenue.alpha_2,
       revenue.channel,
       revenue.traffic_is_paid,
       revenue.total_session_cnt,
       revenue.bot_session_cnt,
       revenue.internal_sessions,
       revenue.local_session_nobot_cnt,
       revenue.mobile_session_nobot_cnt,
       revenue.returned_session_nobot_cnt,
       revenue.last_date,
       revenue.table_name,
       revenue.jooble_goal,
       revenue.organic_revenue_goal,
       revenue.returning_revenue_goal,
       revenue.other_revenue_goal,
       NULL::text AS cost_type,
       0          AS cost_usd,
       0          AS ga_sessions,
       revenue.revenue_usd_discount,
       0          AS forecast,
       0          AS net_forecast
FROM revenue
UNION ALL
SELECT cost.date,
       NULL::text              AS placement,
       NULL::character varying AS name,
       NULL::character varying AS country,
       NULL::numeric           AS revenue_usd,
       NULL::character varying AS continent_name,
       NULL::character varying AS region_name,
       NULL::character varying AS alpha_2,
       NULL::character varying AS channel,
       NULL::boolean           AS traffic_is_paid,
       NULL::integer           AS total_session_cnt,
       NULL::integer           AS bot_session_cnt,
       NULL::integer           AS internal_sessions,
       NULL::integer           AS local_session_nobot_cnt,
       NULL::integer           AS mobile_session_nobot_cnt,
       NULL::integer           AS returned_session_nobot_cnt,
       cost.last_date,
       cost.table_name,
       0                       AS jooble_goal,
       0                       AS organic_revenue_goal,
       0                       AS returning_revenue_goal,
       0                       AS other_revenue_goal,
       cost.cost_type,
       sum(cost.cost)          AS cost_usd,
       0                       AS ga_sessions,
       0                       AS revenue_usd_discount,
       0                       AS forecast,
       0                       AS net_forecast
FROM cost
GROUP BY cost.date, cost.cost_type, cost.last_date, cost.table_name
UNION ALL
SELECT google_analytics.date,
       NULL::text                         AS placement,
       NULL::character varying            AS name,
       countries.name_country_eng         AS country,
       NULL::numeric                      AS revenue_usd,
       NULL::character varying            AS continent_name,
       NULL::character varying            AS region_name,
       NULL::character varying            AS alpha_2,
       google_analytics.channel_grouping  AS channel,
       NULL::boolean                      AS traffic_is_paid,
       NULL::integer                      AS total_session_cnt,
       NULL::integer                      AS bot_session_cnt,
       NULL::integer                      AS internal_sessions,
       NULL::integer                      AS local_session_nobot_cnt,
       NULL::integer                      AS mobile_session_nobot_cnt,
       NULL::integer                      AS returned_session_nobot_cnt,
       max(google_analytics.date) OVER () AS last_date,
       'ga_sessions'::text                AS table_name,
       0                                  AS jooble_goal,
       0                                  AS organic_revenue_goal,
       0                                  AS returning_revenue_goal,
       0                                  AS other_revenue_goal,
       NULL::text                         AS cost_type,
       0                                  AS cost_usd,
       google_analytics.sessions          AS ga_sessions,
       0                                  AS revenue_usd_discount,
       0                                  AS forecast,
       0                                  AS net_forecast
FROM google_analytics
JOIN dimension.countries ON google_analytics.alpha_2::text = countries.alpha_2::text
UNION ALL
SELECT revenue.date,
       revenue.placement,
       revenue.name,
       revenue.country,
       revenue.revenue_usd,
       revenue.continent_name,
       revenue.region_name,
       revenue.alpha_2,
       revenue.channel,
       revenue.traffic_is_paid,
       revenue.total_session_cnt,
       revenue.bot_session_cnt,
       revenue.internal_sessions,
       revenue.local_session_nobot_cnt,
       revenue.mobile_session_nobot_cnt,
       revenue.returned_session_nobot_cnt,
       revenue.last_date,
       revenue.table_name,
       revenue.jooble_goal,
       revenue.organic_revenue_goal,
       revenue.returning_revenue_goal,
       revenue.other_revenue_goal,
       revenue.cost_type,
       revenue.cost_usd,
       revenue.ga_sessions,
       revenue.revenue_usd_discount,
       0 AS forecast,
       0 AS net_forecast
FROM ono.adv_revenue_by_placement_and_src_archive revenue
UNION ALL
SELECT revenue_month_forecast.date::date               AS date,
       NULL::text                                      AS placement,
       NULL::character varying                         AS name,
       NULL::character varying                         AS country,
       NULL::numeric                                   AS revenue_usd,
       NULL::character varying                         AS continent_name,
       NULL::character varying                         AS region_name,
       NULL::character varying                         AS alpha_2,
       NULL::character varying                         AS channel,
       NULL::boolean                                   AS traffic_is_paid,
       NULL::integer                                   AS total_session_cnt,
       NULL::integer                                   AS bot_session_cnt,
       NULL::integer                                   AS internal_sessions,
       NULL::integer                                   AS local_session_nobot_cnt,
       NULL::integer                                   AS mobile_session_nobot_cnt,
       NULL::integer                                   AS returned_session_nobot_cnt,
       max(revenue_month_forecast.date) OVER ()        AS last_date,
       'revenue forecast'::text                        AS table_name,
       0                                               AS jooble_goal,
       0                                               AS organic_revenue_goal,
       0                                               AS returning_revenue_goal,
       0                                               AS other_revenue_goal,
       NULL::text                                      AS cost_type,
       0                                               AS cost_usd,
       0                                               AS ga_sessions,
       0                                               AS revenue_usd_discount,
       revenue_month_forecast.revenue_forecast         AS forecast,
       net_revenue_month_forecast.net_revenue_forecast AS net_forecast
FROM aggregation.revenue_month_forecast
JOIN aggregation.net_revenue_month_forecast ON revenue_month_forecast.date = net_revenue_month_forecast.date;

alter table dv_revenue_by_placement_and_src
    owner to ono;
