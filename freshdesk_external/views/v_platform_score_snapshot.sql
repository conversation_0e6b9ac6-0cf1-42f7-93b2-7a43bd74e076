create view freshdesk_external.v_platform_score_snapshot(date, score, platform_type, rating_cnt) as
select
    trustpilot_score_snapshot.date,
    trustpilot_score_snapshot.score,
    3             as platform_type,
    null::integer as rating_cnt
from
    freshdesk_external.trustpilot_score_snapshot
union all
select
    mobile_statistics_snapshot.record_date as date,
    mobile_statistics_snapshot.score,
    mobile_statistics_snapshot.platform_type,
    mobile_statistics_snapshot.rating_cnt
from
    freshdesk_external.mobile_statistics_snapshot;