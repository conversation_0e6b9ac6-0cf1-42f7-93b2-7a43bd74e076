-- screen 2014 - скрін банера
-- screen 2007 - скрін банера підписки (потрапляємо на нього при кліку на button "підписатись на email" на screen 2014)

create table job_seeker.chat_bot_onboarding_banner_usage_agg as
select s.country                                      as country_id,
       s.id                                           as session_id,
       s.date_diff                                    as banner_view_datediff,
       spa.id_session_profile                         as profile_session_id,
       case when s.flags & 16 = 16 then 1 else 0 end  as is_mobile_session,
       case when s.flags & 2 = 2 then 1 else 0 end    as is_returned_user,
       sp.source                                      as profile_page_source_id,
       -- банер нбординга spa.screen = 2014
       count(case
                 when spa.screen = 2014 and spa.type = 200 /*ScreenLoaded*/ and spa.flags & 0 = 0
                     then spa.id_session_profile end) as onboarding_banner_screen_loaded_cnt,
       count(case
                 when spa.screen = 2014 and spa.type = 15 and spa.flags & 0 = 0
                     then spa.id_session_profile end) as to_viber_bot_click_cnt,
       count(case
                 when spa.screen = 2014 and spa.type = 16 and spa.flags & 0 = 0
                     then spa.id_session_profile end) as to_telegram_bot_click_cnt,
       count(case
                 when spa.screen = 2014 and spa.type = 18 and spa.flags & 0 = 0
                     then spa.id_session_profile end) as to_subscribe_click_cnt,
       count(case
                 when spa.screen = 2014 and spa.type = 6 and spa.flags & 0 = 0
                     then spa.id_session_profile end) as to_vacancies_list_click_onboarding_banner_cnt,
       count(case
                 when spa.screen = 2014 and spa.type = 17 and spa.flags & 0 = 0
                     then spa.id_session_profile end) as to_jdp_click_email_onboarding_banner_cnt,
       count(case
                 when spa.screen = 2014 and spa.type = 7 and spa.flags & 0 = 0
                     then spa.id_session_profile end) as mobile_close_click_onboarding_banner_cnt,
       -- банер підписок на email розсилку spa.screen = 2007
       count(case
                 when spa.screen = 2007 and spa.type = 200 /*ScreenLoaded*/ and spa.flags & 0 = 0
                     then spa.id_session_profile end) as email_subscription_screen_loaded_cnt,

       count(case
                 when spa.screen = 2007 and spa.type = 19 and spa.flags & 0 = 0
                     then spa.id_session_profile end) as to_others_ways_click_cnt,
       count(case
                 when spa.screen = 2007 and spa.type = 6 and spa.flags & 0 = 0
                     then spa.id_session_profile end) as to_vacancies_list_click_email_subscription_banner_cnt,
       count(case
                 when spa.screen = 2007 and spa.type = 17 and spa.flags & 0 = 0
                     then spa.id_session_profile end) as to_jdp_click_email_subscription_banner_cnt,
       count(case
                 when spa.screen = 2007 and spa.type = 5 and spa.flags & 0 = 0
                     then spa.id_session_profile end) as email_success_submit_click_cnt,
       count(case
                 when spa.screen = 2007 and spa.type = 5 and spa.flags & 8 = 8
                     then spa.id_session_profile end) as email_submit_click_empty_email_cnt,
       count(case
                 when spa.screen = 2007 and spa.type = 5 and spa.flags & 4 = 4
                     then spa.id_session_profile end) as email_submit_click_invalid_email_cnt,
       count(case
                 when spa.screen = 2007 and spa.type = 7 and spa.flags & 0 = 0
                     then spa.id_session_profile end) as mobile_close_click_subscription_screen_cnt,
       count(case
                 when spa.screen in (2007, 2014) and spa.flags & 2 = 2
                     then spa.id_session_profile end) as error_cnt /*without problem with email*/

from imp.session s
         join imp.session_profile sp
              on s.country = sp.country
                  and s.date_diff = sp.date_diff
                  and s.id = sp.id_session
         join imp.session_profile_action spa
              on sp.country = spa.country
                  and sp.date_diff = spa.date_diff
                  and sp.id_session = spa.id_session
                  and sp.id = spa.id_session_profile
where s.country = 1
  and s.is_bot = 0
  and sp.date_diff >= 44583 /*дата першого релізу onboarding banner бота 2022-01-24 */
group by 1, 2, 3, 4, 5, 6, 7
having count(case
                 when spa.screen = 2014 and spa.type = 200 /*ScreenLoaded*/ and spa.flags & 0 = 0
                     then spa.id_session_profile end) > 0;



alter table job_seeker.chat_bot_onboarding_banner_usage_agg
    add constraint chat_bot_onboarding_banner_usage_agg_pk
        primary key (country_id, session_id, banner_view_datediff, profile_session_id, is_mobile_session, profile_page_source_id);

grant select on job_seeker.chat_bot_onboarding_banner_usage_agg to readonly;
