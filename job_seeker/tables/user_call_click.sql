select sj.country as country_id,
       sj.date_diff as jdp_viewed_datediff,
       sj.id as jdp_id,
       sj.id_account as account_id,
       s.id as session_id,
       s.cookie_label,
       sj.uid_job as job_uid,
       (case when sj.job_id_project = -1 then 1 else 0 end)
           + (case when sj.flags & 256 = 256 then 2 else 0 end)
            + (case when sj.flags & 2048 = 2048 then 8 else 0 end) as jdp_flag,
       sj.date as jdp_viewed_datetime,
       case when (s.flags & 16) = 16 then 1 else 0 end as device_type
from imp.session_jdp sj
join imp.session s on s.country = sj.country and s.id = sj.id_session and s.date_diff = sj.date_diff
where sj.country = 1
and exists (select 1
            from  imp.session_jdp_action sja
            where sj.country = sja.country and sj.date_diff = sja.date_diff and sj.id = sja.id_jdp and sja.type in (13, 19, 21, 34))
  and sj.date_diff = ${DT_NOW} - 1;
