select s.country      as country_id,
       s.cookie_label as cookie_label,
       eai.id_account as account_id,
       s.date_diff    as account_creation_datediff,
       eai.date       as account_creation_datetime,
       s.id           as account_creation_session_id
from imp.session as s
         join imp.email_account_interactions eai
              on s.id = eai.id_session
                  and eai.country = s.country
                  and s.date_diff = eai.date_diff
                  and eai.interaction_type = 0
where s.country = 1 and s.date_diff = ${DT_NOW} - 1;
