select s.score,
       s.user_type_id,
       s.user_id,
       s.activation_datediff,
       min(s.activation_datetime) as activation_datetime,
       max(case when a.jdp_viewed_datediff - s.activation_datediff = 1 then 1 else 0 end) as is_retained_1day,
       max(case when a.jdp_viewed_datediff - s.activation_datediff between 1 and 7 then 1 else 0 end) as is_retained_1_7days,
       max(case when a.jdp_viewed_datediff - s.activation_datediff between 1 and 28 then 1 else 0 end) as is_retained_1_28days,
       max(case when a.jdp_viewed_datediff - s.activation_datediff between 29 and 98 then 1 else 0 end) as is_retained_29_98days,
       a.country_id
from job_seeker.user_apply a
join job_seeker.user_active_blue_collar_score s on a.user_id = s.user_id and a.country_id = s.country_id
group by s.score,
         s.activation_datediff,
         s.user_type_id,
         s.user_id,
         a.country_id;
