create table job_seeker.session_registration
(
    country_id                  smallint not null,
    cookie_label                bigint,
    account_id                  integer  not null,
    account_creation_datediff   integer  not null,
    account_creation_datetime   timestamp,
    account_creation_session_id bigint   not null,
    constraint pk_session_registration
        primary key (country_id, account_id, account_creation_datediff, account_creation_session_id)
);

alter table job_seeker.session_registration
    owner to postgres;

create index ind_session_registration_cookie_label
    on job_seeker.session_registration (cookie_label);
