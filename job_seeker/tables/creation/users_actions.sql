create table job_seeker.users_actions
(
    dt                                     date,
    is_mobile                              bigint,
    cnt_users                              bigint,
    cnt_sessions                           bigint,
    cnt_searches                           bigint,
    cnt_nonea_jdp_views                    bigint,
    cnt_premium_ea_jdp_views               bigint,
    cnt_free_ea_jdp_views                  bigint,
    cnt_nonpaid_easy_apply_jdp_views       bigint,
    cnt_paid_nonea_jdp_away                bigint,
    cnt_nonpaid_nonea_jdp_away             bigint,
    cnt_nonea_jdp_call                     bigint,
    cnt_premium_ea_jdp_call                bigint,
    cnt_free_ea_jdp_call                   bigint,
    cnt_premium_ea_jdp_apply_cv_click      bigint,
    cnt_free_ea_jdp_apply_cv_click         bigint,
    cnt_premium_ea_jdp_apply_nocv_click    bigint,
    cnt_free_ea_jdp_apply_nocv_click       bigint,
    cnt_premium_ea_jdp_apply_cv            bigint,
    cnt_free_ea_jdp_apply_cv               bigint,
    cnt_premium_ea_jdp_apply_nocv          bigint,
    cnt_free_ea_jdp_apply_nocv             bigint,
    cnt_nonpaid_easy_apply_jdp_apply       bigint,
    cnt_nonpaid_easy_apply_jdp_apply_click bigint,
    revenue_eur                            numeric(38, 9)
);

alter table job_seeker.users_actions
    owner to postgres;
