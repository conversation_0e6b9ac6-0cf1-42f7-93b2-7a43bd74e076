create table job_seeker.chat_bot_onboarding_banner_usage_agg
(
    country_id                                            smallint not null,
    session_id                                            bigint   not null,
    banner_view_datediff                                  integer  not null,
    profile_session_id                                    bigint   not null,
    is_mobile_session                                     integer  not null,
    is_returned_user                                      integer,
    profile_page_source_id                                integer  not null,
    onboarding_banner_screen_loaded_cnt                   bigint,
    to_viber_bot_click_cnt                                bigint,
    to_telegram_bot_click_cnt                             bigint,
    to_subscribe_click_cnt                                bigint,
    to_vacancies_list_click_onboarding_banner_cnt         bigint,
    to_jdp_click_email_onboarding_banner_cnt              bigint,
    mobile_close_click_onboarding_banner_cnt              bigint,
    email_subscription_screen_loaded_cnt                  bigint,
    to_others_ways_click_cnt                              bigint,
    to_vacancies_list_click_email_subscription_banner_cnt bigint,
    to_jdp_click_email_subscription_banner_cnt            bigint,
    email_success_submit_click_cnt                        bigint,
    email_submit_click_empty_email_cnt                    bigint,
    email_submit_click_invalid_email_cnt                  bigint,
    mobile_close_click_subscription_screen_cnt            bigint,
    error_cnt                                             bigint,
    constraint chat_bot_onboarding_banner_usage_agg_pk
        primary key (country_id, session_id, banner_view_datediff, profile_session_id, is_mobile_session,
                     profile_page_source_id)
);

alter table job_seeker.chat_bot_onboarding_banner_usage_agg
    owner to postgres;
