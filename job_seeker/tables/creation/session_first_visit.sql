create table if not exists job_seeker.session_first_visit
(
    country_id             smallint not null,
    first_session_datediff integer  not null,
    first_session_datetime timestamp,
    session_id             bigint   not null,
    cookie_label           bigint,
    constraint pk_session_first_visit_id
        primary key (country_id, first_session_datediff, session_id)
);

alter table job_seeker.session_first_visit
    owner to postgres;

create index if not exists ind_session_first_visit_cookie_label
    on job_seeker.session_first_visit (cookie_label);
