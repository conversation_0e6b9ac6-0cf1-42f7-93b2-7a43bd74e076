create table job_seeker.user_apply
(
    country_id          smallint not null,
    jdp_viewed_datediff integer  not null,
    jdp_id              bigint   not null,
    account_id          integer,
    phone               varchar(255),
    email               varchar(255),
    profile_id          integer,
    user_id             varchar(255),
    user_type_id        smallint,
    session_id          bigint,
    job_uid             bigint,
    apply_flag          integer,
    cookie_label        bigint,
    apply_id            bigint,
    jdp_viewed_datetime timestamp,
    device_id           smallint,
    constraint pk_user_applies_id
        primary key (country_id, jdp_id, jdp_viewed_datediff)
);

alter table job_seeker.user_apply
    owner to postgres;

create index ind_user_applies_u
    on job_seeker.user_apply (country_id, user_id, user_type_id);

create index ind_user_applies_a
    on job_seeker.user_apply (country_id, account_id);

create index ind_user_applies_cl
    on job_seeker.user_apply (country_id, cookie_label);
