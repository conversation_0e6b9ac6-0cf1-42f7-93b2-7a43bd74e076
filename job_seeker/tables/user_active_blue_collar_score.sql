select a.user_id,
       a.user_type_id,
       round(avg(pbs.blue_score),2)::float as  blue_collar_score,
       min(jdp_viewed_datediff) as activation_datediff,
       min(a.jdp_viewed_datetime) as activation_datetime,
       a.country_id
from job_seeker.user_apply a
left join postgres.job.job_profession jp on jp.job_uid = a.job_uid
left join postgres.dimension.professions_blue_score pbs on pbs.profession = jp.profession
where a.country_id = 1
group by a.user_id, a.user_type_id, a.country_id;
