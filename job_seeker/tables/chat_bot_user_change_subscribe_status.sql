-- Tаблиця підписок-відписок від чат бота

create table job_seeker.chat_bot_user_change_subscribe_status as
with subscribe_change_status as (select cb_2.date_diff,
                                        c.name  as chat_bot_source_name,
                                        cb_2.chat_id,
                                        p.id    as profile_id,

                                        case
                                            -- не в пошуку вчора, сьогодні в пошуку => підписався, status = 1
                                            when cb_1.is_searching = 0 and cb_2.is_searching = 1
                                                then 1
                                            -- в пошуку вчора, сьогодні не в пошуку => відписався, status = 2
                                            when cb_1.is_deleted_bot = 0 and cb_1.is_searching = 1 and
                                                 cb_2.is_searching = 0
                                                then 2
                                            end as user_subscribe_status
                                 from imp.chat_bot_user_info cb_1
                                          join imp.chat_bot_user_info cb_2
                                               on cb_1.channel_id = cb_2.channel_id
                                                   and cb_1.chat_id = cb_2.chat_id
                                                   and cb_1.date_diff = cb_2.date_diff - 1
                                          join imp.channel c
                                               on cb_1.channel_id = c.id
                                          left join imp.profiles p
                                     -- різні формати номерів
                                     -- join тут по номеру телефона
                                                    on concat('+', cb_1.phone) = p.phone
                                                        and p.country = 1
),


     all_subscribe as (
         select distinct subscribe_change_status.date_diff,
                         subscribe_change_status.chat_id,
                         subscribe_change_status.profile_id,
                         subscribe_change_status.chat_bot_source_name,
                         subscribe_change_status.user_subscribe_status
         from subscribe_change_status
         where user_subscribe_status in (1, 2)
-- оскільки для зміни статусу з підписка у відписку та з відписки в підписку використовуємо методологію:
-- до поточного дня приджойнуємо вчорашній
-- то втрачаємо кейси коли юзер підписався та не змінював статус
         union all
         select distinct min(cb.date_diff) as date_diff,
                         cb.chat_id,
                         p.id              as profile_id,

                         c.name            as chat_bot_source_name,
                         1                 as user_subscribe_status
         from imp.chat_bot_user_info cb
                  join imp.channel c
                       on cb.channel_id = c.id
                  left join imp.profiles p
             -- різні формати номерів
             -- join тут по номеру телефона
                            on concat('+', cb.phone) = p.phone
                                and p.country = 1
         where cb.is_searching = 1
           and cb.is_deleted_bot = 0
         group by 2, 3, 4
         order by 2, 3
     )
-- беремо distinct (для уникнення дублів за рахунок першої підписки)
select distinct all_subscribe.date_diff,
                all_subscribe.chat_id,
                all_subscribe.profile_id,
                all_subscribe.chat_bot_source_name,
                all_subscribe.user_subscribe_status
from all_subscribe;
