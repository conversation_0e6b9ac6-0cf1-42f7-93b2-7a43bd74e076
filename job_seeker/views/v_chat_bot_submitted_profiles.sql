-- Metric: count profile submitted with chat_bot / count profile submitted
-- % засабмічених профілів, що мають зв'язок з чат-ботом
create view job_seeker.v_chat_bot_submitted_profiles as
with profiles_submitted_daily as (
    select date_submitted     as date_diff,
           count(distinct id) as profile_submitted_cnt
    from imp.profiles p
    where p.country = 1
      and p.is_submitted = true
    group by date_submitted
),
     profile_submitted as (
         select date_diff,
                profile_submitted_cnt,
                sum(profile_submitted_cnt)
                over (order by date_diff rows between unbounded preceding and current row ) as submitted_profiles_cnt
         from profiles_submitted_daily
         order by date_diff),

     profile_submitted_with_bot as (
         select cb.date_diff,
                count(distinct case when cb.is_deleted_bot = 0 then p.id end) as submitted_profiles_with_chat_bot_cnt
         from imp.chat_bot_user_info cb
                  join imp.profiles p
             -- різні формати номерів
             -- join тут по номеру телефона
                       on CONCAT('+', cb.phone) = p.phone
         where p.country = 1
           and p.is_submitted = true
           and fn_get_date_diff(p.date_is_submitted) <= cb.date_diff
         group by 1
     )
select psb.date_diff,
       psb.submitted_profiles_with_chat_bot_cnt,
       ps.submitted_profiles_cnt
from profile_submitted_with_bot psb
         join profile_submitted ps
              using (date_diff);
