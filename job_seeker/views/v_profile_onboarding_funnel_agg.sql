create or replace view job_seeker.v_profile_onboarding_funnel_agg(country_id, first_session_datediff, profile_submission_source_group_id, profile_blue_collar_type_id, lead_with_profile_0_1_day_cnt, lead_with_profile_0_6_day_cnt, interested_profile_0_1_day_cnt, interested_profile_0_6_day_cnt, onboarded_profile_0_1_day_cnt, onboarded_profile_0_6_day_cnt, profile_collar_type_name) as
	SELECT of.country_id,
       of.first_session_datediff,
       of.profile_submission_source_group_id,
       of.profile_blue_collar_type_id,
       count(DISTINCT
             CASE
                 WHEN of.profile_submission_datediff <= (of.first_session_datediff + 1) THEN of.account_id
                 ELSE NULL::integer
                 END) AS lead_with_profile_0_1_day_cnt,
       count(DISTINCT
             CASE
                 WHEN of.profile_submission_datediff <= (of.first_session_datediff + 6) THEN of.account_id
                 ELSE NULL::integer
                 END) AS lead_with_profile_0_6_day_cnt,
       count(DISTINCT
             CASE
                 WHEN of.profile_interested_datediff <= (of.first_session_datediff + 1) THEN of.account_id
                 ELSE NULL::integer
                 END) AS interested_profile_0_1_day_cnt,
       count(DISTINCT
             CASE
                 WHEN of.profile_interested_datediff <= (of.first_session_datediff + 6) THEN of.account_id
                 ELSE NULL::integer
                 END) AS interested_profile_0_6_day_cnt,
       count(DISTINCT
             CASE
                 WHEN of.profile_onboarding_datediff <= (of.first_session_datediff + 1) THEN of.account_id
                 ELSE NULL::integer
                 END) AS onboarded_profile_0_1_day_cnt,
       count(DISTINCT
             CASE
                 WHEN of.profile_onboarding_datediff <= (of.first_session_datediff + 6) THEN of.account_id
                 ELSE NULL::integer
                 END) AS onboarded_profile_0_6_day_cnt,
       pbct.type_name AS profile_collar_type_name
FROM job_seeker.v_onboarding_funnel of
         LEFT JOIN dimension.profile_blue_collar_type pbct ON pbct.id = of.profile_blue_collar_type_id
WHERE of.profile_submission_datediff <= (of.first_session_datediff + 1)
GROUP BY of.country_id, of.first_session_datediff, of.profile_submission_source_group_id,
         of.profile_blue_collar_type_id, pbct.type_name;

alter table job_seeker.v_profile_onboarding_funnel_agg owner to kpav;

