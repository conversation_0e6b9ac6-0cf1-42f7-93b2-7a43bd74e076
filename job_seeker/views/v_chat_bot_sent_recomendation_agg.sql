create view job_seeker.v_chat_bot_sent_recomendation_agg as
select fn_get_date_diff(date_created)  as recomendation_sent_datediff,
       date_part('hour', date_created) as recomendation_sent_hour,
       channel_type                    as channel_id,
       recommendation_type             as recomendation_type_id,
       count(id_profile)               as profile_cnt,
       count(distinct id_profile)      as profile_unique_cnt
from imp_statistic.chat_bot_recommendation_sent_events
where is_test_sent = false /* це не тестова розсилка, ми її надіслали*/
  and fn_get_date_diff(date_created) >= 44592 /* з цього дня почали норм писати стату */
group by 1, 2, 3, 4
-- є два записи з date_created = infinity, прибераємо їх, через такий костиль
having date_part('hour', date_created) >= 0
order by 1, 2;
