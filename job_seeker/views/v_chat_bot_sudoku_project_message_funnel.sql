create view job_seeker.v_chat_bot_sudoku_project_message_funnel as
select 1                                                                                          as country_id,
       fn_get_date_diff(sml.created_on)                                                           as sent_datediff,
       count(distinct case
                          when msl.message_status_id in (0, 1, 2, 3, 4)
                              then msl.sent_message_log_id end)                                   as created_message_cnt,
       count(distinct case
                          when msl.message_status_id in (1, 2, 3, 4)
                              then msl.sent_message_log_id end)                                   as sent_message_to_esputnic_cnt,
       count(distinct case
                          when msl.message_status_id in (2, 3, 4)
                              then msl.message_status_id end)                                     as delivered_message_to_job_seeker_cnt,
       count(distinct case when msl.message_status_id in (3, 4) then msl.sent_message_log_id end) as read_message_cnt,
       count(distinct
             case when msl.message_status_id = 4 then msl.sent_message_log_id end)                as message_open_link_cnt
from imp_statistic.js_message_status_log msl
         join imp_statistic.js_message_status ms
              on msl.message_status_id = ms.id
         join imp_statistic.js_sent_message_log sml
              on msl.sent_message_log_id = sml.id
where template_id = 17
  and sml.created_on >= '2022-01-17'
group by 1, 2;
