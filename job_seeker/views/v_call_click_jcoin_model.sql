create or replace view job_seeker.v_call_click_jcoin_model(country_id, action_date, employer_id, click_call_cnt) as
	SELECT ucc.country_id,
       date_trunc('day'::text, ucc.jdp_viewed_datetime)::date AS action_date,
       j.id_employer                                          AS employer_id,
       count(DISTINCT ucc.jdp_id)                             AS click_call_cnt
FROM job_seeker.user_call_click ucc
         JOIN imp_employer.job_to_uid_mapping jtum
              ON ucc.country_id = 1 AND jtum.sources = 1 AND ucc.job_uid = jtum.uid_job
         JOIN imp_employer.job j ON jtum.sources = j.sources AND jtum.id_job = j.id
WHERE ucc.jdp_viewed_datediff >= fn_get_date_diff('2021-06-01 00:00:00'::timestamp without time zone)
GROUP BY ucc.country_id, j.id_employer, (date_trunc('day'::text, ucc.jdp_viewed_datetime));

alter table job_seeker.v_call_click_jcoin_model owner to dap;
