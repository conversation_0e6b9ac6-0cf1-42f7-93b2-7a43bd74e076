
create view job_seeker.v_iron_sales_sms_cost as
with currency_info as (/*дублювання даних*/
    select info_currency_history."date"::date as currency_date, avg(value_to_eur) as value_to_eur
    from dimension.info_currency_history
    where country = 1
      and info_currency_history."date"::date >= '2023-01-01'
      and id_currency = 0 /*UAN*/
      and value_to_eur is not null
    group by 1)
select isc.country_code,
       'Iron sales'                                                                                  as communication_channel_name,
       isc.date,
       isc.message_name,
       sum(isc.message_cnt)                                                                          as message_sent_cnt,
       case
           when isc.country_code = 'ua' then sum(isc.message_cnt) * 0.65::numeric
           when isc.country_code = 'hu' then sum(isc.message_cnt) * 1.85::numeric
           when isc.country_code = 'ro' then sum(isc.message_cnt) * 1.30::numeric end                as sms_cost_uan,
       case
           when isc.country_code = 'ua' then sum(isc.message_cnt) * 0.65 * value_to_eur::numeric
           when isc.country_code = 'hu' then sum(isc.message_cnt) * 1.85 * value_to_eur::numeric
           when isc.country_code = 'ro' then sum(isc.message_cnt) * 1.30 * value_to_eur::numeric end as sms_cost_eur
from job_seeker.v_iron_sales_communication isc
         left join currency_info ci
                   on isc.date = ci.currency_date
where isc.date between '2023-01-01' and date(current_date) - 1
  and isc.channel_id = 5 /*sms*/
  and isc.message_status = 'Sent'
group by isc.country_code, communication_channel_name, isc.date, isc.message_name, ci.value_to_eur;
