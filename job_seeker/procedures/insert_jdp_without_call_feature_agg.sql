create or replace procedure job_seeker.insert_jdp_without_call_feature_agg()
	language plpgsql
as $$
begin
    insert into job_seeker.jdp_without_call_feature_agg(country_id, date_diff, packet_short_name, is_free_jdp,
                                                    jdp_cnt, jdp_without_call_feature_cnt)
    select sj.country                                             as country_id,
           sj.date_diff                                           as date_diff,
           vpi.packet_short_name,
           case when packet_short_name = 'Free' then 1 else 0 end as is_free_jdp,
           count(sj.id)                                           as jdp_cnt,
           count(distinct (case
                               when sj.flags & 268435456 = 268435456 /*Phones were hidden because: Job is out of contacts*/
                                   then sj.id end))               as jdp_without_call_feature_cnt
    from imp.session_jdp sj
             -- додавання job
             join imp_employer.job_to_uid_mapping jtum
                  on sj.country = 1
                      and jtum.sources = 1
                      and sj.uid_job = jtum.uid_job
             join imp_employer.job j
                  on jtum.sources = j.sources
                      and jtum.id_job = j.id
             join employer.v_subscription_packet_info vpi
                  on vpi.database_source_id = 1
                      and vpi.employer_id = j.id_employer
    where sj.country = 1
      and sj.job_id_project = -1 /*DTE*/
      and sj.date_diff = fn_get_date_diff(current_date) - 1
      and sj.date between subscription_order_datetime and subscription_expiring_datetime
    group by sj.country, sj.date_diff, vpi.packet_short_name;
    end;

$$;

