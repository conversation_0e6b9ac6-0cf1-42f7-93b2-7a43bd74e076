create table dimension.revenue_season_coefficient
(
    country        varchar(50),
    device_type_id smallint,
    season_type    varchar(50),
    season_number  int,
    coefficient    double precision,
    method         varchar(50),
    primary key (country, device_type_id, season_type, season_number)
);


-- Numbers are from: https://docs.google.com/spreadsheets/d/1BFzX-U-hqFGeFLE016ypjpNIJjBW3j6GG8TWdgXlZI8/edit#gid=0
create temp table coefficients_total as
select season_type,
       season_number,
       coefficient,
       'relative to the first season' as method
from (values ('month', 1, 1.000000),
             ('month', 2, 0.925475),
             ('month', 3, 0.949150),
             ('month', 4, 0.841495),
             ('month', 5, 0.884344),
             ('month', 6, 0.850952),
             ('month', 7, 0.838933),
             ('month', 8, 0.889332),
             ('month', 9, 0.934183),
             ('month', 10, 0.939291),
             ('month', 11, 0.898161),
             ('month', 12, 0.777387)) as values(season_type, season_number, coefficient);

create temp table countries as
select name_country_eng as country
from dimension.countries 
where name_country_eng not in ('Russia', 'Belarus')

union all

select 'Total' as country;


insert into dimension.revenue_season_coefficient
select c.country,
       device_type_id,
       coefficients.season_type,
       coefficients.season_number,
       coefficients.coefficient::double precision,
       coefficients.method
from countries c
     cross join (select unnest(array [-1, 0, 1]) as device_type_id) devices
     cross join coefficients_total coefficients;
