select country as country_id,
       4       as session_traffic_source_group_id /*email+viber alerts*/,
       id      as session_traffic_source_id,
       name    as session_traffic_source_name,
       utm     as session_trafffic_source_utm,
       channel as session_traffic_source_channel,
       is_paid,
       daterec
from dimension.u_traffic_source
where case
          when country = 1 then id in (
                                       3/*Email*/,
                                       327685/*from letter traffic, Jo<PERSON>le email*/,
                                       202 /*Viber_push*/,
                                       100000 /*SubscribeConfirmationEmail*/,
                                       100001 /*AccountConfirmationEmail*/,
                                       100002 /*FillProfileEmail*/,
                                       100003 /*ChangeProfileVisibilityEmail*/,
                                       100004 /*EmployersCanCallEmail*/,
                                       100005 /*CreateProfileOfferEmail*/,
                                       100006 /*AbandonedProfileEmail*/,
                                       100007 /*AbandonedProfileViber*/,
                                       100008 /*CreateProfileAfterCallViber*/,
                                       100009 /*NoApplyReminderEmail*/,
                                       100010 /*MoreSuitableJobsEmail*/,
                                       100011 /*FindJobFasterEmail*/,
                                       100012 /*MoreResponsesEmail*/,
                                       100013 /*CreateProfileAfterCallSms*/,
                                       100022 /*Viber*/,
                                       100023 /*UnsubscribeConfirmationEmail*/,
                                       100024 /*RecoverPasswordEmail*/,
                                       100025 /*WelcomeEmail*/,
                                       100026 /*CreatePasswordEmail*/,
                                       100027 /*AccountDeleteEmail*/,
                                       100028 /*GetStartedEmail*/,
                                       100029 /*RestoreSubscriptionEmail*/,
                                       100030 /*SendCvEmail*/,
                                       327697 /*FindJobFasterViber*/,
                                       327698 /*MoreSuitableJobsViber*/,
                                       327699 /*NoApplyReminderViber*/,
                                       327700 /*MoreResponsesViber*/
              )
          when country = 10 then id in (
                                        3/*Email*/,
                                        202 /*Viber_push*/,
                                        300 /*CreateProfileHuEmailDistribution*/,
                                        100000 /*SubscribeConfirmationEmail*/,
                                        100001 /*AccountConfirmationEmail*/,
                                        100002 /*FillProfileEmail*/,
                                        100003 /*ChangeProfileVisibilityEmail*/,
                                        100004 /*EmployersCanCallEmail*/,
                                        100005 /*CreateProfileOfferEmail*/,
                                        100006 /*AbandonedProfileEmail*/,
                                        100007 /*AbandonedProfileViber*/,
                                        100008 /*CreateProfileAfterCallViber*/,
                                        100009 /*NoApplyReminderEmail*/,
                                        100010 /*MoreSuitableJobsEmail*/,
                                        100011 /*FindJobFasterEmail*/,
                                        100012 /*MoreResponsesEmail*/,
                                        100013 /*CreateProfileAfterCallSms*/,
                                        100022 /*Viber*/,
                                        100023 /*UnsubscribeConfirmationEmail*/,
                                        100024 /*RecoverPasswordEmail*/,
                                        100025 /*WelcomeEmail*/,
                                        100026 /*CreatePasswordEmail*/,
                                        100027 /*AccountDeleteEmail*/,
                                        100028 /*GetStartedEmail*/,
                                        100029 /*RestoreSubscriptionEmail*/,
                                        100030 /*SendCvEmail*/,
                                        327705 /*Jooble email*/,
                                        327706 /*WelcomeMessage*/,
                                        327707 /*FillProfileMessage*/,
                                        327708 /*ChangeProfileVisibilityMessage*/,
                                        327709 /*AbandonedProfileMessage*/,
                                        327710 /*ApplyViewedMessage*/,
                                        327711 /*ApplySentMessage*/,
                                        327712 /*ApplyNotViewedMessage*/,
                                        327713 /*FindJobFasterMessage*/,
                                        327714 /*MoreResponsesMessage*/,
                                        327715 /*NoApplyReminderMessage*/,
                                        327716 /*ContactsOpenedMessage*/,
                                        327717 /*EmployerCanCallMessage*/,
                                        327718 /*SubscriptionOfferMessage*/,
                                        327719 /*ContactsNotOpenedMessage*/
              ) end;
