select distinct e.country_code,
       e.id                                as 'employer_id',
       ps.id                               as 'subscription_id',
       ba.payment_method                   as 'payment_method',
       so.id                               as 'payment_id',
       so.net_price                        as 'subscription_price',
       so.currency_iso_code,
       cast(so.date_ordered as date)       as 'packet_start_date',
       so.date_paid                        as 'payment_date',
       cast(so.date_refunded as date)      as 'date_refunded',
       promocode.code                      as 'promocode',
       ps.id_packet                        as 'packet_id',
       p.name                              as 'packet_name',
       pp.month_count                      as 'month_count',
       pcp.days_active,
       pcp.id_packet_trial,
       ssr.id_reason                       as 'id_sub_stop_reason',
       ssr.reason                          as 'sub_stop_reason',
       ssr.date                            as 'stop_reason_date',
       case
           when sub_is_canceled.id_subscription is not null
               then 1 else 0
           end                             as 'sub_is_cancelled',
       ssr.id                              as 'row_id_sub_stop_reason'
from packet_subscription ps
         inner join employer e
                    on e.id = ps.id_employer
         inner join employer_cdp ec
                    on ec.id = e.id_cdp
         inner join subscription_order so
                    on so.id_subscription = ps.id
         inner join packet p
                    on p.id = ps.id_packet
         inner join packet_period pp
                    on pp.id = ps.id_period
         left join billing_agreement ba
                   on ba.id = so.id_agreement
         left join packet_custom_period pcp
                   on pcp.id_packet_trial = p.id
         left join u_traffic_source uts
                   on uts.id = e.id_traffic_source
         left join promocode
                   on promocode.id = so.id_promocode
         left join subscription_stop_reason ssr
                   on ssr.id_subscription = ps.id and ssr.id <> 200 -- bug
         left join (select sc.id_subscription
                    from employer_statistics.subscription_cancellation sc
                    group by sc.id_subscription) sub_is_canceled
                   on sub_is_canceled.id_subscription = ps.id
where e.country_code in ('rs', 'ba', 'gr', 'hr')
  and so.status = 2; -- successfull payment
