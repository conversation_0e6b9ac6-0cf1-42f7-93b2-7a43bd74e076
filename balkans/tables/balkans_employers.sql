WITH test_registration AS
         (SELECT DISTINCT e.id AS 'id_employer',
                          1    AS 'is_poor_registration'
          FROM employer e
                   LEFT JOIN employer_note en
                             ON en.id_employer = e.id
                   LEFT JOIN account_service.account a
                             ON a.id = e.id_account
          WHERE (en.`TEXT` LIKE '%test'
                    OR en.`TEXT` LIKE '%тест'
                    OR a.email LIKE '%jooble.com%')
            AND e.country_code IN ('rs', 'hr', 'gr', 'ba'))
SELECT e.country_code,
       e.id                                 AS 'employer_id',
       ec.company_name,
       ec.industry,
       ec.staff_size,
       e.date_created                       AS 'registration_date',
       uts.name                             AS 'traffic_source',
       e.moderation_status,
       CAST(a.is_verified AS signed)        AS 'email_is_verified',
       COALESCE(tr.is_poor_registration, 0) AS 'is_poor_registration'
FROM employer e
         INNER JOIN account_service.account a
                    ON a.id = e.id_account
         INNER JOIN employer_cdp ec
                    ON ec.id = e.id_cdp
         LEFT JOIN test_registration tr
                   ON tr.id_employer = e.id
         LEFT JOIN u_traffic_source uts
                   ON uts.id = e.id_traffic_source
WHERE e.country_code IN ('rs', 'ba', 'hr', 'gr')
  AND e.date_created;
