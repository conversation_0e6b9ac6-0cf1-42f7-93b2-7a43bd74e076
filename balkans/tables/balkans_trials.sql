select distinct e.country_code,
                e.id                        as 'employer_id',
                ps.id                       as 'subscription_id',
                ps.id_packet                as 'packet_id',
                pcp.days_active,
                cast(ps.order_date as date) as 'packet_start_date',
                p.name                      as 'packet_name',
                ec.company_name             as 'company_name'
from packet_subscription ps
         inner join employer e
                    on e.id = ps.id_employer
         inner join employer_cdp ec
                    on ec.id = e.id_cdp
         inner join packet p
                    on p.id = ps.id_packet
         inner join packet_custom_period pcp
                    on pcp.id_packet_trial = p.id
where ps.id_packet = 61
  and year(ps.order_date) >= 2024;
