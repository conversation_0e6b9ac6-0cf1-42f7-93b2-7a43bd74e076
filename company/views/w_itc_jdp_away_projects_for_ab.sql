create view w_itc_jdp_away_projects_for_ab(country, itc_per_view) as
SELECT v.country,
       sum(v.intention_to_contact) / sum(v.views) AS itc_per_view,
       sum(v.views) as views,
       sum(v.intention_to_contact) as intention_to_contact,
       min(v.dt) as min_dt,
       max(v.dt) as max_dt
FROM company.m_itc_jdp_away_projects v
WHERE v.dt >= (CURRENT_DATE - '30 days'::interval)
GROUP BY v.country;

alter table w_itc_jdp_away_projects_for_ab
    owner to dap;
