create view w_profile_apply_funnel
            (country_id, jdp_viewed_datediff, device_type, is_dte, is_premium, is_cv_apply, is_add_quest_apply,
             is_profile_apply, is_apply_success, is_apply_viewed, is_in_offer_status, is_in_rejected_status,
             blue_collar_score, jdp_view_cnt)
as
WITH jdp_user AS (
    SELECT j_1.country_id,
           j_1.jdp_viewed_datediff,
           j_1.device_type,
           j_1.jdp_flag & 1                                       AS is_dte,
           sign((j_1.jdp_flag & 2)::smallint::double precision)   AS is_premium,
           j_1.apply_flag & 1                                     AS is_cv_apply,
           sign((j_1.apply_flag & 2)::smallint::double precision) AS is_add_quest_apply,
           sign((j_1.apply_flag & 4)::smallint::double precision) AS is_profile_apply,
           afc.is_success                                         AS is_apply_success,
           CASE
               WHEN COALESCE(ubcs_a.score, ubcs_j.score) >= 0::double precision AND
                    COALESCE(ubcs_a.score, ubcs_j.score) <= 0.34::double precision
                   THEN 'white collars'::character varying(255)
               WHEN COALESCE(ubcs_a.score, ubcs_j.score) >= 0.35::double precision AND
                    COALESCE(ubcs_a.score, ubcs_j.score) <= 0.64::double precision
                   THEN 'white-blue collars'::character varying(255)
               WHEN COALESCE(ubcs_a.score, ubcs_j.score) >= 0.65::double precision AND
                    COALESCE(ubcs_a.score, ubcs_j.score) <= 1::double precision
                   THEN 'blue collars'::character varying(255)
               ELSE 'undefined'::character varying(255)
               END                                                AS blue_collar_score,
           j_1.jdp_id,
           afc.apply_id
    FROM apply.jdp j_1
             JOIN apply.application_form_conversion afc
                  ON afc.country_id = j_1.country_id AND afc.jdp_viewed_datediff = j_1.jdp_viewed_datediff AND
                     afc.jdp_id = j_1.jdp_id
             JOIN job_seeker.user_jdp uj
                  ON j_1.jdp_viewed_datediff = uj.jdp_viewed_datediff AND j_1.country_id = uj.country_id AND
                     j_1.jdp_id = uj.jdp_id
             LEFT JOIN job_seeker.user_active_blue_collar_score ubcs_a
                       ON ubcs_a.user_type_id = uj.user_type_id AND ubcs_a.user_id::text = uj.user_id::text
             LEFT JOIN job_seeker.user_jdp_blue_collar_score ubcs_j
                       ON ubcs_j.user_type_id = uj.user_type_id AND ubcs_j.user_id::text = uj.user_id::text
    WHERE j_1.jdp_viewed_datediff >= 44077
      AND (j_1.jdp_flag & 1) = 1
    GROUP BY j_1.country_id, j_1.jdp_viewed_datediff, (j_1.jdp_flag & 1),
             (sign((j_1.jdp_flag & 2)::smallint::double precision)),
             (sign((j_1.apply_flag & 4)::smallint::double precision)), (j_1.apply_flag & 1), j_1.device_type,
             afc.is_success,
             (
                 CASE
                     WHEN COALESCE(ubcs_a.score, ubcs_j.score) >= 0::double precision AND
                          COALESCE(ubcs_a.score, ubcs_j.score) <= 0.34::double precision
                         THEN 'white collars'::character varying(255)
                     WHEN COALESCE(ubcs_a.score, ubcs_j.score) >= 0.35::double precision AND
                          COALESCE(ubcs_a.score, ubcs_j.score) <= 0.64::double precision
                         THEN 'white-blue collars'::character varying(255)
                     WHEN COALESCE(ubcs_a.score, ubcs_j.score) >= 0.65::double precision AND
                          COALESCE(ubcs_a.score, ubcs_j.score) <= 1::double precision
                         THEN 'blue collars'::character varying(255)
                     ELSE 'undefined'::character varying(255)
                     END), j_1.jdp_id, afc.apply_id
)
SELECT j.country_id,
       j.jdp_viewed_datediff,
       j.device_type,
       j.is_dte,
       j.is_premium,
       j.is_cv_apply,
       j.is_add_quest_apply,
       j.is_profile_apply,
       j.is_apply_success,
       COALESCE(sign((ja.flags & 2)::smallint::double precision), 0::smallint::double precision) AS is_apply_viewed,
       CASE
           WHEN ja.status = 3 THEN 1
           ELSE 0
           END                                                                                   AS is_in_offer_status,
       CASE
           WHEN ja.status = ANY (ARRAY [4, 100]) THEN 1
           ELSE 0
           END                                                                                   AS is_in_rejected_status,
       j.blue_collar_score,
       count(DISTINCT j.jdp_id)                                                                  AS jdp_view_cnt
FROM jdp_user j
         LEFT JOIN imp_employer.job_apply ja ON ja.sources = 1 AND ja.id_session_apply = j.apply_id AND
                                                ja.dd_session_apply = j.jdp_viewed_datediff
GROUP BY j.country_id, j.jdp_viewed_datediff, j.device_type, j.is_dte, j.is_premium, j.is_cv_apply,
         j.is_add_quest_apply, j.is_apply_success,
         (COALESCE(sign((ja.flags & 2)::smallint::double precision), 0::smallint::double precision)),
         (
             CASE
                 WHEN ja.status = 3 THEN 1
                 ELSE 0
                 END),
         (
             CASE
                 WHEN ja.status = ANY (ARRAY [4, 100]) THEN 1
                 ELSE 0
                 END), j.blue_collar_score, j.is_profile_apply;

alter table w_profile_apply_funnel
    owner to postgres;
