create or replace view apply.v_additional_questions_funnel(country_id, jdp_viewed_datediff, device_type, is_dte, is_premium, is_cv_apply, is_add_quest_apply, is_apply_success, is_apply_viewed, is_in_offer_status, is_in_rejected_status, is_add_quest_shown, is_add_quest_submited, blue_collar_score, jdp_view_cnt) as
	SELECT m_additional_questions_funnel.country_id,
       m_additional_questions_funnel.jdp_viewed_datediff,
       m_additional_questions_funnel.device_type,
       m_additional_questions_funnel.is_dte,
       m_additional_questions_funnel.is_premium,
       m_additional_questions_funnel.is_cv_apply,
       m_additional_questions_funnel.is_add_quest_apply,
       m_additional_questions_funnel.is_apply_success,
       m_additional_questions_funnel.is_apply_viewed,
       m_additional_questions_funnel.is_in_offer_status,
       m_additional_questions_funnel.is_in_rejected_status,
       m_additional_questions_funnel.is_add_quest_shown,
       m_additional_questions_funnel.is_add_quest_submited,
       m_additional_questions_funnel.blue_collar_score,
       m_additional_questions_funnel.jdp_view_cnt
FROM apply.m_additional_questions_funnel;

alter table apply.v_additional_questions_funnel owner to rlu;

grant select on apply.v_additional_questions_funnel to npo;

grant select on apply.v_additional_questions_funnel to readonly;

grant select on apply.v_additional_questions_funnel to writeonly_product;

grant select on apply.v_additional_questions_funnel to yb;

