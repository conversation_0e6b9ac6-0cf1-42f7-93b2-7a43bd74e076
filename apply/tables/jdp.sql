select  s.country as country_id,
		sjd.id as jdp_id,
		sjd.date_diff as jdp_viewed_datediff,
		sjd.uid_job as job_uid,
		(case when sjd.job_id_project = -1 then 1 else 0 end) + (case when sjd.flags & 256 = 256 then 2 else 0 end) as jdp_flag,
		sjd.date as jdp_viewed_datetime,
		coalesce(sjd.id_account, 0) as account_id,
		(case when (sjd.flags & 1024) = 0 then 1 else 0 end) + (case when (sjd.flags & 131072) = 131072 then 2 else 0 end)
		                                                     + (case when s.country = 1 then 4 else 0 end) as apply_flag,
		s.id as session_id,
		s.cookie_label,
		case when (s.flags & 16) = 16 then 1 else 0 end as device_type,
		sign(s.flags & 2) as is_returned,
		s.id_traf_source as traf_source_id
from imp.session s
inner join imp.session_jdp sjd on sjd.date_diff = s.date_diff and sjd.id_session = s.id and sjd.flags & 4 = 4 and s.country = sjd.country
where s.country in (1, 12, 11, 10, 9, 8, 7) and s.date_diff = ${DT_NOW} - 1 and (sjd.flags & 4) = 4 and s.flags & 1 = 0 and s.flags & 4 = 0;
