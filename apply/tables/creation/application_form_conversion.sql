create table apply.application_form_conversion
(
	country_id smallint not null,
	jdp_id bigint not null,
	jdp_viewed_datediff integer not null,
	apply_click integer,
	is_success integer,
	apply_id bigint,
	userrec varchar(15),
	daterec timestamp,
	usermod varchar(15),
	datemod timestamp,
	constraint pk_application_form_conversion_id
		primary key (country_id, jdp_viewed_datediff, jdp_id)
);

alter table apply.application_form_conversion owner to postgres;

create index ind_apply_flow_uid
	on apply.application_form_conversion (country_id, apply_id);

grant select on apply.application_form_conversion to npo;

grant select on apply.application_form_conversion to readonly;

grant select on apply.application_form_conversion to writeonly_product;

grant select on apply.application_form_conversion to yb;

