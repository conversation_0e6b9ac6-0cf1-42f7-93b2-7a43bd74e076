create table apply.jdp
(
	country_id smallint not null,
	jdp_id bigint not null,
	jdp_viewed_datediff integer not null,
	job_uid bigint,
	jdp_flag integer,
	jdp_viewed_datetime timestamp,
	account_id integer,
	apply_flag integer,
	session_id bigint,
	cookie_label bigint,
	device_type smallint,
	is_returned smallint,
	traf_source_id integer,
	constraint pk_apply_jdp_id
		primary key (country_id, jdp_viewed_datediff, jdp_id)
)
partition by RANGE (country_id);

alter table apply.jdp owner to postgres;

grant select on apply.jdp to readonly;

grant select on apply.jdp to writeonly_product;

grant select on apply.jdp to yb;

create table apply.jdp_pt_ua
partition of apply.jdp
(
	constraint jdp_pt_ua_pkey
		primary key (country_id, jdp_viewed_datediff, jdp_id)
)
FOR VALUES FROM ('1') TO ('2')
partition by RANGE (jdp_viewed_datediff);

alter table apply.jdp_pt_ua owner to rlu;

grant select on apply.jdp_pt_ua to readonly;

grant select on apply.jdp_pt_ua to writeonly_product;

grant select on apply.jdp_pt_ua to yb;

create table apply.jdp_pt_ua_2019
partition of apply.jdp_pt_ua
(
	constraint jdp_pt_ua_2019_pkey
		primary key (country_id, jdp_viewed_datediff, jdp_id)
)
FOR VALUES FROM (43464) TO (43829);

alter table apply.jdp_pt_ua_2019 owner to rlu;

create index jdp_pt_ua_2019_country_id_account_id_idx
	on apply.jdp_pt_ua_2019 (country_id, account_id);

grant select on apply.jdp_pt_ua_2019 to readonly;

grant select on apply.jdp_pt_ua_2019 to writeonly_product;

grant select on apply.jdp_pt_ua_2019 to yb;

create table apply.jdp_pt_ua_2020
partition of apply.jdp_pt_ua
(
	constraint jdp_pt_ua_2020_pkey
		primary key (country_id, jdp_viewed_datediff, jdp_id)
)
FOR VALUES FROM (43829) TO (44195);

alter table apply.jdp_pt_ua_2020 owner to rlu;

create index jdp_pt_ua_2020_country_id_account_id_idx
	on apply.jdp_pt_ua_2020 (country_id, account_id);

grant select on apply.jdp_pt_ua_2020 to readonly;

grant select on apply.jdp_pt_ua_2020 to writeonly_product;

grant select on apply.jdp_pt_ua_2020 to yb;

create table apply.jdp_pt_ua_2021
partition of apply.jdp_pt_ua
(
	constraint jdp_pt_ua_2021_pkey
		primary key (country_id, jdp_viewed_datediff, jdp_id)
)
FOR VALUES FROM (44195) TO (44560);

alter table apply.jdp_pt_ua_2021 owner to rlu;

create index jdp_pt_ua_2021_country_id_account_id_idx
	on apply.jdp_pt_ua_2021 (country_id, account_id);

grant select on apply.jdp_pt_ua_2021 to readonly;

grant select on apply.jdp_pt_ua_2021 to writeonly_product;

grant select on apply.jdp_pt_ua_2021 to yb;

create table apply.jdp_pt_ua_2022
partition of apply.jdp_pt_ua
(
	constraint jdp_pt_ua_2022_pkey
		primary key (country_id, jdp_viewed_datediff, jdp_id)
)
FOR VALUES FROM (44560) TO (44925);

alter table apply.jdp_pt_ua_2022 owner to rlu;

create index jdp_pt_ua_2022_country_id_account_id_idx
	on apply.jdp_pt_ua_2022 (country_id, account_id);

grant select on apply.jdp_pt_ua_2022 to readonly;

grant select on apply.jdp_pt_ua_2022 to writeonly_product;

grant select on apply.jdp_pt_ua_2022 to yb;

create table apply.jdp_pt_7
partition of apply.jdp
(
	constraint jdp_pt_7_pkey
		primary key (country_id, jdp_viewed_datediff, jdp_id)
)
FOR VALUES FROM ('7') TO ('8');

alter table apply.jdp_pt_7 owner to rlu;

create index jdp_pt_7_country_id_account_id_idx
	on apply.jdp_pt_7 (country_id, account_id);

grant select on apply.jdp_pt_7 to readonly;

grant select on apply.jdp_pt_7 to writeonly_product;

grant select on apply.jdp_pt_7 to yb;

create table apply.jdp_pt_8
partition of apply.jdp
(
	constraint jdp_pt_8_pkey
		primary key (country_id, jdp_viewed_datediff, jdp_id)
)
FOR VALUES FROM ('8') TO ('9');

alter table apply.jdp_pt_8 owner to rlu;

create index jdp_pt_8_country_id_account_id_idx
	on apply.jdp_pt_8 (country_id, account_id);

grant select on apply.jdp_pt_8 to readonly;

grant select on apply.jdp_pt_8 to writeonly_product;

grant select on apply.jdp_pt_8 to yb;

create table apply.jdp_pt_9
partition of apply.jdp
(
	constraint jdp_pt_9_pkey
		primary key (country_id, jdp_viewed_datediff, jdp_id)
)
FOR VALUES FROM ('9') TO ('10');

alter table apply.jdp_pt_9 owner to rlu;

create index jdp_pt_9_country_id_account_id_idx
	on apply.jdp_pt_9 (country_id, account_id);

grant select on apply.jdp_pt_9 to readonly;

grant select on apply.jdp_pt_9 to writeonly_product;

grant select on apply.jdp_pt_9 to yb;

create table apply.jdp_pt_10
partition of apply.jdp
(
	constraint jdp_pt_10_pkey
		primary key (country_id, jdp_viewed_datediff, jdp_id)
)
FOR VALUES FROM ('10') TO ('11');

alter table apply.jdp_pt_10 owner to rlu;

create index jdp_pt_10_country_id_account_id_idx
	on apply.jdp_pt_10 (country_id, account_id);

grant select on apply.jdp_pt_10 to readonly;

grant select on apply.jdp_pt_10 to writeonly_product;

grant select on apply.jdp_pt_10 to yb;

create table apply.jdp_pt_11
partition of apply.jdp
(
	constraint jdp_pt_11_pkey
		primary key (country_id, jdp_viewed_datediff, jdp_id)
)
FOR VALUES FROM ('11') TO ('12');

alter table apply.jdp_pt_11 owner to rlu;

create index jdp_pt_11_country_id_account_id_idx
	on apply.jdp_pt_11 (country_id, account_id);

grant select on apply.jdp_pt_11 to readonly;

grant select on apply.jdp_pt_11 to writeonly_product;

grant select on apply.jdp_pt_11 to yb;

create table apply.jdp_pt_12
partition of apply.jdp
(
	constraint jdp_pt_12_pkey
		primary key (country_id, jdp_viewed_datediff, jdp_id)
)
FOR VALUES FROM ('12') TO ('13');

alter table apply.jdp_pt_12 owner to rlu;

create index jdp_pt_12_country_id_account_id_idx
	on apply.jdp_pt_12 (country_id, account_id);

grant select on apply.jdp_pt_12 to readonly;

grant select on apply.jdp_pt_12 to writeonly_product;

grant select on apply.jdp_pt_12 to yb;

create table apply.jdp_pt_def
partition of apply.jdp
(
	constraint jdp_pt_def_pkey
		primary key (country_id, jdp_viewed_datediff, jdp_id)
)
DEFAULT;

alter table apply.jdp_pt_def owner to rlu;

create index jdp_pt_def_country_id_account_id_idx
	on apply.jdp_pt_def (country_id, account_id);

grant select on apply.jdp_pt_def to readonly;

grant select on apply.jdp_pt_def to writeonly_product;

grant select on apply.jdp_pt_def to yb;

