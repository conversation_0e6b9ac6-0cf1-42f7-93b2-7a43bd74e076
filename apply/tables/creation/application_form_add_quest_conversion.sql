create table apply.application_form_add_quest_conversion
(
	id integer default nextval('apply.apply_additional_questions_id_seq'::regclass) not null
		constraint pk_apply_additional_questions_id
			primary key,
	country_id smallint not null,
	jdp_id bigint,
	jdp_viewed_datediff integer not null,
	is_success integer,
	userrec varchar(15),
	daterec timestamp,
	usermod varchar(15),
	datemod timestamp
);

alter table apply.application_form_add_quest_conversion owner to postgres;

create index ind_apply_additional_questions_dc
	on apply.application_form_add_quest_conversion (jdp_viewed_datediff, country_id);

create index ind_apply_additional_questions_ij
	on apply.application_form_add_quest_conversion (jdp_id);

create trigger tr_i_apply_additional_questions
	before insert
	on apply.application_form_add_quest_conversion
	for each row
	execute procedure product.fn_getdateuserins();

create trigger tr_u_apply_additional_questions
	before update
	on apply.application_form_add_quest_conversion
	for each row
	execute procedure product.fn_getdateuserupd();

create trigger tr_i_application_form_add_quest_conversion
	before insert
	on apply.application_form_add_quest_conversion
	for each row
	execute procedure apply.fn_getdateuserins();

create trigger tr_u_application_form_add_quest_conversion
	before update
	on apply.application_form_add_quest_conversion
	for each row
	execute procedure apply.fn_getdateuserupd();

grant select on apply.application_form_add_quest_conversion to npo;

grant select on apply.application_form_add_quest_conversion to readonly;

grant select on apply.application_form_add_quest_conversion to writeonly_product;

grant select on apply.application_form_add_quest_conversion to yb;

