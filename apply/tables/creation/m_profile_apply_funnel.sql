create table if not exists apply.m_profile_apply_funnel
(
	id serial
		constraint m_profile_apply_funnel_pkey
			primary key,
	country_id smallint not null,
	jdp_viewed_datediff integer not null,
	device_type integer,
	is_dte integer,
	is_premium integer,
	is_cv_apply integer,
	is_add_quest_apply integer,
	is_profile_apply integer,
	is_apply_success integer,
	is_apply_viewed integer,
	is_in_offer_status integer,
	is_in_rejected_status integer,
	blue_collar_score varchar(100),
	jdp_view_cnt integer
);

alter table apply.m_profile_apply_funnel owner to postgres;

grant select on sequence apply.m_profile_apply_funnel_id_seq to npo;

create index if not exists ind_m_profile_apply_funnel_d
	on apply.m_profile_apply_funnel (country_id, jdp_viewed_datediff);

grant select on apply.m_profile_apply_funnel to npo;

grant select on apply.m_profile_apply_funnel to readonly;

grant select on apply.m_profile_apply_funnel to writeonly_product;

grant select on apply.m_profile_apply_funnel to yb;

