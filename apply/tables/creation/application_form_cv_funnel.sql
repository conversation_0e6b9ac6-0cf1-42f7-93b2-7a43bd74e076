create table apply.application_form_cv_funnel
(
	id integer default nextval('apply.apply_cv_id_seq'::regclass) not null
		constraint pk_apply_cv_id
			primary key,
	country_id smallint not null,
	jdp_id bigint not null,
	jdp_viewed_datediff integer not null,
	cv_auto_attached integer,
	cv_upload_click integer,
	cv_uploaded integer,
	cv_create_click integer,
	cv_created integer,
	click_select integer,
	cv_selected integer,
	userrec varchar(15),
	daterec timestamp,
	usermod varchar(15),
	datemod timestamp
);

alter table apply.application_form_cv_funnel owner to postgres;

create index ind_apply_cv_dc
	on apply.application_form_cv_funnel (jdp_viewed_datediff, country_id);

create index ind_apply_cv_ij
	on apply.application_form_cv_funnel (jdp_id);

create trigger tr_i_apply_cv
	before insert
	on apply.application_form_cv_funnel
	for each row
	execute procedure product.fn_getdateuserins();

create trigger tr_u_apply_cv
	before update
	on apply.application_form_cv_funnel
	for each row
	execute procedure product.fn_getdateuserupd();

grant select on apply.application_form_cv_funnel to npo;

grant select on apply.application_form_cv_funnel to readonly;

grant select on apply.application_form_cv_funnel to writeonly_product;

grant select on apply.application_form_cv_funnel to yb;

