create table apply.application_form_cv_funnel_datetime
(
	id integer default nextval('apply.date_apply_cv_id_seq'::regclass) not null
		constraint pk_date_apply_cv_id
			primary key,
	country_id smallint not null,
	jdp_id bigint not null,
	jdp_viewed_datediff integer not null,
	cv_auto_attached_datetime timestamp,
	cv_upload_click_datetime timestamp,
	cv_uploaded_datetime timestamp,
	cv_create_click_datetime timestamp,
	cv_created_datetime timestamp,
	click_select_datetime timestamp,
	cv_selected_datetime timestamp,
	userrec varchar(15),
	daterec timestamp,
	usermod varchar(15),
	datemod timestamp
);

alter table apply.application_form_cv_funnel_datetime owner to postgres;

create index ind_date_apply_cv_dc
	on apply.application_form_cv_funnel_datetime (jdp_viewed_datediff, country_id);

create index ind_date_apply_cv_ij
	on apply.application_form_cv_funnel_datetime (jdp_id);

create trigger tr_i_date_apply_cv
	before insert
	on apply.application_form_cv_funnel_datetime
	for each row
	execute procedure product.fn_getdateuserins();

create trigger tr_u_date_apply_cv
	before update
	on apply.application_form_cv_funnel_datetime
	for each row
	execute procedure product.fn_getdateuserupd();

grant select on apply.application_form_cv_funnel_datetime to npo;

grant select on apply.application_form_cv_funnel_datetime to readonly;

grant select on apply.application_form_cv_funnel_datetime to writeonly_product;

grant select on apply.application_form_cv_funnel_datetime to yb;

