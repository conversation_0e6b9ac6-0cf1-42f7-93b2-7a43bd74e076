select country_id,
       jdp_id,
       jdp_viewed_datediff,
       case when cv_upload_click_datetime is null and cv_create_click_datetime is null and click_select_datetime is null and cv_auto_attached_datetime is not null
            then 1
            else 0
       end as cv_auto_attached,
       case when (cv_uploaded_datetime is not null) -- резюме загружено
                  and cv_uploaded_datetime > coalesce(cv_created_datetime,'1900-01-01') and cv_upload_click_datetime > coalesce(click_select_datetime,'1900-01-01') -- дата клика на загрузить резюме позже даты создания и даты клика на выбрать резюме
            then 1
           when (cv_uploaded_datetime is null and cv_created_datetime is null and cv_selected_datetime is null) -- резюме  не прикреплено
            and cv_upload_click_datetime is not null -- есть клик на загрузить резюме
            and cv_upload_click_datetime > cv_create_click_datetime and cv_upload_click_datetime > click_select_datetime -- дата клика на загрузить резюме позже даты создания и даты клика на выбрать резюме
            then 1
            else 0
       end as cv_upload_click,
       case when (cv_uploaded_datetime is not null) -- резюме загружено
                  and cv_uploaded_datetime > cv_created_datetime and cv_upload_click_datetime > click_select_datetime -- дата загрузки резюме позже даты создания и даты клика на выбрать резюме
            then 1
            else 0
       end as cv_uploaded,
       case when (cv_created_datetime is not null) -- резюме создано
                  and cv_created_datetime > cv_uploaded_datetime and cv_create_click_datetime > click_select_datetime -- дата клика на создать резюме позже даты загрузки и даты клика на выбрать резюме
           then 1
            when (cv_uploaded_datetime is null and cv_created_datetime is null and cv_selected_datetime is null) -- резюме не прикреплено
            and cv_create_click_datetime is not null -- есть клик на создать резюме
            and cv_create_click_datetime > cv_upload_click_datetime and cv_create_click_datetime > click_select_datetime -- дата клика на создать резюме позже даты загрузки и даты клика на выбрать резюме
            then 1
            else 0
       end as cv_create_click,
       case when (cv_created_datetime is not null) -- резюме создано
                  and  cv_created_datetime > cv_uploaded_datetime and cv_create_click_datetime > click_select_datetime -- дата клика на создать резюме позже даты загрузки и даты клика на выбрать резюме
            then 1
            else 0
       end as cv_created,
       case when (cv_selected_datetime is not null) -- резюме прикреплено
                  and click_select_datetime > cv_upload_click_datetime and click_select_datetime > cv_create_click_datetime -- дата клика на выбрать резюме позже даты клика на загрузить резюме и даты клика на создать резюме
            then 1
            when (cv_uploaded_datetime is null and cv_created_datetime is null and cv_selected_datetime is null) -- резюме не прикреплено
             and click_select_datetime is not null  -- есть клик на выбрать резюме
             and click_select_datetime > cv_upload_click_datetime and click_select_datetime > cv_create_click_datetime -- дата клика на выбрать резюме позже даты клика на загрузить резюме и даты клика на создать резюме
            then 1
            else 0
       end as click_select,
       case when (cv_selected_datetime is not null) -- резюме прикреплено
                  and click_select_datetime > cv_upload_click_datetime and click_select_datetime > cv_create_click_datetime -- дата клика на выбрать резюме позже даты клика на загрузить резюме и даты клика на создать резюме
                  and  cv_selected_datetime > click_select_datetime -- выбор резюме произошёл после клика на него
           then 1
            else 0
       end as cv_selected
from apply.application_form_cv_funnel_datetime
where jdp_viewed_datediff = ${DT_NOW} - 1;
