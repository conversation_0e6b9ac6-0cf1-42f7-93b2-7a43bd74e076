create or replace view mobile_app.v_ab_tests
            (country, session_date, session_id, account_id, placement, alertview_search_cnt,
             impression_cnt, impression_on_screen_cnt, serp_click_cnt, serp_away_cnt, serp_jdp_cnt, jdp_away_cnt,
             groups, account_verify_date, revenue_usd, session_row_num, platform,
             traffic_source, session_order_num)
as
WITH rev AS (SELECT sr.country_id,
                    sr.session_datediff,
                    sr.session_id,
                    sum(sr.revenue_usd) AS revenue_usd
             FROM mobile_app.session_revenue sr
             WHERE sr.session_datediff >= 44833
             GROUP BY sr.country_id, sr.session_datediff, sr.session_id)
SELECT c.alpha_2                                                                                                   AS country,
       fn_get_timestamp_from_date_diff(sabp.session_datediff)::date                                                AS session_date,
       sabp.session_id,
       sabp.account_id,
       sabp.placement,
       sabp.alertview_search_cnt,
       sabp.impression_cnt,
       sabp.impression_on_screen_cnt,
       sabp.serp_click_cnt,
       sabp.serp_away_cnt,
       sabp.serp_jdp_cnt,
       sabp.jdp_away_cnt,
       sta.groups,
       ac.verify_date::date                                                                                        AS account_verify_date,
       rev.revenue_usd,
       row_number()
       OVER (PARTITION BY sabp.country_id, sabp.session_datediff, sabp.session_id)                                 AS session_row_num,
       fla.platform,
       fla.traffic_source,
       rank()
       OVER (PARTITION BY sabp.country_id, sabp.account_id ORDER BY sabp.session_datediff, sabp.session_id)        AS session_order_num
FROM mobile_app.session_account_by_placement sabp
JOIN dimension.countries c ON c.id = sabp.country_id
LEFT JOIN imp.session_test_agg sta ON sta.country_id = sabp.country_id AND sta.date_diff = sabp.session_datediff AND
                                      sta.id_session = sabp.session_id
LEFT JOIN imp.account_contact ac ON ac.country = sabp.country_id AND ac.id_account = sabp.account_id AND ac.type = 2
LEFT JOIN mobile_app.first_launch_attribute fla ON lower(fla.installation_id::text) = lower(ac.contact::text)
LEFT JOIN rev ON rev.country_id = sabp.country_id AND rev.session_datediff = sabp.session_datediff AND
                 rev.session_id = sabp.session_id;

alter table mobile_app.v_ab_tests
    owner to ypr;

grant select on mobile_app.v_ab_tests to user_agg_team;
