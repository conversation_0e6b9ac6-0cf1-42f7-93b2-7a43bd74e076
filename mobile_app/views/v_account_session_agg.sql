with session_from_push as (
    select country,
           id_session,
           min(fn_get_date_diff(date_add)) as push_sent_datediff
    from imp.push_status ps
    where status_type in (31,32)
    group by country,
           id_session
),
     mobile_app_session_revenue as (select country_id,
                                           session_datediff,
                                           session_id,
                                           sum(revenue_usd) as revenue_usd
         from mobile_app.session_revenue
         group by country_id,
                  session_datediff,
                  session_id)


select c.alpha_2 as country_code,
       maas.platform as mobile_platform_name,
       maas.traffic_source,
       maas.account_verification_datetime::date as account_verification_date,
       masaa.session_datediff,
       case when masaa.session_datediff = maas.session_datediff then 1 /*session in onboarding day*/
            when masaa.session_datediff > maas.session_datediff then 2 /*session after onboarding day*/
            else 0 /*undefined*/ end  as session_retention_type_id,
       case when sfp.id_session is not null then 1 /*push*/ else 0/*undefined*/ end as session_source_id,
       count(distinct masaa.session_id) as session_cnt,
       sum(masaa.impression_on_screen_cnt) as impression_on_screen_cnt,
       sum(masaa.serp_click_cnt) as serp_click_cnt,
       sum(masr.revenue_usd) as revenue_usd
from mobile_app.session_account masaa
left join mobile_app.account_source maas
      on masaa.country_id = maas.country_id and
         masaa.account_id = maas.account_id
left join session_from_push sfp
       on sfp.country = masaa.country_id and
          sfp.id_session = masaa.session_id
left join mobile_app_session_revenue masr
    on masr.country_id = masaa.country_id and
       masr.session_id = masaa.session_id
join dimension.countries c
  on masaa.country_id = c.id

group by c.alpha_2,
       maas.platform,
       maas.traffic_source,
       maas.account_verification_datetime::date,
         masaa.session_datediff,
         --masaa.account_id,
         case when masaa.session_datediff = maas.session_datediff then 1 /*session in onboarding day*/
            when masaa.session_datediff > maas.session_datediff then 2 /*session after onboarding day*/
            else 0 /*undefined*/ end,
         case when sfp.id_session is not null then 1 /*push*/ else 0/*undefined*/ end;
