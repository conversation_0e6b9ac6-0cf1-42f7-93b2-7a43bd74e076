drop table installs;

/* FIRST COLLECT RAW DATA into installs, changing empty strings to nulls */
with
    t as (
        select distinct
            (sfa.action_data::jsonb ->> 'installationId')::varchar as installation_id,
            sfa.date                                               as first_launch_datetime,
            (sfa.action_data::jsonb ->> 'utmSource')::varchar      as traffic_source,
            (sfa.action_data::jsonb ->> 'utmMedium')::varchar      as traffic_medium,
            (sfa.action_data::jsonb ->> 'utmCampaign')::varchar    as traffic_campaign,
            (sfa.action_data::jsonb ->> 'platform')::varchar       as mobile_platform_name,
            (sfa.action_data::jsonb ->> 'countryCode')::varchar    as country_code,
            (sfa.action_data::jsonb ->> 'installSource')::varchar  as install_source,
            sfa.type
        from
            imp.session_feature_action sfa
        where
              type in (10, 11)
          and date_diff >= fn_get_date_diff('2022-06-01')
    )
select
    lower(installation_id::text) as installation_id,
    type,
    first_launch_datetime,
    case when traffic_source != '' then traffic_source end        as traffic_source,
    case when traffic_medium != '' then traffic_medium end        as traffic_medium,
    case when traffic_campaign != '' then traffic_campaign end    as traffic_campaign,
    mobile_platform_name,
    case when country_code != '' then country_code end            as country_code,
    case
        when substring(install_source from '%#"google-play#"%' for '#') is not null then 'store'
        when install_source like '%gclid=%' then 'google-cpc' 
        when substring(install_source from '%#"apps.facebook.com#"%' for '#') is not null then    'facebook' end as install_source
into temp table
    installs
from
    t;

/* TRANSFER INSTALLS WITH ONLY 1 ROW to first_launch_attribute */

with
    t2 as (
        select
            t.*,
            count(*) over (partition by installation_id) cnt
        from
            installs t
    )
select
    installation_id,
    type,
    first_launch_datetime,
    traffic_source,
    traffic_medium,
    traffic_campaign,
    mobile_platform_name,
    country_code,
    install_source
into temp table
    first_launch_attribute_prev
from
    t2
where
    cnt = 1;

/* TRANSFERS INSTALLS WITH MULTIPLE ROWS to double_row_installs */

with
    t2 as (
        select
            t.*,
            count(*) over (partition by installation_id) cnt
        from
            installs t
    )
select
    installation_id,
    type,
    first_launch_datetime,
    traffic_source,
    traffic_medium,
    traffic_campaign,
    mobile_platform_name,
    country_code,
    install_source
into temp table
    double_row_installs
from
    t2
where
    cnt > 1;

/* AGGREGATE double_row_installs BY installation_id, type SO EACH INSTALL WOULD HAVE 2 ROWS AT MAXIMUM */

select
    installation_id,
    type,
    min(first_launch_datetime) as first_launch_datetime,
    min(traffic_source)        as traffic_source,
    min(traffic_medium)        as traffic_medium,
    min(traffic_campaign)      as traffic_campaign,
    min(country_code)          as country_code,
    min(mobile_platform_name)  as platform,
    min(install_source)        as install_source
into temp table
    double_row_installs_agg
from
    double_row_installs
group by
    installation_id, type;

/*
TRANSFER INSTALLS THAT NOW HAVE ONLY ONE AGGREGATED ROW IN double_row_installs_agg (THESE INSTALLS HAD MULTIPLE ROWS OF ONLY ONE TYPE)
OR ROWS WITH type=10 and source='banner'
INTO first_launch_attribute
*/

insert into
    first_launch_attribute_prev(installation_id, type, first_launch_datetime, traffic_source, traffic_medium,
                                traffic_campaign, mobile_platform_name, country_code, install_source)
with
    t as (
        select
            installation_id,
            type,
            first_launch_datetime,
            traffic_source,
            traffic_medium,
            traffic_campaign,
            platform,
            country_code,
            install_source,
            count(*) over (partition by installation_id) as cnt_agg_rows
        from
            double_row_installs_agg
    )
select
    installation_id,
    type,
    first_launch_datetime,
    traffic_source,
    traffic_medium,
    traffic_campaign,
    platform,
    country_code,
    install_source
from
    t
where
     (type = 10
         and traffic_source in ('banner', 'j_banner'))
  or cnt_agg_rows = 1;

/*
DELETE ROWS FOR TRANSFERRED INSTALLS FROM double_row_installs_agg
*/

delete
from
    double_row_installs_agg
where
        installation_id in (select installation_id from first_launch_attribute_prev);

/*
SELECT NEEDED ROWS FOR REMAINING INSTALLS:
- rows with type 10 if difference between type 10 datetime and type 11 datetime > 300 sec
- rows with type 10 if time difference < 300 sec
    AND traffic source in row with type 11 is null AND traffic source in row with type 10 is not null
- rows with type 11 if time difference < 300 sec
    AND (traffic source in row with type 11 is not null OR rows with both types have null traffic source)
*/

with
    t as (
        select
            dria.*,
            case when traffic_source is null then 1 else 0 end as is_null_ts,
            max(case when type = 11 and traffic_source is null then 1 else 0 end)
            over (partition by installation_id)                as is_11_ts_is_null,
            max(case when type = 10 and traffic_source is null then 1 else 0 end)
            over (partition by installation_id)                as is_10_ts_is_null,
            extract(epoch from
                    (min(case when type = 11 then first_launch_datetime end) over (partition by installation_id) -
                     min(case when type = 10 then first_launch_datetime end)
                     over (partition by installation_id)))     as diff
        from
            double_row_installs_agg dria
    )
insert
into
    first_launch_attribute_prev(installation_id, type, first_launch_datetime, traffic_source, traffic_medium,
                                traffic_campaign, mobile_platform_name, country_code, install_source)
select
    installation_id,
    type,
    first_launch_datetime,
    traffic_source,
    traffic_medium,
    traffic_campaign,
    platform,
    country_code,
    install_source
from
    t
where
     (diff > 300 and type = 10)
  or (diff < 300 and type = 11 and
      (is_null_ts = 0 -- type=11 and not null traffic source
          or (is_null_ts = 1 and is_10_ts_is_null = 1) -- type=11 and both traffic sources are null
          ))
  or (diff < 300 and type = 10 and is_null_ts = 0 and is_11_ts_is_null = 1);

select
    installation_id,
    first_launch_datetime,
    case
        when install_source = 'google-cpc' then 'google'
        when install_source = 'facebook' then 'facebook'
        else coalesce(traffic_source, install_source) end as traffic_source,
    case
        when install_source in ('google-cpc','facebook') then 'cpc'
        when install_source = 'store' then 'organic'
        else traffic_medium end                           as traffic_medium,
    case
        when install_source in ('store', 'google-cpc','facebook') then 'none'
        else traffic_campaign end                         as traffic_campaign,
    mobile_platform_name,
    country_code
from
    first_launch_attribute_prev
