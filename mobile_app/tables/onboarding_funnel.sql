create temporary table temp_mobile_app_onboarding_funnel_action as
select sf.id_session                                               as session_id,
       s.cookie_label,
       (sfa.action_data::jsonb ->> 'selectedCountryCode')::varchar as country_code,
       s.ip_cc,
       c.id                                                        as country_id,
       sf.date                                                     as country_screen_show_datetime,
       sf1.type                                                    as feature_type_id,
       sf1.date                                                    as screen_show_datetime,
       sfa1.type                                                   as action_type_id,
       sfa1.action_data                                            as action_datetime,
       ac.id_account                                               as account_id
from imp.session_feature sf
         join imp.session s
              on s.country = sf.country_id and
                 s.id = sf.id_session
         left join imp.session_feature_action sfa
                   on sf.country_id = sfa.country_id and
                      sf.id = sfa.id_session_feature and
                      sfa.type in (7)
         left join dimension.countries c
                   on lower(c.alpha_2) = (sfa.action_data::jsonb ->> 'selectedCountryCode')::varchar
         left join dimension.countries cs on s.country = cs.id
         left join imp.session s1
                   on s1.country = c.id and
                      s1.cookie_label = s.cookie_label and
                      s1.flags & 64 = 64 /*only mobile app session*/ and
                      --s1.start_date between s.start_date - '15 minutes'::interval and s.start_date + '1 days'::interval
                      (s1.start_date - cs.deviation_utc_time) between (s.start_date - cs.deviation_utc_time) - '15 minutes'::interval and (s.start_date - cs.deviation_utc_time) + '1 days'::interval
                        /*rlu, потребує переводу до єдиного часового поясу!!!*/
         left join imp.session_feature sf1
                   on sf1.country_id = c.id and
                      sf1.id_session = s1.id and
                      sf1.type in (6, 7, 8, 9)
         left join imp.session_feature_action sfa1
                   on sfa1.country_id = sf1.country_id and
                      sfa1.id_session_feature = sf1.id
         left join imp.email_account_interactions eai
                   on eai.country = sf1.country_id and
                      eai.id_session = sf1.id_session and
                      eai.interaction_type = 0
          left join imp.account_contact ac
               on ac.country = eai.country and
                  ac.id_account = eai.id_account and
                  ac.type = 2 /*mobile app account*/
where sf.type in (5)
  and s.flags & 64 = 64
  and sf.date_diff >= 44711
    ;


--  гранулярність - юзер, відповідає на запитання, чи був пройдений конкретний етап воронки
create table mobile_app.onboarding_funnel as
with funnel_with_doubles as (
    select country_id                                                         as country_id,
           ip_cc                                                              as ip_country_code,
           cookie_label,
           case when country_code is not null then 1 else 0 end               as has_country_selection,
           country_screen_show_datetime                                       as country_screen_show_datetime,
           /*identifying first session with country screen view*/
           min(country_screen_show_datetime) over (partition by cookie_label) as country_screen_show_first_datetime,
           screen_show_datetime,
           case when feature_type_id = 6 then 1 else 0 end                    as saw_job_page,
           case when feature_type_id = 7 then 1 else 0 end                    as saw_region_page,
           account_id                                                         as account_id
from temp_mobile_app_onboarding_funnel_action
)
select max(country_id) as country_id,
       max (ip_country_code) as ip_country_code,
       cookie_label,
       max(has_country_selection) as has_country_selection,
       country_screen_show_first_datetime as country_screen_show_first_datetime,
       max(saw_job_page) as saw_job_page,
       max(saw_region_page) as saw_region_page,
       max(account_id) as account_id
from funnel_with_doubles
where country_screen_show_datetime = country_screen_show_first_datetime
  and country_screen_show_first_datetime >= '2022-06-01'
group by cookie_label,
         country_screen_show_first_datetime
;
