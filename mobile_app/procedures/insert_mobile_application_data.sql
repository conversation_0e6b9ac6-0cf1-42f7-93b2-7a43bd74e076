create or replace procedure mobile_app.insert_mobile_application_data()
    language plpgsql
as
$$
declare _dt_diff int = fn_get_date_diff(current_date - 1);
        _dt_date date = current_date - 1;

begin
/* FIRST COLLECT RAW DATA into installs, changing empty strings to nulls */


            --delete from mobile_app.first_launch_attribute
            --where first_launch_datetime::date >= current_date - 1;

            --delete from mobile_app.onboarding_funnel
            --where country_screen_show_datetime::date >= current_date - 1;

            delete from mobile_app.account_source
            where account_verification_datetime::date >= current_date - 1;

            --delete from mobile_app.session_account
            --where session_datediff >= fn_get_date_diff(current_date - 1);

            delete from mobile_app.session_revenue
            where session_datediff >= fn_get_date_diff(current_date - 1);




            truncate mobile_app.first_launch_attribute;

            create temp table installs as
            with
                t as (
                    select distinct
                        (sfa.action_data::jsonb ->> 'installationId')::varchar as installation_id,
                        sfa.date                                               as first_launch_datetime,
                        (sfa.action_data::jsonb ->> 'utmSource')::varchar      as traffic_source,
                        (sfa.action_data::jsonb ->> 'utmMedium')::varchar      as traffic_medium,
                        (sfa.action_data::jsonb ->> 'utmCampaign')::varchar    as traffic_campaign,
                        (sfa.action_data::jsonb ->> 'platform')::varchar       as mobile_platform_name,
                        (sfa.action_data::jsonb ->> 'countryCode')::varchar    as country_code,
                        (sfa.action_data::jsonb ->> 'installSource')::varchar  as install_source,
                        sfa.type
                    from
                        imp.session_feature_action sfa
                    where
                          type in (10, 11)
                      and date_diff >= 44711
                )
            select
                installation_id,
                type,
                first_launch_datetime,
                case when traffic_source != '' then traffic_source end        as traffic_source,
                case when traffic_medium != '' then traffic_medium end        as traffic_medium,
                case when traffic_campaign != '' then traffic_campaign end    as traffic_campaign,
                mobile_platform_name,
                case when country_code != '' then country_code end            as country_code,
                case
                    when substring(install_source from '%#"google-play#"%' for '#') is not null then 'store'
                    when install_source like '%gclid=%' then 'google-cpc' end as install_source
            from
                t;

            /* TRANSFER INSTALLS WITH ONLY 1 ROW to first_launch_attribute */
            create temp table first_launch_attribute_prev as
            with
                t2 as (
                    select
                        t.*,
                        count(*) over (partition by installation_id) cnt
                    from
                        installs t
                )
            select
                installation_id,
                type,
                first_launch_datetime,
                traffic_source,
                traffic_medium,
                traffic_campaign,
                mobile_platform_name,
                country_code,
                install_source
            from
                t2
            where
                cnt = 1;

            /* TRANSFERS INSTALLS WITH MULTIPLE ROWS to double_row_installs */
            create temp table double_row_installs as
            with
                t2 as (
                    select
                        t.*,
                        count(*) over (partition by installation_id) cnt
                    from
                        installs t
                )
            select
                installation_id,
                type,
                first_launch_datetime,
                traffic_source,
                traffic_medium,
                traffic_campaign,
                mobile_platform_name,
                country_code,
                install_source
            from
                t2
            where
                cnt > 1;

            /* AGGREGATE double_row_installs BY installation_id, type SO EACH INSTALL WOULD HAVE 2 ROWS AT MAXIMUM */
            create temp table double_row_installs_agg as
            select
                installation_id,
                type,
                min(first_launch_datetime) as first_launch_datetime,
                min(traffic_source)        as traffic_source,
                min(traffic_medium)        as traffic_medium,
                min(traffic_campaign)      as traffic_campaign,
                min(country_code)          as country_code,
                min(mobile_platform_name)  as platform,
                min(install_source)        as install_source
            from
                double_row_installs
            group by
                installation_id, type;

            /*
            TRANSFER INSTALLS THAT NOW HAVE ONLY ONE AGGREGATED ROW IN double_row_installs_agg (THESE INSTALLS HAD MULTIPLE ROWS OF ONLY ONE TYPE)
            OR ROWS WITH type=10 and source='banner'
            INTO first_launch_attribute
            */

            insert into
                first_launch_attribute_prev(installation_id, type, first_launch_datetime, traffic_source, traffic_medium,
                                            traffic_campaign, mobile_platform_name, country_code, install_source)
            with
                t as (
                    select
                        installation_id,
                        type,
                        first_launch_datetime,
                        traffic_source,
                        traffic_medium,
                        traffic_campaign,
                        platform,
                        country_code,
                        install_source,
                        count(*) over (partition by installation_id) as cnt_agg_rows
                    from
                        double_row_installs_agg
                )
            select
                installation_id,
                type,
                first_launch_datetime,
                traffic_source,
                traffic_medium,
                traffic_campaign,
                platform,
                country_code,
                install_source
            from
                t
            where
                 (type = 10
                     and traffic_source in ('banner', 'j_banner'))
              or cnt_agg_rows = 1;

            /*
            DELETE ROWS FOR TRANSFERRED INSTALLS FROM double_row_installs_agg
            */

            delete
            from
                double_row_installs_agg
            where
                    installation_id in (select installation_id from first_launch_attribute_prev);

            /*
            SELECT NEEDED ROWS FOR REMAINING INSTALLS:
            - rows with type 10 if difference between type 10 datetime and type 11 datetime > 300 sec
            - rows with type 10 if time difference < 300 sec
                AND traffic source in row with type 11 is null AND traffic source in row with type 10 is not null
            - rows with type 11 if time difference < 300 sec
                AND (traffic source in row with type 11 is not null OR rows with both types have null traffic source)
            */

            with
                t as (
                    select
                        dria.*,
                        case when traffic_source is null then 1 else 0 end as is_null_ts,
                        max(case when type = 11 and traffic_source is null then 1 else 0 end)
                        over (partition by installation_id)                as is_11_ts_is_null,
                        max(case when type = 10 and traffic_source is null then 1 else 0 end)
                        over (partition by installation_id)                as is_10_ts_is_null,
                        extract(epoch from
                                (min(case when type = 11 then first_launch_datetime end) over (partition by installation_id) -
                                 min(case when type = 10 then first_launch_datetime end)
                                 over (partition by installation_id)))     as diff
                    from
                        double_row_installs_agg dria
                )

            insert
            into
                first_launch_attribute_prev(installation_id, type, first_launch_datetime, traffic_source, traffic_medium,
                                            traffic_campaign, mobile_platform_name, country_code, install_source)
            select
                installation_id,
                type,
                first_launch_datetime,
                traffic_source,
                traffic_medium,
                traffic_campaign,
                platform,
                country_code,
                install_source
            from
                t
            where
                 (diff > 300 and type = 10)
              or (diff < 300 and type = 11 and
                  (is_null_ts = 0 -- type=11 and not null traffic source
                      or (is_null_ts = 1 and is_10_ts_is_null = 1) -- type=11 and both traffic sources are null
                      ))
              or (diff < 300 and type = 10 and is_null_ts = 0 and is_11_ts_is_null = 1);



            insert into mobile_app.first_launch_attribute(installation_id, first_launch_datetime, traffic_source, traffic_medium, traffic_campaign, platform, country_code)
            select
                installation_id,
                first_launch_datetime,
                case
                    when install_source = 'google-cpc' then 'google'
                    else coalesce(traffic_source, install_source) end as traffic_source,
                case
                    when install_source = 'google-cpc' then 'cpc'
                    when install_source = 'store' then 'organic'
                    else traffic_medium end                           as traffic_medium,
                case
                    when install_source in ('store', 'google-cpc') then 'none'
                    else traffic_campaign end                         as traffic_campaign,
                mobile_platform_name,
                country_code
            from
                first_launch_attribute_prev;


            drop table if exists installs;
            drop table if exists first_launch_attribute_prev;
            drop table if exists double_row_installs;
            drop table if exists double_row_installs_agg;






        create temporary table temp_mobile_app_onboarding_funnel_action as
            select sf.id_session                                               as session_id,
                   s.cookie_label,
                   (sfa.action_data::jsonb ->> 'selectedCountryCode')::varchar as country_code,
                   s.ip_cc,
                   c.id                                                        as country_id,
                   sf.date                                                     as country_screen_show_datetime,
                   sf1.type                                                    as feature_type_id,
                   sf1.date                                                    as screen_show_datetime,
                   sfa1.type                                                   as action_type_id,
                   sfa1.action_data                                            as action_datetime,
                   ac.id_account                                               as account_id
            from imp.session_feature sf
            join imp.session s on s.country = sf.country_id and s.id = sf.id_session
            left join imp.session_feature_action sfa on sf.country_id = sfa.country_id and sf.id = sfa.id_session_feature and sfa.type in (7)
            left join dimension.countries c on lower(c.alpha_2) = (sfa.action_data::jsonb ->> 'selectedCountryCode')::varchar
            left join dimension.countries cs on s.country = cs.id
            left join imp.session s1 on s1.country = c.id and s1.cookie_label = s.cookie_label and s1.flags & 64 = 64 and
                                  --s1.start_date between s.start_date - '15 minutes'::interval and s.start_date + '1 days'::interval
                                  (s1.start_date - cs.deviation_utc_time) between (s.start_date - cs.deviation_utc_time) - '15 minutes'::interval and (s.start_date - cs.deviation_utc_time) + '1 days'::interval
                                    /*rlu, потребує переводу до єдиного часового поясу!!!*/
            left join imp.session_feature sf1 on sf1.country_id = c.id and sf1.id_session = s1.id and sf1.type in (6, 7, 8, 9)
            left join imp.session_feature_action sfa1 on sfa1.country_id = sf1.country_id and sfa1.id_session_feature = sf1.id
            left join imp.email_account_interactions eai on eai.country = sf1.country_id and eai.id_session = sf1.id_session and eai.interaction_type = 0
            left join imp.account_contact ac on ac.country = eai.country and ac.id_account = eai.id_account and ac.type = 2 /*mobile app account*/
            where sf.type in (5) and s.flags & 64 = 64
              and sf.date_diff >= _dt_diff - 1;


            -- видаляємо два дні для подальшого перезапису двох попередніх днів
            delete from mobile_app.onboarding_funnel where country_screen_show_datetime::date >= (current_date - 2)::date;
            --  гранулярність - юзер, відповідає на запитання, чи був пройдений конкретний етап воронки
            insert into mobile_app.onboarding_funnel(country_id, ip_country_code, cookie_label, has_country_selection,
                                                     country_screen_show_datetime, saw_job_page, saw_region_page, account_id)
            with funnel_with_doubles as (
            select country_id                                                         as country_id,
                   ip_cc                                                              as ip_country_code,
                   cookie_label,
                   case when country_code is not null then 1 else 0 end               as has_country_selection,
                   country_screen_show_datetime                                       as country_screen_show_datetime,
                   /*identifying first session with country screen view*/
                   min(country_screen_show_datetime) over (partition by cookie_label) as country_screen_show_first_datetime,
                   screen_show_datetime,
                   case when feature_type_id = 6 then 1 else 0 end                    as saw_job_page,
                   case when feature_type_id = 7 then 1 else 0 end                    as saw_region_page,
                   account_id                                                         as account_id
            from temp_mobile_app_onboarding_funnel_action
            )
            select max(country_id) as country_id,
                   max (ip_country_code) as ip_country_code,
                   cookie_label,
                   max(has_country_selection) as has_country_selection,
                   country_screen_show_first_datetime as country_screen_show_first_datetime,
                   max(saw_job_page) as saw_job_page,
                   max(saw_region_page) as saw_region_page,
                   max(account_id) as account_id
            from funnel_with_doubles
            where country_screen_show_datetime = country_screen_show_first_datetime
              and country_screen_show_first_datetime >= _dt_date - 1
            group by cookie_label,
                     country_screen_show_first_datetime;
 -------------------------

            create temp table temp_first_launch_attribute as
            select lower(installation_id) as installation_id,
                   first_launch_datetime,
                   traffic_source,
                   traffic_medium,
                   traffic_campaign,
                   platform,
                   country_code
            from mobile_app.first_launch_attribute;

            create index temp_first_launch_attribute_idx on temp_first_launch_attribute (installation_id);


            insert into mobile_app.account_source(country_id, account_id, account_verification_datetime, installation_id, cookie_label,
                                                  session_id, session_datediff, ip_country_code, traffic_source, mobile_platform_name)
            select ac.country                                     as country_id,
                   ac.id_account                                  as account_id,
                   ac.verify_date                                 as account_verification_datetime,
                   ac.contact                                     as installation_id,
                   s.cookie_label,
                   s.id                                           as session_id,
                   s.date_diff                                    as session_datediff,
                   s.ip_cc as ip_country_code,
                   --case when tle.account_id > 0 then 1 else 0 end as is_in_funnel,
                   maifs.traffic_source,
                   maifs.platform as mobile_platform_name
            from imp.account_contact ac
            join imp.email_account_interactions eai on eai.country = ac.country and eai.id_account = ac.id_account and eai.interaction_type = 0
            join imp.session s on s.country = eai.country and s.id = eai.id_session
            left join temp_first_launch_attribute maifs on  lower(ac.contact) = maifs.installation_id
            where ac.type = 2
              and ac.verify_date::date = _dt_date
              and s.flags & 64 = 64
              and ((s.flags & 1 = 0) or (s.flags & 1 = 1 and s.flags & 128 = 128));
 -------------------------


            -- Таблицю перезаписуємо за два попередні дні
            delete from mobile_app.session_account
            where session_datediff >= _dt_diff - 1;

            insert into mobile_app.session_account(country_id, cookie_label, session_datediff, session_id, account_id, impression_cnt,
                                                               impression_on_screen_cnt, serp_click_cnt, serp_away_cnt, subscription_cnt)
            select s.country as country_id,
                   s.cookie_label,
                   s.date_diff as session_datediff,
                   s.id as session_id,
                   min(sa.id_account) as account_id,
                   count(distinct si.id) as impression_cnt,
                   count(distinct sion.id_impression) as impression_on_screen_cnt,
                   count(distinct sc.id) as serp_click_cnt,
                   count(distinct saw.id) as serp_away_cnt,
                   count(distinct sal.sub_id_alert) as subscription_cnt
            from imp.session s
            join imp.session_account sa on sa.country = s.country and sa.date_diff = s.date_diff and sa.id_session = s.id
            join imp.account_contact ac on ac.country = sa.country and ac.id_account = sa.id_account and ac.type = 2 /*only mobile app accounts*/
            left join imp.session_alertview sal on sal.country = s.country and sal.date_diff = s.date_diff and sal.id_session = s.id
            left join imp.session_impression si on si.country = sal.country and si.date = sal.date_diff and si.id_session = sal.id_session and si.id_alertview = sal.id
            left join imp.session_impression_on_screen sion on sion.country = si.country and sion.date_diff = si.date and sion.id_impression = si.id
            left join imp.session_click sc on sc.country = sion.country and sc.date_diff = sion.date_diff and sc.id_impression = sion.id_impression
            left join imp.session_away saw on saw.country = sc.country and saw.date_diff = sc.date_diff and saw.id_click = sc.id
            where s.flags &64 = 64 /*mobile app*/ and s.flags &1 = 0 /*not bots*/ and
                  s.date_diff >= _dt_diff - 1
            group by s.country,
                     s.cookie_label,
                     s.date_diff,
                     s.id;
 -------------------------


             -- mobile_app.session_revenue

            insert into mobile_app.session_revenue(country_id, session_datediff, session_id, revenue_usd, revenue_type_id)
            select country_id,
                   session_datediff,
                   session_id,
                   sum(revenue_usd) as revenue_usd,
                   revenue_type_id
            from (
                     select s.country                             as country_id,
                            s.date_diff                           as session_datediff,
                            s.id                                  as session_id,
                            sum(sa.click_price * ic.value_to_usd) as revenue_usd,
                            1 /*internal statistics*/             as revenue_type_id
                     from imp.session_away sa
                              join imp.session s
                                   on sa.country = s.country and
                                      sa.date_diff = s.date_diff and
                                      sa.id_session = s.id
                              join dimension.info_currency ic
                                   on ic.country = sa.country and
                                      ic.id = sa.id_currency
                              left join imp.auction_campaign ac
                                        on ac.country_id = sa.country and
                                           ac.id = sa.id_campaign
                              left join imp.site ast
                                        on ac.country_id = ast.country and
                                           ac.id_site = ast.id
                              left join imp.auction_user au
                                        on au.country = ast.country and
                                           au.id = ast.id_user
                         -- serp -> away
                              left join imp.session_click sc
                                        on sc.country = sa.country and
                                           sc.date_diff = sa.date_diff and
                                           sc.id = sa.id_click
                     where sa.date_diff = _dt_diff
                       and s.flags & 64 = 64 /*mobile app*/
                       and s.flags & 1 = 0
                       and (sa.id_campaign = 0 or au.flags & 2 = 0)
                       and sa.flags & 2 = 0
                       and sa.flags & 512 = 0
                     group by s.id,
                              s.country,
                              s.date_diff

                     union all

                     select s.country                             as country_id,
                            s.date_diff                           as session_datediff,
                            s.id                                  as session_id,
                            sum(sc.click_price * ic.value_to_usd) as revenue_usd,
                            2 /*external statistics*/             as revenue_type_id
                     from imp.session_click sc
                              join imp.session s
                                   on sc.country = s.country and
                                      sc.date_diff = s.date_diff and
                                      sc.id_session = s.id
                              join dimension.info_currency ic
                                   on ic.country = sc.country and
                                      ic.id = sc.id_currency
                              left join imp.auction_campaign ac
                                        on ac.country_id = sc.country and
                                           ac.id = sc.id_campaign
                              left join imp.site ast
                                        on ac.country_id = ast.country and
                                           ac.id_site = ast.id
                              left join imp.auction_user au
                                        on au.country = ast.country and
                                           au.id = ast.id_user
                              left join imp.session_away sa
                                        on sa.country = sc.country and
                                           sc.date_diff = sa.date_diff and
                                           sc.id = sa.id_click
                     where sc.date_diff = _dt_diff
                       and s.flags & 64 = 64 /*mobile app*/
                       and s.flags & 1 = 0
                       and au.flags & 2 = 2
                       and sc.flags & 16 = 0
                       and sc.flags & 4096 = 0
                     group by s.id,
                              s.country,
                              s.date_diff

                     union all

                     select s.country                               as country_id,
                            s.date_diff                             as session_datediff,
                            s.id                                    as session_id,
                            sum(scns.click_price * ic.value_to_usd) as revenue_usd,
                            2 /*external statistics*/               as revenue_type_id
                     from imp.session_click_no_serp scns
                              join imp.session s
                                   on scns.country = s.country and
                                      scns.date_diff = s.date_diff and
                                      scns.id_session = s.id
                              join dimension.info_currency ic
                                   on ic.country = scns.country and
                                      ic.id = scns.id_currency
                              join imp.auction_campaign ac
                                   on ac.country_id = scns.country and
                                      ac.id = scns.id_campaign
                              join imp.site ast
                                   on ac.country_id = ast.country and
                                      ac.id_site = ast.id
                              join imp.auction_user au
                                   on au.country = ast.country and
                                      au.id = ast.id_user
                              left join imp.session_away sa
                                        on sa.country = scns.country and
                                           scns.date_diff = sa.date_diff and
                                           scns.id = sa.id_click_no_serp
                     where scns.date_diff = _dt_diff
                       and s.flags & 64 = 64 /*mobile app*/
                       and s.flags & 1 = 0
                       and au.flags & 2 = 2
                       and scns.flags & 16 = 0
                       and scns.flags & 4096 = 0
                     group by s.id,
                              s.country,
                              s.date_diff
                 ) t
            group by country_id, session_datediff, session_id, revenue_type_id;

-----------------------

end;$$;

alter procedure mobile_app.insert_mobile_application_data() owner to yiv;

