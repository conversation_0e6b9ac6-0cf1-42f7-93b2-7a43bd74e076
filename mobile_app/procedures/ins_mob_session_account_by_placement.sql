create or replace procedure mobile_app.ins_mob_session_account_by_placement()
    language plpgsql
as
$$
      --manual data selection
      declare _date_diff int = fn_get_date_diff(current_date - 1);

begin

            delete from mobile_app.session_account_by_placement
            where session_datediff = _date_diff;


            create temporary table temp_placement (placement integer);
            insert into
                temp_placement
            values
                (1), -- 'manual search'
                (2), -- 'homepage alertview'
                (3), -- 'homepage tab'
                (4), -- 'favorites'
                (0); -- 'other'

            /* mapping of placement id to name is stored in mobile_app.dic_placement */
            create temp table temp_session as
            select
                s.country          as country_id,
                s.cookie_label,
                s.date_diff        as session_datediff,
                s.id               as session_id,
                min(sa.id_account) as account_id
            from
                imp.session s
                join imp.session_account sa
                     on sa.country = s.country and
                        sa.date_diff = s.date_diff and
                        sa.id_session = s.id
                join imp.account_contact ac
                     on ac.country = sa.country and
                        ac.id_account = sa.id_account and
                        ac.type = 2 /*only mobile app accounts*/
            where
                  s.flags & 64 = 64 /*mobile app*/
              and s.flags & 1 = 0 /*not bots*/
              and s.date_diff = _date_diff
            group by
                s.country, s.cookie_label, s.date_diff, s.id;

            create temp table temp_searches as
            select
                country_id,
                session_datediff,
                session_id,
                case
                    when (ss.search_source = 128 and ss.date_diff < fn_get_date_diff('2022-11-24')) or ss.search_source = 1004
                        then 1
                    when ss.search_source = 1001 then 2
                    when ss.search_source = 1003 then 3
                    when ss.search_source = 1002 then 4
                    else 0 end as placement,
                ss.id          as id_search,
                null::bigint   as id_alertview
            from
                temp_session s
                join imp.session_search ss
                     on ss.country = s.country_id and
                        ss.date_diff = s.session_datediff and
                        ss.id_session = s.session_id;

            insert into
                temp_searches
            select
                country_id,
                session_datediff,
                session_id,
                case
                    when sal.service_flags & 536870912 = 536870912 then 2
                    else 0 end as placement,
                null::bigint   as id_search,
                sal.id         as id_alertview
            from
                temp_session s
                join imp.session_alertview sal
                     on sal.country = s.country_id and
                        sal.date_diff = s.session_datediff and
                        sal.id_session = s.session_id;

            create temp table temp_searches_agg as
            select
                s.country_id,
                s.cookie_label,
                s.session_datediff,
                s.session_id,
                coalesce(ts.placement, 0)                                      as placement,
                s.account_id,
                count(distinct ts.id_alertview) + count(distinct ts.id_search) as alertview_search_cnt
            from
                temp_session s
                left join temp_searches ts
                          on ts.country_id = s.country_id and
                             ts.session_datediff = s.session_datediff and
                             ts.session_id = s.session_id
            group by
                s.country_id,
                s.cookie_label,
                s.session_datediff,
                s.session_id,
                coalesce(ts.placement, 0),
                s.account_id;

            create temp table temp_clicks_away as
            select
                country_id,
                session_datediff,
                session_id,
                coalesce(sc.id_search, si.id_search)                        as id_search,
                coalesce(sc.id_alertview, si.id_alertview)                  as id_alertview,
                count(distinct sc.id) as serp_click_cnt,
                count(distinct sa.id) as serp_away_cnt
            from
                temp_session s
                join imp.session_click sc
                     on sc.country = s.country_id and
                        sc.date_diff = s.session_datediff and
                        sc.id_session = s.session_id
                left join imp.session_impression si
                     on si.country = sc.country and
                        si.date = sc.date_diff and
                        si.id = sc.id_impression
                left join imp.session_away sa
                          on sa.country = sc.country and
                             sa.date_diff = sc.date_diff and
                             sa.id_click = sc.id
            group by
                country_id,
                session_datediff,
                session_id,
                coalesce(sc.id_search, si.id_search),
                coalesce(sc.id_alertview, si.id_alertview);


            create temp table temp_clicks_jdp as
            select
                country_id,
                session_datediff,
                session_id,
                coalesce(sc.id_search, si.id_search)                        as id_search,
                coalesce(sc.id_alertview, si.id_alertview)                  as id_alertview,
                count(distinct case when sc.flags & 32 = 32 then sc.id end) as serp_jdp_cnt,
                count(distinct sja.id)                                      as jdp_away_cnt
            from
                temp_session s
                join imp.session_click sc
                     on sc.country = s.country_id and
                        sc.date_diff = s.session_datediff and
                        sc.id_session = s.session_id
                left join imp.session_impression si
                          on si.country = sc.country and
                             si.date = sc.date_diff and
                             si.id = sc.id_impression
                left join imp.session_jdp sj
                          on sj.country = sc.country and
                             sj.date_diff = sc.date_diff and
                             sj.id_click = sc.id
                left join imp.session_away sja
                          on sja.country = sj.country and
                             sja.date_diff = sj.date_diff and
                             sja.id_jdp = sj.id
            group by
                country_id,
                session_datediff,
                session_id,
                coalesce(sc.id_search, si.id_search),
                coalesce(sc.id_alertview, si.id_alertview);


            create temp table temp_clicks_away_agg as
            select
                tc.country_id,
                tc.session_datediff,
                tc.session_id,
                coalesce(ts.placement, 0) as placement,
                sum(serp_click_cnt)       as serp_click_cnt,
                sum(serp_away_cnt)        as serp_away_cnt
            from
                temp_clicks_away tc
                left join temp_searches ts
                          on tc.country_id = ts.country_id and
                             tc.session_datediff = ts.session_datediff and
                             (tc.id_search = ts.id_search or tc.id_alertview = ts.id_alertview)
            group by
                tc.country_id,
                tc.session_datediff,
                tc.session_id,
                coalesce(ts.placement, 0);


            create temp table temp_clicks_jdp_agg as
            select
                tc.country_id,
                tc.session_datediff,
                tc.session_id,
                coalesce(ts.placement, 0) as placement,
                sum(serp_jdp_cnt)         as serp_jdp_cnt,
                sum(jdp_away_cnt)         as jdp_away_cnt
            from
                temp_clicks_jdp tc
                left join temp_searches ts
                          on tc.country_id = ts.country_id and
                             tc.session_datediff = ts.session_datediff and
                             (tc.id_search = ts.id_search or tc.id_alertview = ts.id_alertview)
            group by
                tc.country_id,
                tc.session_datediff,
                tc.session_id,
                coalesce(ts.placement, 0);


            create temp table temp_clicks_no_serp_agg as
            select
                country_id,
                session_datediff,
                session_id,
                case when scns.click_flags & 4096 = 4096 then 4 else 0 end      as placement,
                count(distinct scns.id)                                         as serp_click_cnt,
                count(distinct sa.id)                                           as serp_away_cnt,
                count(distinct case when scns.flags & 32 = 32 then scns.id end) as serp_jdp_cnt,
                count(distinct sja.id)                                          as jdp_away_cnt
            from
                temp_session s
                join imp.session_click_no_serp scns
                     on scns.country = s.country_id and
                        scns.date_diff = s.session_datediff and
                        scns.id_session = s.session_id
                left join imp.session_away sa
                          on sa.country = scns.country and
                             sa.date_diff = scns.date_diff and
                             sa.id_click_no_serp = scns.id
                left join imp.session_jdp sj
                          on sj.country = scns.country and
                             sj.date_diff = scns.date_diff and
                             sj.id_click_no_serp = scns.id
                left join imp.session_away sja
                          on sja.country = sj.country and
                             sja.date_diff = sj.date_diff and
                             sja.id_jdp = sj.id
            group by
                country_id,
                session_datediff,
                session_id,
                case when scns.click_flags & 4096 = 4096 then 4 else 0 end;


            create temp table temp_impressions as
            select
                country_id,
                session_datediff,
                session_id,
                si.id_search,
                si.id_alertview,
                count(distinct si.id)              as impression_cnt,
                count(distinct sios.id_impression) as impression_on_screen_cnt
            from
                temp_session s
                join imp.session_impression si
                     on si.country = s.country_id and
                        si.date = s.session_datediff and
                        si.id_session = s.session_id
                left join imp.session_impression_on_screen sios
                          on si.country = sios.country and
                             si.date = sios.date_diff and
                             si.id = sios.id_impression
            group by
                country_id,
                session_datediff,
                session_id,
                si.id_search,
                si.id_alertview;


            create temp table temp_impressions_agg as
            select
                ti.country_id,
                ti.session_datediff,
                ti.session_id,
                coalesce(ts.placement, 0)     as placement,
                sum(impression_cnt)           as impression_cnt,
                sum(impression_on_screen_cnt) as impression_on_screen_cnt
            from
                temp_impressions ti
                left join temp_searches ts
                          on ti.country_id = ts.country_id and
                             ti.session_datediff = ts.session_datediff and
                             (ti.id_search = ts.id_search or ti.id_alertview = ts.id_alertview)
            group by
                ti.country_id,
                ti.session_datediff,
                ti.session_id,
                coalesce(ts.placement, 0);


            create temp table temp_result as
            select
                t.country_id,
                t.cookie_label,
                t.session_datediff,
                t.session_id,
                t.placement,
                t.account_id,
                sum(coalesce(alertview_search_cnt, 0))                          as alertview_search_cnt,
                sum(coalesce(impression_cnt, 0))                                as impression_cnt,
                sum(coalesce(impression_on_screen_cnt, 0))                      as impression_on_screen_cnt,
                sum(coalesce(tc.serp_click_cnt, 0)) + sum(coalesce(tcnsa.serp_click_cnt, 0)) as serp_click_cnt,
                sum(coalesce(tjc.serp_jdp_cnt, 0)) + sum(coalesce(tcnsa.serp_jdp_cnt, 0))    as serp_jdp_cnt,
                sum(coalesce(tc.serp_away_cnt, 0)) + sum(coalesce(tcnsa.serp_away_cnt, 0))   as serp_away_cnt,
                sum(coalesce(tjc.jdp_away_cnt, 0)) + sum(coalesce(tcnsa.jdp_away_cnt, 0))    as jdp_away_cnt
            from
                (
                    select
                        ts.country_id,
                        ts.cookie_label,
                        ts.session_datediff,
                        ts.session_id,
                        tp.placement,
                        ts.account_id
                    from
                        temp_session ts
                        cross join temp_placement tp
                ) t
                left join temp_searches_agg ts
                          on ts.country_id = t.country_id and
                             ts.session_datediff = t.session_datediff and
                             ts.session_id = t.session_id and
                             ts.placement = t.placement
                left join temp_clicks_away_agg tc
                          on t.country_id = tc.country_id and
                             t.session_datediff = tc.session_datediff and
                             t.session_id = tc.session_id and
                             t.placement = tc.placement
                left join temp_clicks_jdp_agg tjc
                          on t.country_id = tjc.country_id and
                             t.session_datediff = tjc.session_datediff and
                             t.session_id = tjc.session_id and
                             t.placement = tjc.placement
                left join temp_impressions_agg ti
                          on t.country_id = ti.country_id and
                             t.session_datediff = ti.session_datediff and
                             t.session_id = ti.session_id and
                             t.placement = ti.placement
                left join temp_clicks_no_serp_agg tcnsa
                          on t.country_id = tcnsa.country_id and
                             t.session_datediff = tcnsa.session_datediff and
                             t.session_id = tcnsa.session_id and
                             t.placement = tcnsa.placement
            group by
                t.country_id,
                t.cookie_label,
                t.session_datediff,
                t.session_id,
                t.placement,
                t.account_id;


            insert into
                mobile_app.session_account_by_placement(country_id, cookie_label, session_datediff, session_id, placement,
                                                        account_id, alertview_search_cnt, impression_cnt,
                                                        impression_on_screen_cnt, serp_click_cnt, serp_jdp_cnt,
                                                        serp_away_cnt, jdp_away_cnt)
            select
                country_id,
                cookie_label,
                session_datediff,
                session_id,
                placement,
                account_id,
                coalesce(alertview_search_cnt, 0),
                coalesce(impression_cnt, 0),
                coalesce(impression_on_screen_cnt, 0),
                coalesce(serp_click_cnt, 0),
                coalesce(serp_jdp_cnt, 0),
                coalesce(serp_away_cnt, 0),
                coalesce(jdp_away_cnt, 0)
            from
                temp_result
            where
                    (coalesce(alertview_search_cnt, 0) +
                     coalesce(impression_cnt, 0) +
                     coalesce(impression_on_screen_cnt, 0) +
                     coalesce(serp_click_cnt, 0) +
                     coalesce(serp_jdp_cnt, 0) +
                     coalesce(serp_away_cnt, 0) +
                     coalesce(jdp_away_cnt, 0)) > 0;

drop table temp_session;
drop table temp_result;
drop table temp_searches;
drop table temp_placement;
drop table temp_searches_agg;
drop table temp_clicks_away;
drop table temp_clicks_jdp;
drop table temp_clicks_away_agg;
drop table temp_clicks_jdp_agg;
drop table temp_clicks_no_serp_agg;
drop table temp_impressions;
drop table temp_impressions_agg;



end;$$;

alter procedure mobile_app.ins_mob_session_account_by_placement() owner to yiv;

