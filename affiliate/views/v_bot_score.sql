create or replace view affiliate.v_bot_score (publisher, date, range, bot_score, clicks)
as
with
    dic_range as (select
                      '0-9' as range
                  union all
                  select
                      '10-19' as range
                  union all
                  select
                      '20-29' as range
                  union all
                  select
                      '30-39' as range
                  union all
                  select
                      '40-49' as range
                  union all
                  select
                      '50-59' as range
                  union all
                  select
                      '60-69' as range
                  union all
                  select
                      '70-79' as range
                  union all
                  select
                      '80-89' as range
                  union all
                  select
                      '90-99' as range),
    data as (select
                 ps.partner::text         as publisher,
                 cld.click_datetime::date as date,
                 case
                     when cld.bot_score between 0 and 9 then '0-9'
                     when cld.bot_score between 10 and 19 then '10-19'
                     when cld.bot_score between 20 and 29 then '20-29'
                     when cld.bot_score between 30 and 39 then '30-39'
                     when cld.bot_score between 40 and 49 then '40-49'
                     when cld.bot_score between 50 and 59 then '50-59'
                     when cld.bot_score between 60 and 69 then '60-69'
                     when cld.bot_score between 70 and 79 then '70-79'
                     when cld.bot_score between 80 and 89 then '80-89'
                     when cld.bot_score between 90 and 99 then '90-99'
                     end                  as range,
                 avg(bot_score)           as bot_score,
                 count(1)                 as clicks
             from
                 affiliate.cloudflare_log_data cld
                 join affiliate.partner_settings as ps
                      on lower(cld.utm_medium::text) = lower(ps.partner::text)
             where cld.click_datetime::date >= (current_date - '3 month'::interval)::date
                   and record_type = 1
             group by publisher, cld.click_datetime::date, range),

    all_values as (select distinct
                       d.publisher,
                       d.date,
                       r.range
                   from
                       data as d
                       cross join dic_range as r)

select
    coalesce(r.publisher, av.publisher) as publisher,
    coalesce(r.date, av.date)           as date,
    coalesce(r.range, av.range)         as range,
    coalesce(r.bot_score, 0)            as bot_score,
    coalesce(r.clicks, 0)               as clicks
from
    data as r
    full join all_values as av
              on av.publisher = r.publisher and av.date = r.date and av.range = r.range;


alter table affiliate.v_bot_score
    owner to ypr;

grant select on affiliate.v_bot_score to readonly;

grant delete, insert, select, update on affiliate.v_bot_score to writeonly_pyscripts;
