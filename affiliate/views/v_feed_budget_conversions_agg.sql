create or replace view affiliate.v_feed_budget_conversions_agg
            (date, country, publisher, id_local_partner, min_cpc_in_usd, manager, id_project, project_name,
             is_job_ad_exchange_project, id_user, client_currency, is_unlim_cmp, in_affiliate_ban, daily_budget_mark,
             campaign_cnt, campaign_with_budget_cnt, click_cnt, revenue_usd, potential_revenue_usd, affiliate_revenue,
             user_budget_month_usd, campaign_budget_month_usd, budget, cnt_gather, avg_click_price_usd,
             avg_feed_cpc_usd, uid_count, conversion_type, conversion_away_revenue,
             conversion_away_revenue_client_currency, conversion_aways, conversions, total_project_away_revenue,
             total_project_away_revenue_client_currency, total_project_aways, total_project_conversions, target_cpa_usd,
             target_cpa_client_currency, total_project_target_cpa_usd, total_project_target_cpa_client_currency,
             certified_click_cnt, certified_cost_usd, cpc_ratio, cs_manager, target_action)
as
with
    budget_revenue_daily_agg as (
        select
            brda.country_id,
            brda.action_date,
            brda.user_id,
            lower(brda.country_code::text)::character varying                  as country_code,
            brda.project_id,
            max(brda.is_unlim_cmp)                                             as is_unlim_cmp,
            sum(brda.click_cnt)                                                as click_cnt,
            sum(
                    case
                        when brda.action_date < '2023-04-28'::date then
                                coalesce(brda.revenue_usd, 0::double precision) -
                                coalesce(brda.dublicated_click_revenue_usd, 0::numeric)::double precision
                        else brda.revenue_usd
                        end)                                                   as revenue_usd,
            sum(brda.revenue_usd -
                case
                    when brda.action_date < '2023-04-28'::date
                        then coalesce(brda.dublicated_click_revenue_usd, 0::numeric)::double precision
                    else 0::double precision
                    end + (brda.revenue_usd -
                           case
                               when brda.action_date < '2023-04-28'::date
                                   then coalesce(brda.dublicated_click_revenue_usd, 0::numeric)::double precision
                               else 0::double precision
                               end) / date_part('day'::text, current_date - 1) *
                          (make_date(date_part('year'::text, current_date - 1 + '1 mon'::interval)::integer,
                                     date_part('month'::text, current_date - 1 + '1 mon'::interval)::integer, 1) -
                           (current_date - 1) - 1)::double precision)          as potential_revenue_usd,
            max(brda.user_budget_month_usd)                                    as user_budget_month_usd,
            sum(coalesce(brda.campaign_budget_month_usd, 0::double precision)) as campaign_budget_month_usd,
            count(distinct brda.id_campaign)                                   as campaign_cnt,
            count(distinct
                  case
                      when coalesce(brda.campaign_budget_month_usd, 0::double precision) > 0::double precision
                          then brda.id_campaign
                      else null::character varying
                      end)                                                     as campaign_with_budget_cnt,
            max(
                    case
                        when brda.is_cmp_with_daily_budget = 1 and brda.is_user_with_daily_budget = 1 then 3
                        when brda.is_cmp_with_daily_budget = 1 then 1
                        when brda.is_user_with_daily_budget = 1 then 2
                        else 0
                        end)                                                   as daily_budget_mark,
            sum(brda.affiliate_revenue)                                        as affiliate_revenue
        from
            aggregation.budget_revenue_daily_agg brda
        where
                brda.action_date >=
                case
                    when current_date = date_trunc('month'::text, current_date::timestamp with time zone)::date then (
                            date_trunc('month'::text, current_date::timestamp with time zone)::date - '1 mon'::interval)::date
                    else date_trunc('month'::text, current_date::timestamp with time zone)::date
                    end
        group by brda.action_date, brda.user_id, (lower(brda.country_code::text)), brda.project_id, brda.country_id
    ),
    budgets_final as (
        select
            t.action_date      as date,
            t.user_id          as id_user,
            t.country_code     as country,
            t.project_id       as id_project,
            ip.name            as project_name,
            info_currency.name as client_currency,
            t.is_unlim_cmp,
            t.click_cnt,
            t.revenue_usd,
            t.potential_revenue_usd,
            t.user_budget_month_usd,
            t.campaign_budget_month_usd,
            case
                when ip.hide_in_search then 1
                else 0
                end            as is_job_ad_exchange_project,
            case
                when general_feed_ignored_projects.id_project is not null then 1
                else 0
                end            as in_affiliate_ban,
            t.campaign_cnt,
            t.campaign_with_budget_cnt,
            case
                when t.daily_budget_mark = 3 then 'User and campaign daily budgets'::text
                when t.daily_budget_mark = 1 then 'Campaign daily budget'::text
                when t.daily_budget_mark = 2 then 'User daily budget'::text
                else 'Without daily budget'::text
                end            as daily_budget_mark,
            t.affiliate_revenue,
            case
                when t.campaign_cnt = t.campaign_with_budget_cnt and
                     t.campaign_budget_month_usd > 0::double precision and
                     t.campaign_budget_month_usd < t.user_budget_month_usd then t.campaign_budget_month_usd
                when t.is_unlim_cmp = 1 and t.user_budget_month_usd > t.campaign_budget_month_usd
                    then t.user_budget_month_usd
                when t.user_budget_month_usd = 0::double precision then t.campaign_budget_month_usd
                else t.user_budget_month_usd
                end            as budget,
            vsm.sale_manager   as cs_manager
        from
            budget_revenue_daily_agg t
            left join imp.auction_user
                      on t.country_id = auction_user.country and t.user_id = auction_user.id
            left join dimension.info_currency
                      on auction_user.country = info_currency.country and auction_user.currency = info_currency.id
            left join dimension.info_project ip
                      on ip.country = t.country_id::integer and ip.id = t.project_id
            left join imp.general_feed_ignored_projects
                      on general_feed_ignored_projects.country_id = t.country_id::integer and
                         general_feed_ignored_projects.id_project = t.project_id
            left join aggregation.v_sale_manager vsm
                      on t.country_id = vsm.country and t.project_id = vsm.id_project
    ),
    project_currency as (
        select
            p.country,
            p.id    as id_project,
            ic.name as client_currency
        from
            dimension.info_project p
            join imp.site s
                 on s.country = p.country and s.id_project = p.id and s.site_status = 0
            join imp.auction_user au
                 on au.country = s.country and au.id = s.id_user and (au.flags & 32) = 0 and (au.flags & 8) = 8
            join dimension.info_currency ic
                 on ic.country = au.country and ic.id = au.currency
    ),
    job_stat_final as (
        select
            ps.country,
            ps.partner                                                                                  as publisher,
            jsad.id_partner                                                                             as id_local_partner,
            ps.min_cpc_in_usd,
            jsad.date,
            jsad.id_project,
            p.name                                                                                      as project,
            pc.client_currency,
            case
                when p.hide_in_search then 1
                else 0
                end                                                                                     as is_job_ad_exchange_project,
            m.code                                                                                      as manager,
            jsad.cnt_gather,
            sum(jsad.total_jobs_count::numeric * jsad.avg_click_price_usd) /
            sum(jsad.total_jobs_count)                                                                  as avg_click_price_usd,
            sum(jsad.total_jobs_count::numeric * jsad.avg_feed_cpc_usd) /
            sum(jsad.total_jobs_count)                                                                  as avg_feed_cpc_usd,
            sum(jsad.total_jobs_count)                                                                  as uid_count,
            ps.cpc_ratio,
            vsm.sale_manager                                                                            as cs_manager
        from
            affiliate.job_stat_agg_daily jsad
            join affiliate.partner_settings ps
                 on ps.source_id = jsad.id_source and ps.id_local_partner = jsad.id_partner
            join dimension.countries c
                 on c.id = jsad.id_country
            left join affiliate.dic_manager m
                      on m.id = ps.id_manager
            left join dimension.info_project p
                      on p.country = jsad.id_country and p.id = jsad.id_project
            left join project_currency pc
                      on pc.country = jsad.id_country and pc.id_project = jsad.id_project
            left join aggregation.v_sale_manager vsm
                      on c.id = vsm.country and jsad.id_project = vsm.id_project
        where
                jsad.date >= (date_trunc('month'::text, current_date::timestamp with time zone) -
                              2::double precision * '1 mon'::interval)::date
        group by
            ps.country, ps.partner, jsad.id_partner, ps.min_cpc_in_usd, jsad.date, jsad.id_project, p.name,
            pc.client_currency,
            (
                case
                    when p.hide_in_search then 1
                    else 0
                    end), m.code, jsad.cnt_gather, ps.cpc_ratio, vsm.sale_manager
    ),
    internal_conversion_data as (
        select
            vpcbs.record_date     as session_date,
            vpcbs.id_project      as project_id,
            vpcbs.id_campaign     as campaign_id,
            vpcbs.project_name,
            case
                when p.hide_in_search then 1
                else 0
                end               as is_job_ad_exchange_project,
            vpcbs.traffic_source  as traffic_name,
            vpcbs.traffic_channel as channel,
            vpcbs.away_revenue,
            vpcbs.away_revenue_client_currency,
            vpcbs.aways,
            vpcbs.conversions,
            vpcbs.country,
            vpcbs.tracking_type   as type,
            vpcbs.country_id,
            vpcbs.target_action,
            vpcbs.target_cpa_usd,
            vpcbs.target_cpa_client_currency
        from
            affiliate.v_project_conversions_by_source vpcbs
            left join dimension.info_project p
                      on p.country = vpcbs.country_id and p.id = vpcbs.id_project
        where
                vpcbs.record_date >= (date_trunc('month'::text, current_date::timestamp with time zone) -
                                      2::double precision * '1 mon'::interval)::date
    ),
    internal_conversions_final as (
        select
            t.session_date,
            t.country_id,
            t.project_id,
            t.project_name,
            t.is_job_ad_exchange_project,
            t.campaign_id,
            info_currency.name                                                                     as client_currency,
            t.traffic_name,
            t.channel,
            t.away_revenue,
            t.away_revenue_client_currency,
            t.aways,
            t.conversions,
            t.country,
            t.type,
            t.target_action,
            t.target_cpa_usd,
            t.target_cpa_client_currency,
            sum(t.away_revenue)
            over (partition by t.session_date, t.project_id, t.project_name, t.country_id, t.type) as total_project_away_revenue,
            sum(t.away_revenue_client_currency)
            over (partition by t.session_date, t.project_id, t.project_name, t.country_id, t.type) as total_project_away_revenue_client_currency,
            sum(t.aways)
            over (partition by t.session_date, t.project_id, t.project_name, t.country_id, t.type) as total_project_aways,
            sum(t.conversions)
            over (partition by t.session_date, t.project_id, t.project_name, t.country_id, t.type) as total_project_conversions,
            sum(t.aways::double precision * t.target_cpa_client_currency)
            over (partition by t.session_date, t.project_id, t.project_name, t.country_id, t.type) as total_project_avg_cpa_numerator,
            sum(t.aways::double precision * t.target_cpa_usd)
            over (partition by t.session_date, t.project_id, t.project_name, t.country_id, t.type) as total_project_avg_cpa_usd_numerator,
            sum(
            case
                when t.target_cpa_client_currency > 0::double precision then t.aways
                else null::bigint
                end)
            over (partition by t.session_date, t.project_id, t.project_name, t.country_id, t.type) as total_project_cpa_denominator
        from
            internal_conversion_data t
            left join imp.auction_campaign ac
                      on ac.country_id = t.country_id and ac.id = t.campaign_id
            left join dimension.info_currency
                      on info_currency.country = ac.country_id and info_currency.id = ac.currency
    ),
    conversions_final as (
        select
            r.session_date,
            r.project_id,
            r.project_name,
            r.is_job_ad_exchange_project,
            r.campaign_id,
            r.traffic_name,
            r.client_currency,
            ps.id_local_partner,
            ps.min_cpc_in_usd,
            r.channel,
            r.away_revenue,
            r.away_revenue_client_currency,
            r.aways,
            r.conversions,
            lower(r.country::text) as country,
            r.type,
            m.code                 as manager,
            r.total_project_away_revenue,
            r.total_project_away_revenue_client_currency,
            r.total_project_aways,
            r.total_project_conversions,
            r.target_cpa_usd,
            r.target_cpa_client_currency,
            case
                when r.total_project_cpa_denominator > 0::numeric then r.total_project_avg_cpa_usd_numerator /
                                                                       r.total_project_cpa_denominator::double precision
                else null::double precision
                end                as total_project_target_cpa_usd,
            case
                when r.total_project_cpa_denominator > 0::numeric then r.total_project_avg_cpa_numerator /
                                                                       r.total_project_cpa_denominator::double precision
                else null::double precision
                end                as total_project_target_cpa_client_currency,
            ps.cpc_ratio,
            vsm.sale_manager       as cs_manager,
            r.target_action
        from
            internal_conversions_final r
            left join affiliate.partner_settings ps
                      on ps.country::text = lower(r.country::text) and ps.partner::text = r.traffic_name::text
            left join affiliate.dic_manager m
                      on m.id = ps.id_manager
            left join aggregation.v_sale_manager vsm
                      on r.country_id = vsm.country and r.project_id = vsm.id_project
    ),
    certified_clicks_final as (
        select
            vmma.click_date,
            lower(vmma.country::text)     as country,
            vmma.publisher,
            ps.id_local_partner,
            ps.min_cpc_in_usd,
            m.code                        as manager,
            vmma.id_project               as project_id,
            p.name                        as project_name,
            case
                when p.hide_in_search then 1
                else 0
                end                       as is_job_ad_exchange_project,
            sum(vmma.certified_click_cnt) as certified_click_cnt,
            sum(vmma.certified_cost_usd)  as certified_cost_usd,
            ps.cpc_ratio,
            vsm.sale_manager              as cs_manager
        from
            affiliate.v_main_metrics_agg vmma
            left join affiliate.partner_settings ps
                      on ps.country::text = lower(vmma.country::text) and ps.partner::text = vmma.publisher::text
            left join affiliate.dic_manager m
                      on m.id = ps.id_manager
            left join dimension.info_project p
                      on p.country = vmma.id_country and p.id = vmma.id_project
            left join aggregation.v_sale_manager vsm
                      on vmma.id_country = vsm.country and vmma.id_project = vsm.id_project
        where
                vmma.click_date >= (date_trunc('month'::text, current_date::timestamp with time zone) -
                                    2::double precision * '1 mon'::interval)::date
        group by
            vmma.click_date, (lower(vmma.country::text)), vmma.publisher, ps.id_local_partner, ps.min_cpc_in_usd,
            m.code, vmma.id_project, p.name,
            (
                case
                    when p.hide_in_search then 1
                    else 0
                    end), ps.cpc_ratio, vsm.sale_manager
    )
select
    b.date,
    b.country,
    null::character varying as publisher,
    null::integer           as id_local_partner,
    null::numeric           as min_cpc_in_usd,
    null::character varying as manager,
    b.id_project,
    b.project_name,
    b.is_job_ad_exchange_project,
    b.id_user,
    b.client_currency,
    b.is_unlim_cmp,
    b.in_affiliate_ban,
    b.daily_budget_mark,
    b.campaign_cnt,
    b.campaign_with_budget_cnt,
    b.click_cnt,
    b.revenue_usd,
    b.potential_revenue_usd,
    b.affiliate_revenue,
    b.user_budget_month_usd,
    b.campaign_budget_month_usd,
    b.budget,
    null::integer           as cnt_gather,
    null::numeric           as avg_click_price_usd,
    null::numeric           as avg_feed_cpc_usd,
    null::bigint            as uid_count,
    null::character varying as conversion_type,
    null::numeric           as conversion_away_revenue,
    null::numeric           as conversion_away_revenue_client_currency,
    null::bigint            as conversion_aways,
    null::bigint            as conversions,
    null::numeric           as total_project_away_revenue,
    null::numeric           as total_project_away_revenue_client_currency,
    null::bigint            as total_project_aways,
    null::bigint            as total_project_conversions,
    null::numeric           as target_cpa_usd,
    null::numeric           as target_cpa_client_currency,
    null::numeric           as total_project_target_cpa_usd,
    null::numeric           as total_project_target_cpa_client_currency,
    null::bigint            as certified_click_cnt,
    null::numeric           as certified_cost_usd,
    null::numeric           as cpc_ratio,
    b.cs_manager,
    null::character varying as target_action
from
    budgets_final b
union all
select
    j.date,
    j.country,
    j.publisher,
    j.id_local_partner,
    j.min_cpc_in_usd,
    j.manager,
    j.id_project,
    j.project               as project_name,
    j.is_job_ad_exchange_project,
    null::bigint            as id_user,
    j.client_currency,
    null::bigint            as is_unlim_cmp,
    null::integer           as in_affiliate_ban,
    null::text              as daily_budget_mark,
    null::bigint            as campaign_cnt,
    null::bigint            as campaign_with_budget_cnt,
    null::numeric           as click_cnt,
    null::double precision  as revenue_usd,
    null::double precision  as potential_revenue_usd,
    null::numeric           as affiliate_revenue,
    null::double precision  as user_budget_month_usd,
    null::double precision  as campaign_budget_month_usd,
    null::double precision  as budget,
    j.cnt_gather,
    j.avg_click_price_usd,
    j.avg_feed_cpc_usd,
    j.uid_count,
    null::character varying as conversion_type,
    null::numeric           as conversion_away_revenue,
    null::numeric           as conversion_away_revenue_client_currency,
    null::bigint            as conversion_aways,
    null::bigint            as conversions,
    null::numeric           as total_project_away_revenue,
    null::numeric           as total_project_away_revenue_client_currency,
    null::bigint            as total_project_aways,
    null::bigint            as total_project_conversions,
    null::numeric           as target_cpa_usd,
    null::numeric           as target_cpa_client_currency,
    null::numeric           as total_project_target_cpa_usd,
    null::numeric           as total_project_target_cpa_client_currency,
    null::bigint            as certified_click_cnt,
    null::numeric           as certified_cost_usd,
    j.cpc_ratio,
    j.cs_manager,
    null::character varying as target_action
from
    job_stat_final j
union all
select
    c.session_date                 as date,
    c.country,
    c.traffic_name                 as publisher,
    c.id_local_partner,
    c.min_cpc_in_usd,
    c.manager,
    c.project_id                   as id_project,
    c.project_name,
    c.is_job_ad_exchange_project,
    null::bigint                   as id_user,
    c.client_currency,
    null::bigint                   as is_unlim_cmp,
    null::integer                  as in_affiliate_ban,
    null::text                     as daily_budget_mark,
    null::bigint                   as campaign_cnt,
    null::bigint                   as campaign_with_budget_cnt,
    null::numeric                  as click_cnt,
    null::double precision         as revenue_usd,
    null::double precision         as potential_revenue_usd,
    null::numeric                  as affiliate_revenue,
    null::double precision         as user_budget_month_usd,
    null::double precision         as campaign_budget_month_usd,
    null::double precision         as budget,
    null::integer                  as cnt_gather,
    null::numeric                  as avg_click_price_usd,
    null::numeric                  as avg_feed_cpc_usd,
    null::bigint                   as uid_count,
    c.type                         as conversion_type,
    c.away_revenue                 as conversion_away_revenue,
    c.away_revenue_client_currency as conversion_away_revenue_client_currency,
    c.aways::bigint                as conversion_aways,
    c.conversions::bigint          as conversions,
    c.total_project_away_revenue,
    c.total_project_away_revenue_client_currency,
    c.total_project_aways,
    c.total_project_conversions,
    c.target_cpa_usd,
    c.target_cpa_client_currency,
    c.total_project_target_cpa_usd,
    c.total_project_target_cpa_client_currency,
    null::bigint                   as certified_click_cnt,
    null::numeric                  as certified_cost_usd,
    c.cpc_ratio,
    c.cs_manager,
    c.target_action
from
    conversions_final c
union all
select
    cc.click_date           as date,
    cc.country,
    cc.publisher,
    cc.id_local_partner,
    cc.min_cpc_in_usd,
    cc.manager,
    cc.project_id           as id_project,
    cc.project_name,
    cc.is_job_ad_exchange_project,
    null::bigint            as id_user,
    null::character varying as client_currency,
    null::bigint            as is_unlim_cmp,
    null::integer           as in_affiliate_ban,
    null::text              as daily_budget_mark,
    null::bigint            as campaign_cnt,
    null::bigint            as campaign_with_budget_cnt,
    null::numeric           as click_cnt,
    null::double precision  as revenue_usd,
    null::double precision  as potential_revenue_usd,
    null::numeric           as affiliate_revenue,
    null::double precision  as user_budget_month_usd,
    null::double precision  as campaign_budget_month_usd,
    null::double precision  as budget,
    null::integer           as cnt_gather,
    null::numeric           as avg_click_price_usd,
    null::numeric           as avg_feed_cpc_usd,
    null::bigint            as uid_count,
    null::character varying as conversion_type,
    null::numeric           as conversion_away_revenue,
    null::numeric           as conversion_away_revenue_client_currency,
    null::bigint            as conversion_aways,
    null::bigint            as conversions,
    null::numeric           as total_project_away_revenue,
    null::numeric           as total_project_away_revenue_client_currency,
    null::bigint            as total_project_aways,
    null::bigint            as total_project_conversions,
    null::numeric           as target_cpa_usd,
    null::numeric           as target_cpa_client_currency,
    null::numeric           as total_project_target_cpa_usd,
    null::numeric           as total_project_target_cpa_client_currency,
    cc.certified_click_cnt,
    cc.certified_cost_usd,
    cc.cpc_ratio,
    cc.cs_manager,
    null::character varying as target_action
from
    certified_clicks_final cc;

alter table affiliate.v_feed_budget_conversions_agg
    owner to ypr;

grant select on affiliate.v_feed_budget_conversions_agg to readonly;

grant select on affiliate.v_feed_budget_conversions_agg to math;

grant delete, insert, select, update on affiliate.v_feed_budget_conversions_agg to writeonly_pyscripts;

