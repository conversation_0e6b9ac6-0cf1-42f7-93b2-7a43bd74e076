create or replace view affiliate.v_click_data
            (publisher, feed_type, id_country, country, date_diff, click_date, id_click, id_external_init, id_external,
             uid_job, click_type, job_click_type, click_datetime, user_ip, user_country, feed_gather_datetime,
             cpc_change_datetime, job_inactive_datetime, feed_cpc_publisher_currency, feed_cpc_usd,
             publisher_sub_source, pub_tracking_id, affiliate_flags, is_redirected_click, id_project, id_campaign,
             feed_id_campaign, feed_id_project, client_cpc_usd, client_cpc, has_postback, conversion_count,
             is_client_gap_click, jdp_away_cnt, api_request_id, id_category, is_conversion_considered, target_action,
             job_title_hash, user_agent_hash, feed_cpa_publisher_currency, feed_cpa_usd, url_extra_flags,
             publisher_click_id, apply_revenue_usd, tracking_type, conversion_revenue_usd, conversion_click_count,
             bot_flags, bot_reason, bot_reason_group, redirected_to_uid)
as
with
    exclude_all_countries_list as (
        select
            crpl.date_from                       as min_date,
            coalesce(crpl.date_to, current_date) as max_date,
            crpl.id_project
        from
            affiliate.conversion_project_list crpl
        where
              crpl.country::text = 'ALL'::text
          and crpl.record_type::text = 'exclude'::text
    ),
    include_countries_list as (
            select
                min(crpl.date_from)                       as min_date,
                max(coalesce(crpl.date_to, current_date)) as max_date,
                crpl.country
            from
                affiliate.conversion_project_list crpl
            where
                  crpl.country::text <> 'ALL'::text
              and crpl.record_type::text = 'include'::text
            group by crpl.country
    ),
    include_project_list as (
        select
            crpl.date_from                                              as min_date,
            coalesce(crpl.date_to, current_date) as max_date,
            crpl.country,
            crpl.id_project
        from
            affiliate.conversion_project_list crpl
        where
              crpl.country::text <> 'ALL'::text
          and crpl.record_type::text = 'include'::text
    ),
    target_actions_raw as (
        select
            auction_user_target_action.country,
            auction_user_target_action.id_project,
            auction_user_target_action.target_action,
            auction_user_target_action.date::date                                                                                                                                                as record_date,
            row_number()
            over (partition by auction_user_target_action.country, auction_user_target_action.id_project, (auction_user_target_action.date::date) order by auction_user_target_action.date desc) as row_num
        from
            aggregation.auction_user_target_action
    ),
    target_actions as (
        select
            target_actions_raw.country,
            target_actions_raw.id_project,
            target_actions_raw.target_action,
            target_actions_raw.record_date,
            case
                when lag(target_actions_raw.record_date)
                     over (partition by target_actions_raw.country, target_actions_raw.id_project order by target_actions_raw.record_date) is null
                    then '2022-01-01'::date
                else target_actions_raw.record_date
                end                as min_dt,
            coalesce(lead(target_actions_raw.record_date)
                     over (partition by target_actions_raw.country, target_actions_raw.id_project order by target_actions_raw.record_date),
                     current_date) as max_dt
        from
            target_actions_raw
        where
            target_actions_raw.row_num = 1
    ),
    data as (
        select distinct
            a.partner                                   as publisher,
            p.feed_type,
            a.id_country,
            c.alpha_2                                   as country,
            a.date_diff,
            ic.dt                                       as click_date,
            coalesce(a.id_external, a.id_external_init) as id_click,
            a.id_external_init,
            a.id_external,
            a.uid_job,
            dct.name                                    as click_type,
            case
                when a.is_bot = 1 then 'bot'::text
                when a.id_jdp is not null then 'jdp'::text
                when a.id_away is not null then 'away'::text
                when (a.external_flags & 2) = 2 then 'non-local away attempt'::text
                when (a.external_flags & 1) = 1 and a.id_away is null and a.id_jdp is null then 'closed job'::text
                else null::text
                end                                     as job_click_type,
            a.click_datetime,
            a.user_ip,
            a.user_country,
            a.last_gather_datetime                      as feed_gather_datetime,
            a.cpc_change_date                           as cpc_change_datetime,
            a.inactivation_date                         as job_inactive_datetime,
            a.feed_cost                                 as feed_cpc_publisher_currency,
            a.feed_cost_usd                             as feed_cpc_usd,
            a.id_sub_client                             as publisher_sub_source,
            a.pub_tracking_id,
            a.affiliate_flags,
            case
                when a.redirected_to_uid is not null then 1
                else 0
                end                                     as is_redirected_click,
            coalesce(a.id_project, ac.id_project)       as id_project,
            a.id_campaign,
            a.last_gather_id_campaign                   as feed_id_campaign,
            ac.id_project                               as feed_id_project,
            case
                when a.id_jdp_away is not null then (select
                                                         sum(coalesce(
                                                                 case
                                                                     when adr.is_bot = 1 or adr.is_internal_duplicated = 1
                                                                         then 0::numeric
                                                                     else adr.revenue_usd::numeric
                                                                     end, 0::numeric)) as sum
                                                     from
                                                         affiliate.statistics_daily_raw adr
                                                     where
                                                           adr.id_country = a.id_country
                                                       and adr.date_diff = a.date_diff
                                                       and adr.id_external_init = a.id_external_init
                                                       and adr.id_external = a.id_external)
                else
                    case
                        when a.is_bot = 1 or a.is_internal_duplicated = 1 then 0::numeric
                        else a.revenue_usd
                        end
                end                                     as client_cpc_usd,
            case
                when a.id_jdp_away is not null then (select
                                                         sum(coalesce(
                                                                 case
                                                                     when adr.is_bot = 1 or adr.is_internal_duplicated = 1
                                                                         then 0::numeric
                                                                     else adr.revenue::numeric
                                                                     end, 0::numeric)) as sum
                                                     from
                                                         affiliate.statistics_daily_raw adr
                                                     where
                                                           adr.id_country = a.id_country
                                                       and adr.date_diff = a.date_diff
                                                       and adr.id_external_init = a.id_external_init
                                                       and adr.id_external = a.id_external)
                else
                    case
                        when a.is_bot = 1 or a.is_internal_duplicated = 1 then 0::numeric
                        else a.revenue
                        end
                end                                     as client_cpc,
            case
                when pp.id_country is not null and a.is_bot = 0 then 1
                else 0
                end                                     as has_postback,
            case
                when a.is_apply = 1 then 1::bigint
                when a.id_jdp_away is not null then (select
                                                         sum(adr.has_jdp_away_conversion) as sum
                                                     from
                                                         affiliate.statistics_daily_raw adr
                                                     where
                                                           adr.id_country = a.id_country
                                                       and adr.date_diff = a.date_diff
                                                       and adr.id_external_init = a.id_external_init
                                                       and adr.id_external = a.id_external)
                else a.has_away_conversion::bigint
                end                                     as conversion_count,
            case
                when (a.external_flags & 1) = 1 and (a.id_jdp is not null or a.id_away is not null) then 1
                else 0
                end                                     as is_client_gap_click,
            case
                when a.id_jdp_away is not null then (select
                                                         count(distinct
                                                               case
                                                                   when adr.is_bot = 0 then adr.id_jdp_away
                                                                   else null::bigint
                                                                   end) as count
                                                     from
                                                         affiliate.statistics_daily_raw adr
                                                     where
                                                           adr.id_country = a.id_country
                                                       and adr.date_diff = a.date_diff
                                                       and adr.id_external_init = a.id_external_init
                                                       and adr.id_external = a.id_external)
                else 0::bigint
                end                                     as jdp_away_cnt,
            a.api_request_id,
            a.id_category,
            case
                when el.id_project is null and (icl.country is null or ipl.id_project is not null) and
                     pp.id_country is not null and a.is_bot = 0 then 1
                else 0
                end                                     as is_conversion_considered,
            case
                when pp.type in ('Apply on Jooble', 'Easy Apply') then 'Apply'
                else ta.target_action end                          as target_action,
            a.job_title_hash,
            a.user_agent_hash,
            a.feed_cpa                                  as feed_cpa_publisher_currency,
            a.feed_cpa_usd,
            a.url_extra_flags,
            a.publisher_click_id,
            a.apply_revenue_usd,
            case
                when (ip.apply_flags & 240) = 240 then 1
                else 0
                end                                     as is_aoj_project,
            a.client_cpa_usd,
            pp.type                                     as tracking_type,
            a.bot_flags,
            case
                 when bot_flags & 16 = 16 then 1
                 when bot_flags & 2 = 2 then 2
                 when bot_flags & 256 = 256 then 3
                 when bot_flags & 1024 = 1024 then 4
                 when bot_flags & 2048 = 2048 then 5
                 when bot_flags & 32 = 32 then 6
                 when bot_flags & 8 = 8 then 7
                 when bot_flags & 4 = 4 then 8
                 when bot_flags & 512 = 512 then 9
                 end                                    as bot_reason_id,
       a.redirected_to_uid
        from
            affiliate.statistics_daily_raw a
            join dimension.info_calendar ic
                 on ic.date_diff = a.date_diff
            join dimension.countries c
                 on c.id = a.id_country
            left join affiliate.partner_daily_snapshot p
                      on p.date_diff = a.date_diff and p.partner::text = a.partner::text and p.id_country = a.id_country
            left join affiliate.dic_click_type dct
                      on dct.id = a.click_type
            left join imp.auction_campaign ac
                      on a.id_country = ac.country_id and a.last_gather_id_campaign = ac.id
            left join affiliate.mv_tracking_project_list pp
                      on pp.id_country = a.id_country and pp.id_project = coalesce(a.id_project, ac.id_project) and
                         pp.conversion_cnt > 5 and a.date_diff >= pp.conversion_start_date_diff and
                         (pp.type = 'Postback' and a.date_diff <= (pp.conversion_max_date_diff + 7) or
                          pp.type <> 'Postback' and pp.revenue_usd > 0)
            left join exclude_all_countries_list el
                      on el.id_project = coalesce(a.id_project, ac.id_project) and ic.dt >= el.min_date and
                         ic.dt < el.max_date
            left join include_countries_list icl
                      on icl.country::text = c.alpha_2::text and ic.dt >= icl.min_date and ic.dt < icl.max_date
            left join include_project_list ipl
                      on ipl.country::text = c.alpha_2::text and
                         ipl.id_project = coalesce(a.id_project, ac.id_project) and ic.dt >= ipl.min_date and
                         ic.dt < ipl.max_date
            left join target_actions ta
                      on ta.country = c.alpha_2 and ta.id_project = coalesce(a.id_project, ac.id_project)
                             and ic.dt >= ta.min_dt and ic.dt < ta.max_dt
            left join dimension.info_project ip
                      on ip.country = a.id_country and ip.id = a.id_project
    )
select
    publisher,
    feed_type,
    id_country,
    country,
    date_diff,
    click_date,
    id_click,
    id_external_init,
    id_external,
    uid_job,
    click_type,
    job_click_type,
    click_datetime,
    user_ip,
    user_country,
    feed_gather_datetime,
    cpc_change_datetime,
    job_inactive_datetime,
    feed_cpc_publisher_currency,
    feed_cpc_usd,
    publisher_sub_source,
    pub_tracking_id,
    affiliate_flags,
    is_redirected_click,
    id_project,
    id_campaign,
    feed_id_campaign,
    feed_id_project,
    client_cpc_usd,
    client_cpc,
    has_postback,
    conversion_count,
    is_client_gap_click,
    jdp_away_cnt,
    api_request_id,
    id_category,
    is_conversion_considered,
    target_action,
    job_title_hash,
    user_agent_hash,
    feed_cpa_publisher_currency,
    feed_cpa_usd,
    url_extra_flags,
    publisher_click_id,
    case
        when is_aoj_project = 1 then apply_revenue_usd
        else (client_cpa_usd * conversion_count::numeric)::numeric(14, 5)
        end as apply_revenue_usd,
    tracking_type,
    case
        when has_postback = 1 and (tracking_type = 'Postback' or job_click_type = 'jdp')
            then client_cpc_usd
        else 0::numeric
        end as conversion_revenue_usd,
    case
        when has_postback = 1 then
            case
                when tracking_type = 'Postback' and job_click_type = 'away' or
                     tracking_type <> 'Postback' and job_click_type = 'jdp' then 1
                when tracking_type = 'Postback' then jdp_away_cnt
                else 0 end
        else 0 end as conversion_click_count,
    bot_flags,
    dbr.name         as bot_reason,
    dbr.reason_group as bot_reason_group,
    d.redirected_to_uid
from
    data d
    left join amo.dic_bot_reasons dbr
              on dbr.id = d.bot_reason_id and d.click_type::text = 'bot'::text;

alter table affiliate.v_click_data
    owner to ypr;

grant select on affiliate.v_click_data to readonly;

grant delete, insert, select, update on affiliate.v_click_data to writeonly_pyscripts;
