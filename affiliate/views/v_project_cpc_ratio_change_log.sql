create or replace view affiliate.v_project_cpc_ratio_change_log
            (source_id, local_id, project_id, campaign_id, field_name, field_value, min_dt, max_dt, min_gather_dt,
             max_gather_dt) as
with
    t as (
        select
            pcrcl.source_id,
            pcrcl.local_id,
            pcrcl.project_id,
            pcrcl.campaign_id,
            pcrcl.field_name,
            pcrcl.value_after::numeric as field_value,
            pcrcl.update_datetime,
            min(tsl.task_datetime)     as min_gather_dt
        from
            affiliate.project_cpc_ratio_change_log pcrcl
            left join affiliate.task_status_log tsl
                      on pcrcl.source_id = tsl.source_id and pcrcl.local_id = tsl.id_partner and
                         pcrcl.update_datetime < tsl.task_datetime
        group by
            pcrcl.source_id, pcrcl.local_id, pcrcl.project_id, pcrcl.campaign_id, pcrcl.field_name,
            (pcrcl.value_after::numeric), pcrcl.update_datetime
    ),
    t2 as (
        select
            t.source_id,
            t.local_id,
            t.project_id,
            t.campaign_id,
            t.field_name,
            t.field_value,
            t.update_datetime                                                                                                                       as min_dt,
            t.min_gather_dt,
            coalesce(lead(t.update_datetime)
                     over (partition by t.source_id, t.local_id, t.project_id, t.campaign_id, t.field_name order by t.update_datetime)::timestamp with time zone,
                     now())                                                                                                                         as max_dt,
            coalesce(lead(t.min_gather_dt)
                     over (partition by t.source_id, t.local_id, t.project_id, t.campaign_id, t.field_name order by t.update_datetime)::timestamp with time zone,
                     now())                                                                                                                         as max_gather_dt,
            row_number()
            over (partition by t.source_id, t.local_id, t.project_id, t.campaign_id, t.field_name, t.min_gather_dt order by t.update_datetime desc) as row_num
        from
            t
    )
select
    t2.source_id,
    t2.local_id,
    t2.project_id,
    t2.campaign_id,
    t2.field_name,
    t2.field_value,
    t2.min_dt,
    t2.max_dt,
    t2.min_gather_dt,
    t2.max_gather_dt
from
    t2
where
    t2.row_num = 1;

alter table affiliate.v_project_cpc_ratio_change_log
    owner to ypr;

grant select on affiliate.v_project_cpc_ratio_change_log to readonly;

grant delete, insert, select, update on affiliate.v_project_cpc_ratio_change_log to writeonly_pyscripts;

