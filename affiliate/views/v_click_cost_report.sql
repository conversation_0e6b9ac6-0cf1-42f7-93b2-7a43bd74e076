create or replace view affiliate.v_click_cost_report
            (date, partner, total_click_cnt, bot_click_cnt, bot_click_percent, bot_click_cost, duplicated_click_cnt,
             duplicated_click_percent, duplicated_click_cost, expired_non_billable_click_cnt,
             expired_non_billable_click_percent, expired_non_billable_click_cost, foreign_click_cnt,
             foreign_click_percent, foreign_click_cost, total_cost, certified_click_cnt, certified_revenue,
             conversion_cnt)
as
with
    t as (
        select
            t.click_date::text                                                             as date,
            t.publisher                                                                    as partner,
            t.click_type,
            count(*)                                                                       as click_cnt,
            sum(
                    case
                        when t.feed_cpa_publisher_currency is not null
                            then t.feed_cpa_publisher_currency * coalesce(t.conversion_count, 0)
                        else coalesce(t.feed_cpc_publisher_currency, 0::numeric) end)      as cost,
            sum(case when is_conversion_considered = 1 then t.conversion_count else 0 end) as conversion_cnt
        from
            affiliate.v_click_data t
        where
            t.click_date >= (current_date - 70)
        group by t.click_date, t.publisher, t.click_type
    ),
    t2 as (
        select
            t.date,
            t.partner,
            sum(t.click_cnt)                  as total_click_cnt,
            sum(
                    case
                        when t.click_type::text = 'bot'::text then t.click_cnt
                        else 0::bigint
                        end)                  as bot_click_cnt,
            sum(
                    case
                        when t.click_type::text = 'bot'::text then t.cost
                        else 0::numeric
                        end)                  as bot_click_cost,
            sum(
                    case
                        when t.click_type::text = 'duplicated'::text then t.click_cnt
                        else 0::bigint
                        end)                  as duplicated_click_cnt,
            sum(
                    case
                        when t.click_type::text = 'duplicated'::text then t.cost
                        else 0::numeric
                        end)                  as duplicated_click_cost,
            sum(
                    case
                        when t.click_type::text = 'expired'::text then t.click_cnt
                        else 0::bigint
                        end)                  as expired_non_billable_click_cnt,
            sum(
                    case
                        when t.click_type::text = 'expired'::text then t.cost
                        else 0::numeric
                        end)                  as expired_non_billable_click_cost,
            sum(coalesce(t.cost, 0::numeric)) as total_cost,
            sum(
                    case
                        when t.click_type::text = 'certified'::text then t.click_cnt
                        else 0::bigint
                        end)                  as certified_click_cnt,
            sum(
                    case
                        when t.click_type::text = 'certified'::text then t.cost
                        else 0::numeric
                        end)                  as certified_revenue,
            sum(
                    case
                        when t.click_type::text = 'foreign'::text then t.click_cnt
                        else 0::bigint
                        end)                  as foreign_click_cnt,
            sum(
                    case
                        when t.click_type::text = 'foreign'::text then t.cost
                        else 0::numeric
                        end)                  as foreign_click_cost,
            sum(conversion_cnt)               as conversion_cnt
        from
            t
        group by t.date, t.partner
    )
select
    t2.date,
    t2.partner,
    t2.total_click_cnt,
    t2.bot_click_cnt,
    round(100.0 * t2.bot_click_cnt / t2.total_click_cnt, 1)                  as bot_click_percent,
    round(t2.bot_click_cost, 2)                                              as bot_click_cost,
    t2.duplicated_click_cnt,
    round(100.0 * t2.duplicated_click_cnt / t2.total_click_cnt, 1)           as duplicated_click_percent,
    round(t2.duplicated_click_cost, 2)                                       as duplicated_click_cost,
    t2.expired_non_billable_click_cnt,
    round(100.0 * t2.expired_non_billable_click_cnt / t2.total_click_cnt, 1) as expired_non_billable_click_percent,
    round(t2.expired_non_billable_click_cost, 2)                             as expired_non_billable_click_cost,
    t2.foreign_click_cnt,
    round(100.0 * t2.foreign_click_cnt / t2.total_click_cnt, 1)              as foreign_click_percent,
    round(t2.foreign_click_cost, 2)                                          as foreign_click_cost,
    round(t2.total_cost, 2)                                                  as total_cost,
    t2.certified_click_cnt,
    round(t2.certified_revenue, 2)                                           as certified_revenue,
    conversion_cnt
from
    t2;

alter table affiliate.v_click_cost_report
    owner to ypr;

grant select on affiliate.v_click_cost_report to readonly;

grant delete, insert, select, truncate, update on affiliate.v_click_cost_report to writeonly_pyscripts;

grant truncate on affiliate.v_click_cost_report to pyapi;

grant select on affiliate.v_click_cost_report to vsk;

grant select on affiliate.v_click_cost_report to "pavlo.kvasnii";

