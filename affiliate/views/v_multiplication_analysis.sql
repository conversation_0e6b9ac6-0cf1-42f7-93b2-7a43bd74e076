create or replace view affiliate.v_multiplication_analysis
            (country_code, publisher, feed_type, manager, id_project, project_name, click_date, is_multiplied_job,
             uid_cnt, uid_cnt_certified_click, total_uid_cnt, total_uid_cnt_certified_click, job_variant_cnt,
             job_variant_cnt_certified_click, multiplication_type, click_cnt, certified_click_cnt, certified_cost_usd,
             total_revenue, conversion_click_revenue, conversion_cnt, conversion_click_cnt)
as
with
    cte_partner_flags_log as (
        select
            vpscl.source_id,
            vpscl.local_id,
            vpscl.min_gather_dt,
            vpscl.max_gather_dt,
            vpscl.field_name,
            (vpscl.field_value::integer & 256) = 256 as is_multiplication
        from
            affiliate.v_partner_settings_change_log vpscl
        where
            vpscl.field_name::text = 'local_flags'::text
    ),
    cte_raw_data as (
        select
            t_1.id_click                             as click_id,
            t_1.publisher,
            lower(t_1.country::text)                 as country_code,
            m.code                                   as manager,
            ps.feed_type,
            t_1.id_project,
            ip.name                                  as project_name,
            t_1.click_date,
            t_1.uid_job,
            t_1.job_title_hash,
            (t_1.affiliate_flags & 16) = 16          as is_applied_multiplication,
            (t_1.url_extra_flags & 256) = 256        as is_category_multiplication,
            (t_1.url_extra_flags & 1) = 1            as is_multiplied_job,
            coalesce(t_1.client_cpc_usd, 0::numeric) as total_revenue,
            case
                when t_1.click_type::text = 'certified'::text then 1::bigint
                else null::bigint
                end                                  as certified_click_cnt,
            case
                when t_1.feed_cpa_usd is not null and t_1.click_type::text = 'certified'::text then t_1.feed_cpa_usd *
                                                                                                    coalesce(t_1.conversion_count, 0::bigint)::numeric
                when t_1.click_type::text = 'certified'::text then coalesce(t_1.feed_cpc_usd, 0::numeric)
                else 0::numeric
                end                                  as certified_cost_usd,
            case
                when t_1.is_conversion_considered = 1 then coalesce(t_1.conversion_click_count, 0)
                end                                  as conversion_click_count,
            case
                when t_1.is_conversion_considered = 1 then t_1.conversion_count
                else null::bigint
                end                                  as conversion_count,
            case
                when t_1.is_conversion_considered = 1 then coalesce(t_1.conversion_revenue_usd, 0::numeric)
                else null::numeric
                end                                  as conversion_click_revenue
        from
            affiliate.v_click_data t_1
            join affiliate.partner_settings ps
                 on ps.country::text = lower(t_1.country::text) and ps.partner::text = t_1.publisher::text
            left join affiliate.dic_manager m
                      on m.id = ps.id_manager
            left join dimension.info_project ip
                      on ip.country = t_1.id_country and ip.id = t_1.id_project
            left join cte_partner_flags_log par_flags
                      on ps.source_id = par_flags.source_id and ps.id_local_partner = par_flags.local_id and
                         t_1.feed_gather_datetime >= par_flags.min_gather_dt and
                         t_1.feed_gather_datetime < par_flags.max_gather_dt
        where
              t_1.click_date >= '2024-04-01'::date
          and ps.is_api_publisher = 0
          and not (t_1.is_redirected_click = 1 and coalesce(t_1.feed_id_project, 0) <> coalesce(t_1.id_project, 0))
          and (par_flags.is_multiplication and (t_1.affiliate_flags & 16) = 16 or (t_1.url_extra_flags & 256) = 256)
    ),
    cte_uid_agg_data as (
        select
            t_1.country_code,
            t_1.publisher,
            t_1.feed_type,
            t_1.manager,
            t_1.id_project,
            t_1.project_name,
            t_1.click_date,
            t_1.uid_job,
            t_1.job_title_hash,
            t_1.is_multiplied_job,
            max(
                    case
                        when t_1.is_category_multiplication then 2
                        when t_1.is_applied_multiplication then 1
                        else 0
                        end)                  as multiplication_type,
            count(t_1.*)                      as click_cnt,
            sum(t_1.certified_click_cnt)      as certified_click_cnt,
            sum(t_1.certified_cost_usd)       as certified_cost_usd,
            sum(t_1.total_revenue)            as total_revenue,
            sum(t_1.conversion_click_revenue) as conversion_click_revenue,
            sum(t_1.conversion_count)         as conversion_count,
            sum(t_1.conversion_click_count)   as conversion_click_count
        from
            cte_raw_data t_1
        group by
            t_1.country_code, t_1.publisher, t_1.feed_type, t_1.manager, t_1.id_project, t_1.project_name,
            t_1.click_date, t_1.uid_job, t_1.job_title_hash, t_1.is_multiplied_job
    ),
    cte_total_uid_cnt as (
        select
            t_1.country_code,
            t_1.publisher,
            t_1.id_project,
            t_1.click_date,
            count(distinct t_1.uid_job) as uid_cnt,
            count(distinct
                  case
                      when t_1.certified_click_cnt > 0::numeric then t_1.uid_job
                      else null::bigint
                      end)              as uid_cnt_certified_click
        from
            cte_uid_agg_data t_1
        group by t_1.country_code, t_1.publisher, t_1.id_project, t_1.click_date
    )
select
    t.country_code,
    t.publisher,
    t.feed_type,
    t.manager,
    t.id_project,
    t.project_name,
    t.click_date,
    t.is_multiplied_job,
    count(distinct t.uid_job)       as uid_cnt,
    count(distinct
          case
              when t.certified_click_cnt > 0::numeric then t.uid_job
              else null::bigint
              end)                  as uid_cnt_certified_click,
    avg(t2.uid_cnt)                 as total_uid_cnt,
    avg(t2.uid_cnt_certified_click) as total_uid_cnt_certified_click,
    case
        when t.is_multiplied_job then count(distinct concat(t.job_title_hash, '--', t.uid_job::character varying))
        else count(distinct t.uid_job)
        end                         as job_variant_cnt,
    case
        when t.is_multiplied_job then count(distinct
                                            case
                                                when t.certified_click_cnt > 0::numeric
                                                    then concat(t.job_title_hash, '--', t.uid_job::character varying)
                                                else null::text
                                                end)
        when sum(t.certified_click_cnt) > 0::numeric then count(distinct
                                                                case
                                                                    when t.certified_click_cnt > 0::numeric
                                                                        then t.uid_job
                                                                    else null::bigint
                                                                    end)
        else 0::bigint
        end                         as job_variant_cnt_certified_click,
    max(t.multiplication_type)      as multiplication_type,
    sum(t.click_cnt)                as click_cnt,
    sum(t.certified_click_cnt)      as certified_click_cnt,
    sum(t.certified_cost_usd)       as certified_cost_usd,
    sum(t.total_revenue)            as total_revenue,
    sum(t.conversion_click_revenue) as conversion_click_revenue,
    sum(t.conversion_count)         as conversion_cnt,
    sum(t.conversion_click_count)   as conversion_click_cnt
from
    cte_uid_agg_data t
    left join cte_total_uid_cnt t2
              on t2.country_code = t.country_code and t2.publisher::text = t.publisher::text and
                 coalesce(t2.id_project, 0) = coalesce(t.id_project, 0) and t2.click_date = t.click_date
group by
    t.country_code, t.publisher, t.feed_type, t.manager, t.id_project, t.project_name, t.click_date,
    t.is_multiplied_job;

alter table affiliate.v_multiplication_analysis
    owner to ypr;

grant select on affiliate.v_multiplication_analysis to readonly;

grant delete, insert, select, update on affiliate.v_multiplication_analysis to writeonly_pyscripts;

