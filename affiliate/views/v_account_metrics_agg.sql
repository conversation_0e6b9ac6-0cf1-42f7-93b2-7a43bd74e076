create or replace view affiliate.v_account_metrics_agg
    (country, date, publisher, metric_name, metric_value, feed_type, manager) as
select
    lower(c.alpha_2::text) as country,
    d.dt                   as date,
    ab.partner             as publisher,
    ab.metric_name,
    ab.metric_value,
    ps.feed_type,
    m.code                 as manager
from
    affiliate.account_metrics_agg ab
    join dimension.countries c
         on ab.country_id = c.id
    join dimension.info_calendar d
         on ab.action_datediff = d.date_diff
    left join affiliate.partner_settings ps
              on ps.country::text = lower(c.alpha_2::text) and ps.partner::text = ab.partner::text
    left join affiliate.dic_manager m
              on m.id = ps.id_manager
where
      d.dt >= '2023-01-01'::date
  and d.dt <= '2023-11-30'::date
  and ab.metric_name::text <> 'new_alert_cnt'::text
union all
select
    ar.country,
    ar.date,
    ar.traffic_source                      as publisher,
    'email_revenue_usd'::character varying as metric_name,
    sum(ar.revenue_usd)                    as metric_value,
    ps.feed_type,
    m.code                                 as manager
from
    affiliate.additional_revenue_agg ar
    join dimension.countries c
         on lower(c.alpha_2::text) = ar.country
    left join affiliate.partner_settings ps
              on ps.country::text = ar.country and ps.partner::text = ar.traffic_source::text
    left join affiliate.dic_manager m
              on m.id = ps.id_manager
where
      (ar.placement::text ~~ '%letter type%'::text or ar.placement::text = 'alertview'::text)
  and ar.date >= '2023-01-01'::date
  and ar.date <= '2023-11-30'::date
group by
    ar.country, c.id, ar.date, ar.traffic_source, ps.feed_type, m.code
union all
select
    lower(c.alpha_2::text) as country,
    ar.account_date::date  as date,
    ts.name                as publisher,
    metrics.metric_name,
    sum(
            case
                when metrics.metric_name = 'new_account_cnt'::text then ar.new_account_cnt
                when metrics.metric_name = 'new_verified_account_cnt'::text then ar.new_verified_account_cnt
                else null::integer
                end)       as metric_value,
    ps.feed_type,
    m.code                 as manager
from
    aggregation.account_revenue ar
    join dimension.u_traffic_source ts
         on ts.country = ar.country_id and ts.id = ar.id_traf_src and ts.channel::text = 'Affiliate'::text
    join dimension.countries c
         on c.id = ar.country_id
    left join affiliate.partner_settings ps
              on ps.country::text = lower(c.alpha_2::text) and ps.partner::text = ts.name::text
    left join affiliate.dic_manager m
              on m.id = ps.id_manager
    cross join (select
                    'new_verified_account_cnt'::text as metric_name
                union all
                select
                    'new_account_cnt'::text as metric_name) metrics
where
    ar.account_date::text >= '2023-12-01'::text
group by
    (lower(c.alpha_2::text)), ar.country_id, ar.account_date, ts.name, metrics.metric_name, ps.feed_type, m.code
union all
select
    lower(c.alpha_2::text) as country,
    ar.revenue_date::date  as date,
    ts.name                as publisher,
    metrics.metric_name,
    sum(
            case
                when metrics.metric_name = 'email_revenue_usd'::text then ar.email_revenue
                when metrics.metric_name = 'account_serp_revenue_usd'::text then ar.serp_revenue
                else null::integer::numeric
                end)       as metric_value,
    ps.feed_type,
    m.code                 as manager
from
    aggregation.account_revenue ar
    join dimension.u_traffic_source ts
         on ts.country = ar.country_id and ts.id = ar.id_traf_src and ts.channel::text = 'Affiliate'::text
    join dimension.countries c
         on c.id = ar.country_id
    left join affiliate.partner_settings ps
              on ps.country::text = lower(c.alpha_2::text) and ps.partner::text = ts.name::text
    left join affiliate.dic_manager m
              on m.id = ps.id_manager
    cross join (select
                    'email_revenue_usd'::text as metric_name
                union all
                select
                    'account_serp_revenue_usd'::text as metric_name) metrics
where
    ar.revenue_date::text >= '2023-12-01'::text
group by
    (lower(c.alpha_2::text)), ar.country_id, ar.revenue_date, ts.name, ps.feed_type, m.code, metrics.metric_name
union all
select
    lower(c.alpha_2::text) as country,
    ar.account_date::date  as date,
    ts.name                as publisher,
    t.metric_name,
    sum(
            case
                when t.metric_name = 'arpu_3'::text then
                    case
                        when ar.revenue_date::date < (ar.account_date::date + 3) then
                                coalesce(ar.email_revenue, 0::numeric) + coalesce(ar.serp_revenue, 0::numeric)
                        else null::numeric
                        end
                when t.metric_name = 'arpu_7'::text then
                    case
                        when ar.revenue_date::date < (ar.account_date::date + 7) then
                                coalesce(ar.email_revenue, 0::numeric) + coalesce(ar.serp_revenue, 0::numeric)
                        else null::numeric
                        end
                when t.metric_name = 'arpu_14'::text then
                    case
                        when ar.revenue_date::date < (ar.account_date::date + 14) then
                                coalesce(ar.email_revenue, 0::numeric) + coalesce(ar.serp_revenue, 0::numeric)
                        else null::numeric
                        end
                when t.metric_name = 'arpu_28'::text then
                    case
                        when ar.revenue_date::date < (ar.account_date::date + 28) then
                                coalesce(ar.email_revenue, 0::numeric) + coalesce(ar.serp_revenue, 0::numeric)
                        else null::numeric
                        end
                else null::numeric
                end)       as metric_value,
    ps.feed_type,
    m.code                 as manager
from
    aggregation.account_revenue ar
    join dimension.u_traffic_source ts
         on ts.country = ar.country_id and ts.id = ar.id_traf_src and ts.channel::text = 'Affiliate'::text
    join dimension.countries c
         on c.id = ar.country_id
    left join affiliate.partner_settings ps
              on ps.country::text = lower(c.alpha_2::text) and ps.partner::text = ts.name::text
    left join affiliate.dic_manager m
              on m.id = ps.id_manager
    cross join (select
                    'arpu_3'::text as metric_name
                union
                select
                    'arpu_7'::text as text
                union
                select
                    'arpu_14'::text as text
                union
                select
                    'arpu_28'::text as text) t
where
      ar.account_date::date >= '2023-01-01'::date
  and case
          when t.metric_name = 'arpu_3'::text then ar.account_date::date <= (current_date - 3)
          when t.metric_name = 'arpu_7'::text then ar.account_date::date <= (current_date - 7)
          when t.metric_name = 'arpu_14'::text then ar.account_date::date <= (current_date - 14)
          when t.metric_name = 'arpu_28'::text then ar.account_date::date <= (current_date - 28)
          else null::boolean
          end
group by
    (lower(c.alpha_2::text)), ar.country_id, (ar.account_date::date), ts.name, ps.feed_type, m.code, t.metric_name
having
        sum(
                case
                    when t.metric_name = 'arpu_3'::text then
                        case
                            when ar.revenue_date::date <= (ar.account_date::date + 3) then
                                    coalesce(ar.email_revenue, 0::numeric) + coalesce(ar.serp_revenue, 0::numeric)
                            else null::numeric
                            end
                    when t.metric_name = 'arpu_7'::text then
                        case
                            when ar.revenue_date::date <= (ar.account_date::date + 7) then
                                    coalesce(ar.email_revenue, 0::numeric) + coalesce(ar.serp_revenue, 0::numeric)
                            else null::numeric
                            end
                    when t.metric_name = 'arpu_14'::text then
                        case
                            when ar.revenue_date::date <= (ar.account_date::date + 14) then
                                    coalesce(ar.email_revenue, 0::numeric) + coalesce(ar.serp_revenue, 0::numeric)
                            else null::numeric
                            end
                    when t.metric_name = 'arpu_28'::text then
                        case
                            when ar.revenue_date::date <= (ar.account_date::date + 28) then
                                    coalesce(ar.email_revenue, 0::numeric) + coalesce(ar.serp_revenue, 0::numeric)
                            else null::numeric
                            end
                    else null::numeric
                    end) > 0::numeric
union all
select
    lower(vmma.country::text)             as country,
    vmma.click_date                       as date,
    vmma.publisher,
    t.metric_name,
    sum(
            case
                when t.metric_name = 'affiliate_revenue_usd'::text then vmma.revenue_usd
                when t.metric_name = 'certified_cost_usd'::text then vmma.certified_cost_usd
                else null::numeric
                end)                      as metric_value,
    vmma.feed_type::character varying(10) as feed_type,
    m.code                                as manager
from
    affiliate.v_main_metrics_agg vmma
    left join affiliate.partner_settings ps
              on ps.country::text = lower(vmma.country::text) and ps.partner::text = vmma.publisher::text
    left join affiliate.dic_manager m
              on m.id = ps.id_manager
    cross join (select
                    'affiliate_revenue_usd'::text as metric_name
                union all
                select
                    'certified_cost_usd'::text as text) t
where
      vmma.click_date >= '2023-01-01'::date
  and (vmma.feed_type::text = any (array ['organic'::character varying::text, 'paid'::character varying::text]))
group by
    (lower(vmma.country::text)), vmma.id_country, vmma.click_date, vmma.publisher, vmma.feed_type, m.code, t.metric_name
having
        sum(
                case
                    when t.metric_name = 'affiliate_revenue_usd'::text then vmma.revenue_usd
                    when t.metric_name = 'certified_cost_usd'::text then vmma.certified_cost_usd
                    else null::numeric
                    end) > 0::numeric
union all
select
    ar.country,
    ar.date,
    ar.traffic_source                     as publisher,
    'serp_revenue_usd'::character varying as metric_name,
    sum(ar.revenue_usd)                   as metric_value,
    ps.feed_type,
    m.code                                as manager
from
    affiliate.additional_revenue_agg ar
    left join dimension.countries c
              on lower(c.alpha_2::text) = ar.country
    left join affiliate.partner_settings ps
              on ps.country::text = lower(c.alpha_2::text) and lower(ps.partner::text) = lower(ar.traffic_source::text)
    left join affiliate.dic_manager m
              on m.id = ps.id_manager
where
      ar.date >= '2023-01-01'::date
  and not ((ar.placement::text = any (array ['search'::text, 'recommendations'::text])) and
           lower(ar.traffic_source::text) ~~ '%dtl%'::text)
  and ar.placement::text !~~ '%letter type%'::text
  and ar.placement::text <> 'alertview'::text
group by
    ar.country, c.id, ar.date, ar.traffic_source, ps.feed_type, m.code
having
    sum(ar.revenue_usd) > 0::numeric;

alter table affiliate.v_account_metrics_agg
    owner to ypr;

grant select on affiliate.v_account_metrics_agg to readonly;

grant delete, insert, select, truncate, update on affiliate.v_account_metrics_agg to writeonly_pyscripts;
