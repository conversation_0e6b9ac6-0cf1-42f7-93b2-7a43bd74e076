create or replace view affiliate.v_dtl_search_stat_agg
            (country, publisher, search_date, search_type, result_cnt_bin, search_cnt, avg_search_results,
             impression_cnt, impression_on_screen_cnt, paid_impression_cnt, paid_impression_on_screen_cnt, revenue_usd,
             total_serp_click_cnt, paid_serp_click_cnt, serp_jdp_click_cnt, serp_away_click_cnt, jdp_away_click_cnt)
as
with
    search_stat as (
        select
            dsc.country,
            dsc.date_diff,
            dsc.id_search,
            sum(dsc.revenue_usd)        as revenue_usd,
            sum(dsc.total_click_cnt)    as total_click_cnt,
            sum(dsc.paid_click_cnt)     as paid_click_cnt,
            sum(dsc.jdp_click_cnt)      as jdp_click_cnt,
            sum(dsc.jdp_away_click_cnt) as jdp_away_click_cnt
        from
            affiliate.dtl_search_clicks dsc
        group by dsc.country, dsc.date_diff, dsc.id_search
    )
select
    upper(ds.country::text)                         as country,
    ds.traffic_source                               as publisher,
    fn_get_date_from_date_diff(ds.date_diff)        as search_date,
    case
        when dcsc.click_type = 1 or ds.is_bot = 1 then 'bot'::text
        when dcsc.click_type = 2 then 'duplicate dtl click'::text
        when dcsc.click_type = 4 then 'foreign dtl click'::text
        when dcsc.click_type = 0 then 'certified dtl click'::text
        when ds.user_country::text <> lower(ds.country::text) and
             not (ds.user_country::text = 'gb'::text and lower(ds.country::text) = 'uk'::text)
            then 'foreign additional search'::text
        else 'valid additional search'::text
        end                                         as search_type,
    case
        when ds.results_total = 0 then '0'::text
        when ds.results_total <= 10 then '1-10'::text
        when ds.results_total <= 20 then '11-20'::text
        when ds.results_total <= 30 then '21-30'::text
        when ds.results_total <= 50 then '31-50'::text
        else '50+'::text
        end                                         as result_cnt_bin,
    count(distinct ds.id_search)                    as search_cnt,
    avg(ds.results_total)                           as avg_search_results,
    sum(ds.impression_cnt)                          as impression_cnt,
    sum(ds.impression_on_screen_cnt)                as impression_on_screen_cnt,
    sum(ds.paid_impression_cnt)                     as paid_impression_cnt,
    sum(ds.paid_impression_on_screen_cnt)           as paid_impression_on_screen_cnt,
    sum(ss.revenue_usd)                             as revenue_usd,
    sum(ss.total_click_cnt)                         as total_serp_click_cnt,
    sum(ss.paid_click_cnt)                          as paid_serp_click_cnt,
    sum(ss.jdp_click_cnt)                           as serp_jdp_click_cnt,
    sum(ss.total_click_cnt) - sum(ss.jdp_click_cnt) as serp_away_click_cnt,
    sum(ss.jdp_away_click_cnt)                      as jdp_away_click_cnt
from
    affiliate.dtl_searches ds
    left join affiliate.dtl_click_search_connection dcsc
              on dcsc.country::text = ds.country::text and dcsc.date_diff = ds.date_diff and
                 dcsc.search_id = ds.id_search
    left join search_stat ss
              on ss.country::text = ds.country::text and ss.date_diff = ds.date_diff and ss.id_search = ds.id_search
where
        ds.date_diff >= fn_get_date_diff('2023-11-21 00:00:00'::timestamp without time zone)
group by
    (upper(ds.country::text)), ds.traffic_source, (fn_get_date_from_date_diff(ds.date_diff)),
    (
        case
            when dcsc.click_type = 1 or ds.is_bot = 1 then 'bot'::text
            when dcsc.click_type = 2 then 'duplicate dtl click'::text
            when dcsc.click_type = 4 then 'foreign dtl click'::text
            when dcsc.click_type = 0 then 'certified dtl click'::text
            when ds.user_country::text <> lower(ds.country::text) and
                 not (ds.user_country::text = 'gb'::text and lower(ds.country::text) = 'uk'::text)
                then 'foreign additional search'::text
            else 'valid additional search'::text
            end),
    (
        case
            when ds.results_total = 0 then '0'::text
            when ds.results_total <= 10 then '1-10'::text
            when ds.results_total <= 20 then '11-20'::text
            when ds.results_total <= 30 then '21-30'::text
            when ds.results_total <= 50 then '31-50'::text
            else '50+'::text
            end);

alter table affiliate.v_dtl_search_stat_agg
    owner to ypr;

grant select on affiliate.v_dtl_search_stat_agg to readonly;

grant delete, insert, select, update on affiliate.v_dtl_search_stat_agg to writeonly_pyscripts;

