create or replace view affiliate.v_project_pub_category_multiplication
            (country, id_project, project_name, publisher, uid_count, id_category, category, feed_type,
             affiliate_manager, turned_on_multiplication_category, total_jobs_count, underutilised_budget,
             min_date, max_date)
as
with
    category_data as (
       select distinct
              jkc.id_child   as id_category,
              jkc.child_name as category_name
          from
              dimension.job_kaiju_category as jkc

          union

          select distinct
              jkc.id_parent   as id_category,
              jkc.parent_name as category_name
          from
              dimension.job_kaiju_category jkc
       ),
    gather_cnt_data as (
       select
            js1.id_source,
            js1.id_partner,
            count(distinct js1.start_gather_dt) as cnt_gather,
            js1.start_gather_date_diff
        from
            affiliate.job_stat_agg js1
        where
            js1.start_gather_date_diff between
                public.fn_get_date_diff(current_date - 3)
                and public.fn_get_date_diff(current_date - 1)
        group by js1.id_source, js1.id_partner, js1.start_gather_date_diff
       ),
    raw_data as (
       select
            ubc.country,
            ac.id_project,
            ip.name                                        as project_name,
            ps.partner                                     as publisher,
            sum(jsac.uid_count)::double precision / t.cnt_gather::double precision as uid_count,
            cd.id_category,
            cd.category_name                               as category,
            ps.feed_type,
            dm.code                                        as affiliate_manager,
            case
                when ps.local_flags & 2048 = 2048 then 1
                else 0
                end                                        as turned_on_multiplication_category,
            sum(coalesce(jsac.uid_count, 0) +
                 case
                     when coalesce(jsa.is_multiplied, false) = false then 0
                     when jsa.is_multiplied = true then coalesce(jsac.reassembled_job_count, 0)
                     else null::integer
                 end)::integer / t.cnt_gather::double precision
                                                           as total_jobs_count,
                     ubc.underutilised_budget
        from
            affiliate.job_stat_agg jsa
            left join affiliate.job_stat_agg_category as jsac
                      on jsa.id = jsac.id_job_stat_agg
            left join affiliate.partner_settings as ps
                      on ps.id_local_partner = jsa.id_partner and ps.source_id = jsa.id_source
            left join dimension.countries as c
                      on c.alpha_2 = upper(ps.country)
            left join imp.auction_campaign as ac
                      on ac.id = jsa.id_campaign and ac.country_id = c.id
            join affiliate.underutilised_budget_category as ubc
                 on lower(ubc.country) = lower(c.alpha_2) and ubc.id_project = ac.id_project
                     and ubc.id_job_category = jsac.id_category
            left join affiliate.dic_manager as dm
                      on dm.id = ps.id_manager
            left join dimension.info_project as ip
                      on ip.country = c.id and ip.id = ac.id_project
            left join category_data as cd
                      on cd.id_category = jsac.id_category
            left join gather_cnt_data as t
                      on t.id_source = jsa.id_source and t.id_partner = jsa.id_partner
                         and t.start_gather_date_diff = jsa.start_gather_date_diff
        where
             jsa.start_gather_date_diff between
                 public.fn_get_date_diff(current_date - 3)
                 and public.fn_get_date_diff(current_date - 1)
        group by
                 ubc.country, ac.id_project, ps.partner, cd.id_category, cd.category_name, ps.feed_type, dm.code,
                 ip.name, t.cnt_gather, jsa.start_gather_date_diff,
                 (case
                     when ps.local_flags & 2048 = 2048 then 1
                     else 0
                     end), ubc.underutilised_budget
       )


select
    rd.country,
    rd.id_project,
    rd.project_name,
    rd.publisher,
    avg(rd.uid_count::double precision)::integer        as uid_count,
    rd.id_category,
    rd.category,
    rd.feed_type,
    rd.affiliate_manager,
    rd.turned_on_multiplication_category,
    avg(rd.total_jobs_count::double precision)::integer as total_jobs_count,
    avg(rd.underutilised_budget)                        as underutilised_budget,
    (current_date - 3)                                  as min_date,
    (current_date - 1)                                  as max_date
from
    raw_data as rd
group by
    rd.country,
    rd.id_project,
    rd.project_name,
    rd.publisher,
    rd.id_category,
    rd.category,
    rd.feed_type,
    rd.affiliate_manager,
    rd.turned_on_multiplication_category;


alter table affiliate.v_project_pub_category_multiplication
    owner to ypr;

grant select on affiliate.v_project_pub_category_multiplication to readonly;

grant delete, insert, select, truncate, update on affiliate.v_project_pub_category_multiplication to writeonly_pyscripts;