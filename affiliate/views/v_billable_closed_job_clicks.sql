create or replace view affiliate.v_billable_closed_job_clicks
            (country, date, partner, threshold, type, is_closed, cnt_click, feed_cost_usd) as
with
    t as (
        select generate_series(0, 24, 2) as threshold
    ),
    clicks as (
        select
            lower(t_1.country::text)        as country,
            t_1.click_date                  as date,
            t_1.publisher                   as partner,
            t_1.click_datetime,
            t_1.job_inactive_datetime       as inactivation_date,
            t_1.cpc_change_datetime         as cpc_change_date,
            t_1.id_click                    as id_external,
            case when t_1.feed_cpa_usd is not null
                then t_1.feed_cpa_usd * coalesce(t_1.conversion_count, 0)
                else t_1.feed_cpc_usd end   as feed_cost_usd,
            t_1.feed_type,
            p.is_api_publisher,
            t_1.feed_cpc_publisher_currency as feed_cost,
            arh.request_datetime,
            case
                when t_1.job_click_type = 'closed job'::text then 1
                else 0
                end                         as is_closed
        from
            affiliate.v_click_data t_1
            join affiliate.partner_settings p
                 on t_1.publisher::text = p.partner::text and lower(t_1.country::text) = p.country::text
            left join affiliate.api_request_history arh
                      on arh.source_id = p.source_id and arh.partner_id = p.id_local_partner and
                         arh.request_id::text = t_1.api_request_id::text and p.is_api_publisher = 1
        where
              (t_1.click_type::text = any
               (array ['certified'::character varying::text, 'expired'::character varying::text]))
          and p.feed_type::text = 'paid'::text
          and t_1.click_date > (current_date - 90)
    )
select
    clicks.country,
    clicks.date,
    clicks.partner,
    t.threshold,
    case
        when clicks.is_api_publisher = 1 and
             (clicks.request_datetime + '01:00:00'::interval * t.threshold::double precision) < clicks.click_datetime
            then 'Non-billable'::text
        when clicks.is_api_publisher = 1 then 'Billable'::text
        when (clicks.inactivation_date + '01:00:00'::interval * t.threshold::double precision) <
             clicks.click_datetime and
             (clicks.inactivation_date > clicks.cpc_change_date or clicks.cpc_change_date is null) or
             clicks.feed_type::text = 'paid'::text and clicks.inactivation_date is null and
             clicks.cpc_change_date is null and clicks.feed_cost is null then 'Non-billable'::text
        else 'Billable'::text
        end                   as type,
    clicks.is_closed,
    count(*)                  as cnt_click,
    sum(clicks.feed_cost_usd) as feed_cost_usd
from
    clicks
    cross join t
group by
    clicks.country, clicks.date, clicks.partner, t.threshold, clicks.is_closed,
    (
        case
            when clicks.is_api_publisher = 1 and
                 (clicks.request_datetime + '01:00:00'::interval * t.threshold::double precision) <
                 clicks.click_datetime then 'Non-billable'::text
            when clicks.is_api_publisher = 1 then 'Billable'::text
            when (clicks.inactivation_date + '01:00:00'::interval * t.threshold::double precision) <
                 clicks.click_datetime and
                 (clicks.inactivation_date > clicks.cpc_change_date or clicks.cpc_change_date is null) or
                 clicks.feed_type::text = 'paid'::text and clicks.inactivation_date is null and
                 clicks.cpc_change_date is null and clicks.feed_cost is null then 'Non-billable'::text
            else 'Billable'::text
            end);

alter table affiliate.v_billable_closed_job_clicks
    owner to ypr;

grant select on affiliate.v_billable_closed_job_clicks to readonly;

grant select on affiliate.v_billable_closed_job_clicks to ypr;

grant delete, insert, select, truncate, update on affiliate.v_billable_closed_job_clicks to writeonly_pyscripts;

grant truncate on affiliate.v_billable_closed_job_clicks to pyapi;

grant select on affiliate.v_billable_closed_job_clicks to user_agg_team;

