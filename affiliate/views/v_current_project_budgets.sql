create or replace view affiliate.v_current_project_budgets
            (country, id_project, revenue_usd, affiliate_revenue_usd, potential_revenue_usd, budget_usd,
             affiliate_revenue_share, potential_revenue_budget_diff, potential_revenue_budget_percent_diff)
as
with
    date_info as (
       select
             max(br.action_date)                       as date,
             date_part('day'::text, date_trunc('month'::text, max(br.action_date) + '1 mon'::interval) -
                                             '1 day'::interval) as last_day_current_month
       from
             aggregation.budget_revenue_daily_agg br
       ),
    data as (
       select
             b.country_code                                                  as country,
             b.project_id                                                    as id_project,
             sum(coalesce(b.revenue_usd, 0::numeric))                        as revenue_usd,
             sum(coalesce(b.affiliate_revenue, 0::numeric))                  as affiliate_revenue_usd,
             count(distinct b.id_campaign)                                   as campaign_cnt,
             count(distinct
                   case
                       when coalesce(b.campaign_budget_month_usd, 0::double precision) > 0::double precision
                            then b.id_campaign
                       else null::character varying
                       end)                                                  as campaign_with_budget_cnt,
             max(b.user_budget_month_usd)                                    as user_budget_month_usd,
             sum(coalesce(b.campaign_budget_month_usd, 0::double precision)) as campaign_budget_month_usd,
             max(b.is_unlim_cmp)                                             as is_unlim_cmp,
             d.date,
             d.last_day_current_month
       from
             aggregation.budget_revenue_daily_agg b
             join date_info d
                  on d.date = b.action_date
       group by b.country_code, b.project_id, d.date, d.last_day_current_month
       ),
    data_preparation as (
       select
             d.country,
             d.id_project,
             d.user_budget_month_usd,
             d.campaign_budget_month_usd,
             d.revenue_usd,
             d.affiliate_revenue_usd,
             case
                 when d.campaign_cnt = d.campaign_with_budget_cnt and
                      d.campaign_budget_month_usd > 0::double precision and
                      d.campaign_budget_month_usd < d.user_budget_month_usd
                    then d.campaign_budget_month_usd
                 when d.is_unlim_cmp = 1 and d.user_budget_month_usd > d.campaign_budget_month_usd
                    then d.user_budget_month_usd
                 when d.user_budget_month_usd = 0::double precision then d.campaign_budget_month_usd
                 else d.user_budget_month_usd
                 end                                                                   as budget_usd,
             case
                 when d.revenue_usd = 0::double precision then 0::double precision
                 else coalesce(d.affiliate_revenue_usd::double precision / d.revenue_usd,
                               0::double precision)
                 end                                                                   as affiliate_revenue_share,
             d.revenue_usd / date_part('day'::text, d.date) * d.last_day_current_month as potential_revenue_usd_wo_limits
       from
            data d
       ),
    data_final as (
       select
             f.country,
             f.id_project,
             f.revenue_usd,
             f.affiliate_revenue_usd,
             f.budget_usd,
             f.affiliate_revenue_share,
             case
                 when (coalesce(f.user_budget_month_usd, 0::double precision) = 0::double precision and
                      coalesce(f.campaign_budget_month_usd, 0::double precision) = 0::double precision) or
                      (f.revenue_usd < f.budget_usd and f.potential_revenue_usd_wo_limits <= f.budget_usd)
                    then f.potential_revenue_usd_wo_limits
                 when f.revenue_usd < f.budget_usd and f.potential_revenue_usd_wo_limits > f.budget_usd
                    then f.budget_usd
                 else f.revenue_usd
                 end as potential_revenue_usd
       from
            data_preparation f
       )
select
    df.country,
    df.id_project,
    df.revenue_usd,
    df.affiliate_revenue_usd,
    df.potential_revenue_usd,
    df.budget_usd,
    df.affiliate_revenue_share,
    case
        when (df.budget_usd - df.potential_revenue_usd) > 0::double precision
            then df.budget_usd - df.potential_revenue_usd
        else 0::double precision
        end as potential_revenue_budget_diff,
    case
        when df.budget_usd = 0::double precision or (df.budget_usd - df.potential_revenue_usd) < 0 then 0::double precision
        else (df.budget_usd - df.potential_revenue_usd) / df.budget_usd
        end as potential_revenue_budget_percent_diff
from
    data_final df;

alter table affiliate.v_current_project_budgets
    owner to ypr;

grant select on affiliate.v_current_project_budgets to readonly;

grant delete, insert, select, update on affiliate.v_current_project_budgets to writeonly_pyscripts;

