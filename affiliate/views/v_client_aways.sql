create or replace view affiliate.v_client_aways
            (country, click_date, id_project, project_name, is_ad_exchange, click_price_usd, id_campaign,
             is_price_per_job, campaign_name, category_name, parent_category_name, min_cpc_usd, away_cnt)
as
with
    min_cpcs as (
        select
            cpc_cluster_range.date,
            cpc_cluster_range.country_id,
            cpc_cluster_range.low_min_cpc_usd                                                                       as min_cpc_usd,
            coalesce(lead(cpc_cluster_range.date)
                     over (partition by cpc_cluster_range.country_id order by cpc_cluster_range.date),
                     now()::date)                                                                                   as next_date
        from
            aggregation.cpc_cluster_range
    ),
    agg_data as (
        select
            vmma.country,
            vmma.click_date,
            vmma.id_project,
            ip.name                                              as project_name,
            ip.hide_in_search                                    as is_ad_exchange,
            round(vmma.client_cpc_usd, 3)                         as click_price_usd,
            vmma.id_campaign,
            ac.is_price_per_job,
            ac.name                                              as campaign_name,
            vmma.category_name,
            vmma.parent_category_name,
            mc.min_cpc_usd,
            sum(coalesce(vmma.away_click_cnt, 0::bigint))::bigint as away_cnt
        from
            affiliate.v_main_metrics_agg vmma
            join affiliate.partner_daily_snapshot pds
                 on pds.date_diff = vmma.date_diff and pds.partner::text = vmma.publisher::text and
                    pds.id_country = vmma.id_country
            join dimension.info_project ip
                 on ip.country = vmma.id_country and ip.id = vmma.id_project
            left join imp.auction_campaign ac
                      on ac.country_id = vmma.id_country and ac.id = vmma.id_campaign
            left join min_cpcs mc
                      on mc.country_id = vmma.id_country and vmma.click_date >= mc.date and vmma.click_date < mc.next_date
        where
              round(vmma.client_cpc_usd, 3) > 0::numeric
          and coalesce(vmma.away_click_cnt, 0::bigint) > 0
          and vmma.click_date >= (current_date - 90)
        group by
            vmma.country, vmma.click_date, vmma.id_project, ip.name,
            ip.hide_in_search, (round(vmma.client_cpc_usd, 3)), vmma.id_campaign,
            ac.is_price_per_job, ac.name, vmma.category_name, vmma.parent_category_name, mc.min_cpc_usd
    ),
    num_range as (
        select
            t_1.t as num
        from
            generate_series(1::bigint, (select
                                            max(agg_data.away_cnt) as max
                                        from
                                            agg_data), 1::bigint) t_1(t)
    )
select
    t.country,
    t.click_date,
    t.id_project,
    t.project_name,
    t.is_ad_exchange,
    t.click_price_usd,
    t.id_campaign,
    t.is_price_per_job,
    t.campaign_name,
    t.category_name,
    t.parent_category_name,
    t.min_cpc_usd,
    t.away_cnt
from
    agg_data t
    join num_range nr
         on nr.num <= t.away_cnt;

alter table affiliate.v_client_aways
    owner to ypr;

grant select on affiliate.v_client_aways to readonly;

grant delete, insert, select, update on affiliate.v_client_aways to writeonly_pyscripts;

