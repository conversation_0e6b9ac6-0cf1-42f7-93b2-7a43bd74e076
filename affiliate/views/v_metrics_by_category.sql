create or replace view affiliate.v_metrics_by_category
            (date, id_country, country, publisher, id_project, project_name, feed_type, manager, category_name,
             child_category_name_1, child_category_name_2, child_category_name_3, category_level, client_cpc_usd,
             feed_cpc_usd, feed_cpa_usd, certified_click_cnt, conversion_cnt, conversion_away_cnt, certified_cost_usd,
             revenue_usd, conversion_revenue_usd, paid_client_click, paid_feed_click)
as
with
    child_data_raw as (
        select
            id_child,
            child_name
        from
           dimension.job_kaiju_category
       ),
    parent_data_raw as (
        select distinct
            id_parent,
            parent_name
        from
            dimension.job_kaiju_category
       ),
    category_data as (
        select
              p.parent_name,
              jkr1.child_name as child_name_1,
              jkr2.child_name as child_name_2,
              jkr3.child_name as child_name_3,
              -3              as level
          from
              parent_data_raw as p
              left join child_data_raw as c
                        on c.id_child = p.id_parent
              left join dimension.job_kaiju_category as jkr1
                        on jkr1.id_parent = p.id_parent
              left join dimension.job_kaiju_category as jkr2
                        on jkr2.id_parent = jkr1.id_child
              left join dimension.job_kaiju_category as jkr3
                        on jkr3.id_parent = jkr2.id_child
          where
                c.id_child is null
            and jkr3.id_child is not null

          union all

          select
              p.parent_name,
              jkr1.child_name as child_name_1,
              jkr2.child_name as child_name_2,
              null::text      as child_name_3,
              -2              as level
          from
              parent_data_raw as p
              left join child_data_raw as c
                        on c.id_child = p.id_parent
              left join dimension.job_kaiju_category as jkr1
                        on jkr1.id_parent = p.id_parent
              left join dimension.job_kaiju_category as jkr2
                        on jkr2.id_parent = jkr1.id_child
          where
                c.id_child is null
            and jkr2.id_child is not null

          union all

          select
              p.parent_name,
              jkr1.child_name as child_name_1,
              null::text      as child_name_2,
              null::text      as child_name_3,
              -1              as level
          from
              parent_data_raw as p
              left join child_data_raw as c
                        on c.id_child = p.id_parent
              left join dimension.job_kaiju_category as jkr1
                        on jkr1.id_parent = p.id_parent
          where
                c.id_child is null
            and jkr1.id_child is not null

          union all

          select
              p.parent_name,
              null::text   as child_name_1,
              null::text   as child_name_2,
              null::text   as child_name_3,
              0            as level
          from
              parent_data_raw as p
              left join child_data_raw as c
                        on c.id_child = p.id_parent
          where
              c.id_child is null
          )

select
    vmma.click_date                                                                   as date,
    vmma.id_country,
    vmma.country,
    vmma.publisher,
    vmma.id_project,
    p.name                                                                            as project_name,
    vmma.feed_type,
    m.name                                                                            as manager,
    cd.parent_name                                                                    as category_name,
    cd.child_name_1                                                                   as child_category_name_1,
    cd.child_name_2                                                                   as child_category_name_2,
    cd.child_name_3                                                                   as child_category_name_3,
    cd.level                                                                          as category_level,
    case
        when sum(total_click_cnt) filter ( where client_cpc_usd > 0) > 0 then
            sum(client_cpc_usd * total_click_cnt) / sum(total_click_cnt) filter (where client_cpc_usd > 0)
        else 0
        end                                                                           as client_cpc_usd,
    case
        when sum(certified_click_cnt) > 0 then sum(feed_cpc_usd * certified_click_cnt) / sum(certified_click_cnt)
        else 0
        end                                                                           as feed_cpc_usd,
    case
        when sum(total_click_cnt) filter ( where feed_cpa_usd > 0) > 0 then
            sum(feed_cpa_usd * total_click_cnt) / sum(total_click_cnt) filter (where feed_cpa_usd > 0)
        else 0
        end                                                                           as feed_cpa_usd,
    sum(vmma.certified_click_cnt)                                                     as certified_click_cnt,
    sum(vmma.conversion_cnt) filter (where vmma.is_conversion_considered = 1)         as conversion_cnt,
    sum(vmma.conversion_away_cnt) filter (where vmma.is_conversion_considered = 1)    as conversion_away_cnt,
    sum(vmma.certified_cost_usd)                                                      as certified_cost_usd,
    sum(vmma.revenue_usd)                                                             as revenue_usd,
    sum(vmma.conversion_revenue_usd) filter (where vmma.is_conversion_considered = 1) as conversion_revenue_usd,
    sum(vmma.total_click_cnt) filter (where vmma.client_cpc_usd > 0::numeric)         as paid_client_click,
    sum(vmma.certified_click_cnt) filter (where vmma.feed_cpc_usd > 0::numeric)       as paid_feed_click
from
    affiliate.v_main_metrics_agg as vmma
    left join category_data as cd
              on coalesce(cd.child_name_3, cd.child_name_2, cd.child_name_1, cd.parent_name) = vmma.category_name
    left join dimension.info_project p
              on p.country = vmma.id_country and p.id = vmma.id_project
    left join affiliate.partner_settings ps
              on ps.country::text = lower(vmma.country) and ps.partner::text = vmma.publisher::text
    left join affiliate.dic_manager m
              on m.id = ps.id_manager
where
      vmma.click_date >= (current_date - 90)
  and vmma.click_date <= (current_date - 1)
group by
    vmma.click_date, vmma.id_country, vmma.country, vmma.publisher, vmma.id_project, p.name, m.name,
    vmma.feed_type, cd.parent_name, cd.child_name_1, cd.child_name_2, cd.child_name_3,
    cd.level;


alter table affiliate.v_metrics_by_category
    owner to ypr;

grant select on affiliate.v_metrics_by_category to readonly;

grant delete, insert, select, truncate, update on affiliate.v_metrics_by_category to writeonly_pyscripts;