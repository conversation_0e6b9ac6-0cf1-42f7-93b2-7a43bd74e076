create or replace view affiliate.v_underutilized_budget_by_category(country, id_project, id_job_category, underutilised_budget) as
with
    paid_jobs_by_category as (
        select
            jobs_stat_daily.date::date                                        as date,
            jobs_stat_daily.id_country,
            countries.alpha_2                                                 as country,
            coalesce(jobs_stat_daily.job_category_id::integer, '-1'::integer) as id_job_category,
            jobs_stat_daily.id_project,
            sum(jobs_stat_daily.paid_job_count)                               as paid_jobs
        from
            aggregation.jobs_stat_daily
            left join dimension.countries
                      on jobs_stat_daily.id_country = countries.id
        where
            jobs_stat_daily.date::date = (current_date - 1)
        group by
            (jobs_stat_daily.date::date), jobs_stat_daily.id_country, countries.alpha_2,
            (coalesce(jobs_stat_daily.job_category_id::integer, '-1'::integer)), jobs_stat_daily.id_project
        having
            sum(jobs_stat_daily.paid_job_count) > 0
    ),
    paid_jobs_by_project as (
        select
            paid_jobs_by_category.date,
            paid_jobs_by_category.id_country,
            paid_jobs_by_category.country,
            paid_jobs_by_category.id_project,
            sum(paid_jobs_by_category.paid_jobs) as total
        from
            paid_jobs_by_category
        group by
            paid_jobs_by_category.date, paid_jobs_by_category.id_project, paid_jobs_by_category.id_country,
            paid_jobs_by_category.country
    ),
    percentage as (
        select
            paid_jobs_by_category.paid_jobs::numeric / paid_jobs_by_project.total as percent,
            paid_jobs_by_category.country,
            paid_jobs_by_category.id_country,
            paid_jobs_by_category.id_project,
            paid_jobs_by_category.date,
            paid_jobs_by_category.id_job_category
        from
            paid_jobs_by_category
            join paid_jobs_by_project
                 on paid_jobs_by_category.id_project = paid_jobs_by_project.id_project and
                    paid_jobs_by_category.date = paid_jobs_by_project.date and
                    paid_jobs_by_category.id_country = paid_jobs_by_project.id_country
    ),
    underutilised as (
        select
            v_budget_and_revenue.date,
            v_budget_and_revenue.country,
            countries.id                                                        as id_country,
            v_budget_and_revenue.id_project,
            sum(v_budget_and_revenue.budget) * extract(day from v_budget_and_revenue.date) /
              extract(day from (date_trunc('month'::text, v_budget_and_revenue.date)
                               + '1 month'::interval - '1 day'::interval)::date) -
              sum(v_budget_and_revenue.revenue_usd)                             as underutilised_budget
        from
            aggregation.v_budget_and_revenue
            left join dimension.countries
                      on lower(v_budget_and_revenue.country::text) = lower(countries.alpha_2::text)
        where
            v_budget_and_revenue.date = (current_date - 1)
        group by v_budget_and_revenue.date, v_budget_and_revenue.country, countries.id, v_budget_and_revenue.id_project
        having
                sum(v_budget_and_revenue.budget - v_budget_and_revenue.revenue_usd) > 0::double precision
    )
select
    underutilised.country,
    underutilised.id_project,
    percentage.id_job_category,
    sum(underutilised.underutilised_budget *
        percentage.percent::double precision)::numeric(19, 4) as underutilised_budget
from
    underutilised
    join percentage
         on underutilised.id_project = percentage.id_project and underutilised.date = percentage.date and
            percentage.id_country = underutilised.id_country
where
    percentage.id_job_category <> '-1'::integer
group by
    underutilised.date, underutilised.country, underutilised.id_project, percentage.id_job_category
having
    sum(underutilised.underutilised_budget * percentage.percent::double precision) >=
    (750.0 * extract(day from (current_date - 1)) / extract(day from
                                                          date_trunc('month', (current_date - 1)) +
                                                          '1 month'::interval -
                                                          '1 day'::interval));

grant select on affiliate.v_underutilized_budget_by_category to readonly;

grant delete, insert, select, update on affiliate.v_underutilized_budget_by_category to writeonly_pyscripts;

