create or replace view affiliate.v_job_stat_agg
            (country, publisher, date, id_project, project, is_job_ad_exchange_project, manager, parent_category_name,
             child_category_name, cnt_gather, click_price_usd, feed_cpc_usd, avg_click_price_usd, avg_feed_cpc_usd,
             uid_count, click_count, cost_sum, revenue_sum, reassembled_jobs_count, total_jobs_count)
as
select
    c.alpha_2     as country,
    ps.partner    as publisher,
    jsad.date,
    jsad.id_project,
    p.name        as project,
    case
        when p.hide_in_search then 1
        else 0
        end       as is_job_ad_exchange_project,
    m.code        as manager,
    vjkc.parent_category_name,
    vjkc.child_category_name,
    jsad.cnt_gather,
    jsad.click_price_usd,
    jsad.feed_cpc_usd,
    jsad.avg_click_price_usd,
    jsad.avg_feed_cpc_usd,
    jsad.uid_count,
    null::integer as click_count,
    null::numeric as cost_sum,
    null::numeric as revenue_sum,
    jsad.reassembled_jobs_count,
    jsad.total_jobs_count
from
    affiliate.job_stat_agg_daily jsad
    join affiliate.partner_settings ps
         on ps.source_id = jsad.id_source and ps.id_local_partner = jsad.id_partner
    join dimension.countries c
         on c.id = jsad.id_country
    left join aggregation.v_job_kaiju_category vjkc
              on vjkc.child_category_id = jsad.id_category
    left join affiliate.dic_manager m
              on m.id = ps.id_manager
    left join dimension.info_project p
              on p.country = jsad.id_country and p.id = jsad.id_project
union all
select
    vmma.country,
    vmma.publisher,
    vmma.click_date                       as date,
    vmma.id_project,
    p.name                                as project,
    case
        when p.hide_in_search then 1
        else 0
        end                               as is_job_ad_exchange_project,
    pds.manager,
    vmma.parent_category_name,
    vmma.category_name                    as child_category_name,
    null::integer                         as cnt_gather,
    round(vmma.client_cpc_usd, 2)         as click_price_usd,
    round(vmma.feed_cpc_usd, 2)           as feed_cpc_usd,
    null::numeric                         as avg_click_price_usd,
    null::numeric                         as avg_feed_cpc_usd,
    null::integer                         as uid_count,
    sum(vmma.certified_click_cnt)::bigint as click_count,
    sum(vmma.certified_cost_usd)          as cost_sum,
    sum(vmma.revenue_usd)                 as revenue_sum,
    null::numeric                         as reassembled_jobs_count,
    null::numeric                         as total_jobs_count
from
    affiliate.v_main_metrics_agg vmma
    join affiliate.partner_daily_snapshot pds
         on pds.date_diff = vmma.date_diff and pds.id_country = vmma.id_country and
            pds.partner::text = vmma.publisher::text and pds.feed_type::text = 'paid'::text
    left join dimension.info_project p
              on p.country = vmma.id_country and p.id = vmma.id_project
where
    vmma.click_date >= (current_date - 91)::timestamp without time zone
group by
    vmma.country, vmma.publisher, vmma.click_date, vmma.id_project, p.name,
    vmma.category_name,
    (
        case
            when p.hide_in_search then 1
            else 0
            end), pds.manager, vmma.parent_category_name, (round(vmma.client_cpc_usd, 2)),
    (round(vmma.feed_cpc_usd, 2));

alter table affiliate.v_job_stat_agg
    owner to ypr;

grant select on affiliate.v_job_stat_agg to readonly;

grant delete, insert, select, update on affiliate.v_job_stat_agg to writeonly_pyscripts;

