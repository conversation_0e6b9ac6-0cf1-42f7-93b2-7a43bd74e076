create or replace view affiliate.v_dtl_click_cost_report
            (date, partner, total_click_cnt, bot_click_cnt, bot_click_percent, bot_click_cost, duplicated_click_cnt,
             duplicated_click_percent, duplicated_click_cost, foreign_click_cnt, foreign_click_percent,
             foreign_click_cost, total_cost, certified_click_cnt, certified_revenue)
as
with
    t as (
        select
            vdcca.click_date::text                                                                             as date,
            vdcca.publisher                                                                                    as partner,
            vdcca.click_type,
            sum(coalesce(vdcca.pub_clicks, 0::bigint))                                                         as click_cnt,
            sum(coalesce(vdcca.cpc_pub_currency, 0::numeric) * coalesce(vdcca.pub_clicks, 0::bigint)::numeric) as cost
        from
            affiliate.v_dtl_click_cost_agg vdcca
        where
            vdcca.click_date >= (current_date - 70)
        group by (vdcca.click_date::text), vdcca.publisher, vdcca.click_type
    ),
    t2 as (
        select
            t.date,
            t.partner,
            sum(t.click_cnt) as total_click_cnt,
            sum(
                    case
                        when t.click_type = 'bot'::text then t.click_cnt
                        else 0::bigint::numeric
                        end) as bot_click_cnt,
            sum(
                    case
                        when t.click_type = 'bot'::text then t.cost
                        else 0::numeric
                        end) as bot_click_cost,
            sum(
                    case
                        when t.click_type = 'duplicated'::text then t.click_cnt
                        else 0::bigint::numeric
                        end) as duplicated_click_cnt,
            sum(
                    case
                        when t.click_type = 'duplicated'::text then t.cost
                        else 0::numeric
                        end) as duplicated_click_cost,
            sum(t.cost)      as total_cost,
            sum(
                    case
                        when t.click_type = 'certified'::text then t.click_cnt
                        else 0::bigint::numeric
                        end) as certified_click_cnt,
            sum(
                    case
                        when t.click_type = 'certified'::text then t.cost
                        else 0::numeric
                        end) as certified_revenue,
            sum(
                    case
                        when t.click_type = 'foreign'::text then t.click_cnt
                        else 0::bigint::numeric
                        end) as foreign_click_cnt,
            sum(
                    case
                        when t.click_type = 'foreign'::text then t.cost
                        else 0::numeric
                        end) as foreign_click_cost
        from
            t
        group by t.date, t.partner
    )
select
    t2.date,
    t2.partner,
    t2.total_click_cnt,
    t2.bot_click_cnt,
    round(100.0 * t2.bot_click_cnt / t2.total_click_cnt, 1)        as bot_click_percent,
    round(t2.bot_click_cost, 2)                                    as bot_click_cost,
    t2.duplicated_click_cnt,
    round(100.0 * t2.duplicated_click_cnt / t2.total_click_cnt, 1) as duplicated_click_percent,
    round(t2.duplicated_click_cost, 2)                             as duplicated_click_cost,
    t2.foreign_click_cnt,
    round(100.0 * t2.foreign_click_cnt / t2.total_click_cnt, 1)    as foreign_click_percent,
    round(t2.foreign_click_cost, 2)                                as foreign_click_cost,
    round(t2.total_cost, 2)                                        as total_cost,
    t2.certified_click_cnt,
    round(t2.certified_revenue, 2)                                 as certified_revenue
from
    t2;

alter table affiliate.v_dtl_click_cost_report
    owner to ypr;

grant select on affiliate.v_dtl_click_cost_report to readonly;

grant delete, insert, select, update on affiliate.v_dtl_click_cost_report to writeonly_pyscripts;

