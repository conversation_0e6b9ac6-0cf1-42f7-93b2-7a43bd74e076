create or replace view affiliate.v_client_conversions
            (session_date, project_id, project_name, campaign_id, traffic_name, channel, away_revenue,
             away_revenue_client_currency, aways, conversions, country, type, manager, total_project_away_revenue,
             total_project_away_revenue_client_currency, total_project_aways, total_project_conversions, target_cpa_usd,
             target_cpa_client_currency, total_project_target_cpa_usd, total_project_target_cpa_client_currency,
             target_action)
as
with
    exclude_all_countries_list as (
        select
            crpl.date_from                       as min_date,
            coalesce(crpl.date_to, current_date) as max_date,
            crpl.id_project
        from
            affiliate.conversion_project_list crpl
        where
              crpl.country::text = 'ALL'::text
          and crpl.record_type::text = 'exclude'::text
    ),
    include_countries_list as (
            select
                min(crpl.date_from)                       as min_date,
                max(coalesce(crpl.date_to, current_date)) as max_date,
                crpl.country
            from
                affiliate.conversion_project_list crpl
            where
                  crpl.country::text <> 'ALL'::text
              and crpl.record_type::text = 'include'::text
            group by crpl.country
    ),
    include_project_list as (
        select
            crpl.date_from                                              as min_date,
            coalesce(crpl.date_to, current_date) as max_date,
            crpl.country,
            crpl.id_project
        from
            affiliate.conversion_project_list crpl
        where
              crpl.country::text <> 'ALL'::text
          and crpl.record_type::text = 'include'::text
    ),
    target_actions_raw as (
        select
            auction_user_target_action.country,
            auction_user_target_action.id_project,
            auction_user_target_action.target_action,
            auction_user_target_action.date::date                                                                                                                                                as record_date,
            row_number()
            over (partition by auction_user_target_action.country, auction_user_target_action.id_project, (auction_user_target_action.date::date) order by auction_user_target_action.date desc) as row_num
        from
            aggregation.auction_user_target_action
    ),
    target_actions as (
        select
            country,
            id_project,
            target_action,
            record_date,
            case
                when lag(record_date) over (partition by country, id_project order by record_date) is null
                    then '2022-01-01'::date
                else record_date end                                                                               as min_dt,
            coalesce(lead(record_date) over (partition by country, id_project order by record_date),
                     current_date)                                                                                 as max_dt
        from
            target_actions_raw
        where
            row_num = 1
    ),
    manager_dic as (
        select
            ps.partner,
            m.name                     as manager,
            pscl.update_datetime::date as min_date,
            coalesce(lead(pscl.update_datetime::date)
                     over (partition by pscl.source_id, pscl.local_id, pscl.field_name order by pscl.update_datetime),
                     current_date)     as max_date
        from
            affiliate.partner_settings_change_log pscl
            join affiliate.partner_settings ps
                 on ps.source_id = pscl.source_id and ps.id_local_partner = pscl.local_id
            left join affiliate.dic_manager m
                      on m.id = pscl.value_after::integer
        where
            pscl.field_name::text = 'id_soska_user'::text
    ),
    internal_conversion_data as (
        select
            pcd.session_date,
            pcd.project_id,
            pcd.campaign_id,
            pcd.project_name,
            uts.name                              as traffic_name,
            uts.channel::character varying        as channel,
            sum(pcd.away_revenue)                 as away_revenue,
            sum(pcd.away_revenue_origin_currency) as away_revenue_client_currency,
            sum(pcd.aways)                        as aways,
            sum(pcd.conversions)                  as conversions,
            countries.alpha_2                     as country,
            pp.type,
            pcd.country_id
        from
            aggregation.project_conversions_daily pcd
            join dimension.info_calendar ic
                 on ic.dt = pcd.session_date
            join dimension.countries
                 on pcd.country_id = countries.id
            left join dimension.u_traffic_source uts
                      on uts.country = pcd.country_id and uts.id = pcd.id_current_traf_source and
                         uts.channel::text = 'Affiliate'::text
             join affiliate.mv_tracking_project_list pp
                  on pp.id_country = pcd.country_id and pp.id_project = pcd.project_id and
                     pp.conversion_cnt >= 5::numeric and ic.date_diff >= pp.conversion_start_date_diff and
                     ((pp.type = 'Postback'::text and ic.date_diff <= (pp.conversion_max_date_diff + 7)) or
                      (pp.type <> 'Postback'::text and pp.revenue_usd > 0::double precision)) and
                     case
                         when pp.type = 'Postback'::text then pcd.metric::text = 'aways'::text
                         else pcd.metric::text = 'applies'::text
                         end
            left join exclude_all_countries_list el
                      on el.id_project = pcd.project_id and pcd.session_date >= el.min_date and
                         pcd.session_date < el.max_date
            left join include_countries_list icl
                      on icl.country::text = countries.alpha_2::text and pcd.session_date >= icl.min_date and
                         pcd.session_date < icl.max_date
            left join include_project_list ipl
                      on ipl.country::text = countries.alpha_2::text and ipl.id_project = pcd.project_id and
                         pcd.session_date >= ipl.min_date and pcd.session_date < ipl.max_date
        where
                pcd.session_date >= (date_trunc('month'::text, now()) - 18::double precision * '1 mon'::interval)::date
          and   pcd.session_date < '2024-06-01'
          and   el.id_project is null
          and   (icl.country is null or ipl.id_project is not null)
        group by
            pcd.session_date, pcd.project_id, pcd.project_name, uts.name, uts.channel, countries.alpha_2,
            pcd.campaign_id, pcd.country_id, pp.type

         union all

         select
             ic.dt                                                                  as session_date,
             pcd.id_project                                                         as project_id,
             pcd.id_campaign                                                        as campaign_id,
             ip.name                                                                as project_name,
             uts.name                                                               as traffic_name,
             uts.channel::character varying                                         as channel,
             sum(coalesce(pcd.revenue_usd, 0::numeric::double precision))           as away_revenue,
             sum(coalesce(pcd.revenue_user_currency, 0::numeric::double precision)) as away_revenue_client_currency,

             sum(coalesce(pcd.jdp_away_count, 0::bigint::double precision))         as aways,
             sum(case
                     when pcd.job_destination = any
                          (array [1::double precision, 4::double precision])
                         then coalesce(pcd.unic_client_conversion_count, 0::bigint)
                     else coalesce(pcd.jooble_apply_count, 0::bigint)
                 end)                                                               as conversions,
             countries.alpha_2                                                      as country,
             pp.type,
             pcd.country_id
         from
             aggregation.click_data_agg pcd
             join dimension.info_calendar ic
                  on ic.date_diff = pcd.action_datediff
             join dimension.countries
                  on pcd.country_id = countries.id
             join dimension.info_project ip
                  on ip.country = pcd.country_id and ip.id = pcd.id_project
             join affiliate.mv_tracking_project_list pp
                  on pp.id_country = pcd.country_id and pp.id_project = pcd.id_project and
                     pp.conversion_cnt >= 5::numeric and
                     ic.date_diff >= pp.conversion_start_date_diff and
                     (pp.type = 'Postback'::text and
                      ic.date_diff <= (pp.conversion_max_date_diff + 7) or
                      pp.type <> 'Postback'::text and pp.revenue_usd > 0::double precision) and
                      case
                          when pp.type = 'Postback'::text then pcd.job_destination = any
                                                              (array [1::double precision, 4::double precision])
                          else pcd.job_destination = 3::double precision
                          end
             left join dimension.u_traffic_source uts
                       on uts.country = pcd.country_id and
                          uts.id = pcd.id_current_traf_source and
                          uts.channel::text = 'Affiliate'::text
             left join exclude_all_countries_list el
                       on el.id_project = pcd.id_project and ic.dt >= el.min_date and
                          ic.dt < el.max_date
             left join include_countries_list icl
                       on icl.country::text = countries.alpha_2::text and
                          ic.dt >= icl.min_date and ic.dt < icl.max_date
             left join include_project_list ipl
                       on ipl.country::text = countries.alpha_2::text and
                          ipl.id_project = pcd.id_project and
                          ic.dt >= ipl.min_date and
                          ic.dt < ipl.max_date
     where
           ic.dt >= '2024-06-01'
           and ic.dt >=
               (date_trunc('month'::text, now()) - 18::double precision * '1 mon'::interval)::date
       and el.id_project is null
       and (icl.country is null or ipl.id_project is not null)
     group by
         ic.dt, pcd.id_project, ip.name, uts.name, uts.channel, countries.alpha_2,
         pcd.id_campaign, pcd.country_id, pp.type),
    target_cpa_source as (
        select distinct on (campaign_cpa.country, campaign_cpa.id_campaign, (campaign_cpa.date::date))
            campaign_cpa.country,
            campaign_cpa.id_campaign,
            campaign_cpa.date::date as date,
            campaign_cpa.cpa
        from
            aggregation.campaign_cpa
        where
            campaign_cpa.id_campaign <> 0
        order by
            campaign_cpa.country, campaign_cpa.id_campaign, (campaign_cpa.date::date), campaign_cpa.date desc,
            campaign_cpa.cpa
    ),
    target_cpa_date_range as (
        select
            t.country,
            c.id                                                                      as country_id,
            t.id_campaign,
            ac.id_project,
            t.date                                                                    as date_start,
            t.cpa,
            lead(t.date) over (partition by t.country, t.id_campaign order by t.date) as date_end,
            row_number() over (partition by t.country, t.id_campaign order by t.date) as row_num
        from
            target_cpa_source t
            left join dimension.countries c
                      on c.alpha_2::text = t.country
            left join imp.auction_campaign ac
                      on ac.country_id = c.id and ac.id = t.id_campaign
    ),
    campaign_target_cpa as (
        select
            tdr.country_id,
            tdr.country,
            tdr.id_project,
            tdr.id_campaign,
            case
                when tdr.row_num = 1 then (date_trunc('month'::text, now()) - 18::double precision * '1 mon'::interval)::date
                else tdr.date_start
                end                                  as date_start,
            coalesce(tdr.date_end - 1, current_date) as date_end,
            tdr.cpa
        from
            target_cpa_date_range tdr
    ),
    info_currency_data as (
       select distinct on ((ich.date::date), ic.name)
                           ich.date::date,
                           ic.name        as currency,
                           ich.value_to_usd
                       from
                           dimension.info_currency_history ich
                           join dimension.info_currency ic
                                on ic.country = ich.country and ic.id = ich.id_currency
                       where
                               ich.date::date >=
                               (date_trunc('month'::text, now()) - 18::double precision * '1 mon'::interval)::date
                       order by (ich.date::date), ic.name, ich.date desc
    ),
    internal_conversions_final as (
        select
            t.session_date,
            t.country_id,
            t.project_id,
            t.project_name,
            t.campaign_id,
            t.traffic_name,
            t.channel,
            t.away_revenue,
            t.away_revenue_client_currency,
            t.aways,
            t.conversions,
            t.country,
            t.type,
            ta.target_action,
            info_currency_history.value_to_usd,
            manager_dic.manager,
            sum(t.away_revenue)
            over (partition by t.session_date, t.project_id, t.project_name, t.country_id, t.type)                    as total_project_away_revenue,
            sum(t.away_revenue_client_currency)
            over (partition by t.session_date, t.project_id, t.project_name, t.country_id, t.type)                    as total_project_away_revenue_client_currency,
            sum(t.aways)
            over (partition by t.session_date, t.project_id, t.project_name, t.country_id, t.type)                    as total_project_aways,
            sum(t.conversions)
            over (partition by t.session_date, t.project_id, t.project_name, t.country_id, t.type)                    as total_project_conversions,
            sum(t.aways::double precision * ctc.cpa)
            over (partition by t.session_date, t.project_id, t.project_name, t.country_id, t.type)                    as total_project_avg_cpa_numerator,
            sum(t.aways::double precision * ctc.cpa * info_currency_history.value_to_usd::double precision)
            over (partition by t.session_date, t.project_id, t.project_name, t.country_id, t.type)                    as total_project_avg_cpa_usd_numerator,
            sum(
            case
                when ctc.cpa > 0::double precision then t.aways
                else null::bigint
                end)
            over (partition by t.session_date, t.project_id, t.project_name, t.country_id, t.type)                    as total_project_cpa_denominator
        from
            internal_conversion_data t
            left join manager_dic
                      on t.traffic_name::text = manager_dic.partner::text and t.session_date >= manager_dic.min_date and
                         t.session_date < manager_dic.max_date
            left join imp.auction_campaign ac
                      on ac.country_id = t.country_id and ac.id = t.campaign_id
            left join dimension.info_currency
                      on info_currency.country = ac.country_id and info_currency.id = ac.currency
            left join (select
                            date as from_date,
                            currency,
                            value_to_usd,
                            coalesce(lead(d.date::date) over (partition by d.currency order by d.date::date), current_date) as to_date
                        from info_currency_data as d) info_currency_history
                      on info_currency_history.currency::text = info_currency.name::text and
                         t.session_date >= info_currency_history.from_date and
                         t.session_date < info_currency_history.to_date
            left join campaign_target_cpa ctc
                      on ctc.country_id = t.country_id and
                         ctc.id_campaign = t.campaign_id and
                         t.session_date >= ctc.date_start and
                         t.session_date <= ctc.date_end
             left join target_actions ta
                      on ta.country = t.country and
                         ta.id_project = t.project_id and
                         t.session_date >= ta.min_dt and t.session_date < ta.max_dt
    ),
    manual_conversions_data as (
        select
            conversions.date,
            conversions.country,
            conversions.id_project                  as project_id,
            conversions_source.id_traffic_source,
            max(conversions.conversions)            as total_conversions,
            sum(conversions_source.cnt_conversions) as conversions
        from
            imp_statistic.conversions
            join imp_statistic.conversions_source
                 on conversions.id = conversions_source.id_conversion
            left join dimension.countries countries_1
                      on conversions.country::text = countries_1.alpha_2::text
            left join dimension.u_traffic_source
                      on countries_1.id = u_traffic_source.country and
                         conversions_source.id_traffic_source = u_traffic_source.id
            left join exclude_all_countries_list el
                      on el.id_project = conversions.id_project and conversions.date >= el.min_date and
                         conversions.date < el.max_date
            left join include_countries_list icl
                      on icl.country::text = conversions.country::text and conversions.date >= icl.min_date and
                         conversions.date < icl.max_date
            left join include_project_list ipl
                      on ipl.country::text = conversions.country::text and ipl.id_project = conversions.id_project and
                         conversions.date >= ipl.min_date and conversions.date < ipl.max_date
            left join dimension.info_calendar as ic
                      on ic.dt = conversions.date
            left join affiliate.mv_tracking_project_list pp
                      on pp.id_country = countries_1.id and
                         pp.id_project = conversions.id_project and pp.conversion_cnt > 5 and
                         ic.date_diff >= pp.conversion_start_date_diff and
                         ic.date_diff <= (pp.conversion_max_date_diff + 7)
        where
              conversions.id_date_period = 1
          and conversions.date >= (date_trunc('month'::text, now()) - 18::double precision * '1 mon'::interval)::date
          and el.id_project is null
          and (icl.country is null or ipl.id_project is not null)
          and pp.conversion_cnt is null
        group by conversions.date, conversions.country, conversions.id_project, conversions_source.id_traffic_source
    ),
    manual_conversions_revenue as (
        select
            jacaod.country_id,
            info_calendar.dt                           as date,
            jacaod.id_project,
            jacaod.id_traf_source,
            sum(jacaod.click_count) as click_count,
            sum(jacaod.total_value) as total_value
        from
            affiliate.jdp_away_clicks_agg_old_data as jacaod
            join dimension.info_calendar
                 on info_calendar.date_diff = jacaod.action_datediff
            join dimension.countries
                 on countries.id = jacaod.country_id
            join (select distinct
                      manual_conversions_data.country,
                      manual_conversions_data.project_id
                  from
                      manual_conversions_data) t
                 on t.country::text = countries.alpha_2::text and
                    t.project_id = jacaod.id_project
        where
             info_calendar.dt >=
             (date_trunc('month'::text, now()) - 18::double precision * '1 mon'::interval)::date
         and info_calendar.dt < '2024-06-01'
        group by
             jacaod.country_id, info_calendar.dt, jacaod.id_project,
             jacaod.id_traf_source

        union all

        select
               jacaod.country_id,
               info_calendar.dt           as date,
               jacaod.id_project,
               jacaod.id_traf_source,
               sum(jacaod.jdp_away_count) as click_count,
               sum(jacaod.revenue_usd)    as total_value
         from
               aggregation.click_data_agg as jacaod
               join dimension.info_calendar
                    on info_calendar.date_diff = jacaod.action_datediff
               join dimension.countries
                    on countries.id = jacaod.country_id
               join (select distinct
                         manual_conversions_data.country,
                         manual_conversions_data.project_id
                     from
                         manual_conversions_data) t
                    on t.country::text = countries.alpha_2::text and
                       t.project_id = jacaod.id_project
         where
               job_destination in (1, 4)
           and info_calendar.dt >= '2024-06-01'
           and info_calendar.dt >=
               (date_trunc('month'::text, now()) - 18::double precision * '1 mon'::interval)::date
         group by
               jacaod.country_id, info_calendar.dt, jacaod.id_project,
               jacaod.id_traf_source
       ),
    manual_conversions_final as (
       select
            revenue.date                      as session_date,
            revenue.id_project                as project_id,
            p.name                            as project_name,
            null::integer                     as campaign_id,
            u_traffic_source.name             as traffic_name,
            u_traffic_source.channel,
            sum(revenue.total_value)          as away_revenue,
            sum(revenue.click_count)          as aways,
            max(conversion_total.conversions) as conversions,
            countries.alpha_2                 as country,
            'manual'::text                    as type,
            ta.target_action
        from
            manual_conversions_revenue revenue
            join dimension.countries
                 on revenue.country_id = countries.id
            left join manual_conversions_data conversion_total
                      on countries.alpha_2::text = conversion_total.country::text and
                         revenue.id_project = conversion_total.project_id and revenue.date = conversion_total.date and
                         revenue.id_traf_source = conversion_total.id_traffic_source
            left join dimension.u_traffic_source
                      on revenue.country_id = u_traffic_source.country and revenue.id_traf_source = u_traffic_source.id
            left join dimension.info_project p
                      on p.country = revenue.country_id and p.id = revenue.id_project
            left join target_actions ta
                      on ta.country = countries.alpha_2::text and
                         ta.id_project = revenue.id_project and
                         revenue.date >= ta.min_dt and revenue.date < ta.max_dt
        group by
            countries.alpha_2, p.name, revenue.id_project, revenue.date, u_traffic_source.name,
            u_traffic_source.channel, ta.target_action
    )
select
    r.session_date,
    r.project_id::integer                                 as project_id,
    r.project_name,
    r.campaign_id::integer                                as campaign_id,
    r.traffic_name,
    r.channel,
    r.away_revenue::numeric                               as away_revenue,
    r.away_revenue_client_currency::numeric               as away_revenue_client_currency,
    r.aways::numeric                                      as aways,
    r.conversions::bigint                                 as conversions,
    r.country,
    r.type,
    r.manager,
    r.total_project_away_revenue::numeric                 as total_project_away_revenue,
    r.total_project_away_revenue_client_currency::numeric as total_project_away_revenue_client_currency,
    r.total_project_aways::numeric                        as total_project_aways,
    r.total_project_conversions,
    ctc.cpa * r.value_to_usd::double precision            as target_cpa_usd,
    ctc.cpa                                               as target_cpa_client_currency,
    case
        when r.total_project_cpa_denominator > 0::numeric::double precision
            then r.total_project_avg_cpa_usd_numerator / r.total_project_cpa_denominator
        else null::double precision
        end                                               as total_project_target_cpa_usd,
    case
        when r.total_project_cpa_denominator > 0::numeric::double precision
            then r.total_project_avg_cpa_numerator / r.total_project_cpa_denominator
        else null::double precision
        end                                               as total_project_target_cpa_client_currency,
    r.target_action
from
    internal_conversions_final r
    left join campaign_target_cpa ctc
              on ctc.country_id = r.country_id and ctc.id_campaign::double precision = r.campaign_id and
                 r.session_date >= ctc.date_start and r.session_date <= ctc.date_end
where
    r.channel::text = 'Affiliate'::text
union all
select
    manual_conversions_final.session_date,
    manual_conversions_final.project_id::integer                                                                                                                            as project_id,
    manual_conversions_final.project_name,
    manual_conversions_final.campaign_id,
    manual_conversions_final.traffic_name,
    manual_conversions_final.channel,
    manual_conversions_final.away_revenue::numeric                                                                                                                          as away_revenue,
    null::numeric                                                                                                                                                           as away_revenue_client_currency,
    manual_conversions_final.aways::numeric                                                                                                                                 as aways,
    manual_conversions_final.conversions,
    manual_conversions_final.country,
    manual_conversions_final.type,
    manager_dic.manager,
    sum(manual_conversions_final.away_revenue::numeric)
    over (partition by manual_conversions_final.session_date, manual_conversions_final.project_id, manual_conversions_final.project_name, manual_conversions_final.country) as total_project_away_revenue,
    null::numeric                                                                                                                                                           as total_project_away_revenue_client_currency,
    sum(manual_conversions_final.aways::numeric)
    over (partition by manual_conversions_final.session_date, manual_conversions_final.project_id, manual_conversions_final.project_name, manual_conversions_final.country) as total_project_aways,
    sum(manual_conversions_final.conversions)
    over (partition by manual_conversions_final.session_date, manual_conversions_final.project_id, manual_conversions_final.project_name, manual_conversions_final.country) as total_project_conversions,
    null::double precision                                                                                                                                                  as target_cpa_usd,
    null::double precision                                                                                                                                                  as target_cpa_client_currency,
    null::double precision                                                                                                                                                  as total_project_target_cpa_usd,
    null::double precision                                                                                                                                                  as total_project_target_cpa_client_currency,
    manual_conversions_final.target_action
from
    manual_conversions_final
    left join manager_dic
              on manual_conversions_final.traffic_name::text = manager_dic.partner::text and
                 manual_conversions_final.session_date >= manager_dic.min_date and
                 manual_conversions_final.session_date < manager_dic.max_date
where
    manual_conversions_final.channel::text = 'Affiliate'::text;

alter table affiliate.v_client_conversions
    owner to ypr;

grant select on affiliate.v_client_conversions to readonly;

grant delete, insert, select, update on affiliate.v_client_conversions to writeonly_pyscripts;

