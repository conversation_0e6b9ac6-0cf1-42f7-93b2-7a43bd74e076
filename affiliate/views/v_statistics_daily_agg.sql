create or replace view affiliate.v_statistics_daily_agg
            (date, country, name_country, partner, manager, is_local, is_bot, id_project, project, id_campaign,
             campaign, is_job_ad_exchange_project, has_postback, feed_type, is_billable, is_duplicated, id_sub_client,
             partner_cpc_usd, feed_cpc_usd, client_cpc_usd, is_project_accepted_location, external_click_cnt,
             redirected_click_cnt, closed_job_click_cnt, away_click_cnt, jdp_click_cnt, jdp_away_click_cnt,
             away_conversion_cnt, jdp_away_conversion_cnt, is_internal_bot_method, is_external_stat_client,
             lower_cpc_flow_type, category_name, parent_category_name, is_redirected, redirect_id_campaign,
             redirect_campaign_name, redirect_id_project, redirect_project_name, click_type, redirect_from_id_project,
             redirect_from_project_name, redirect_from_id_campaign, redirect_from_campaign_name, is_internal_duplicated,
             non_local_away_attempt_cnt, feed_cpc_pub_currency, serp_revenue_usd, is_paid_overflow,
             is_conversion_considered, target_action, feed_cpa_usd, feed_cpa_pub_currency, api_requests_count,
             publisher_type, fixed_cost_usd, apply_cnt, apply_revenue_usd, tracking_type, bot_reason, bot_reason_group)
as
with
    exclude_all_countries_list as (
        select
            crpl.date_from                       as min_date,
            coalesce(crpl.date_to, current_date) as max_date,
            crpl.id_project
        from
            affiliate.conversion_project_list crpl
        where
              crpl.country::text = 'ALL'::text
          and crpl.record_type::text = 'exclude'::text
    ),
    include_countries_list as (
            select
                min(crpl.date_from)                       as min_date,
                max(coalesce(crpl.date_to, current_date)) as max_date,
                crpl.country
            from
                affiliate.conversion_project_list crpl
            where
                  crpl.country::text <> 'ALL'::text
              and crpl.record_type::text = 'include'::text
            group by crpl.country
    ),
    include_project_list as (
        select
            crpl.date_from                                              as min_date,
            coalesce(crpl.date_to, current_date) as max_date,
            crpl.country,
            crpl.id_project
        from
            affiliate.conversion_project_list crpl
        where
              crpl.country::text <> 'ALL'::text
          and crpl.record_type::text = 'include'::text
    ),
    target_actions_raw as (
        select
            auction_user_target_action.country,
            auction_user_target_action.id_project,
            auction_user_target_action.target_action,
            auction_user_target_action.date::date                                                                                                                                                as record_date,
            row_number()
            over (partition by auction_user_target_action.country, auction_user_target_action.id_project, (auction_user_target_action.date::date) order by auction_user_target_action.date desc) as row_num
        from
            aggregation.auction_user_target_action
    ),
    target_actions as (
        select
            country,
            id_project,
            target_action,
            record_date,
            case
                when lag(record_date) over (partition by country, id_project order by record_date) is null
                    then '2022-01-01'::date
                else record_date end as min_dt,
            coalesce(lead(record_date) over (partition by country, id_project order by record_date),
                     current_date)   as max_dt
        from
            target_actions_raw
        where
            target_actions_raw.row_num = 1
    ),
    affiliate_data as (
        select
            d.dt                     as date,
            lower(c.alpha_2::text)   as country,
            c.name_country_eng       as name_country,
            aff.partner,
            pds.manager,
            aff.user_country,
            case
                when aff.user_country::text = lower(c.alpha_2::text) or
                     aff.user_country::text = 'gb'::text and lower(c.alpha_2::text) = 'uk'::text then 1
                when aff.user_country is null then null::integer
                else 0
                end                  as is_local,
            aff.is_bot,
            aff.id_project,
            p.name                   as project,
            aff.id_campaign,
            ac.name                  as campaign,
            case
                when p.hide_in_search then 1
                else 0
                end                  as is_job_ad_exchange_project,
            case
                when red_pr.hide_in_search then 1
                else 0
                end                  as is_job_ad_exchange_redirect_project,
            case
                when pp.id_country is not null
                     and aff.is_bot = 0 then 1
                else 0
                end                  as has_postback,
            case
                when coalesce(lower(p.name::text), ''::text) ~~ 'j-vers.%'::text then 'j-vers'::character varying(100)
                else pds.feed_type
                end                  as feed_type,
            aff.is_billable,
            aff.is_duplicated,
            aff.id_sub_client,
            aff.external_click_cnt,
            aff.closed_job_click_cnt,
            aff.away_click_cnt,
            aff.jdp_click_cnt,
            aff.jdp_away_click_cnt,
            aff.away_conversion_cnt,
            aff.jdp_away_conversion_cnt,
            aff.partner_cpc_usd,
            aff.feed_cpc_usd,
            aff.client_cpc_usd,
            aff.is_internal_bot_method,
            aff.is_external_stat_client,
            aff.lower_cpc_flow_type,
            vjkc.child_category_name as category_name,
            vjkc.parent_category_name,
            aff.is_redirected,
            aff.redirect_id_campaign,
            aff.redirect_id_project,
            red_pr.name              as redirect_project_name,
            red_camp.name            as redirect_campaign_name,
            dct.name                 as click_type,
            aff.is_project_accepted_location,
            aff.is_internal_duplicated,
            aff.non_local_away_attempt_cnt,
            aff.feed_cpc,
            aff.is_paid_overflow,
            case
                when el.id_project is null and (icl.country is null or ipl.id_project is not null) and
                     pp.id_country is not null and aff.is_bot = 0
                     then 1
                else 0
                end                  as is_conversion_considered,
            case
                when pp.type in ('Apply on Jooble', 'Easy Apply') then 'Apply'
                else ta.target_action
                end                  as target_action,
            case
                when pp.type in ('Apply on Jooble', 'Easy Apply') then 'Apply'
                else ta1.target_action
                end                  as redirect_project_target_action,
            aff.feed_cpa_usd,
            aff.feed_cpa             as feed_cpa_pub_currency,
            ps.source_id,
            ps.id_local_partner,
            case
                when ps.is_api_publisher = 1 then 'API'
                when ps.is_cpa_publisher = 1 then 'CPA feed'
                when ps.id_local_partner is not null then 'CPC feed'
                end                  as publisher_type,
            aff.apply_cnt,
            aff.apply_revenue_usd,
            pp.type                  as tracking_type,
            dbr.name                 as bot_reason,
            dbr.reason_group         as bot_reason_group
        from
            affiliate.statistics_daily_agg aff
            left join affiliate.partner_daily_snapshot pds
                      on pds.date_diff = aff.date_diff and pds.partner::text = aff.partner::text
            join dimension.countries c
                 on c.id = aff.id_country
            join dimension.info_calendar d
                 on d.date_diff = aff.date_diff
            left join dimension.info_project p
                      on p.country = aff.id_country and p.id = aff.id_project
            left join imp.auction_campaign ac
                      on ac.country_id = aff.id_country and ac.id = aff.id_campaign
            left join dimension.info_project red_pr
                      on red_pr.country = aff.id_country and red_pr.id = aff.redirect_id_project
            left join imp.auction_campaign red_camp
                      on red_camp.country_id = aff.id_country and red_camp.id = aff.redirect_id_campaign
            left join affiliate.mv_tracking_project_list pp
                      on pp.id_country = aff.id_country and
                         pp.id_project = coalesce(aff.redirect_id_project, aff.id_project) and
                         pp.conversion_cnt > 5 and aff.date_diff >= pp.conversion_start_date_diff and
                         ((pp.type = 'Postback'::text and aff.date_diff <= (pp.conversion_max_date_diff + 7)) or
                         (pp.type <> 'Postback'::text and pp.revenue_usd > 0::double precision))
            left join aggregation.v_job_kaiju_category vjkc
                      on vjkc.child_category_id = aff.id_category
            left join affiliate.dic_click_type dct
                      on dct.id = aff.click_type
            left join exclude_all_countries_list el
                      on el.id_project = coalesce(aff.redirect_id_project, aff.id_project) and d.dt >= el.min_date and
                         d.dt < el.max_date
            left join include_countries_list icl
                      on icl.country::text = c.alpha_2::text and d.dt >= icl.min_date and d.dt < icl.max_date
            left join include_project_list ipl
                      on ipl.country::text = c.alpha_2::text and
                         ipl.id_project = coalesce(aff.redirect_id_project, aff.id_project) and d.dt >= ipl.min_date and
                         d.dt < ipl.max_date
            left join target_actions ta
                      on ta.country = c.alpha_2::text and ta.id_project = aff.id_project and
                         d.dt >= ta.min_dt and d.dt < ta.max_dt
            left join target_actions ta1
                      on ta1.country = c.alpha_2::text and ta1.id_project = aff.redirect_id_project and
                         d.dt >= ta1.min_dt and d.dt < ta1.max_dt
            left join affiliate.partner_settings ps
                      on ps.country::text = lower(c.alpha_2::text) and
                         lower(ps.partner::text) = lower(aff.partner::text)
            left join affiliate.dic_bot_reasons as dbr
                      on aff.bot_reason_id = dbr.id and aff.click_type = 1
        where
                d.dt >= (date_trunc('month'::text, now()) - 18::double precision * '1 mon'::interval)::date
    ),
    monthly_dates_prepared as (
       select
            ic.dt                                                         as date_from,
            (ic.dt + '1 mon'::interval - '1 day'::interval)::date         as date_to,
            ich.value_to_usd,
            row_number() over (partition by ic.dt order by ich.date desc) as row
        from
            dimension.info_calendar ic
            join (select distinct on ((ich_1.date::date), ich_1.country, c.name)
                     ich_1.date::date as date,
                     ich_1.country,
                     c.name           as currency,
                     ich_1.value_to_usd
                 from
                     dimension.info_currency_history ich_1
                     join dimension.info_currency c
                          on c.id = ich_1.id_currency and c.country = ich_1.country
                 order by (ich_1.date::date), ich_1.country, c.name, ich_1.date) as ich
                on ich.date::date <= ic.dt
        where
              ic.dt >= '2024-04-08'::date
          and ic.dt <= (date_trunc('month'::text, current_date + '1 mon'::interval) +
                        6::double precision * '1 day'::interval)::date
          and date_part('day'::text, ic.dt) = 8::double precision
          and ich.currency::text = 'EUR'::text
          and ich.country = '2'::smallint
       ),
    monthly_dates as (
       select
            date_from,
            date_to,
            value_to_usd
       from
            monthly_dates_prepared
       where
            row = 1
       ),
    click_data as (
        select
            vd.date,
            vd.country,
            vd.partner,
            vd.manager,
            ps.feed_type,
            vd.publisher_type,
            sum(vd.external_click_cnt) as total_clicks
        from
            affiliate_data as vd
            join affiliate.partner_settings as ps
                 on ps.partner = vd.partner
        where
              ps.partner ilike '%linkedin%'
          and date >= '2024-04-01'
          and click_type = 'certified'
          and external_click_cnt > 0
        group by vd.date, vd.country, vd.partner, vd.manager, ps.feed_type, vd.publisher_type
    ),
    dates_with_click_data as (
        select distinct
            vd.date
        from
            click_data as vd
    ),
    daily_dates as (
        select
            ic.dt                                                                 as date,
            md.date_from,
            md.date_to,
            case when now() between md.date_from and md.date_to then 1 else 0 end as current_period,
            case
                when dwcd.date is not null
                    and case when now() between md.date_from and md.date_to then 1 else 0 end <> 1
                    then 1
                else 0
                end                                                               as use_date,
            md.value_to_usd
        from
            dimension.info_calendar as ic
            join monthly_dates as md
                 on ic.dt between md.date_from and md.date_to
            left join dates_with_click_data as dwcd
                      on ic.dt = dwcd.date
        where
              dt >= '2024-04-08'
          and dt <=
              (date_trunc('month', current_date + '1 month'::interval) + 6 * '1 day'::interval)::date
    ),
    clean_dates as (
        select *,
               sum(
               case when use_date = 1 then use_date when current_period = 1 then current_period else 0 end)
               over (partition by use_date, current_period, date_from) as duration_days
        from
            daily_dates
    ),
    per_month_rate as (
        select *,
               case
                   when duration_days > 0 then '1105.84'::numeric / duration_days
                   else 0
                   end as cost_eur_per_day
        from
            clean_dates
    )
select
    a.date,
    a.country,
    a.name_country,
    a.partner,
    a.manager,
    a.is_local,
    a.is_bot,
    a.id_project,
    a.project,
    a.id_campaign,
    a.campaign,
    a.is_job_ad_exchange_project,
    a.has_postback,
    a.feed_type,
    a.is_billable,
    a.is_duplicated,
    a.id_sub_client,
    a.partner_cpc_usd,
    a.feed_cpc_usd,
    a.client_cpc_usd,
    a.is_project_accepted_location,
    sum(a.external_click_cnt)                                                as external_click_cnt,
    sum(
            case
                when a.is_redirected = 1 then a.external_click_cnt
                else 0
                end)                                                         as redirected_click_cnt,
    sum(
            case
                when a.is_redirected = 1 then 0
                else a.closed_job_click_cnt
                end)                                                         as closed_job_click_cnt,
    sum(
            case
                when a.is_redirected = 1 then 0
                else a.away_click_cnt
                end)                                                         as away_click_cnt,
    sum(
            case
                when a.is_redirected = 1 then 0
                else a.jdp_click_cnt
                end)                                                         as jdp_click_cnt,
    sum(
            case
                when a.is_redirected = 1 then 0
                else a.jdp_away_click_cnt
                end)                                                         as jdp_away_click_cnt,
    sum(
            case
                when a.is_redirected = 1 then 0
                else a.away_conversion_cnt
                end)                                                         as away_conversion_cnt,
    sum(
            case
                when a.is_redirected = 1 then 0
                else a.jdp_away_conversion_cnt
                end)                                                         as jdp_away_conversion_cnt,
    a.is_internal_bot_method,
    a.is_external_stat_client,
    a.lower_cpc_flow_type,
    a.category_name,
    a.parent_category_name,
    a.is_redirected,
    a.redirect_id_campaign,
    a.redirect_campaign_name,
    a.redirect_id_project,
    a.redirect_project_name,
    a.click_type,
    a.id_project                                                             as redirect_from_id_project,
    a.project                                                                as redirect_from_project_name,
    a.id_campaign                                                            as redirect_from_id_campaign,
    a.campaign                                                               as redirect_from_campaign_name,
    a.is_internal_duplicated,
    sum(
            case
                when a.is_redirected = 1 then 0
                else a.non_local_away_attempt_cnt
                end)                                                         as non_local_away_attempt_cnt,
    a.feed_cpc                                                               as feed_cpc_pub_currency,
    0::numeric                                                               as serp_revenue_usd,
    a.is_paid_overflow,
    case when a.is_redirected = 1 then 0 else a.is_conversion_considered end as is_conversion_considered,
    a.target_action,
    a.feed_cpa_usd,
    a.feed_cpa_pub_currency,
    null::integer                                                            as api_requests_count,
    a.publisher_type,
    null::numeric             as fixed_cost_usd,
    sum(
            case
                when a.is_redirected = 1 then 0
                else a.apply_cnt
                end)          as apply_cnt,
    sum(
            case
                when a.is_redirected = 1 then 0
                else a.apply_revenue_usd
                end)          as apply_revenue_usd,
    tracking_type,
    a.bot_reason,
    a.bot_reason_group
from
    affiliate_data a
group by
    a.date, a.country, a.name_country, a.partner, a.manager, a.is_local, a.is_bot, a.id_project, a.project,
    a.id_campaign, a.campaign, a.is_job_ad_exchange_project, a.has_postback, a.feed_type, a.is_billable,
    a.is_duplicated, a.id_sub_client, a.partner_cpc_usd, a.feed_cpc_usd, a.client_cpc_usd, a.is_internal_bot_method,
    a.is_external_stat_client, a.lower_cpc_flow_type, a.category_name, a.parent_category_name, a.is_redirected,
    a.redirect_id_campaign, a.redirect_campaign_name, a.redirect_id_project, a.redirect_project_name, a.click_type,
    a.is_project_accepted_location, a.is_internal_duplicated, a.feed_cpc, a.is_paid_overflow, a.target_action,
    (case when a.is_redirected = 1 then 0 else a.is_conversion_considered end), a.feed_cpa_usd,
    a.feed_cpa_pub_currency, a.publisher_type, a.tracking_type, a.bot_reason, a.bot_reason_group
union all
select
    a.date,
    a.country,
    a.name_country,
    a.partner,
    a.manager,
    a.is_local,
    a.is_bot,
    a.redirect_id_project                 as id_project,
    a.redirect_project_name               as project,
    a.redirect_id_campaign                as id_campaign,
    a.redirect_campaign_name              as campaign,
    a.is_job_ad_exchange_redirect_project as is_job_ad_exchange_project,
    a.has_postback,
    a.feed_type,
    a.is_billable,
    a.is_duplicated,
    a.id_sub_client,
    a.partner_cpc_usd,
    a.feed_cpc_usd,
    a.client_cpc_usd,
    a.is_project_accepted_location,
    0                                     as external_click_cnt,
    0                                     as redirected_click_cnt,
    sum(a.closed_job_click_cnt)           as closed_job_click_cnt,
    sum(a.away_click_cnt)                 as away_click_cnt,
    sum(a.jdp_click_cnt)                  as jdp_click_cnt,
    sum(a.jdp_away_click_cnt)             as jdp_away_click_cnt,
    sum(a.away_conversion_cnt)            as away_conversion_cnt,
    sum(a.jdp_away_conversion_cnt)        as jdp_away_conversion_cnt,
    a.is_internal_bot_method,
    a.is_external_stat_client,
    a.lower_cpc_flow_type,
    a.category_name,
    a.parent_category_name,
    a.is_redirected,
    a.redirect_id_campaign,
    a.redirect_campaign_name,
    a.redirect_id_project,
    a.redirect_project_name,
    a.click_type,
    a.id_project                          as redirect_from_id_project,
    a.project                             as redirect_from_project_name,
    a.id_campaign                         as redirect_from_id_campaign,
    a.campaign                            as redirect_from_campaign_name,
    a.is_internal_duplicated,
    sum(a.non_local_away_attempt_cnt)     as non_local_away_attempt_cnt,
    a.feed_cpc                            as feed_cpc_pub_currency,
    0::numeric                            as serp_revenue_usd,
    a.is_paid_overflow,
    a.is_conversion_considered,
    a.redirect_project_target_action      as target_action,
    a.feed_cpa_usd,
    a.feed_cpa_pub_currency,
    null::integer                         as api_requests_count,
    a.publisher_type,
    null::numeric                         as fixed_cost_usd,
    sum(a.apply_cnt)                      as apply_cnt,
    sum(a.apply_revenue_usd)              as apply_revenue_usd,
    a.tracking_type,
    a.bot_reason,
    a.bot_reason_group
from
    affiliate_data a
where
    a.is_redirected = 1
group by
    a.date, a.country, a.name_country, a.partner, a.manager, a.is_local, a.is_bot, a.redirect_id_project,
    a.redirect_project_name, a.redirect_id_campaign, a.redirect_campaign_name, a.is_job_ad_exchange_redirect_project,
    a.has_postback, a.feed_type, a.is_billable, a.is_duplicated, a.id_sub_client, a.partner_cpc_usd, a.feed_cpc_usd,
    a.client_cpc_usd, a.is_internal_bot_method, a.is_external_stat_client, a.lower_cpc_flow_type, a.category_name,
    a.parent_category_name, a.is_redirected, a.click_type, a.id_project, a.project, a.id_campaign, a.campaign,
    a.is_project_accepted_location, a.is_internal_duplicated, a.feed_cpc, a.is_paid_overflow,
    a.is_conversion_considered, a.redirect_project_target_action, a.feed_cpa_usd, a.feed_cpa_pub_currency,
    a.publisher_type, a.tracking_type, a.bot_reason, a.bot_reason_group
union all
select
    ar.date,
    ar.country,
    c.name_country_eng                   as name_country,
    ar.traffic_source                    as partner,
    m.code::character varying(50)        as manager,
    null::integer                        as is_local,
    null::integer                        as is_bot,
    ar.project_id                        as id_project,
    p.name                               as project,
    ar.campaign_id                       as id_campaign,
    ac.name                              as campaign,
    case
        when p.hide_in_search then 1
        else 0
        end                              as is_job_ad_exchange_project,
    null::integer                        as has_postback,
    ps.feed_type::character varying(100) as feed_type,
    null::smallint                       as is_billable,
    null::smallint                       as is_duplicated,
    null::character varying(200)         as id_sub_client,
    null::numeric(14, 5)                 as partner_cpc_usd,
    null::numeric(14, 5)                 as feed_cpc_usd,
    null::numeric(14, 5)                 as client_cpc_usd,
    null::smallint                       as is_project_accepted_location,
    null::bigint                         as external_click_cnt,
    null::bigint                         as redirected_click_cnt,
    null::bigint                         as closed_job_click_cnt,
    null::bigint                         as away_click_cnt,
    null::bigint                         as jdp_click_cnt,
    null::bigint                         as jdp_away_click_cnt,
    null::bigint                         as away_conversion_cnt,
    null::bigint                         as jdp_away_conversion_cnt,
    null::integer                        as is_internal_bot_method,
    null::smallint                       as is_external_stat_client,
    null::integer                        as lower_cpc_flow_type,
    null::text                           as category_name,
    null::text                           as parent_category_name,
    null::integer                        as is_redirected,
    null::integer                        as redirect_id_campaign,
    null::character varying              as redirect_campaign_name,
    null::integer                        as redirect_id_project,
    null::character varying              as redirect_project_name,
    null::character varying(255)         as click_type,
    null::integer                        as redirect_from_id_project,
    null::character varying              as redirect_from_project_name,
    null::integer                        as redirect_from_id_campaign,
    null::character varying              as redirect_from_campaign_name,
    null::smallint                       as is_internal_duplicated,
    null::bigint                         as non_local_away_attempt_cnt,
    null::numeric                        as feed_cpc_pub_currency,
    ar.revenue_usd                       as serp_revenue_usd,
    null::integer                        as is_paid_overflow,
    null::integer                        as is_conversion_considered,
    null::character varying              as target_action,
    null::numeric                        as feed_cpa_usd,
    null::numeric                        as feed_cpa_pub_currency,
    null::integer                        as api_requests_count,
    case
        when ps.is_api_publisher = 1 then 'API'
        when ps.is_cpa_publisher = 1 then 'CPA feed'
        when ps.id_local_partner is not null then 'CPC feed'
        end                              as publisher_type,
    null::numeric                        as fixed_cost_usd,
    null::numeric                        as apply_cnt,
    null::numeric                        as apply_revenue_usd,
    null::character varying              as tracking_type,
    null::character varying              as bot_reason,
    null::character varying              as bot_reason_group
from
    affiliate.additional_revenue_agg ar
    left join dimension.countries c
              on lower(c.alpha_2::text) = ar.country
    left join dimension.info_project p
              on p.country = c.id and p.id = ar.project_id
    left join affiliate.partner_settings ps
              on ps.country::text = lower(c.alpha_2::text) and lower(ps.partner::text) = lower(ar.traffic_source::text)
    left join affiliate.dic_manager m
              on m.id = ps.id_manager
    left join imp.auction_campaign ac
              on ac.country_id = c.id and ac.id = ar.campaign_id
where
      not ((ar.placement::text = any (array ['search'::text, 'recommendations'::text])) and
           lower(ar.traffic_source::text) ~~ '%dtl%'::text)
  and ar.placement::text !~~ '%letter type%'::text
  and ar.placement::text <> 'alertview'::text
  and ar.date >= (date_trunc('month'::text, now()) - 18::double precision * '1 mon'::interval)::date
union all
select
    arsa.request_date                    as date,
    ps.country,
    c.name_country_eng                   as name_country,
    ps.partner,
    m.code::character varying(50)        as manager,
    null::integer                        as is_local,
    null::integer                        as is_bot,
    null::integer                        as id_project,
    null::character varying(200)         as project,
    null::integer                        as id_campaign,
    null::character varying(200)         as campaign,
    null::integer                        as is_job_ad_exchange_project,
    null::integer                        as has_postback,
    ps.feed_type::character varying(100) as feed_type,
    null::smallint                       as is_billable,
    null::smallint                       as is_duplicated,
    null::character varying(200)         as id_sub_client,
    null::numeric(14, 5)                 as partner_cpc_usd,
    null::numeric(14, 5)                 as feed_cpc_usd,
    null::numeric(14, 5)                 as client_cpc_usd,
    null::smallint                       as is_project_accepted_location,
    null::bigint                         as external_click_cnt,
    null::bigint                         as redirected_click_cnt,
    null::bigint                         as closed_job_click_cnt,
    null::bigint                         as away_click_cnt,
    null::bigint                         as jdp_click_cnt,
    null::bigint                         as jdp_away_click_cnt,
    null::bigint                         as away_conversion_cnt,
    null::bigint                         as jdp_away_conversion_cnt,
    null::integer                        as is_internal_bot_method,
    null::smallint                       as is_external_stat_client,
    null::integer                        as lower_cpc_flow_type,
    null::text                           as category_name,
    null::text                           as parent_category_name,
    null::integer                        as is_redirected,
    null::integer                        as redirect_id_campaign,
    null::character varying              as redirect_campaign_name,
    null::integer                        as redirect_id_project,
    null::character varying              as redirect_project_name,
    null::character varying(255)         as click_type,
    null::integer                        as redirect_from_id_project,
    null::character varying              as redirect_from_project_name,
    null::integer                        as redirect_from_id_campaign,
    null::character varying              as redirect_from_campaign_name,
    null::smallint                       as is_internal_duplicated,
    null::bigint                         as non_local_away_attempt_cnt,
    null::numeric                        as feed_cpc_pub_currency,
    null::numeric                        as serp_revenue_usd,
    null::integer                        as is_paid_overflow,
    null::integer                        as is_conversion_considered,
    null::character varying              as target_action,
    null::numeric                        as feed_cpa_usd,
    null::numeric                        as feed_cpa_pub_currency,
    sum(request_count)                   as api_requests_count,
    case
        when ps.is_api_publisher = 1 then 'API'::text
        when ps.is_cpa_publisher = 1 then 'CPA feed'::text
        when ps.id_local_partner is not null then 'CPC feed'::text
        else null::text
        end                              as publisher_type,
    null::numeric                        as fixed_cost_usd,
    null::numeric                        as apply_cnt,
    null::numeric                        as apply_revenue_usd,
    null::character varying              as tracking_type,
    null::character varying              as bot_reason,
    null::character varying              as bot_reason_group
from
    affiliate.api_request_stat_agg arsa
    left join affiliate.partner_settings ps
              on ps.source_id = arsa.id_source and ps.id_local_partner = arsa.id_partner
    left join dimension.countries c
              on lower(c.alpha_2::text) = ps.country
    left join affiliate.dic_manager m
              on m.id = ps.id_manager
where
        arsa.request_date >= (date_trunc('month'::text, now()) - 18::double precision * '1 mon'::interval)::date
group by
    arsa.request_date, ps.country, c.name_country_eng, ps.partner, ps.feed_type, m.code,
    ps.is_api_publisher, ps.is_cpa_publisher, ps.id_local_partner
union all
select
    cd.date                        as date,
    cd.country,
    c.name_country_eng             as name_country,
    cd.partner,
    cd.manager                     as manager,
    null::integer                  as is_local,
    null::integer                  as is_bot,
    null::integer                  as id_project,
    null::character varying(200)   as project,
    null::integer                  as id_campaign,
    null::character varying(200)   as campaign,
    null::integer                  as is_job_ad_exchange_project,
    null::integer                  as has_postback,
    cd.feed_type::varchar(100)     as feed_type,
    null::smallint                 as is_billable,
    null::smallint                 as is_duplicated,
    null::character varying(200)   as id_sub_client,
    null::numeric(14, 5)           as partner_cpc_usd,
    null::numeric(14, 5)           as feed_cpc_usd,
    null::numeric(14, 5)           as client_cpc_usd,
    null::smallint                 as is_project_accepted_location,
    null::bigint                   as external_click_cnt,
    null::bigint                   as redirected_click_cnt,
    null::bigint                   as closed_job_click_cnt,
    null::bigint                   as away_click_cnt,
    null::bigint                   as jdp_click_cnt,
    null::bigint                   as jdp_away_click_cnt,
    null::bigint                   as away_conversion_cnt,
    null::bigint                   as jdp_away_conversion_cnt,
    null::integer                  as is_internal_bot_method,
    null::smallint                 as is_external_stat_client,
    null::integer                  as lower_cpc_flow_type,
    null::text                     as category_name,
    null::text                     as parent_category_name,
    null::integer                  as is_redirected,
    null::integer                  as redirect_id_campaign,
    null::character varying        as redirect_campaign_name,
    null::integer                  as redirect_id_project,
    null::character varying        as redirect_project_name,
    null::character varying(255)   as click_type,
    null::integer                  as redirect_from_id_project,
    null::character varying        as redirect_from_project_name,
    null::integer                  as redirect_from_id_campaign,
    null::character varying        as redirect_from_campaign_name,
    null::smallint                 as is_internal_duplicated,
    null::bigint                   as non_local_away_attempt_cnt,
    null::numeric                  as feed_cpc_pub_currency,
    null::numeric                  as serp_revenue_usd,
    null::integer                  as is_paid_overflow,
    null::integer                  as is_conversion_considered,
    null::character varying        as target_action,
    null::numeric                  as feed_cpa_usd,
    null::numeric                  as feed_cpa_pub_currency,
    null::integer                  as api_requests_count,
    cd.publisher_type              as publisher_type,
    round(cd.total_clicks * 1.0 / sum(cd.total_clicks) over (partition by cd.date) * pmr.value_to_usd *
          pmr.cost_eur_per_day, 2) as fixed_cost_usd,
    null::numeric                  as apply_cnt,
    null::numeric                  as apply_revenue_usd,
    null::character varying        as tracking_type,
    null::character varying        as bot_reason,
    null::character varying        as bot_reason_group

from
    click_data as cd
    left join per_month_rate as pmr
              on pmr.date = cd.date
    left join dimension.countries c
              on lower(c.alpha_2::text) = cd.country::text;

alter table affiliate.v_statistics_daily_agg
    owner to ypr;

grant select on affiliate.v_statistics_daily_agg to readonly;

grant delete, insert, select, update on affiliate.v_statistics_daily_agg to write_ono;

grant delete, insert, select, truncate, update on affiliate.v_statistics_daily_agg to writeonly_pyscripts;

grant truncate on affiliate.v_statistics_daily_agg to pyapi;

grant select on affiliate.v_statistics_daily_agg to user_agg_team;

grant select on affiliate.v_statistics_daily_agg to vnov;

grant select on affiliate.v_statistics_daily_agg to readonly_aggregation;

grant select on affiliate.v_statistics_daily_agg to affiliate_iud;
