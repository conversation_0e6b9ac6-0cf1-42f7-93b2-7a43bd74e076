create view affiliate.v_project_conversions_by_source
            (record_date, country, country_id, id_project, project_name, id_campaign, sale_manager, traffic_source,
             traffic_channel, aways, away_revenue, away_revenue_client_currency, conversions, target_cpa_usd,
             target_cpa_client_currency, conv_rate_target, is_conversion_considered, target_action, tracking_type)
as
with
    target_actions_raw as (
        select
            auction_user_target_action.country,
            auction_user_target_action.id_project,
            auction_user_target_action.target_action,
            auction_user_target_action.date::date                                                                                                                                                as record_date,
            row_number()
            over (partition by auction_user_target_action.country, auction_user_target_action.id_project, (auction_user_target_action.date::date) order by auction_user_target_action.date desc) as row_num
        from
            aggregation.auction_user_target_action
    ),
    target_actions as (
        select
            target_actions_raw.country,
            target_actions_raw.id_project,
            target_actions_raw.target_action,
            target_actions_raw.record_date,
            case
                when lag(target_actions_raw.record_date)
                     over (partition by target_actions_raw.country, target_actions_raw.id_project order by target_actions_raw.record_date) is null
                    then '2022-01-01'::date
                else target_actions_raw.record_date
                end                as min_dt,
            coalesce(lead(target_actions_raw.record_date)
                     over (partition by target_actions_raw.country, target_actions_raw.id_project order by target_actions_raw.record_date),
                     current_date) as max_dt
        from
            target_actions_raw
        where
            target_actions_raw.row_num = 1
    ),
    conversion_source as (
        select
            isc.date,
            isc.country,
            cn.id                     as country_id,
            isc.id_project            as project_id,
            ip.name                   as project_name,
            iscs.id_traffic_source,
            max(isc.conversions)      as total_conversions,
            sum(iscs.cnt_conversions) as conversions
        from
            imp_statistic.conversions isc
            left join dimension.info_calendar ic
                      on ic.dt = isc.date
            join imp_statistic.conversions_source iscs
                 on isc.id = iscs.id_conversion
            left join dimension.countries cn
                      on isc.country::text = cn.alpha_2::text
            left join dimension.info_project ip
                      on cn.id = ip.country and isc.id_project = ip.id
            left join dimension.u_traffic_source uts
                      on cn.id = uts.country and iscs.id_traffic_source = uts.id
            left join affiliate.mv_tracking_project_list pp
                      on pp.id_country = cn.id and pp.id_project = isc.id_project and pp.conversion_cnt > 5::numeric and
                         ic.date_diff >= pp.conversion_start_date_diff and
                         ic.date_diff <= (pp.conversion_max_date_diff + 7)
        where
              isc.id_date_period = 1
          and isc.date >= (current_date - 90)
          and pp.conversion_cnt is null
        group by isc.date, isc.country, cn.id, isc.id_project, ip.name, iscs.id_traffic_source
    ),
    source_revenue as (
        select
            jaca.country_id,
            ic.dt                       as date,
            jaca.id_project,
            ip.name                     as site,
            jaca.id_current_traf_source as id_traf_source,
            sum(jaca.jdp_away_count)    as click_count,
            sum(jaca.revenue_usd)       as total_value
        from
            aggregation.click_data_agg jaca
            join dimension.countries cn
                 on cn.id = jaca.country_id
            join (select distinct
                      conversion_source.country,
                      conversion_source.project_id
                  from
                      conversion_source) t
                 on t.country::text = cn.alpha_2::text and t.project_id = jaca.id_project
            join dimension.info_calendar ic
                 on ic.date_diff = jaca.action_datediff
            join dimension.info_project ip
                 on ip.country = jaca.country_id and ip.id = jaca.id_project
        where
              (jaca.job_destination = any (array [1::double precision, 4::double precision]))
          and ic.dt >= '2024-06-01'::date
          and ic.dt >= (current_date - 90)
        group by jaca.country_id, ic.dt, jaca.id_project, ip.name, jaca.id_current_traf_source
    ),
    conversion_source_agg as (
        select
            cn.alpha_2          as country,
            r.country_id,
            r.id_project,
            r.site              as project_name,
            uts.name            as traffic_source,
            uts.channel         as traffic_channel,
            r.date,
            sum(r.total_value)  as away_revenue,
            sum(r.click_count)  as aways,
            max(ct.conversions) as conversions
        from
            source_revenue r
            join dimension.countries cn
                 on r.country_id = cn.id
            left join conversion_source ct
                      on cn.alpha_2::text = ct.country::text and r.id_project = ct.project_id and r.date = ct.date and
                         r.id_traf_source = ct.id_traffic_source
            left join dimension.u_traffic_source uts
                      on r.country_id = uts.country and r.id_traf_source = uts.id
        where
            (concat(cn.alpha_2, r.id_project) in (select distinct
                                                      concat(cs.country, cs.project_id) as concat
                                                  from
                                                      conversion_source cs))
        group by cn.alpha_2, r.country_id, r.site, r.id_project, uts.name, uts.channel, r.date
    ),
    conv_raw as (
        select
            cn.alpha_2                                                             as country,
            pcd.country_id,
            pcd.id_project                                                         as project_id,
            ip.name                                                                as project_name,
            uts.name                                                               as traffic_source,
            uts.channel                                                            as traffic_channel,
            sum(coalesce(pcd.revenue_usd, 0::numeric::double precision))           as away_revenue,
            sum(coalesce(pcd.jdp_away_count, 0::bigint::double precision))         as aways,
            sum(
                    case
                        when pcd.job_destination = any (array [1::double precision, 4::double precision])
                            then coalesce(pcd.unic_client_conversion_count, 0::bigint)
                        else coalesce(pcd.jooble_apply_count, 0::bigint)
                        end)                                                       as conversions,
            sum(coalesce(pcd.revenue_user_currency, 0::numeric::double precision)) as away_revenue_client_currency,
            pcd.id_campaign,
            ic.dt                                                                  as session_date,
            ta.target_action,
            pp.type                                                                as tracking_type
        from
            aggregation.click_data_agg pcd
            join dimension.info_calendar ic
                 on ic.date_diff = pcd.action_datediff
            join dimension.info_project ip
                 on ip.country = pcd.country_id and ip.id = pcd.id_project
            join affiliate.mv_tracking_project_list pp
                 on pp.id_country = pcd.country_id and pp.id_project = pcd.id_project and
                    pp.conversion_cnt >= 5::numeric and ic.date_diff >= pp.conversion_start_date_diff and
                    (pp.type = 'Postback'::text and ic.date_diff <= (pp.conversion_max_date_diff + 7) or
                     pp.type <> 'Postback'::text and pp.revenue_usd > 0::double precision) and
                    case
                        when pp.type = 'Postback'::text then pcd.job_destination = any
                                                             (array [1::double precision, 4::double precision])
                        else pcd.job_destination = 3::double precision
                        end
            join dimension.countries cn
                 on pcd.country_id = cn.id
            left join dimension.u_traffic_source uts
                      on pcd.country_id = uts.country and pcd.id_current_traf_source = uts.id
            left join target_actions ta
                      on ta.country = cn.alpha_2::text and ta.id_project = pcd.id_project and ic.dt >= ta.min_dt and
                         ic.dt < ta.max_dt
        where
              ic.dt >= '2024-06-01'::date
          and ic.dt >= (current_date - 90)
        group by
            pcd.country_id, pcd.id_project, cn.alpha_2, ip.name, uts.name, uts.channel, pcd.id_campaign, ic.dt,
            ta.target_action, pp.type
    ),
    conversions_temp as (
        select
            cr.country_id,
            cr.country,
            cr.project_id,
            crm.benchmark_cr as conv_rate_target,
            cr.project_name,
            cr.traffic_source,
            cr.traffic_channel,
            cr.away_revenue,
            cr.aways,
            cr.conversions,
            cr.id_campaign,
            cr.session_date,
            cr.away_revenue_client_currency,
            cr.target_action,
            cr.tracking_type
        from
            conv_raw cr
            left join (select
                           ccs.soska_project_link,
                           "left"(ccs.soska_project_link::text, 2)                                              as country,
                           substr(ccs.soska_project_link::text, 3, length(ccs.soska_project_link::text) -
                                                                   2)                                           as id_project,
                           ccs.benchmark_cr,
                           row_number() over (partition by ccs.soska_project_link order by ccs.created_on desc) as num
                       from
                           aggregation.crm_client_account ccs
                       where
                             ccs.benchmark_cr > 0::numeric
                         and ccs.soska_project_link is not null
                         and length(ccs.soska_project_link::text) > 2) crm
                      on cr.country::text = crm.country and
                         cr.project_id = nullif(crm.id_project, ''::text)::integer and crm.num = 1
    ),
    target_cpa_source as (
        select distinct on (cc.country, cc.id_campaign, (cc.date::date))
            cc.country,
            cc.id_campaign,
            cc.date::date as date,
            cc.cpa
        from
            aggregation.campaign_cpa cc
        where
            cc.id_campaign <> 0
        order by cc.country, cc.id_campaign, (cc.date::date), cc.date desc, cc.cpa
    ),
    target_cpa_date_range as (
        select
            t.country,
            c.id                                                                      as country_id,
            t.id_campaign,
            ac.id_project,
            t.date                                                                    as date_start,
            t.cpa,
            lead(t.date) over (partition by t.country, t.id_campaign order by t.date) as date_end,
            row_number() over (partition by t.country, t.id_campaign order by t.date) as row_num,
            ic.name                                                                   as currency_name
        from
            target_cpa_source t
            left join dimension.countries c
                      on c.alpha_2::text = t.country
            left join imp.auction_campaign ac
                      on ac.country_id = c.id and ac.id = t.id_campaign
            left join dimension.info_currency ic
                      on ic.country = ac.country_id and ic.id = ac.currency
    ),
    campaign_target_cpa as (
        select
            tdr.country_id,
            tdr.country,
            tdr.id_project,
            tdr.id_campaign,
            case
                when tdr.row_num = 1 then (date_trunc('month'::text, now()) - 18::double precision * '1 mon'::interval)::date
                else tdr.date_start
                end                                  as date_start,
            coalesce(tdr.date_end - 1, current_date) as date_end,
            tdr.cpa,
            tdr.currency_name
        from
            target_cpa_date_range tdr
    ),
    exclude_all_countries_list as (
        select
            crpl.date_from                       as min_date,
            coalesce(crpl.date_to, current_date) as max_date,
            crpl.id_project
        from
            affiliate.conversion_project_list crpl
        where
              crpl.country::text = 'ALL'::text
          and crpl.record_type::text = 'exclude'::text
    ),
    include_countries_list as (
        select
            min(crpl.date_from)                       as min_date,
            max(coalesce(crpl.date_to, current_date)) as max_date,
            crpl.country
        from
            affiliate.conversion_project_list crpl
        where
              crpl.country::text <> 'ALL'::text
          and crpl.record_type::text = 'include'::text
        group by crpl.country
    ),
    include_project_list as (
        select
            crpl.date_from                       as min_date,
            coalesce(crpl.date_to, current_date) as max_date,
            crpl.country,
            crpl.id_project
        from
            affiliate.conversion_project_list crpl
        where
              crpl.country::text <> 'ALL'::text
          and crpl.record_type::text = 'include'::text
    ),
    result as (
        select
            vpc.country,
            vpc.country_id,
            vpc.project_id                                                                as id_project,
            vpc.project_name,
            vpc.traffic_source,
            vpc.traffic_channel,
            sum(coalesce(vpc.away_revenue, 0::numeric::double precision))                 as away_revenue,
            sum(coalesce(vpc.aways, 0::bigint::double precision))                         as aways,
            sum(coalesce(vpc.conversions, 0::bigint::numeric))                            as conversions,
            vpc.id_campaign,
            vpc.session_date                                                              as record_date,
            sum(coalesce(vpc.away_revenue_client_currency,
                         0::numeric::double precision))                                   as away_revenue_client_currency,
            vpc.conv_rate_target,
            vpc.target_action,
            vpc.tracking_type
        from
            conversions_temp vpc
        group by
            vpc.country, vpc.country_id, vpc.project_id, vpc.project_name, vpc.traffic_source, vpc.traffic_channel,
            vpc.session_date, vpc.id_campaign, vpc.conv_rate_target, vpc.target_action, vpc.tracking_type
        union all
        select
            csa.country,
            csa.country_id,
            csa.id_project,
            csa.project_name,
            csa.traffic_source,
            csa.traffic_channel,
            sum(csa.away_revenue) as away_revenue,
            sum(csa.aways)        as aways,
            sum(csa.conversions)  as conversions,
            null::integer         as id_campaign,
            csa.date              as record_date,
            null::numeric         as away_revenue_client_currency,
            null::numeric         as conv_rate_target,
            ta.target_action,
            'Manual'::text        as tracking_type
        from
            conversion_source_agg csa
            left join target_actions ta
                      on ta.country = csa.country::text and ta.id_project = csa.id_project and csa.date >= ta.min_dt and
                         csa.date < ta.max_dt
        group by
            csa.country, csa.country_id, csa.id_project, csa.project_name, csa.traffic_source, csa.traffic_channel,
            csa.date, ta.target_action
    ),
    info_currency_data as (
        select distinct on ((ich_1.date::date), ic.name)
            ich_1.date::date as date,
            ic.name          as currency,
            ich_1.value_to_usd
        from
            dimension.info_currency_history ich_1
            join dimension.info_currency ic
                 on ic.country = ich_1.country and ic.id = ich_1.id_currency
        where
                ich_1.date::date >= (date_trunc('day'::text, now()) - 90::double precision * '1 day'::interval)::date
        order by (ich_1.date::date), ic.name, ich_1.date desc
    )
select
    rs.record_date,
    rs.country,
    rs.country_id::integer                       as country_id,
    rs.id_project::integer                       as id_project,
    rs.project_name,
    rs.id_campaign::integer                      as id_campaign,
    vsm.sale_manager,
    rs.traffic_source,
    rs.traffic_channel,
    rs.aways::numeric                            as aways,
    rs.away_revenue::numeric                     as away_revenue,
    rs.away_revenue_client_currency::numeric     as away_revenue_client_currency,
    rs.conversions,
    ctc.cpa * ich.value_to_usd::double precision as target_cpa_usd,
    ctc.cpa                                      as target_cpa_client_currency,
    rs.conv_rate_target,
    case
        when el.id_project is null and (icl.country is null or ipl.id_project is not null) and
             rs.conversions is not null then 1
        else 0
        end                                      as is_conversion_considered,
    rs.target_action,
    rs.tracking_type
from
    result rs
    left join aggregation.v_sale_manager vsm
              on rs.country_id = vsm.country and rs.id_project = vsm.id_project
    left join campaign_target_cpa ctc
              on ctc.country_id = rs.country_id and ctc.id_campaign::double precision = rs.id_campaign and
                 rs.record_date >= ctc.date_start and rs.record_date <= ctc.date_end
    left join (select
                   d.date                                                                              as from_date,
                   d.currency,
                   d.value_to_usd,
                   coalesce(lead(d.date) over (partition by d.currency order by d.date), current_date) as to_date
               from
                   info_currency_data d) ich
              on ich.currency::text = ctc.currency_name::text and rs.record_date >= ich.from_date and
                 rs.record_date < ich.to_date
    left join exclude_all_countries_list el
              on el.id_project = rs.id_project and rs.record_date >= el.min_date and rs.record_date < el.max_date
    left join include_countries_list icl
              on icl.country::text = rs.country::text and rs.record_date >= icl.min_date and
                 rs.record_date < icl.max_date
    left join include_project_list ipl
              on ipl.country::text = rs.country::text and ipl.id_project = rs.id_project and
                 rs.record_date >= ipl.min_date and rs.record_date < ipl.max_date;

alter table affiliate.v_project_conversions_by_source
    owner to ypr;

grant select on affiliate.v_project_conversions_by_source to readonly;

grant select on affiliate.v_project_conversions_by_source to math;

grant delete, insert, select, update on affiliate.v_project_conversions_by_source to writeonly_pyscripts;

grant select on affiliate.v_project_conversions_by_source to kristianpetrych;

