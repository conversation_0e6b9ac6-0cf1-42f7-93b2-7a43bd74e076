create or replace view affiliate.v_additional_jobs_agg(country, feed_name, start_gather_dt, id_project, job_type, uid_count) as
with
    job_stat_agg as (
        select
            upper(ps.country::text) as country,
            ps.partner              as feed_name,
            j.start_gather_dt,
            j.click_price_usd,
            ac.id_project,
            sum(j.uid_count)        as uid_count
        from
            affiliate.job_stat_agg j
            join dimension.info_calendar ic
                 on ic.date_diff = j.start_gather_date_diff
            join affiliate.partner_settings ps
                 on ps.source_id = j.id_source and ps.id_local_partner = j.id_partner
            join dimension.countries c
                 on c.alpha_2::text = upper(ps.country::text)
            left join imp.auction_campaign ac
                      on ac.country_id = c.id and ac.id = j.id_campaign
        where
            ic.dt >= (current_date - 30)
        group by ps.partner, j.start_gather_dt, j.click_price_usd, (upper(ps.country::text)), ac.id_project
    ),
    gather as (
        select distinct
            job_stat_agg.feed_name,
            job_stat_agg.start_gather_dt
        from
            job_stat_agg
    ),
    min_cpc_changes as (
        select
            partner_settings_change_log.feed_name,
            partner_settings_change_log.value_after::numeric as min_cpc_in_usd,
            partner_settings_change_log.update_datetime
        from
            affiliate.partner_settings_change_log
        where
            partner_settings_change_log.field_name::text = 'min_cpc_in_usd'::text
    ),
    local_flags_changes as (
        select
            partner_settings_change_log.feed_name,
            (partner_settings_change_log.value_after::integer & 16) = 16 as is_static_cpc,
            (partner_settings_change_log.value_after::integer & 32) = 32 as is_static_cpc_ratio,
            partner_settings_change_log.update_datetime
        from
            affiliate.partner_settings_change_log
        where
            partner_settings_change_log.field_name::text = 'local_flags'::text
    ),
    current_settings as (
        select
            partner_settings.partner,
            partner_settings.min_cpc_in_usd,
            (coalesce(partner_settings.local_flags, 0) & 16) = 16 as is_static_cpc,
            (coalesce(partner_settings.local_flags, 0) & 32) = 32 as is_static_cpc_ratio
        from
            affiliate.partner_settings
    ),
    min_cpc_log as (
        select
            m.feed_name,
            m.start_gather_dt,
            m.min_cpc_in_usd
        from
            (select
                 g.feed_name,
                 g.start_gather_dt,
                 mcc.update_datetime,
                 mcc.min_cpc_in_usd,
                 row_number()
                 over (partition by g.feed_name, g.start_gather_dt order by mcc.update_datetime desc) as row_num
             from
                 gather g
                 join min_cpc_changes mcc
                      on mcc.feed_name::text = g.feed_name::text and mcc.update_datetime < g.start_gather_dt) m
        where
            m.row_num = 1
    ),
    local_flags_log as (
        select
            m.feed_name,
            m.start_gather_dt,
            m.is_static_cpc,
            m.is_static_cpc_ratio
        from
            (select
                 g.feed_name,
                 g.start_gather_dt,
                 lfc.update_datetime,
                 lfc.is_static_cpc,
                 lfc.is_static_cpc_ratio,
                 row_number()
                 over (partition by g.feed_name, g.start_gather_dt order by lfc.update_datetime desc) as row_num
             from
                 gather g
                 join local_flags_changes lfc
                      on lfc.feed_name::text = g.feed_name::text and lfc.update_datetime < g.start_gather_dt) m
        where
            m.row_num = 1
    ),
    feed_settings as (
        select
            g.feed_name,
            g.start_gather_dt,
            coalesce(mcl.min_cpc_in_usd, cs.min_cpc_in_usd)           as min_cpc_in_usd,
            coalesce(lfl.is_static_cpc, cs.is_static_cpc)             as is_static_cpc,
            coalesce(lfl.is_static_cpc_ratio, cs.is_static_cpc_ratio) as is_static_cpc_ratio
        from
            gather g
            left join current_settings cs
                      on cs.partner::text = g.feed_name::text
            left join min_cpc_log mcl
                      on mcl.feed_name::text = g.feed_name::text and mcl.start_gather_dt = g.start_gather_dt
            left join local_flags_log lfl
                      on lfl.feed_name::text = g.feed_name::text and lfl.start_gather_dt = g.start_gather_dt
    )
select
    js.country,
    js.feed_name,
    js.start_gather_dt,
    js.id_project,
    case
        when js.click_price_usd < fs.min_cpc_in_usd and fs.is_static_cpc then 'static_cpc'::text
        when js.click_price_usd < fs.min_cpc_in_usd and fs.is_static_cpc_ratio then 'static_cpc_ratio'::text
        else 'main'::text
        end           as job_type,
    sum(js.uid_count) as uid_count
from
    job_stat_agg js
    join feed_settings fs
         on fs.feed_name::text = js.feed_name::text and fs.start_gather_dt = js.start_gather_dt
group by
    js.feed_name, js.start_gather_dt, js.country, js.id_project,
    (
        case
            when js.click_price_usd < fs.min_cpc_in_usd and fs.is_static_cpc then 'static_cpc'::text
            when js.click_price_usd < fs.min_cpc_in_usd and fs.is_static_cpc_ratio then 'static_cpc_ratio'::text
            else 'main'::text
            end);

alter table affiliate.v_additional_jobs_agg
    owner to ypr;

grant select on affiliate.v_additional_jobs_agg to readonly;

grant delete, insert, select, update on affiliate.v_additional_jobs_agg to writeonly_pyscripts;

