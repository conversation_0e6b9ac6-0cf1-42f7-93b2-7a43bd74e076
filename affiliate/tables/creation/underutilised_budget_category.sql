create table affiliate.underutilised_budget_category
(
    id                   bigint,
    source_id            smallint,
    country              text,
    id_project           integer,
    id_job_category      integer,
    underutilised_budget numeric,
    category             text,
    primary key (id, source_id)
);

alter table affiliate.underutilised_budget_category
    owner to ypr;

grant select on affiliate.underutilised_budget_category to readonly;

grant delete, insert, references, select, trigger, truncate, update on affiliate.underutilised_budget_category to writeonly_pyscripts;

grant delete, insert, select, truncate, update on affiliate.underutilised_budget_category to pyapi;

grant delete, insert, select, update on affiliate.underutilised_budget_category to affiliate_iud;