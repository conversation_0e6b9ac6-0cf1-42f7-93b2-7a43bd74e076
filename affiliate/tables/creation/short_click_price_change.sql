create table affiliate.short_click_price_change
(
    source_id       integer,
    id              bigint,
    country_code    text,
    job_uid         bigint,
    click_price_usd numeric(14, 5),
    datetime        timestamp
)
    tablespace data4;

alter table affiliate.short_click_price_change
    owner to postgres;

create index short_click_price_change_idx
    on affiliate.short_click_price_change (source_id, country_code, job_uid)
    tablespace data4;

grant select on affiliate.short_click_price_change to readonly;

grant delete, insert, select, truncate, update on affiliate.short_click_price_change to writeonly_pyscripts;

grant delete, insert, select, truncate, update on affiliate.short_click_price_change to pyapi;

grant select on affiliate.short_click_price_change to affiliate_iud;

