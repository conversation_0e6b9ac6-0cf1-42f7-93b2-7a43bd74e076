create table if not exists affiliate.dtl_searches
(
    country                       varchar(2) not null,
    date_diff                     integer    not null,
    datetime                      timestamp,
    id_search                     bigint     not null,
    keyword_hash                  bigint,
    keyword                       varchar(1000),
    region_name                   varchar(255),
    id_region                     integer,
    results_total                 integer,
    traffic_source                varchar(255),
    id_traffic_source             integer,
    user_ip                       varchar(100),
    user_country                  varchar(10),
    is_bot                        integer,
    impression_cnt                integer,
    impression_on_screen_cnt      integer,
    paid_impression_cnt           integer,
    paid_impression_on_screen_cnt integer,
    primary key (country, date_diff, id_search)
);

alter table affiliate.dtl_searches
    owner to ypr;

grant select on affiliate.dtl_searches to readonly;

grant delete, insert, select, update on affiliate.dtl_searches to writeonly_pyscripts;
