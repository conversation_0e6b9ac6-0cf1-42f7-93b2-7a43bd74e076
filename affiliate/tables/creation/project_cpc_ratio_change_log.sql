create table affiliate.project_cpc_ratio_change_log
(
    source_id       smallint     not null,
    id              integer      not null,
    local_id        integer      not null,
    project_id      integer      not null,
    campaign_id     integer      not null,
    field_name      varchar(100) not null,
    value_before    numeric(14, 5),
    value_after     numeric(14, 5),
    update_datetime timestamp    not null,
    primary key (source_id, id, project_id, campaign_id)
);

alter table affiliate.project_cpc_ratio_change_log
    owner to rlu;

grant select on affiliate.project_cpc_ratio_change_log to readonly;

grant delete, insert, select, truncate, update on affiliate.project_cpc_ratio_change_log to writeonly_pyscripts;

grant truncate on affiliate.project_cpc_ratio_change_log to pyapi;

grant select on affiliate.project_cpc_ratio_change_log to affiliate_iud;