create table affiliate.short_job_in_feed_log
(
    source_id        integer not null,
    id_local_partner integer,
    id               bigint  not null,
    job_uid          bigint,
    inactive         boolean,
    datetime         timestamp,
    click_price      numeric(14, 5),
    constraint pkey_short_job_in_feed_log_id
        primary key (source_id, id)
)
    tablespace data4;

alter table affiliate.short_job_in_feed_log
    owner to postgres;

grant select on affiliate.short_job_in_feed_log to readonly;

grant delete, insert, select, truncate, update on affiliate.short_job_in_feed_log to writeonly_pyscripts;

grant delete, insert, select, truncate, update on affiliate.short_job_in_feed_log to pyapi;

grant select on affiliate.short_job_in_feed_log to affiliate_iud;

