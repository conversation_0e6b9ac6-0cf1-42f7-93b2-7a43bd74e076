-- auto-generated definition
create table affiliate.dic_manager
(
    id              integer not null
        constraint dic_manager_pkey
            primary key,
    code            varchar(100),
    name            varchar(100),
    update_datetime timestamp
);

alter table affiliate.dic_manager
    owner to ypr;

grant select on affiliate.dic_manager to readonly;

grant delete, insert, references, select, trigger, truncate, update on affiliate.dic_manager to writeonly_pyscripts;

grant delete, insert, select, truncate, update on affiliate.dic_manager to pyapi;

grant delete, insert, select, update on affiliate.dic_manager to affiliate_iud;

