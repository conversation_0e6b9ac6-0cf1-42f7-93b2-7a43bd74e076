create table affiliate.dagster_job_log
(
    run_id            varchar,
    job_name          varchar,
    operation_name    varchar,
    op_start_datetime timestamp,
    op_end_datetime   timestamp,
    record_date       date,
    schema_name       varchar,
    table_name        varchar
);

alter table affiliate.dagster_job_log
    owner to ypr;

grant select on affiliate.dagster_job_log to readonly;

grant delete, insert, select, update on affiliate.dagster_job_log to writeonly_pyscripts;

grant delete, insert, select, truncate, update on affiliate.dagster_job_log to pyapi;

