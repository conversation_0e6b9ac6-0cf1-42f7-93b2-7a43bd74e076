create table affiliate.abtest_stat_agg
(
    country_code       varchar(2),
    publisher          varchar(50),
    project_id         integer,
    project_name       varchar,
    test_id            integer,
    iteration          smallint,
    test_group         smallint,
    is_control_group   smallint,
    record_date        timestamp,
    metric_type        varchar(20),
    metric_name        varchar(50),
    metric_numerator   numeric(14, 5),
    metric_denominator numeric(14, 5)
);

alter table affiliate.abtest_stat_agg
    owner to ypr;

grant select on affiliate.abtest_stat_agg to readonly;

grant delete, insert, select, truncate, update on affiliate.abtest_stat_agg to writeonly_pyscripts;

grant delete, insert, select, truncate, update on affiliate.abtest_stat_agg to pyapi;

grant select on affiliate.abtest_stat_agg to affiliate_iud;
