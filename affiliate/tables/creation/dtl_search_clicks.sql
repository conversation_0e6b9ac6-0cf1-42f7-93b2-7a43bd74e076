create table if not exists affiliate.dtl_search_clicks
(
    country                       varchar(2) not null,
    date_diff                     integer not null,
    id_search                     bigint not null,
    id_project                    integer,
    id_campaign                   integer,
    revenue_usd                   numeric(14, 5),
    total_click_cnt               integer,
    paid_click_cnt                integer,
    jdp_click_cnt                 integer,
    jdp_away_click_cnt            integer,
    duplicated_click_cnt          integer
);

alter table affiliate.dtl_search_stat
    owner to ypr;

grant select on affiliate.dtl_search_stat to readonly;

grant delete, insert, select, update on affiliate.dtl_search_stat to writeonly_pyscripts;


