create table affiliate.statistics_daily_agg
(
    date_diff                    integer,
    id_country                   integer,
    partner                      varchar(100),
    user_country                 varchar(10),
    is_bot                       integer,
    id_project                   integer,
    external_click_cnt           integer,
    closed_job_click_cnt         integer,
    away_click_cnt               integer,
    jdp_click_cnt                integer,
    jdp_away_click_cnt           integer,
    away_conversion_cnt          integer,
    jdp_away_conversion_cnt      integer,
    partner_cpc                  numeric(14, 5),
    partner_cpc_usd              numeric(14, 5),
    feed_cpc                     numeric(14, 5),
    feed_cpc_usd                 numeric(14, 5),
    client_cpc                   numeric(14, 5),
    client_cpc_usd               numeric(14, 5),
    is_billable                  smallint,
    is_external_stat_client      smallint,
    is_duplicated                smallint default 0,
    is_internal_duplicated       smallint,
    id_sub_client                varchar(200),
    id_campaign                  integer,
    is_internal_bot_method       integer,
    lower_cpc_flow_type          integer,
    id_category                  integer,
    is_redirected                integer,
    redirect_id_campaign         integer,
    redirect_id_project          integer,
    click_type                   integer,
    is_project_accepted_location smallint,
    non_local_away_attempt_cnt   integer,
    is_paid_overflow             integer,
    feed_cpa                     numeric(14, 5),
    feed_cpa_usd                 numeric(14, 5),
    apply_cnt                    integer,
    apply_revenue_usd            numeric(14, 5),
    bot_reason_id                smallint
);

alter table affiliate.statistics_daily_agg
    owner to postgres;

grant select on affiliate.statistics_daily_agg to readonly;

grant delete, insert, select, truncate, update on affiliate.statistics_daily_agg to writeonly_pyscripts;

grant delete, insert, select, truncate, update on affiliate.statistics_daily_agg to pyapi;

