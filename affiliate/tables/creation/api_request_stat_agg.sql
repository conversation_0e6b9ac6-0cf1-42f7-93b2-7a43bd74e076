create table affiliate.api_request_stat_agg
(
    id_source        smallint,
    id_partner       integer,
    request_date     date,
    request_count    integer,
    primary key (id_source, id_partner, request_date)
);

alter table affiliate.api_request_stat_agg
    owner to ypr;

grant select on affiliate.api_request_stat_agg to readonly;

grant delete, insert, select, truncate, update on affiliate.api_request_stat_agg to writeonly_pyscripts;

grant delete, insert, select, truncate, update on affiliate.api_request_stat_agg to pyapi;

