create table if not exists affiliate.project_conversions_delay
(
    country    varchar(2),
    project_id integer,
    delay      integer,
    primary key (country, project_id)
);


alter table affiliate.project_conversions_delay
    owner to ypr;

grant select on affiliate.project_conversions_delay to readonly;

grant delete, insert, references, select, trigger, truncate, update on affiliate.project_conversions_delay to writeonly_pyscripts;

grant delete, insert, select, truncate, update on affiliate.project_conversions_delay to pyapi;

grant delete, insert, select, update on affiliate.project_conversions_delay to affiliate_iud;

