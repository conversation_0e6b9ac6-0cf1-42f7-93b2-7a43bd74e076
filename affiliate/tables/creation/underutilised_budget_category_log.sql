create table affiliate.underutilised_budget_category_log
(
    id                       bigint,
    source_id                smallint,
    operation_type           text,
    operation_timestamp      timestamp,
    country                  text,
    id_project               integer,
    id_job_category          integer,
    underutilised_budget     numeric,
    category                 text,
    old_country              text,
    old_id_project           integer,
    old_id_job_category      integer,
    old_underutilised_budget numeric,
    old_category             text,
    primary key (id, source_id)
);

alter table affiliate.underutilised_budget_category_log
    owner to ypr;

grant select on affiliate.underutilised_budget_category_log to readonly;

grant delete, insert, references, select, trigger, truncate, update on affiliate.underutilised_budget_category_log to writeonly_pyscripts;

grant delete, insert, select, truncate, update on affiliate.underutilised_budget_category_log to pyapi;

grant delete, insert, select, update on affiliate.underutilised_budget_category_log to affiliate_iud;