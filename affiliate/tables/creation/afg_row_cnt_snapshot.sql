-- auto-generated definition
create table affiliate.afg_row_cnt_snapshot
(
    table_name   varchar(100),
    source_id    integer,
    log_date     date,
    row_cnt      integer
);

alter table affiliate.afg_row_cnt_snapshot
    owner to ypr;

grant select on affiliate.afg_row_cnt_snapshot to readonly;

grant delete, insert, select, truncate, update on affiliate.afg_row_cnt_snapshot to writeonly_pyscripts;

grant delete, insert, select, truncate, update on affiliate.afg_row_cnt_snapshot to pyapi;

