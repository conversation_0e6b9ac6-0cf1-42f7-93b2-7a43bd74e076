create table affiliate.abtest_significance_metrics
(
    country_code       varchar(5) not null,
    publisher          varchar    not null,
    date_start         date       not null,
    date_end           date       not null,
    test_id            integer    not null,
    iteration          integer    not null,
    test_group         integer    not null,
    metric_name        varchar    not null,
    metric_value       numeric,
    metric_ci_lower_95 numeric,
    metric_ci_upper_95 numeric,
    metric_ci_lower_99 numeric,
    metric_ci_upper_99 numeric,
    delta_value        numeric,
    delta_ci_lower_95  numeric,
    delta_ci_upper_95  numeric,
    delta_ci_lower_99  numeric,
    delta_ci_upper_99  numeric,
    u_test_p_value     numeric,
    t_test_p_value     numeric,
    z_test_p_value     numeric,
    ac_test_p_value    numeric,
    primary key (country_code, publisher, date_start, date_end, test_id, iteration, test_group, metric_name)
);

alter table affiliate.abtest_significance_metrics
    owner to ypr;

grant select on affiliate.abtest_significance_metrics to readonly;

grant delete, insert, select, update on affiliate.abtest_significance_metrics to writeonly_pyscripts;

