create table affiliate.dtl_click_log
(
    source_id           smallint,
    id                  integer,
    country             varchar(2)     not null,
    click_datetime      timestamp      not null,
    user_ip             varchar        not null,
    id_traffic_source   integer        not null,
    keyword             varchar(255)   not null,
    keyword_hash        bigint         not null,
    region_name         varchar(255)   not null,
    region_id           integer,
    click_cpc           numeric(14, 5) not null,
    country_currency_id integer,
    primary key (source_id, id)
);

alter table dtl_click_log
    owner to ypr;

grant select on dtl_click_log to readonly;

grant delete, insert, select, update on dtl_click_log to writeonly_pyscripts;