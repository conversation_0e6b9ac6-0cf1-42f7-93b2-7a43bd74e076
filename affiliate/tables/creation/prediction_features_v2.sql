create table affiliate.prediction_features_v2
(
    date_created                   timestamp,
    record_date                    date         not null,
    day_of_week                    smallint,
    publisher                      var<PERSON><PERSON>(255) not null,
    country_code                   varchar(2),
    feed_cpc_usd                   numeric      not null,
    month_completed                numeric,
    avg_uid_cnt_3d                 numeric,
    avg_uid_cnt_7d                 numeric,
    avg_uid_cnt_14d                numeric,
    avg_uid_cnt_prevd              numeric,
    avg_uid_cnt_prev2d             numeric,
    avg_uid_cnt_prev3d             numeric,
    avg_uid_cnt_prevd_pub          numeric,
    avg_uid_cnt_prev2d_pub         numeric,
    avg_uid_cnt_prev3d_pub         numeric,
    min_uid_cnt_3d_pub             integer,
    min_uid_cnt_7d_pub             integer,
    min_uid_cnt_14d_pub            integer,
    uid_percent_pub_3d             numeric,
    uid_percent_pub_7d             numeric,
    uid_percent_pub_14d            numeric,
    avg_job_cpc_percentile_3d_pub  numeric,
    avg_job_cpc_percentile_7d_pub  numeric,
    avg_job_cpc_percentile_14d_pub numeric,
    avg_uid_cnt_diff_12            numeric,
    avg_uid_cnt_diff_23            numeric,
    avg_uid_cnt_diff_12_pub        numeric,
    avg_uid_cnt_diff_23_pub        numeric,
    click_cnt_3d                   integer,
    click_cnt_7d                   integer,
    click_cnt_14d                  integer,
    click_cnt_3d_pub               integer,
    click_cnt_7d_pub               integer,
    click_cnt_14d_pub              integer,
    click_percent_pub_3d           numeric,
    click_percent_pub_7d           numeric,
    click_percent_pub_14d          numeric,
    click_cpc_percentile_3d_pub    numeric,
    click_cpc_percentile_7d_pub    numeric,
    click_cpc_percentile_14d_pub   numeric,
    job_click_cnt_3d               integer,
    job_click_cnt_7d               integer,
    job_click_cnt_14d              integer,
    job_click_cnt_3d_pub           integer,
    job_click_cnt_7d_pub           integer,
    job_click_cnt_14d_pub          integer,
    click_cnt_prevd                integer,
    click_cnt_prevd_pub            integer,
    click_cnt_prev2d_pub           integer,
    click_cnt_prev3d_pub           integer,
    click_cnt_diff_12              numeric,
    click_cnt_diff_23              numeric,
    click_cnt_diff_12_pub          numeric,
    click_cnt_diff_23_pub          numeric,
    primary key (record_date, publisher, feed_cpc_usd)
);

alter table affiliate.prediction_features_v2
    owner to ypr;

grant select on affiliate.prediction_features_v2 to readonly;

grant delete, insert, select, update on affiliate.prediction_features_v2 to writeonly_pyscripts;
