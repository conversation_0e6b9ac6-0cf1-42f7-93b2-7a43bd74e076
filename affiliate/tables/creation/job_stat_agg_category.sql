create table affiliate.job_stat_agg_category
(
    id                    bigint primary key,
    id_job_stat_agg       bigint,
    id_category           integer,
    uid_count             integer,
    reassembled_job_count integer
);

alter table affiliate.job_stat_agg_category
    owner to ypr;

create index idx_job_stat_agg_category_id_job_stat_agg on affiliate.job_stat_agg_category(id_job_stat_agg);

grant select on affiliate.job_stat_agg_category to readonly;

grant delete, insert, select, truncate, update on affiliate.job_stat_agg_category to writeonly_pyscripts;

grant delete, insert, select, truncate, update on affiliate.job_stat_agg_category to pyapi;
