create table affiliate.task_status_log
(
    source_id             integer   not null,
    id                    integer   not null,
    id_partner            integer   not null,
    task_datetime         timestamp not null,
    start_gather_datetime timestamp,
    job_count             integer,
    id_consumer_run       uuid,
    primary key (source_id, id)
);

alter table affiliate.task_status_log
    owner to mb;

create index task_status_log_idx
    on affiliate.task_status_log (source_id, id_partner)
    tablespace data4;

grant delete, insert, select, update on affiliate.task_status_log to pentaho;

grant select on affiliate.task_status_log to readonly;

grant delete, insert, select, truncate, update on affiliate.task_status_log to writeonly_pyscripts;

grant delete, insert, select, truncate, update on affiliate.task_status_log to pyapi;