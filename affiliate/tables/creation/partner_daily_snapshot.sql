create table affiliate.partner_daily_snapshot
(
    date_diff               integer      not null,
    id_country              integer      not null,
    partner                 varchar(100) not null,
    manager                 varchar(10),
    partner_id_currency     integer,
    feed_cpc_ratio          numeric(14, 5),
    feed_min_cpc_usd        numeric(14, 5),
    feed_is_only_easy_apply integer,
    feed_run_interval       integer,
    feed_type               varchar(100),
    update_gap              integer default 0,
    feed_is_static_min_cpc  smallint,
    is_rounded_cpc          smallint,
    local_flags             integer,
    worst_cpc_ratio         numeric(14, 5),
    optimized_cpc_part      numeric,
    reassembled_jobs_part   numeric,
    global_flags            integer,
    max_cpc_in_usd          numeric(14, 5),
    primary key (date_diff, id_country, partner)
);

alter table affiliate.partner_daily_snapshot
    owner to postgres;

grant select on affiliate.partner_daily_snapshot to readonly;

grant delete, insert, select, truncate, update on affiliate.partner_daily_snapshot to writeonly_pyscripts;

grant delete, insert, select, truncate, update on affiliate.partner_daily_snapshot to pyapi;


