create table affiliate.cpa_static_settings_log
(
    id_source             integer,
    id                    bigint,
    id_partner            integer,
    id_project            integer,
    cpa                   double precision,
    datetime_utc          timestamp,
    record_datetime_local timestamp,
    constraint cpa_static_settings_log_id_source_id_key
        unique (id_source, id)
);

alter table affiliate.cpa_static_settings_log
    owner to ypr;

grant select on affiliate.cpa_static_settings_log to readonly;

grant delete, insert, select, update on affiliate.cpa_static_settings_log to writeonly_pyscripts;