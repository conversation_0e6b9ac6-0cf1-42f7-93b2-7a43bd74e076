create table affiliate.api_request_history
(
    source_id        smallint    not null,
    request_id       varchar(32) not null,
    partner_id       integer     not null,
    request_datetime timestamp   not null,
    params           json,
    primary key (source_id, request_id)
);

alter table affiliate.api_request_history
    owner to postgres;

grant select on affiliate.api_request_history to readonly;

grant delete, insert, select, truncate, update on affiliate.api_request_history to writeonly_pyscripts;

grant delete, insert, select, truncate, update on affiliate.api_request_history to pyapi;

