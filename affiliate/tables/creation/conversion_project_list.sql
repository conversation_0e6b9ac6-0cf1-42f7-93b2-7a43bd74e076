create table affiliate.conversion_project_list
(
    country     varchar(10) not null,
    id_project  integer     not null,
    record_type varchar(20) not null,
    date_from   date        not null,
    date_to     date,
    created_at  timestamp   not null,
    updated_at  timestamp,
    constraint uidx_conversion_project_list_country_id_project_record_type_da
        unique (country, id_project, record_type, date_from, date_to)
);

alter table affiliate.conversion_project_list
    owner to ypr;

grant select on affiliate.conversion_project_list to readonly;

grant delete, insert, select, update on affiliate.conversion_project_list to writeonly_pyscripts;

grant insert, update on affiliate.conversion_project_list to cxt_devs;
