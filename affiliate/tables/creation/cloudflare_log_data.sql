create table affiliate.cloudflare_log_data
(
    id             serial primary key,
    date_diff      integer,
    id_country     smallint,
    click_datetime timestamp,
    ip             varchar,
    user_agent     text,
    utm_source     varchar,
    utm_medium     varchar,
    uid_job        bigint,
    bot_score      integer,
    bot_score_src  varchar,
    bot_tags       varchar,
    waf_score      integer,
    waf_action     varchar,
    url_prev_uid   bigint,
    url_flags      integer,
    url_request_id varchar,
    url_title_hash varchar,
    url_pub_params varchar,
    referer        varchar,
    log_date       date,
    record_type    smallint,
    rule_id        varchar
);

create index cloudflare_log_data_idx
    on affiliate.cloudflare_log_data(date_diff, id_country);

alter table affiliate.cloudflare_log_data
    owner to ypr;

grant select on affiliate.cloudflare_log_data to readonly;

grant delete, insert, select, truncate, update on affiliate.cloudflare_log_data to writeonly_pyscripts;

grant delete, insert, select, truncate, update on affiliate.cloudflare_log_data to pya<PERSON>;

