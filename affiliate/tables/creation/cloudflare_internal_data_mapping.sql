create table affiliate.cloudflare_internal_data_mapping
(
    date_diff              integer not null,
    id_country             integer not null,
    id_external_init       bigint  not null,
    id_external            bigint,
    cloudflare_log_data_id bigint  not null,
    unique(date_diff, id_country, id_external_init, id_external, cloudflare_log_data_id)
);

alter table affiliate.cloudflare_internal_data_mapping
    owner to ypr;

grant select on affiliate.cloudflare_internal_data_mapping to readonly;

grant delete, insert, select, truncate, update on affiliate.cloudflare_internal_data_mapping to writeonly_pyscripts;

grant delete, insert, select, truncate, update on affiliate.cloudflare_internal_data_mapping to pyapi;