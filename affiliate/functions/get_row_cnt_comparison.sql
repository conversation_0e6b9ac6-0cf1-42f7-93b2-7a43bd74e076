create or replace function affiliate.get_row_cnt_comparison(target_date date)
    returns TABLE
            (
                source      character varying,
                table_name  character varying,
                row_cnt_dwh integer,
                row_cnt_aff integer
            )
    language plpgsql
as
$$
begin
    drop table if exists aff_row_cnt;
    drop table if exists dwh_row_cnt;
    drop table if exists job_stat_row_cnt;

    create temporary table dwh_row_cnt
    (
        table_name  varchar,
        source_id   integer,
        row_cnt_dwh integer,
        source      varchar
    );

    insert into dwh_row_cnt(table_name, source_id, row_cnt_dwh)
    select
        arcs.table_name,
        arcs.source_id,
        arcs.row_cnt as row_cnt_dwh
    from
        affiliate.afg_row_cnt_snapshot arcs
    where
        arcs.log_date = target_date

    union all

    select
        'task_status_log' as table_name,
        tsl.source_id,
        count(*)          as row_cnt_dwh
    from
        affiliate.task_status_log tsl
    where
        cast(tsl.task_datetime as date) = target_date
    group by tsl.source_id

    union all

    select
        'partner_settings_change_log' as table_name,
        pscl.source_id,
        count(*)                      as row_cnt_dwh
    from
        affiliate.partner_settings_change_log pscl
    where
        cast(pscl.update_datetime as date) = target_date
    group by pscl.source_id

    union all

    select
        'project_cpc_ratio_change_log' as table_name,
        pcrcl.source_id,
        count(distinct pcrcl.id)       as row_cnt_dwh
    from
        affiliate.project_cpc_ratio_change_log pcrcl
    where
        cast(pcrcl.update_datetime as date) = target_date
    group by pcrcl.source_id

    union all

    select
        'api_request_history'   as table_name,
        arsa.id_source          as source_id,
        sum(arsa.request_count) as row_cnt_dwh
    from
        affiliate.api_request_stat_agg arsa
    where
        arsa.request_date = target_date
    group by arsa.id_source

    union all

    select
        'ab_history'::text as table_name,
        ab_history.source_id,
        count(*)           as row_cnt_dwh
    from
        affiliate.ab_history
    where
        start_utc::date < current_date
    group by
        ab_history.source_id

    union all

    select
        'functionality_matrix'::text as table_name,
        1                            as source_id,
        count(*)                     as row_cnt_dwh
    from
        affiliate.functionality_matrix

    union all

    select
        'cpa_static_settings_log' as table_name,
        cscl.id_source,
        count(distinct cscl.id)   as row_cnt_dwh
    from
        affiliate.cpa_static_settings_log cscl
    where
        record_datetime_local::date = target_date
    group by cscl.id_source;

    update dwh_row_cnt
    set source = case when source_id = 1 then 'nl' when source_id = 2 then 'us' when source_id = 0 then 'all' end;

    -- connect to **********
    perform dblink_connect('myconn',
                           'dbname=dwh host=********** user= ''pentaho'' password= ''!faP@jhWYWb&3rwv'' options=-csearch_path=');

    create temporary table job_stat_row_cnt as
    select
        job_stat.id_consumer_run,
        job_stat.row_cnt as row_cnt_aff
    from
        dblink('myconn',
               format('with
                           last_records as (
                               select
                                   id_consumer_run,
                                   max(processed_at) as last_processed_at
                               from public.affiliate_stat
                               group by id_consumer_run
                       )
                       select
                           ast.id_consumer_run,
                           count(*)         as row_cnt
                       from
                           public.affiliate_stat ast
                           join public.country_offset co
                               on co.country = ast.country
                           join last_records t
                               on t.id_consumer_run = ast.id_consumer_run and t.last_processed_at = ast.processed_at
                       where
                           (start_gather_utc + co.offset_in_hour * ''1 hour''::interval)::date >= (%L::date - ''1 day''::interval)::date
                       group by ast.id_consumer_run',
                      target_date)) as job_stat (id_consumer_run uuid, row_cnt integer);

    perform dblink_disconnect('myconn');

    delete from job_stat_row_cnt
    where
            id_consumer_run not in (
            select id_consumer_run
            from affiliate.task_status_log
            where task_datetime::date = target_date
        );

    create temporary table aff_row_cnt as
    select
        'all'::varchar                             as source,
        'job_stat_agg'::varchar                    as table_name,
        sum(job_stat_row_cnt.row_cnt_aff)::integer as row_cnt_aff
    from
        job_stat_row_cnt;

    -- connect to NL affiliate db
    perform dblink_connect('myconnNL'::text,
                           'dbname=affiliate host=********* user= ''pentaho'' password= ''!faP@jhWYWb&3rwv'' options=-csearch_path=');

    insert into aff_row_cnt(source, table_name, row_cnt_aff)
    select
        nl_v_tables_row_cnt.source,
        nl_v_tables_row_cnt.table_name,
        nl_v_tables_row_cnt.row_cnt_aff
    from
        dblink('myconnNL',
               format('select
                           ''nl''   as source,
                           table_name,
                           cnt_rows as row_cnt_aff
                       from
                           public.v_tables_row_cnt
                       where
                           dt = %L',
                      target_date)) as nl_v_tables_row_cnt (source varchar, table_name varchar, row_cnt_aff integer);

    perform dblink_disconnect('myconnNL');

    --connect to US affiliate db
    perform dblink_connect('myconnUS'::text,
                           'dbname=affiliate host=************ user= ''pentaho'' password= ''!faP@jhWYWb&3rwv'' options=-csearch_path=');

    insert into aff_row_cnt(source, table_name, row_cnt_aff)
    select
        us_v_tables_row_cnt.source,
        us_v_tables_row_cnt.table_name,
        us_v_tables_row_cnt.row_cnt_aff
    from
        dblink('myconnUS',
               format('select
                           ''us''   as source,
                           table_name,
                           cnt_rows as row_cnt_aff
                       from
                           public.v_tables_row_cnt
                       where
                           dt = %L',
                      target_date)) as us_v_tables_row_cnt (source varchar, table_name varchar, row_cnt_aff integer);


    perform dblink_disconnect('myconnUS');

    return query
        select
            coalesce(a.source, d.source)         as source,
            coalesce(a.table_name, d.table_name) as table_name,
            coalesce(d.row_cnt_dwh, 0)           as row_cnt_dwh,
            coalesce(a.row_cnt_aff, 0)           as row_cnt_aff
        from
            dwh_row_cnt d
            full join aff_row_cnt a
                      on a.source = d.source and
                         a.table_name = d.table_name
        where
             target_date = current_date - 1
          or coalesce(a.table_name, d.table_name) not in ('functionality_matrix', 'ab_history');

    drop table aff_row_cnt;
    drop table dwh_row_cnt;
    drop table job_stat_row_cnt;

end
$$;

alter function affiliate.get_row_cnt_comparison(date) owner to ypr;

