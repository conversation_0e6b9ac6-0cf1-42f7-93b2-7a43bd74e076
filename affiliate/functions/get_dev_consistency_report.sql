create or replace function affiliate.get_dev_consistency_report()
    returns TABLE
            (
                table_name               character varying,
                record_date              date,
                dimension_name           varchar,
                dimension_value          varchar,
                metric_name              character varying,
                metric_value             numeric,
                reference_metric_value   numeric,
                reference_source         character varying,
                check_type               character varying,
                is_incomplete            boolean,
                max_acceptable_deviation character varying
            )
    language plpgsql
as
$$
begin

    drop table if exists temp_result;
    drop table if exists temp_vmma_aff;
    drop table if exists temp_vmma_dev;
    drop table if exists temp_v_click_data_aff;
    drop table if exists temp_v_click_data_dev;
    drop table if exists stat_daily_agg_aff;
    drop table if exists stat_daily_agg_dev;
    drop table if exists clean_asm;
    drop table if exists clean_asa;

    create temporary table temp_result
    (
        table_name               varchar,
        record_date              date,
        dimension_name           varchar,
        dimension_value          varchar,
        metric_name              varchar,
        metric_value             numeric,
        reference_metric_value   numeric,
        reference_source         varchar,
        check_type               varchar,
        is_incomplete            boolean,
        max_acceptable_deviation varchar
    );

    --------------------------------------------------------------------------------------------------------------------
    -- Perform update recency check for tables with known update date and without risks of incomplete data transfer.
    --------------------------------------------------------------------------------------------------------------------

    insert into
        temp_result(table_name, record_date, dimension_name, dimension_value, metric_name, metric_value,
                    reference_metric_value, reference_source, check_type, is_incomplete, max_acceptable_deviation)

    with
        update_date_dic_manager_dev as (
            select
                max(update_datetime)::date as update_date_dev,
                'dic_manager'              as table_name
            from
                affiliate_dev.dic_manager
        ),
        update_date_dic_manager_aff as (
            select
                max(update_datetime)::date as update_date_aff,
                'dic_manager'              as table_name
            from
                affiliate.dic_manager
        ),
        update_date_dic_manager_data as (
            select
                d.update_date_dev,
                a.update_date_aff,
                d.table_name
            from
                update_date_dic_manager_dev as d
                join update_date_dic_manager_aff as a
                     on d.table_name = a.table_name
        )

    select
        dm.table_name                                        as table_name,
        current_date                                         as record_date,
        null::varchar                                        as dimension_name,
        null::varchar                                        as dimension_value,
        null::varchar                                        as metric_name,
        public.fn_get_date_diff(dm.update_date_dev)          as metric_value,
        public.fn_get_date_diff(dm.update_date_aff)          as reference_metric_value,
        'affiliate.' || dm.table_name                        as reference_source,
        'table update date'                                  as check_type,
        dm.update_date_dev::date != dm.update_date_aff::date as is_incomplete,
        '0'                                                  as max_acceptable_deviation
    from
        update_date_dic_manager_data as dm;

    insert into
        temp_result(table_name, record_date, dimension_name, dimension_value, metric_name, metric_value,
                    reference_metric_value, reference_source, check_type, is_incomplete, max_acceptable_deviation)

    with
        update_date_project_cpc_ratio_dev as (
            select
                max(update_datetime)::date as update_date_dev,
                'project_cpc_ratio'        as table_name
            from
                affiliate_dev.project_cpc_ratio
        ),
        update_date_project_cpc_ratio_aff as (
            select
                max(update_datetime)::date as update_date_aff,
                'project_cpc_ratio'        as table_name
            from
                affiliate.project_cpc_ratio
        ),
        update_date_project_cpc_ratio_data as (
            select
                d.update_date_dev,
                a.update_date_aff,
                d.table_name
            from
                update_date_project_cpc_ratio_dev as d
                join update_date_project_cpc_ratio_aff as a
                     on d.table_name = a.table_name
        )

    select
        pcr.table_name                                         as table_name,
        current_date                                           as record_date,
        null::varchar                                          as dimension_name,
        null::varchar                                          as dimension_value,
        null::varchar                                          as metric_name,
        public.fn_get_date_diff(pcr.update_date_dev)           as metric_value,
        public.fn_get_date_diff(pcr.update_date_aff)           as reference_metric_value,
        'affiliate.' || pcr.table_name                         as reference_source,
        'table update date'                                    as check_type,
        pcr.update_date_dev::date != pcr.update_date_aff::date as is_incomplete,
        '0'                                                    as max_acceptable_deviation
    from
        update_date_project_cpc_ratio_data as pcr;


    insert into
        temp_result(table_name, record_date, dimension_name, dimension_value, metric_name, metric_value,
                    reference_metric_value, reference_source, check_type, is_incomplete, max_acceptable_deviation)

    with
        update_date_partner_settings_dev as (
            select
                max(update_datetime)::date as update_date_dev,
                'partner_settings'         as table_name
            from
                affiliate_dev.partner_settings
        ),
        update_date_partner_settings_aff as (
            select
                max(update_datetime)::date as update_date_aff,
                'partner_settings'         as table_name
            from
                affiliate.partner_settings
        ),
        update_date_partner_settings_data as (
            select
                d.update_date_dev,
                a.update_date_aff,
                d.table_name
            from
                update_date_partner_settings_dev as d
                join update_date_partner_settings_aff as a
                     on d.table_name = a.table_name
        )

    select
        ps.table_name                                        as table_name,
        current_date                                         as record_date,
        null::varchar                                        as dimension_name,
        null::varchar                                        as dimension_value,
        null::varchar                                        as metric_name,
        public.fn_get_date_diff(ps.update_date_dev)          as metric_value,
        public.fn_get_date_diff(ps.update_date_aff)          as reference_metric_value,
        'affiliate.' || ps.table_name                        as reference_source,
        'table update date'                                  as check_type,
        ps.update_date_dev::date != ps.update_date_aff::date as is_incomplete,
        '0'                                                  as max_acceptable_deviation
    from
        update_date_partner_settings_data as ps;

    --------------------------------------------------------------------------------------------------------------------
    -- Perform most recent date check for tables with click statistics.
    --------------------------------------------------------------------------------------------------------------------

    insert into
        temp_result(table_name, record_date, dimension_name, dimension_value, metric_name, metric_value,
                    reference_metric_value, reference_source, check_type, is_incomplete, max_acceptable_deviation)

    with
        max_dates_partner_daily_snapshot_dev as (
            select
                max(date_diff)           as max_date_diff_dev,
                'partner_daily_snapshot' as table_name
            from
                affiliate_dev.partner_daily_snapshot
        ),
        max_dates_partner_daily_snapshot_aff as (
            select
                max(date_diff)           as max_date_diff_aff,
                'partner_daily_snapshot' as table_name
            from
                affiliate.partner_daily_snapshot
        ),
        max_dates_partner_daily_snapshot_data as (
            select
                d.max_date_diff_dev,
                a.max_date_diff_aff,
                d.table_name
            from
                max_dates_partner_daily_snapshot_dev as d
                join max_dates_partner_daily_snapshot_aff as a
                     on d.table_name = a.table_name
        )

    select
        pds.table_name                                 as table_name,
        current_date                                   as record_date,
        null::varchar                                  as dimension_name,
        null::varchar                                  as dimension_value,
        null::varchar                                  as metric_name,
        pds.max_date_diff_dev                          as metric_value,
        pds.max_date_diff_aff                          as reference_metric_value,
        'affiliate.' || pds.table_name                 as reference_source,
        'max date in table'                            as check_type,
        pds.max_date_diff_dev != pds.max_date_diff_aff as is_incomplete,
        '0'                                            as max_acceptable_deviation
    from
        max_dates_partner_daily_snapshot_data as pds;


    insert into
        temp_result(table_name, record_date, dimension_name, dimension_value, metric_name, metric_value,
                    reference_metric_value, reference_source, check_type, is_incomplete, max_acceptable_deviation)

    with
        max_dates_statistics_daily_raw_dev as (
            select
                max(date_diff)         as max_date_diff_dev,
                'statistics_daily_raw' as table_name
            from
                affiliate_dev.statistics_daily_raw
        ),
        max_dates_statistics_daily_raw_aff as (
            select
                max(date_diff)         as max_date_diff_aff,
                'statistics_daily_raw' as table_name
            from
                affiliate.statistics_daily_raw
        ),
        max_dates_statistics_daily_raw_data as (
            select
                d.max_date_diff_dev,
                a.max_date_diff_aff,
                d.table_name
            from
                max_dates_statistics_daily_raw_dev as d
                join max_dates_statistics_daily_raw_aff as a
                     on d.table_name = a.table_name
        )

    select
        sdr.table_name                                 as table_name,
        current_date                                   as record_date,
        null::varchar                                  as dimension_name,
        null::varchar                                  as dimension_value,
        null::varchar                                  as metric_name,
        sdr.max_date_diff_dev                          as metric_value,
        sdr.max_date_diff_aff                          as reference_metric_value,
        'affiliate.' || sdr.table_name                 as reference_source,
        'max date in table'                            as check_type,
        sdr.max_date_diff_dev != sdr.max_date_diff_aff as is_incomplete,
        '0'                                            as max_acceptable_deviation
    from
        max_dates_statistics_daily_raw_data as sdr;


    insert into
        temp_result(table_name, record_date, dimension_name, dimension_value, metric_name, metric_value,
                    reference_metric_value, reference_source, check_type, is_incomplete, max_acceptable_deviation)

    with
        max_dates_statistics_daily_agg_dev as (
            select
                max(date_diff)         as max_date_diff_dev,
                'statistics_daily_agg' as table_name
            from
                affiliate_dev.statistics_daily_agg
        ),
        max_dates_statistics_daily_agg_aff as (
            select
                max(date_diff)         as max_date_diff_aff,
                'statistics_daily_agg' as table_name
            from
                affiliate.statistics_daily_agg
        ),
        max_dates_statistics_daily_agg_data as (
            select
                d.max_date_diff_dev,
                a.max_date_diff_aff,
                d.table_name
            from
                max_dates_statistics_daily_agg_dev as d
                join max_dates_statistics_daily_agg_aff as a
                     on d.table_name = a.table_name
        )

    select
        sda.table_name                                 as table_name,
        current_date                                   as record_date,
        null::varchar                                  as dimension_name,
        null::varchar                                  as dimension_value,
        null::varchar                                  as metric_name,
        sda.max_date_diff_dev                          as metric_value,
        sda.max_date_diff_aff                          as reference_metric_value,
        'affiliate.' || sda.table_name                 as reference_source,
        'max date in table'                            as check_type,
        sda.max_date_diff_dev != sda.max_date_diff_aff as is_incomplete,
        '0'                                            as max_acceptable_deviation
    from
        max_dates_statistics_daily_agg_data as sda;

    insert into
        temp_result(table_name, record_date, dimension_name, dimension_value, metric_name, metric_value,
                    reference_metric_value, reference_source, check_type, is_incomplete, max_acceptable_deviation)

    with
        max_dates_prediction_features_v2_dev as (
            select
                max(d.record_date)::date as max_date_diff_dev,
                'prediction_features_v2' as table_name
            from
                affiliate_dev.prediction_features_v2 as d
        ),
        max_dates_prediction_features_v2_aff as (
            select
                max(a.record_date)::date as max_date_diff_aff,
                'prediction_features_v2' as table_name
            from
                affiliate.prediction_features_v2 as a
        ),
        max_dates_prediction_features_v2_data as (
            select
                d.max_date_diff_dev,
                a.max_date_diff_aff,
                d.table_name
            from
                max_dates_prediction_features_v2_dev as d
                join max_dates_prediction_features_v2_aff as a
                     on d.table_name = a.table_name
        )

    select
        pf2.table_name                                 as table_name,
        current_date                                   as record_date,
        null::varchar                                  as dimension_name,
        null::varchar                                  as dimension_value,
        null::varchar                                  as metric_name,
        public.fn_get_date_diff(pf2.max_date_diff_dev) as metric_value,
        public.fn_get_date_diff(pf2.max_date_diff_aff) as reference_metric_value,
        'affiliate.' || pf2.table_name                 as reference_source,
        'max date in table'                            as check_type,
        pf2.max_date_diff_dev != pf2.max_date_diff_aff as is_incomplete,
        '0'                                            as max_acceptable_deviation
    from
        max_dates_prediction_features_v2_data as pf2;

    --------------------------------------------------------------------------------------------------------------------
    -- Perform most recent date check for tables with ab tests.
    --------------------------------------------------------------------------------------------------------------------
    insert into
        temp_result(table_name, record_date, dimension_name, dimension_value, metric_name, metric_value,
                    reference_metric_value, reference_source, check_type, is_incomplete, max_acceptable_deviation)

    with
        max_dates_abtest_stat_agg_dev as (
            select
                max(dasa.record_date)::date as max_date_diff_dev,
                'abtest_stat_agg'           as table_name
            from
                affiliate_dev.abtest_stat_agg as dasa
        ),
        max_dates_abtest_stat_agg_aff as (
            select
                max(aasa.record_date)::date as max_date_diff_aff,
                'abtest_stat_agg'           as table_name
            from
                affiliate.abtest_stat_agg as aasa
        ),
        max_dates_abtest_stat_agg_data as (
            select
                d.max_date_diff_dev,
                a.max_date_diff_aff,
                d.table_name
            from
                max_dates_abtest_stat_agg_dev as d
                join max_dates_abtest_stat_agg_aff as a
                     on d.table_name = a.table_name
        )

    select
        asa.table_name                                              as table_name,
        current_date                                                as record_date,
        null::varchar                                               as dimension_name,
        null::varchar                                               as dimension_value,
        null::varchar                                               as metric_name,
        public.fn_get_date_diff(asa.max_date_diff_dev)              as metric_value,
        public.fn_get_date_diff(asa.max_date_diff_aff)              as reference_metric_value,
        'affiliate.' || asa.table_name                              as reference_source,
        'max date in table'                                         as check_type,
        coalesce(public.fn_get_date_diff(asa.max_date_diff_dev), 0) !=
        coalesce(public.fn_get_date_diff(asa.max_date_diff_aff), 0) as is_incomplete,
        '0'                                                         as max_acceptable_deviation
    from
        max_dates_abtest_stat_agg_data as asa;


    insert into
        temp_result(table_name, record_date, dimension_name, dimension_value, metric_name, metric_value,
                    reference_metric_value, reference_source, check_type, is_incomplete, max_acceptable_deviation)

    with
        max_dates_abtest_significance_metrics_dev as (
            select
                max(dasm.date_end)::date      as max_date_diff_dev,
                'abtest_significance_metrics' as table_name
            from
                affiliate_dev.abtest_significance_metrics as dasm
        ),
        max_dates_abtest_significance_metrics_aff as (
            select
                max(aasm.date_end)::date      as max_date_diff_aff,
                'abtest_significance_metrics' as table_name
            from
                affiliate.abtest_significance_metrics as aasm
        ),
        max_dates_abtest_significance_metrics_data as (
            select
                d.max_date_diff_dev,
                a.max_date_diff_aff,
                d.table_name
            from
                max_dates_abtest_significance_metrics_dev as d
                join max_dates_abtest_significance_metrics_aff as a
                     on d.table_name = a.table_name
        )

    select
        asm.table_name                                              as table_name,
        current_date                                                as record_date,
        null::varchar                                               as dimension_name,
        null::varchar                                               as dimension_value,
        null::varchar                                               as metric_name,
        public.fn_get_date_diff(asm.max_date_diff_dev)              as metric_value,
        public.fn_get_date_diff(asm.max_date_diff_aff)              as reference_metric_value,
        'affiliate.' || asm.table_name                              as reference_source,
        'max date in table'                                         as check_type,
        coalesce(public.fn_get_date_diff(asm.max_date_diff_dev), 0) !=
        coalesce(public.fn_get_date_diff(asm.max_date_diff_aff), 0) as is_incomplete,
        '0'                                                         as max_acceptable_deviation
    from
        max_dates_abtest_significance_metrics_data as asm;

    --------------------------------------------------------------------------------------------------------------------
    -- Compare feed gathers with CPC ratio optimization count in affiliate.optimal_cpc_ratio and affiliate.job_stat_agg.
    --------------------------------------------------------------------------------------------------------------------
    insert into
        temp_result(table_name, record_date, dimension_name, dimension_value, metric_name, metric_value,
                    reference_metric_value, reference_source, check_type, is_incomplete, max_acceptable_deviation)

    with
        optimal_cpc_ratio_data_aff as (
            select distinct
                ocra.task_datetime::date as date_aff,
                ocra.task_datetime,
                tsla.start_gather_datetime,
                ocra.id_local_partner,
                ocra.source_id
            from
                affiliate.optimal_cpc_ratio ocra
                join affiliate.task_status_log tsla
                     on tsla.source_id = ocra.source_id and
                        tsla.id_partner = ocra.id_local_partner and
                        tsla.task_datetime = ocra.task_datetime
            where
                ocra.task_datetime::date >= current_date - 7
        ),
        optimal_cpc_ratio_check_aff as (
            select
                ocrda.date_aff,
                count(*) as feed_gather_cnt_aff
            from
                optimal_cpc_ratio_data_aff as ocrda
            group by
                ocrda.date_aff
        ),
        optimal_cpc_ratio_data_dev as (
            select distinct
                ocrd.task_datetime::date as date_dev,
                ocrd.task_datetime,
                tsld.start_gather_datetime,
                ocrd.id_local_partner,
                ocrd.source_id
            from
                affiliate_dev.optimal_cpc_ratio ocrd
                join affiliate.task_status_log tsld
                     on tsld.source_id = ocrd.source_id and
                        tsld.id_partner = ocrd.id_local_partner and
                        tsld.task_datetime = ocrd.task_datetime
            where
                ocrd.task_datetime::date >= current_date - 7
        ),
        optimal_cpc_ratio_check_dev as (
            select
                ocrdd.date_dev,
                count(*) as feed_gather_cnt_dev
            from
                optimal_cpc_ratio_data_dev as ocrdd
            group by
                ocrdd.date_dev
        )

    select
        'optimal_cpc_ratio'                              as table_name,
        ocrca.date_aff                                   as record_date,
        null::varchar                                    as dimension_name,
        null::varchar                                    as dimension_value,
        'number of feed gathers with optimal CPC ratios' as metric_name,
        ocrcd.feed_gather_cnt_dev                        as metric_value,
        ocrca.feed_gather_cnt_aff                        as reference_metric_value,
        'affiliate.job_stat_agg'                         as reference_source,
        'metric value comparison'                        as check_type,
        coalesce(ocrcd.feed_gather_cnt_dev, 0) !=
        coalesce(ocrca.feed_gather_cnt_aff, 0)           as is_incomplete,
        '0'                                              as max_acceptable_deviation
    from
        optimal_cpc_ratio_check_aff ocrca
        left join optimal_cpc_ratio_check_dev ocrcd
                  on ocrca.date_aff = ocrcd.date_dev;

    --------------------------------------------------------------------------------------------------------------------
    -- Compare metrics in v_main_metrics_agg.
    --------------------------------------------------------------------------------------------------------------------

    create temporary table temp_vmma_aff as
    select
        vmmaa.click_date,
        sum(vmmaa.certified_cost_usd)     as certified_cost_usd,
        sum(vmmaa.certified_click_cnt)    as certified_click_cnt,
        sum(vmmaa.revenue_usd)            as revenue_usd,
        sum(vmmaa.conversion_cnt)         as conversion_cnt,
        sum(vmmaa.conversion_revenue_usd) as conversion_revenue_usd,
        sum(vmmaa.conversion_away_cnt)    as conversion_away_cnt
    from
        affiliate.v_main_metrics_agg as vmmaa
    where
        vmmaa.click_date >= current_date - 7
    group by
        vmmaa.click_date;

    create temporary table temp_vmma_dev as
    select
        vmmad.click_date,
        sum(vmmad.certified_cost_usd)     as certified_cost_usd,
        sum(vmmad.certified_click_cnt)    as certified_click_cnt,
        sum(vmmad.revenue_usd)            as revenue_usd,
        sum(vmmad.conversion_cnt)         as conversion_cnt,
        sum(vmmad.conversion_revenue_usd) as conversion_revenue_usd,
        sum(vmmad.conversion_away_cnt)    as conversion_away_cnt
    from
        affiliate_dev.v_main_metrics_agg as vmmad
    where
        vmmad.click_date >= current_date - 7
    group by
        vmmad.click_date;

    insert into
        temp_result(table_name, record_date, dimension_name, dimension_value, metric_name, metric_value,
                    reference_metric_value, reference_source, check_type, is_incomplete, max_acceptable_deviation)
    select
        'v_main_metrics_agg'                    as table_name,
        tvmmaa.click_date                       as record_date,
        null::varchar                           as dimension_name,
        null::varchar                           as dimension_value,
        'certified_click_cnt'                   as metric_name,
        coalesce(tvmmad.certified_click_cnt, 0) as metric_value,
        coalesce(tvmmaa.certified_click_cnt, 0) as reference_metric_value,
        'affiliate.v_main_metrics_agg'          as reference_source,
        'metric value comparison'               as check_type,
        coalesce(tvmmad.certified_click_cnt, 0) !=
        coalesce(tvmmaa.certified_click_cnt, 0) as is_incomplete,
        '0'                                     as max_acceptable_deviation
    from
        temp_vmma_aff tvmmaa
        left join temp_vmma_dev tvmmad
                  on tvmmaa.click_date = tvmmad.click_date;

    insert into
        temp_result(table_name, record_date, dimension_name, dimension_value, metric_name, metric_value,
                    reference_metric_value, reference_source, check_type, is_incomplete, max_acceptable_deviation)

    select
        'v_main_metrics_agg'                                         as table_name,
        tvmmaa.click_date                                            as record_date,
        null::varchar                                                as dimension_name,
        null::varchar                                                as dimension_value,
        'certified_cost_usd'                                         as metric_name,
        coalesce(tvmmad.certified_cost_usd, 0)                       as metric_value,
        coalesce(tvmmaa.certified_cost_usd, 0)                       as reference_metric_value,
        'affiliate.v_main_metrics_agg'                               as reference_source,
        'metric value comparison'                                    as check_type,
        case
            when coalesce(tvmmaa.certified_cost_usd, 0) = 0 then false
            else abs(coalesce(tvmmad.certified_cost_usd, 0) - coalesce(tvmmaa.certified_cost_usd, 0)) /
                 coalesce(tvmmaa.certified_cost_usd, 0) > 0.0001 end as is_incomplete,
        '0.01%'                                                      as max_acceptable_deviation
    from
        temp_vmma_aff tvmmaa
        left join temp_vmma_dev tvmmad
                  on tvmmaa.click_date = tvmmad.click_date;

    insert into
        temp_result(table_name, record_date, dimension_name, dimension_value, metric_name, metric_value,
                    reference_metric_value, reference_source, check_type, is_incomplete, max_acceptable_deviation)

    select
        'v_main_metrics_agg'                                  as table_name,
        tvmmaa.click_date                                     as record_date,
        null::varchar                                         as dimension_name,
        null::varchar                                         as dimension_value,
        'revenue_usd'                                         as metric_name,
        coalesce(tvmmad.revenue_usd, 0)                       as metric_value,
        coalesce(tvmmaa.revenue_usd, 0)                       as reference_metric_value,
        'affiliate.v_main_metrics_agg'                        as reference_source,
        'metric value comparison'                             as check_type,
        case
            when coalesce(tvmmaa.revenue_usd, 0) = 0 then false
            else abs(coalesce(tvmmad.revenue_usd, 0) - coalesce(tvmmaa.revenue_usd, 0)) /
                 coalesce(tvmmaa.revenue_usd, 0) > 0.0001 end as is_incomplete,
        '0.01%'                                               as max_acceptable_deviation
    from
        temp_vmma_aff tvmmaa
        left join temp_vmma_dev tvmmad
                  on tvmmaa.click_date = tvmmad.click_date;

    insert into
        temp_result(table_name, record_date, dimension_name, dimension_value, metric_name, metric_value,
                    reference_metric_value, reference_source, check_type, is_incomplete, max_acceptable_deviation)

    select
        'v_main_metrics_agg'               as table_name,
        tvmmaa.click_date                  as record_date,
        null::varchar                      as dimension_name,
        null::varchar                      as dimension_value,
        'conversion_cnt'                   as metric_name,
        coalesce(tvmmad.conversion_cnt, 0) as metric_value,
        coalesce(tvmmaa.conversion_cnt, 0) as reference_metric_value,
        'affiliate.v_main_metrics_agg'     as reference_source,
        'metric value comparison'          as check_type,
        coalesce(tvmmad.conversion_cnt, 0) !=
        coalesce(tvmmaa.conversion_cnt, 0) as is_incomplete,
        '0'                                as max_acceptable_deviation
    from
        temp_vmma_aff tvmmaa
        left join temp_vmma_dev tvmmad
                  on tvmmaa.click_date = tvmmad.click_date;

    insert into
        temp_result(table_name, record_date, dimension_name, dimension_value, metric_name, metric_value,
                    reference_metric_value, reference_source, check_type, is_incomplete, max_acceptable_deviation)
    select
        'v_main_metrics_agg'                                             as table_name,
        tvmmaa.click_date                                                as record_date,
        null::varchar                                                    as dimension_name,
        null::varchar                                                    as dimension_value,
        'conversion_revenue_usd'                                         as metric_name,
        coalesce(tvmmad.conversion_revenue_usd, 0)                       as metric_value,
        coalesce(tvmmaa.conversion_revenue_usd, 0)                       as reference_metric_value,
        'affiliate.v_main_metrics_agg'                                   as reference_source,
        'metric value comparison'                                        as check_type,
        case
            when coalesce(tvmmaa.conversion_revenue_usd, 0) = 0 then false
            else abs(coalesce(tvmmad.conversion_revenue_usd, 0) - coalesce(tvmmaa.conversion_revenue_usd, 0)) /
                 coalesce(tvmmaa.conversion_revenue_usd, 0) > 0.0001 end as is_incomplete,
        '0.01%'                                                          as max_acceptable_deviation
    from
        temp_vmma_aff tvmmaa
        left join temp_vmma_dev tvmmad
                  on tvmmaa.click_date = tvmmad.click_date;

    insert into
        temp_result(table_name, record_date, dimension_name, dimension_value, metric_name, metric_value,
                    reference_metric_value, reference_source, check_type, is_incomplete, max_acceptable_deviation)
    select
        'v_main_metrics_agg'                    as table_name,
        tvmmaa.click_date                       as record_date,
        null::varchar                           as dimension_name,
        null::varchar                           as dimension_value,
        'conversion_away_cnt'                   as metric_name,
        coalesce(tvmmad.conversion_away_cnt, 0) as metric_value,
        coalesce(tvmmaa.conversion_away_cnt, 0) as reference_metric_value,
        'affiliate.v_main_metrics_agg'          as reference_source,
        'metric value comparison'               as check_type,
        coalesce(tvmmad.conversion_away_cnt, 0) !=
        coalesce(tvmmaa.conversion_away_cnt, 0) as is_incomplete,
        '0'                                     as max_acceptable_deviation
    from
        temp_vmma_aff tvmmaa
        left join temp_vmma_dev tvmmad
                  on tvmmaa.click_date = tvmmad.click_date;

    --------------------------------------------------------------------------------------------------------------------
    -- Compare metrics in v_click_data.
    --------------------------------------------------------------------------------------------------------------------

    create temporary table temp_v_click_data_aff as
    select
        acda.click_date,
        sum(case
                when acda.click_type = 'certified' and acda.feed_cpa_usd is not null
                    then acda.feed_cpa_usd * coalesce(acda.conversion_count, 0)
                when acda.click_type = 'certified' then acda.feed_cpc_usd end)    as certified_cost_usd,
        sum(case when acda.click_type = 'certified' then 1 end)                   as certified_click_cnt,
        sum(acda.client_cpc_usd)                                                  as revenue_usd,
        sum(case when acda.has_postback = 1 then acda.conversion_count end)       as conversion_cnt,
        sum(case when acda.has_postback = 1 then acda.conversion_revenue_usd end) as conversion_revenue_usd,
        sum(case when acda.has_postback = 1 then acda.conversion_click_count end) as conversion_away_cnt
    from
        affiliate.v_click_data as acda
    where
        acda.click_date >= current_date - 7
    group by
        acda.click_date;


    create temporary table temp_v_click_data_dev as
    select
        acdd.click_date,
        sum(case
                when acdd.click_type = 'certified' and acdd.feed_cpa_usd is not null
                    then acdd.feed_cpa_usd * coalesce(acdd.conversion_count, 0)
                when acdd.click_type = 'certified' then acdd.feed_cpc_usd end)    as certified_cost_usd,
        sum(case when acdd.click_type = 'certified' then 1 end)                   as certified_click_cnt,
        sum(acdd.client_cpc_usd)                                                  as revenue_usd,
        sum(case when acdd.has_postback = 1 then acdd.conversion_count end)       as conversion_cnt,
        sum(case when acdd.has_postback = 1 then acdd.conversion_revenue_usd end) as conversion_revenue_usd,
        sum(case when acdd.has_postback = 1 then acdd.conversion_click_count end) as conversion_away_cnt
    from
        affiliate_dev.v_click_data acdd
    where
        acdd.click_date >= current_date - 7
    group by
        acdd.click_date;

    insert into
        temp_result(table_name, record_date, dimension_name, dimension_value, metric_name, metric_value,
                    reference_metric_value, reference_source, check_type, is_incomplete, max_acceptable_deviation)
    select
        'v_click_data'                         as table_name,
        tvcda.click_date                       as record_date,
        null::varchar                          as dimension_name,
        null::varchar                          as dimension_value,
        'certified_click_cnt'                  as metric_name,
        coalesce(tvcdd.certified_click_cnt, 0) as metric_value,
        coalesce(tvcda.certified_click_cnt, 0) as reference_metric_value,
        'affiliate.v_click_data'               as reference_source,
        'metric value comparison'              as check_type,
        coalesce(tvcdd.certified_click_cnt, 0) !=
        coalesce(tvcda.certified_click_cnt, 0) as is_incomplete,
        '0'                                    as max_acceptable_deviation
    from
        temp_v_click_data_aff tvcda
        left join temp_v_click_data_dev tvcdd
                  on tvcda.click_date = tvcdd.click_date;

    insert into
        temp_result(table_name, record_date, dimension_name, dimension_value, metric_name, metric_value,
                    reference_metric_value, reference_source, check_type, is_incomplete, max_acceptable_deviation)
    select
        'v_click_data'                                              as table_name,
        tvcda.click_date                                            as record_date,
        null::varchar                                               as dimension_name,
        null::varchar                                               as dimension_value,
        'certified_cost_usd'                                        as metric_name,
        coalesce(tvcdd.certified_cost_usd, 0)                       as metric_value,
        coalesce(tvcda.certified_cost_usd, 0)                       as reference_metric_value,
        'affiliate.v_click_data'                                    as reference_source,
        'metric value comparison'                                   as check_type,
        case
            when coalesce(tvcda.certified_cost_usd, 0) = 0 then false
            else abs(coalesce(tvcdd.certified_cost_usd, 0) - coalesce(tvcda.certified_cost_usd, 0)) /
                 coalesce(tvcda.certified_cost_usd, 0) > 0.0001 end as is_incomplete,
        '0.01%'                                                     as max_acceptable_deviation
    from
        temp_v_click_data_aff tvcda
        left join temp_v_click_data_dev tvcdd
                  on tvcda.click_date = tvcdd.click_date;

    insert into
        temp_result(table_name, record_date, dimension_name, dimension_value, metric_name, metric_value,
                    reference_metric_value, reference_source, check_type, is_incomplete, max_acceptable_deviation)
    select
        'v_click_data'                                       as table_name,
        tvcda.click_date                                     as record_date,
        null::varchar                                        as dimension_name,
        null::varchar                                        as dimension_value,
        'revenue_usd'                                        as metric_name,
        coalesce(tvcdd.revenue_usd, 0)                       as metric_value,
        coalesce(tvcda.revenue_usd, 0)                       as reference_metric_value,
        'affiliate.v_click_data'                             as reference_source,
        'metric value comparison'                            as check_type,
        case
            when coalesce(tvcda.revenue_usd, 0) = 0 then false
            else abs(coalesce(tvcdd.revenue_usd, 0) - coalesce(tvcda.revenue_usd, 0)) /
                 coalesce(tvcda.revenue_usd, 0) > 0.0001 end as is_incomplete,
        '0.01%'                                              as max_acceptable_deviation
    from
        temp_v_click_data_aff tvcda
        left join temp_v_click_data_dev tvcdd
                  on tvcda.click_date = tvcdd.click_date;

    insert into
        temp_result(table_name, record_date, dimension_name, dimension_value, metric_name, metric_value,
                    reference_metric_value, reference_source, check_type, is_incomplete, max_acceptable_deviation)
    select
        'v_click_data'                           as table_name,
        tvcda.click_date                         as record_date,
        null::varchar                            as dimension_name,
        null::varchar                            as dimension_value,
        'conversion_cnt'                         as metric_name,
        coalesce(tvcdd.conversion_cnt, 0)        as metric_value,
        coalesce(tvcda.conversion_cnt, 0)        as reference_metric_value,
        'affiliate.v_click_data'                 as reference_source,
        'metric value comparison'                as check_type,
        coalesce(tvcdd.conversion_cnt, 0)
            != coalesce(tvcda.conversion_cnt, 0) as is_incomplete,
        '0'                                      as max_acceptable_deviation
    from
        temp_v_click_data_aff tvcda
        left join temp_v_click_data_dev tvcdd
                  on tvcda.click_date = tvcdd.click_date;

    insert into
        temp_result(table_name, record_date, dimension_name, dimension_value, metric_name, metric_value,
                    reference_metric_value, reference_source, check_type, is_incomplete, max_acceptable_deviation)
    select
        'v_click_data'                                                  as table_name,
        tvcda.click_date                                                as record_date,
        null::varchar                                                   as dimension_name,
        null::varchar                                                   as dimension_value,
        'conversion_revenue_usd'                                        as metric_name,
        coalesce(tvcdd.conversion_revenue_usd, 0)                       as metric_value,
        coalesce(tvcda.conversion_revenue_usd, 0)                       as reference_metric_value,
        'affiliate.v_click_data'                                        as reference_source,
        'metric value comparison'                                       as check_type,
        case
            when coalesce(tvcda.conversion_revenue_usd, 0) = 0 then false
            else abs(coalesce(tvcdd.conversion_revenue_usd, 0) - coalesce(tvcda.conversion_revenue_usd, 0)) /
                 coalesce(tvcda.conversion_revenue_usd, 0) > 0.0001 end as is_incomplete,
        '0.01%'                                                         as max_acceptable_deviation
    from
        temp_v_click_data_aff tvcda
        left join temp_v_click_data_dev tvcdd
                  on tvcda.click_date = tvcdd.click_date;

    insert into
        temp_result(table_name, record_date, dimension_name, dimension_value, metric_name, metric_value,
                    reference_metric_value, reference_source, check_type, is_incomplete, max_acceptable_deviation)
    select
        'v_click_data'                                as table_name,
        tvcda.click_date                              as record_date,
        null::varchar                                 as dimension_name,
        null::varchar                                 as dimension_value,
        'conversion_away_cnt'                         as metric_name,
        coalesce(tvcdd.conversion_away_cnt, 0)        as metric_value,
        coalesce(tvcda.conversion_away_cnt, 0)        as reference_metric_value,
        'affiliate.v_click_data'                      as reference_source,
        'metric value comparison'                     as check_type,
        coalesce(tvcdd.conversion_away_cnt, 0)
            != coalesce(tvcda.conversion_away_cnt, 0) as is_incomplete,
        '0'                                           as max_acceptable_deviation
    from
        temp_v_click_data_aff tvcda
        left join temp_v_click_data_dev tvcdd
                  on tvcda.click_date = tvcdd.click_date;

    --------------------------------------------------------------------------------------------------------------------
    -- Compare metrics in v_statistics_daily_agg.
    --------------------------------------------------------------------------------------------------------------------

    create temporary table stat_daily_agg_aff as
    select
        vsdaa.date                                                                                                    as click_date,
        sum(case
                when vsdaa.feed_cpa_usd is not null and vsdaa.click_type = 'certified'
                    then (coalesce(vsdaa.jdp_away_conversion_cnt, 0) + coalesce(vsdaa.away_conversion_cnt, 0)) *
                         vsdaa.feed_cpa_usd
                when vsdaa.click_type = 'certified'
                    then vsdaa.external_click_cnt * vsdaa.feed_cpc_usd end)                                           as certified_cost_usd,
        sum(case
                when vsdaa.click_type = 'certified'
                    then vsdaa.external_click_cnt end)                                                                as certified_click_cnt,
        sum(case
                when vsdaa.is_bot = 0 and vsdaa.is_internal_duplicated = 0 and vsdaa.is_external_stat_client = 1
                    then
                        vsdaa.client_cpc_usd *
                        (coalesce(vsdaa.away_click_cnt, 0) + coalesce(vsdaa.jdp_click_cnt, 0))
                when vsdaa.is_bot = 0 and vsdaa.is_internal_duplicated = 0 and vsdaa.is_external_stat_client = 0
                    then
                        vsdaa.client_cpc_usd *
                        (coalesce(vsdaa.away_click_cnt, 0) +
                         coalesce(vsdaa.jdp_away_click_cnt, 0)) end)                                                  as revenue_usd,
        sum(case
                when vsdaa.has_postback = 1 and vsdaa.is_bot = 0 and vsdaa.is_internal_duplicated = 0 and
                     vsdaa.is_external_stat_client = 1
                    then
                        vsdaa.client_cpc_usd *
                        (coalesce(vsdaa.away_click_cnt, 0) + coalesce(vsdaa.jdp_click_cnt, 0))
                when vsdaa.has_postback = 1 and vsdaa.is_bot = 0 and vsdaa.is_internal_duplicated = 0 and
                     vsdaa.is_external_stat_client = 0
                    then
                        vsdaa.client_cpc_usd *
                        (coalesce(vsdaa.away_click_cnt, 0) +
                         coalesce(vsdaa.jdp_away_click_cnt, 0)) end)                                                  as conversion_revenue_usd,
        sum(case
                when vsdaa.has_postback = 1 and vsdaa.is_bot = 0 then
                            coalesce(vsdaa.jdp_away_click_cnt, 0) + coalesce(vsdaa.away_click_cnt, 0) -
                            coalesce(vsdaa.non_local_away_attempt_cnt, 0) end)                                        as conversion_away_cnt,
        sum(case
                when vsdaa.has_postback = 1 and vsdaa.is_bot = 0 then coalesce(vsdaa.away_conversion_cnt, 0) +
                                                                      coalesce(vsdaa.jdp_away_conversion_cnt, 0) end) as conversion_cnt
    from
        affiliate.v_statistics_daily_agg vsdaa
    where
        vsdaa.date >= current_date - 7
    group by
        vsdaa.date;

    create temporary table stat_daily_agg_dev as
    select
        vsdad.date                                                                                                    as click_date,
        sum(case
                when vsdad.feed_cpa_usd is not null and vsdad.click_type = 'certified'
                    then (coalesce(vsdad.jdp_away_conversion_cnt, 0) + coalesce(vsdad.away_conversion_cnt, 0)) *
                         vsdad.feed_cpa_usd
                when vsdad.click_type = 'certified'
                    then vsdad.external_click_cnt * vsdad.feed_cpc_usd end)                                           as certified_cost_usd,
        sum(case
                when vsdad.click_type = 'certified'
                    then vsdad.external_click_cnt end)                                                                as certified_click_cnt,
        sum(case
                when vsdad.is_bot = 0 and vsdad.is_internal_duplicated = 0 and vsdad.is_external_stat_client = 1
                    then
                        vsdad.client_cpc_usd *
                        (coalesce(vsdad.away_click_cnt, 0) + coalesce(vsdad.jdp_click_cnt, 0))
                when vsdad.is_bot = 0 and vsdad.is_internal_duplicated = 0 and vsdad.is_external_stat_client = 0
                    then
                        vsdad.client_cpc_usd *
                        (coalesce(vsdad.away_click_cnt, 0) +
                         coalesce(vsdad.jdp_away_click_cnt, 0)) end)                                                  as revenue_usd,
        sum(case
                when vsdad.has_postback = 1 and vsdad.is_bot = 0 and vsdad.is_internal_duplicated = 0 and
                     vsdad.is_external_stat_client = 1
                    then
                        vsdad.client_cpc_usd *
                        (coalesce(vsdad.away_click_cnt, 0) + coalesce(vsdad.jdp_click_cnt, 0))
                when vsdad.has_postback = 1 and vsdad.is_bot = 0 and vsdad.is_internal_duplicated = 0 and
                     vsdad.is_external_stat_client = 0
                    then
                        vsdad.client_cpc_usd *
                        (coalesce(vsdad.away_click_cnt, 0) +
                         coalesce(vsdad.jdp_away_click_cnt, 0)) end)                                                  as conversion_revenue_usd,
        sum(case
                when vsdad.has_postback = 1 and vsdad.is_bot = 0 then
                            coalesce(vsdad.jdp_away_click_cnt, 0) + coalesce(vsdad.away_click_cnt, 0) -
                            coalesce(vsdad.non_local_away_attempt_cnt, 0) end)                                        as conversion_away_cnt,
        sum(case
                when vsdad.has_postback = 1 and vsdad.is_bot = 0 then coalesce(vsdad.away_conversion_cnt, 0) +
                                                                      coalesce(vsdad.jdp_away_conversion_cnt, 0) end) as conversion_cnt
    from
        affiliate_dev.v_statistics_daily_agg vsdad
    where
        vsdad.date >= current_date - 7
    group by
        vsdad.date;


    insert into
        temp_result(table_name, record_date, dimension_name, dimension_value, metric_name, metric_value,
                    reference_metric_value, reference_source, check_type, is_incomplete, max_acceptable_deviation)
    select
        'v_statistics_daily_agg'              as table_name,
        sdaa.click_date                       as record_date,
        null::varchar                         as dimension_name,
        null::varchar                         as dimension_value,
        'certified_click_cnt'                 as metric_name,
        coalesce(sdad.certified_click_cnt, 0) as metric_value,
        coalesce(sdaa.certified_click_cnt, 0) as reference_metric_value,
        'affiliate.v_statistics_daily_agg'    as reference_source,
        'metric value comparison'             as check_type,
        coalesce(sdad.certified_click_cnt, 0) !=
        coalesce(sdaa.certified_click_cnt, 0) as is_incomplete,
        '0'                                   as max_acceptable_deviation
    from
        stat_daily_agg_aff sdaa
        left join stat_daily_agg_dev sdad
                  on sdaa.click_date = sdad.click_date;

    insert into
        temp_result(table_name, record_date, dimension_name, dimension_value, metric_name, metric_value,
                    reference_metric_value, reference_source, check_type, is_incomplete, max_acceptable_deviation)
    select
        'v_statistics_daily_agg'                                   as table_name,
        sdaa.click_date                                            as record_date,
        null::varchar                                              as dimension_name,
        null::varchar                                              as dimension_value,
        'certified_cost_usd'                                       as metric_name,
        coalesce(sdad.certified_cost_usd, 0)                       as metric_value,
        coalesce(sdaa.certified_cost_usd, 0)                       as reference_metric_value,
        'affiliate.v_statistics_daily_agg'                         as reference_source,
        'metric value comparison'                                  as check_type,
        case
            when coalesce(sdaa.certified_cost_usd, 0) = 0 then false
            else abs(coalesce(sdad.certified_cost_usd, 0) - coalesce(sdaa.certified_cost_usd, 0)) /
                 coalesce(sdaa.certified_cost_usd, 0) > 0.0001 end as is_incomplete,
        '0.01%'                                                    as max_acceptable_deviation
    from
        stat_daily_agg_aff sdaa
        left join stat_daily_agg_dev sdad
                  on sdaa.click_date = sdad.click_date;

    insert into
        temp_result(table_name, record_date, dimension_name, dimension_value, metric_name, metric_value,
                    reference_metric_value, reference_source, check_type, is_incomplete, max_acceptable_deviation)
    select
        'v_statistics_daily_agg'                            as table_name,
        sdaa.click_date                                     as record_date,
        null::varchar                                       as dimension_name,
        null::varchar                                       as dimension_value,
        'revenue_usd'                                       as metric_name,
        coalesce(sdad.revenue_usd, 0)                       as metric_value,
        coalesce(sdaa.revenue_usd, 0)                       as reference_metric_value,
        'affiliate.v_statistics_daily_agg'                  as reference_source,
        'metric value comparison'                           as check_type,
        case
            when coalesce(sdaa.revenue_usd, 0) = 0 then false
            else abs(coalesce(sdad.revenue_usd, 0) - coalesce(sdaa.revenue_usd, 0)) /
                 coalesce(sdaa.revenue_usd, 0) > 0.0001 end as is_incomplete,
        '0.01%'                                             as max_acceptable_deviation
    from
        stat_daily_agg_aff sdaa
        left join stat_daily_agg_dev sdad
                  on sdaa.click_date = sdad.click_date;

    insert into
        temp_result(table_name, record_date, dimension_name, dimension_value, metric_name, metric_value,
                    reference_metric_value, reference_source, check_type, is_incomplete, max_acceptable_deviation)
    select
        'v_statistics_daily_agg'                                       as table_name,
        sdaa.click_date                                                as record_date,
        null::varchar                                                  as dimension_name,
        null::varchar                                                  as dimension_value,
        'conversion_revenue_usd'                                       as metric_name,
        coalesce(sdad.conversion_revenue_usd, 0)                       as metric_value,
        coalesce(sdaa.conversion_revenue_usd, 0)                       as reference_metric_value,
        'affiliate.v_statistics_daily_agg'                             as reference_source,
        'metric value comparison'                                      as check_type,
        case
            when coalesce(sdaa.conversion_revenue_usd, 0) = 0 then false
            else abs(coalesce(sdad.conversion_revenue_usd, 0) - coalesce(sdaa.conversion_revenue_usd, 0)) /
                 coalesce(sdaa.conversion_revenue_usd, 0) > 0.0001 end as is_incomplete,
        '0.01%'                                                        as max_acceptable_deviation
    from
        stat_daily_agg_aff sdaa
        left join stat_daily_agg_dev sdad
                  on sdaa.click_date = sdad.click_date;

    insert into
        temp_result(table_name, record_date, dimension_name, dimension_value, metric_name, metric_value,
                    reference_metric_value, reference_source, check_type, is_incomplete, max_acceptable_deviation)
    select
        'v_statistics_daily_agg'              as table_name,
        sdaa.click_date                       as record_date,
        null::varchar                         as dimension_name,
        null::varchar                         as dimension_value,
        'conversion_away_cnt'                 as metric_name,
        coalesce(sdad.conversion_away_cnt, 0) as metric_value,
        coalesce(sdaa.conversion_away_cnt, 0) as reference_metric_value,
        'affiliate.v_statistics_daily_agg'    as reference_source,
        'metric value comparison'             as check_type,
        coalesce(sdad.conversion_away_cnt, 0) !=
        coalesce(sdaa.conversion_away_cnt, 0) as is_incomplete,
        '0'                                   as max_acceptable_deviation
    from
        stat_daily_agg_aff sdaa
        left join stat_daily_agg_dev sdad
                  on sdaa.click_date = sdad.click_date;

    insert into
        temp_result(table_name, record_date, dimension_name, dimension_value, metric_name, metric_value,
                    reference_metric_value, reference_source, check_type, is_incomplete, max_acceptable_deviation)
    select
        'v_statistics_daily_agg'           as table_name,
        sdaa.click_date                    as record_date,
        null::varchar                      as dimension_name,
        null::varchar                      as dimension_value,
        'conversion_cnt'                   as metric_name,
        coalesce(sdad.conversion_cnt, 0)   as metric_value,
        coalesce(sdaa.conversion_cnt, 0)   as reference_metric_value,
        'affiliate.v_statistics_daily_agg' as reference_source,
        'metric value comparison'          as check_type,
        coalesce(sdad.conversion_cnt, 0) !=
        coalesce(sdaa.conversion_cnt, 0)   as is_incomplete,
        '0'                                as max_acceptable_deviation
    from
        stat_daily_agg_aff sdaa
        left join stat_daily_agg_dev sdad
                  on sdaa.click_date = sdad.click_date;

    --------------------------------------------------------------------------------------------------------------------
    -- Compare metrics in v_statistics_daily_agg.
    --------------------------------------------------------------------------------------------------------------------
    insert into
        temp_result(table_name, record_date, dimension_name, dimension_value, metric_name, metric_value,
                    reference_metric_value, reference_source, check_type, is_incomplete, max_acceptable_deviation)

    with
        metrics_external_apply_raw_dev as (
            select
                count(*) as count_rows_dev,
                date_diff
            from
                affiliate_dev.external_apply_raw as dear
            where
                date_diff = public.fn_get_date_diff(current_date) - 1
            group by date_diff
        ),
        metrics_external_apply_raw_aff as (
            select
                count(*) as count_rows_aff,
                date_diff
            from
                affiliate.external_apply_raw as aear
            where
                date_diff = public.fn_get_date_diff(current_date) - 1
            group by date_diff
        ),
        metrics_external_apply_raw_data as (
            select
                d.count_rows_dev,
                a.count_rows_aff,
                a.date_diff
            from
                metrics_external_apply_raw_aff as a
                left join metrics_external_apply_raw_dev as d
                          on d.date_diff = a.date_diff
        )

    select
        'external_apply_raw'                               as table_name,
        public.fn_get_date_from_date_diff(meard.date_diff) as record_date,
        null::varchar                                      as dimension_name,
        null::varchar                                      as dimension_value,
        'count_rows'                                       as metric_name,
        coalesce(meard.count_rows_dev, 0)                  as metric_value,
        coalesce(meard.count_rows_aff, 0)                  as reference_metric_value,
        'affiliate.external_apply_raw'                     as reference_source,
        'metric value comparison'                          as check_type,
        coalesce(meard.count_rows_dev, 0) !=
        coalesce(meard.count_rows_aff, 0)                  as is_incomplete,
        '0'                                                as max_acceptable_deviation
    from
        metrics_external_apply_raw_data as meard;

    --------------------------------------------------------------------------------------------------------------------
    -- Compare ab test metrics in abtest_significance_metrics
    --------------------------------------------------------------------------------------------------------------------
    create temporary table clean_asm as
    with
        max_data_raw_asm_aff as (
            select
                asma.test_id,
                asma.iteration,
                max(asma.date_end)             as max_date_end,
                min(asma.date_start)           as min_date_start,
                count(distinct asma.publisher) as cnt_pubs
            from
                affiliate.abtest_significance_metrics as asma
            group by asma.test_id, asma.iteration
        ),
        max_data_clean_asm_aff as (
            select
                mdraa.*
            from
                max_data_raw_asm_aff as mdraa
            where
                  mdraa.cnt_pubs > 3
              and mdraa.max_date_end >= current_date - '2 days'::interval
            order by mdraa.max_date_end desc, mdraa.min_date_start asc
            limit 1
        )
    select
        asm.test_id || ' - ' || asm.iteration || ' - ' || asm.test_group as dimension_value,
        asm.test_id,
        asm.iteration,
        asm.test_group,
        asm.metric_name,
        asm.metric_value                                                 as asm_metric_aff,
        asm.date_end,
        asm.date_start
    from
        affiliate.abtest_significance_metrics as asm
        join max_data_clean_asm_aff as mdk
             on asm.test_id = mdk.test_id and asm.iteration = mdk.iteration
                 and asm.date_start = mdk.min_date_start and asm.date_end = mdk.max_date_end
                 and asm.country_code = 'Total' and asm.publisher = 'Total';


    insert into
        temp_result(table_name, record_date, dimension_name, dimension_value, metric_name, metric_value,
                    reference_metric_value, reference_source, check_type, is_incomplete, max_acceptable_deviation)
    select
        'abtest_significance_metrics'                          as table_name,
        casma.date_end                                         as record_date,
        'test - iteration - group'                             as dimension_name,
        casma.dimension_value                                  as dimension_value,
        casma.metric_name                                      as metric_name,
        coalesce(casmd.metric_value, 0)                        as metric_value,
        coalesce(casma.asm_metric_aff, 0)                      as reference_metric_value,
        'affiliate.abtest_significance_metrics'                as reference_source,
        'metric value comparison'                              as check_type,
        case
            when coalesce(casma.asm_metric_aff, 0) = 0
                then coalesce(casma.asm_metric_aff, 0) != coalesce(casmd.metric_value, 0)
            else abs(coalesce(casmd.metric_value, 0) - coalesce(casma.asm_metric_aff, 0)) /
                 coalesce(casma.asm_metric_aff, 0) > 0.001 end as is_incomplete,
        '0.1%'                                                 as max_acceptable_deviation
    from
        clean_asm casma
        left join affiliate_dev.abtest_significance_metrics casmd
                  on casma.test_id = casmd.test_id
                      and casma.iteration = casmd.iteration
                      and casma.metric_name = casmd.metric_name
                      and casma.test_group = casmd.test_group
                      and casma.date_end = casmd.date_end
                      and casma.date_start = casmd.date_start
                      and casmd.country_code = 'Total' and casmd.publisher = 'Total';

    --------------------------------------------------------------------------------------------------------------------
    -- Compare ab test metrics in abtest_stat_agg
    --------------------------------------------------------------------------------------------------------------------

    create temporary table clean_asa as
    with
        unique_metrics_aff as (
            select distinct
                ca.metric_name as metric_name
            from
                clean_asm as ca
        ),
        max_data_raw_asa_aff as (
            select
                asaa.test_id,
                asaa.iteration,
                max(asaa.record_date)::date    as max_date,
                min(asaa.record_date)::date    as min_date,
                count(distinct asaa.publisher) as cnt_pubs
            from
                affiliate.abtest_stat_agg as asaa
            group by asaa.test_id, asaa.iteration
        ),
        max_data_clean_asa_aff as (
            select
                mdraf.*
            from
                max_data_raw_asa_aff as mdraf
            where
                  mdraf.cnt_pubs > 3
              and mdraf.max_date >= current_date - '2 days'::interval
            order by mdraf.max_date desc, mdraf.min_date desc
            limit 1
        ),
        clean_asa_aff as (
            select
                asa.test_id || ' - ' || asa.iteration || ' - ' || asa.test_group as dimension_value,
                asa.test_id,
                asa.iteration,
                asa.test_group,
                asa.metric_name                                                  as metric_name,
                case
                    when asa.metric_type in ('count', 'sum')
                        then coalesce(sum(asa.metric_numerator), 0)
                    else case
                             when coalesce(sum(asa.metric_denominator), 0) = 0 then 0
                             else round(
                                         coalesce(sum(asa.metric_numerator), 0) /
                                         coalesce(sum(asa.metric_denominator), 0),
                                         4)
                        end end                                                  as asa_metric_aff,
                mdka.max_date,
                mdka.min_date
            from
                affiliate.abtest_stat_agg as asa
                join unique_metrics_aff as um
                     on um.metric_name = asa.metric_name
                join max_data_clean_asa_aff as mdka
                     on asa.test_id = mdka.test_id and asa.iteration = mdka.iteration and
                        asa.record_date::date between mdka.min_date and mdka.max_date
            group by
                asa.test_id, asa.iteration, asa.test_group, asa.metric_name, um.metric_name,
                mdka.max_date, mdka.min_date, metric_type
        )

    select
        casaa.dimension_value,
        casaa.test_id,
        casaa.iteration,
        casaa.test_group,
        casaa.metric_name,
        casaa.asa_metric_aff,
        casaa.max_date,
        case
            when casad.metric_type in ('count', 'sum')
                then coalesce(sum(casad.metric_numerator), 0)
            else case
                     when coalesce(sum(casad.metric_denominator), 0) = 0 then 0
                     else round(
                                 coalesce(sum(casad.metric_numerator), 0) /
                                 coalesce(sum(casad.metric_denominator), 0),
                                 4)
                end end as asa_metric_dev
    from
        clean_asa_aff as casaa
        left join affiliate_dev.abtest_stat_agg casad
                  on casaa.test_id = casad.test_id
                      and casaa.iteration = casad.iteration
                      and casaa.metric_name = casad.metric_name
                      and casaa.test_group = casad.test_group
                      and casad.record_date between casaa.min_date and casaa.max_date
    group by
        casaa.dimension_value,
        casaa.test_id,
        casaa.iteration,
        casaa.test_group,
        casaa.metric_name,
        casaa.asa_metric_aff,
        casaa.max_date,
        casaa.min_date,
        casad.metric_type;

    insert into
        temp_result(table_name, record_date, dimension_name, dimension_value, metric_name, metric_value,
                    reference_metric_value, reference_source, check_type, is_incomplete, max_acceptable_deviation)
    select
        'abtest_stat_agg'                                    as table_name,
        caa.max_date                                         as record_date,
        'test - iteration - group'                           as dimension_name,
        caa.dimension_value                                  as dimension_value,
        caa.metric_name                                      as metric_name,
        coalesce(caa.asa_metric_dev, 0)                      as metric_value,
        coalesce(caa.asa_metric_aff, 0)                      as reference_metric_value,
        'affiliate.abtest_stat_agg'                          as reference_source,
        'metric value comparison'                            as check_type,
        case
            when coalesce(caa.asa_metric_aff, 0) = 0
                then coalesce(caa.asa_metric_aff, 0) != coalesce(caa.asa_metric_dev, 0)
            else abs(coalesce(caa.asa_metric_dev, 0) - coalesce(caa.asa_metric_aff, 0)) /
                 coalesce(caa.asa_metric_aff, 0) > 0.001 end as is_incomplete,
        '0.1%'                                               as max_acceptable_deviation
    from
        clean_asa caa;

    return query
        select
            t.table_name,
            t.record_date,
            t.dimension_name,
            t.dimension_value,
            t.metric_name,
            t.metric_value,
            t.reference_metric_value,
            t.reference_source,
            t.check_type,
            t.is_incomplete,
            t.max_acceptable_deviation
        from
            temp_result t;

    drop table temp_result;
    drop table temp_vmma_aff;
    drop table temp_vmma_dev;
    drop table temp_v_click_data_aff;
    drop table temp_v_click_data_dev;
    drop table stat_daily_agg_aff;
    drop table stat_daily_agg_dev;
    drop table clean_asm;
    drop table clean_asa;


end
$$;

alter function affiliate.get_dev_consistency_report() owner to ypr;

