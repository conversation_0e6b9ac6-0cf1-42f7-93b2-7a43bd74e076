create or replace function affiliate.update_conversion_project_list(
       p_record_type character varying, p_id_project bigint, p_country character varying, p_date_to date)
    returns table
            (
                status      text,
                description text
            )
    language plpgsql
as
$$
declare
    _record_type varchar  := p_record_type;
    _id_project  bigint   := p_id_project;
    _country     varchar  := p_country;
    _date_to     date     := p_date_to;

begin

    if _date_to > (date_trunc('month', now() + '1 month'::interval) - '1 day'::interval)::date then
        return query select
                         'Error'                                                               as status,
                         'The parameter date_to can''t be later than the end of the current month' as description;
    elsif (_record_type = 'include' and (select count(1) from dimension.countries where alpha_2 = _country) = 0)
        or (_record_type = 'exclude' and _country <> 'ALL')
    then
        return query select
                         'Error'                                                                                  as status,
                         'The parameter ''country'' is invalid. For ''include'', accepted values are ''DE'', ''FR'', etc. ' ||
                         'For ''exclude'', only ''ALL'' is allowed' as description;
    elsif _record_type <> 'include'::varchar and _record_type <> 'exclude'::varchar then
        return query select
                         'Error'                                                                                     as status,
                         'The parameter ''record_type'' is invalid. Accepted values are: ''include'' or ''exclude''' as description;
    elsif (select
               cpl.date_from
           from
               affiliate.conversion_project_list as cpl
           where
                 cpl.country = _country
             and cpl.id_project = _id_project
             and cpl.record_type = _record_type
             and cpl.date_to is null) > _date_to then
        return query select
                         'Error'                                                  as status,
                         'The parameter date_to can''t be earlier than the date_from' as description;

    elsif (select
               count(1)
           from
               affiliate.conversion_project_list as cpl
           where
                 cpl.country = _country
             and cpl.id_project = _id_project
             and cpl.record_type = _record_type
             and cpl.date_to is null) > 0 then

        update affiliate.conversion_project_list
        set
            date_to = _date_to, updated_at = now()
        where
              country = _country
          and id_project = _id_project
          and record_type = _record_type
          and date_to is null;
        return query select
                         'Success'                     as status,
                         'Record updated successfully' as description;

    else
        return query select
                         'Error'                                                       as status,
                         'Record doesn''t exist for this id_project, country, record_type' as description;
    end if;

end;
$$;

alter function affiliate.update_conversion_project_list(character varying, bigint, varchar, date) owner to ypr;

grant execute on function affiliate.update_conversion_project_list(varchar, bigint, varchar, date)  to cxt_devs;
