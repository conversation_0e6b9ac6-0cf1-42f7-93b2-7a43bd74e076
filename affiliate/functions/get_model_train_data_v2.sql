create or replace function affiliate.get_model_train_data_v2(date_min date, date_max date)
    returns TABLE(record_date date, day_of_week smallint, publisher character varying, country_code character varying, feed_cpc_usd numeric, month_completed numeric, avg_uid_cnt_3d numeric, avg_uid_cnt_7d numeric, avg_uid_cnt_14d numeric, avg_uid_cnt_prevd numeric, avg_uid_cnt_prev2d numeric, avg_uid_cnt_prev3d numeric, avg_uid_cnt_prevd_pub numeric, avg_uid_cnt_prev2d_pub numeric, avg_uid_cnt_prev3d_pub numeric, min_uid_cnt_3d_pub integer, min_uid_cnt_7d_pub integer, min_uid_cnt_14d_pub integer, uid_percent_pub_3d numeric, uid_percent_pub_7d numeric, uid_percent_pub_14d numeric, avg_job_cpc_percentile_3d_pub numeric, avg_job_cpc_percentile_7d_pub numeric, avg_job_cpc_percentile_14d_pub numeric, avg_uid_cnt_diff_12 numeric, avg_uid_cnt_diff_23 numeric, avg_uid_cnt_diff_12_pub numeric, avg_uid_cnt_diff_23_pub numeric, click_cnt integer, click_cnt_3d integer, click_cnt_7d integer, click_cnt_14d integer, click_cnt_3d_pub integer, click_cnt_7d_pub integer, click_cnt_14d_pub integer, click_percent_pub_3d numeric, click_percent_pub_7d numeric, click_percent_pub_14d numeric, click_cpc_percentile_3d_pub numeric, click_cpc_percentile_7d_pub numeric, click_cpc_percentile_14d_pub numeric, job_click_cnt_3d integer, job_click_cnt_7d integer, job_click_cnt_14d integer, job_click_cnt_3d_pub integer, job_click_cnt_7d_pub integer, job_click_cnt_14d_pub integer, click_cnt_prevd integer, click_cnt_prevd_pub integer, click_cnt_prev2d_pub integer, click_cnt_prev3d_pub integer, click_cnt_diff_12 numeric, click_cnt_diff_23 numeric, click_cnt_diff_12_pub numeric, click_cnt_diff_23_pub numeric, avg_uid_cnt numeric)
    language plpgsql
as
$$
begin

    drop table if exists temp_calendar;
    drop table if exists temp_job_sample_data;
    drop table if exists temp_job_sample_data1;
    drop table if exists temp_job_current;
    drop table if exists temp_pub_raw;
    drop table if exists temp_job_pub;
    drop table if exists temp_pub_cpc_raw;
    drop table if exists temp_job_pub_cpc;
    drop table if exists temp_job_result;
    drop table if exists temp_pred_job_data;
    drop table if exists temp_click_sample_data;
    drop table if exists temp_click_current;
    drop table if exists temp_click_pub_cpc;
    drop table if exists temp_click_pub_cpc2;
    drop table if exists temp_click_pub;
    drop table if exists temp_click_result;
    drop table if exists temp_prediction_dataset;

    create temporary table temp_calendar as
    select
        d.dt::date                            as date,
        (d.dt - '1 day'::interval)::date      as date_end,
        (d.dt - 3 * '1 day'::interval)::date  as date_start_3d,
        (d.dt - 7 * '1 day'::interval)::date  as date_start_7d,
        (d.dt - 14 * '1 day'::interval)::date as date_start_14d
    from
        dimension.info_calendar d
    where
        d.dt between date_min and date_max;

    create temporary table temp_job_sample_data as
    select
        upper(ps.country)        as country,
        ps.partner               as publisher,
        j.start_gather_dt,
        round(j.feed_cpc_usd, 2) as feed_cpc_usd,
        sum(j.uid_count)         as uid_count
    from
        affiliate.job_stat_agg j
        join affiliate.partner_settings ps
             on ps.source_id = j.id_source and ps.id_local_partner = j.id_partner
        join dimension.countries c
             on c.alpha_2 = upper(ps.country)
        join dimension.info_calendar ic
             on ic.date_diff = j.start_gather_date_diff
        left join imp.auction_campaign ac
                  on ac.country_id = c.id and
                     ac.id = j.id_campaign
    where
          ps.country in ('us', 'de', 'fr', 'nl', 'uk', 'ca')
      and ic.dt between date_min - 14 and date_max
      and ps.feed_type = 'paid'
      and (lower(ps.partner) not like 'bebee_organic_%' or ic.dt >= '2024-01-01')
    group by
        upper(ps.country),
        ps.partner,
        j.start_gather_dt,
        round(j.feed_cpc_usd, 2);

    create temporary table temp_job_sample_data1 as
    select
        t.country,
        t.publisher,
        t.start_gather_dt,
        t.start_gather_dt::date                                                        as record_date,
        t.feed_cpc_usd,
        t.uid_count,
        sum(t.uid_count)
        over (partition by t.country, t.publisher, t.start_gather_dt, t.feed_cpc_usd)  as uid_cnt_pub_cpc,
        sum(t.uid_count) over (partition by t.country, t.publisher, t.start_gather_dt) as uid_cnt_pub,
        sum(coalesce(t.uid_count, 0))
        over (partition by t.start_gather_dt, t.publisher, t.country order by t.feed_cpc_usd desc
            range between unbounded preceding and current row)                         as exp_uid_cnt_pub
    from
        temp_job_sample_data t;

    create temporary table temp_job_current as
    select
        t.country,
        t.publisher,
        coalesce(t.feed_cpc_usd, -1) as feed_cpc_usd,
        t.record_date,
        avg(t.uid_count)             as avg_uid_cnt
    from
        temp_job_sample_data1 t
    group by
        t.country,
        t.publisher,
        coalesce(t.feed_cpc_usd, -1),
        t.record_date;

    alter table temp_job_current
        add primary key (record_date, country, publisher, feed_cpc_usd);

    create temporary table temp_pub_raw as
    select distinct
        t.country,
        t.record_date,
        t.start_gather_dt,
        t.publisher,
        t.uid_cnt_pub
    from
        temp_job_sample_data1 t;

    create temporary table temp_job_pub as
    select
        c.date,
        t.country,
        t.publisher,
        avg(case when t.record_date = c.date_end then t.uid_cnt_pub end)                           as avg_uid_cnt_prevd,
        avg(case
                when t.record_date = (c.date_end - '1 day'::interval)
                    then t.uid_cnt_pub end)                                                        as avg_uid_cnt_prev2d,
        avg(case
                when t.record_date = (c.date_end - 2 * '1 day'::interval)
                    then t.uid_cnt_pub end)                                                        as avg_uid_cnt_prev3d,
        avg(case when t.record_date between c.date_start_3d and c.date_end then t.uid_cnt_pub end) as avg_uid_cnt_3d,
        min(case
                when t.record_date between c.date_start_3d and c.date_end then t.uid_cnt_pub end)  as min_uid_cnt_3d,
        max(case
                when t.record_date between c.date_start_3d and c.date_end then t.uid_cnt_pub end)  as max_uid_cnt_3d,
        avg(case when t.record_date between c.date_start_7d and c.date_end then t.uid_cnt_pub end) as avg_uid_cnt_7d,
        min(case
                when t.record_date between c.date_start_7d and c.date_end then t.uid_cnt_pub end)  as min_uid_cnt_7d,
        max(case
                when t.record_date between c.date_start_7d and c.date_end then t.uid_cnt_pub end)  as max_uid_cnt_7d,
        avg(t.uid_cnt_pub)                                                                         as avg_uid_cnt_14d,
        min(t.uid_cnt_pub)                                                                         as min_uid_cnt_14d,
        max(t.uid_cnt_pub)                                                                         as max_uid_cnt_14d
    from
        temp_calendar c
        join temp_pub_raw t
             on t.record_date between c.date_start_14d and c.date_end
    group by
        c.date,
        t.country,
        t.publisher;

    alter table temp_job_pub
        add primary key (date, country, publisher);

    create temporary table temp_pub_cpc_raw as
    with
        t as (
            select distinct
                temp_job_sample_data1.country,
                temp_job_sample_data1.record_date,
                temp_job_sample_data1.start_gather_dt,
                temp_job_sample_data1.publisher,
                temp_job_sample_data1.feed_cpc_usd,
                temp_job_sample_data1.uid_cnt_pub_cpc,
                temp_job_sample_data1.uid_cnt_pub
            from
                temp_job_sample_data1
        )
    select
        t.country,
        t.record_date,
        t.start_gather_dt,
        t.publisher,
        t.feed_cpc_usd,
        t.uid_cnt_pub_cpc,
        sum(coalesce(t.uid_cnt_pub_cpc, 0))
        over (partition by t.start_gather_dt, t.publisher, t.country order by t.feed_cpc_usd desc
            range between unbounded preceding and current row) as exp_uid_cnt_pub,
        t.uid_cnt_pub
    from
        t;

    create temporary table temp_job_pub_cpc as
    select
        c.date,
        t.country,
        t.publisher,
        coalesce(t.feed_cpc_usd, -1)                                                 as feed_cpc_usd,
        avg(case when t.record_date = c.date_end then t.uid_cnt_pub_cpc end)         as avg_uid_cnt_prevd,
        avg(case
                when t.record_date = (c.date_end - '1 day'::interval)
                    then t.uid_cnt_pub_cpc end)                                      as avg_uid_cnt_prev2d,
        avg(case
                when t.record_date = (c.date_end - 2 * '1 day'::interval)
                    then t.uid_cnt_pub_cpc end)                                      as avg_uid_cnt_prev3d,
        avg(case
                when t.record_date between c.date_start_3d and c.date_end
                    then t.uid_cnt_pub_cpc end)                                      as avg_uid_cnt_3d,
        min(case
                when t.record_date between c.date_start_3d and c.date_end
                    then t.uid_cnt_pub_cpc end)                                      as min_uid_cnt_3d,
        max(case
                when t.record_date between c.date_start_3d and c.date_end
                    then t.uid_cnt_pub_cpc end)                                      as max_uid_cnt_3d,
        avg(case
                when t.record_date between c.date_start_7d and c.date_end
                    then t.uid_cnt_pub_cpc end)                                      as avg_uid_cnt_7d,
        min(case
                when t.record_date between c.date_start_7d and c.date_end
                    then t.uid_cnt_pub_cpc end)                                      as min_uid_cnt_7d,
        max(case
                when t.record_date between c.date_start_7d and c.date_end
                    then t.uid_cnt_pub_cpc end)                                      as max_uid_cnt_7d,
        avg(t.uid_cnt_pub_cpc)                                                       as avg_uid_cnt_14d,
        min(t.uid_cnt_pub_cpc)                                                       as min_uid_cnt_14d,
        max(t.uid_cnt_pub_cpc)                                                       as max_uid_cnt_14d,
        1 - avg(case
                    when t.record_date between c.date_start_3d and c.date_end and t.uid_cnt_pub != 0
                        then t.exp_uid_cnt_pub / t.uid_cnt_pub end)                  as avg_job_cpc_percentile_3d_pub,
        1 - avg(case
                    when t.record_date between c.date_start_7d and c.date_end and t.uid_cnt_pub != 0
                        then t.exp_uid_cnt_pub / t.uid_cnt_pub end)                  as avg_job_cpc_percentile_7d_pub,
        1 -
        avg(case when t.uid_cnt_pub != 0 then t.exp_uid_cnt_pub / t.uid_cnt_pub end) as avg_job_cpc_percentile_14d_pub
    from
        temp_calendar c
        join temp_pub_cpc_raw t
             on t.record_date between c.date_start_14d and c.date_end
    group by
        c.date,
        t.country,
        t.publisher,
        coalesce(t.feed_cpc_usd, -1);

    alter table temp_job_pub_cpc
        add primary key (date, country, publisher, feed_cpc_usd);

    create temporary table temp_job_result as
    select
        coalesce(tc.record_date, tppc.date)              as record_date,
        coalesce(tc.country, tppc.country)               as country,
        coalesce(tc.publisher, tppc.publisher)           as publisher,
        coalesce(tc.feed_cpc_usd, tppc.feed_cpc_usd, -1) as feed_cpc_usd,
        coalesce(tc.avg_uid_cnt, 0)                      as avg_uid_cnt,
        coalesce(tppc.avg_uid_cnt_prevd, 0)              as avg_uid_cnt_prevd,
        coalesce(tppc.avg_uid_cnt_prev2d, 0)             as avg_uid_cnt_prev2d,
        coalesce(tppc.avg_uid_cnt_prev3d, 0)             as avg_uid_cnt_prev3d,
        coalesce(tppc.avg_uid_cnt_3d, 0)                 as avg_uid_cnt_3d,
        coalesce(tppc.min_uid_cnt_3d, 0)                 as min_uid_cnt_3d,
        coalesce(tppc.max_uid_cnt_3d, 0)                 as max_uid_cnt_3d,
        coalesce(tppc.avg_uid_cnt_7d, 0)                 as avg_uid_cnt_7d,
        coalesce(tppc.min_uid_cnt_7d, 0)                 as min_uid_cnt_7d,
        coalesce(tppc.max_uid_cnt_7d, 0)                 as max_uid_cnt_7d,
        coalesce(tppc.avg_uid_cnt_14d, 0)                as avg_uid_cnt_14d,
        coalesce(tppc.min_uid_cnt_14d, 0)                as min_uid_cnt_14d,
        coalesce(tppc.max_uid_cnt_14d, 0)                as max_uid_cnt_14d,
        coalesce(tppc.avg_job_cpc_percentile_3d_pub, 0)  as avg_job_cpc_percentile_3d_pub,
        coalesce(tppc.avg_job_cpc_percentile_7d_pub, 0)  as avg_job_cpc_percentile_7d_pub,
        coalesce(tppc.avg_job_cpc_percentile_14d_pub, 0) as avg_job_cpc_percentile_14d_pub
    from
        temp_job_current tc
        full join temp_job_pub_cpc tppc
                  on tc.record_date = tppc.date and
                     tc.country = tppc.country and
                     tc.publisher = tppc.publisher and
                     tc.feed_cpc_usd = tppc.feed_cpc_usd;

    create temporary table temp_pred_job_data as
    select
        t.record_date,
        lower(t.country)                   as country_code,
        t.publisher,
        t.feed_cpc_usd,
        t.avg_uid_cnt,
        t.avg_uid_cnt_prevd,
        t.avg_uid_cnt_prev2d,
        t.avg_uid_cnt_prev3d,
        t.avg_uid_cnt_3d,
        t.min_uid_cnt_3d,
        t.max_uid_cnt_3d,
        t.avg_uid_cnt_7d,
        t.min_uid_cnt_7d,
        t.max_uid_cnt_7d,
        t.avg_uid_cnt_14d,
        t.min_uid_cnt_14d,
        t.max_uid_cnt_14d,
        t.avg_job_cpc_percentile_3d_pub,
        t.avg_job_cpc_percentile_7d_pub,
        t.avg_job_cpc_percentile_14d_pub,
        coalesce(tp.avg_uid_cnt_3d, 0)     as avg_uid_cnt_3d_pub,
        coalesce(tp.min_uid_cnt_3d, 0)     as min_uid_cnt_3d_pub,
        coalesce(tp.max_uid_cnt_3d, 0)     as max_uid_cnt_3d_pub,
        coalesce(tp.avg_uid_cnt_7d, 0)     as avg_uid_cnt_7d_pub,
        coalesce(tp.min_uid_cnt_7d, 0)     as min_uid_cnt_7d_pub,
        coalesce(tp.max_uid_cnt_7d, 0)     as max_uid_cnt_7d_pub,
        coalesce(tp.avg_uid_cnt_14d, 0)    as avg_uid_cnt_14d_pub,
        coalesce(tp.min_uid_cnt_14d, 0)    as min_uid_cnt_14d_pub,
        coalesce(tp.max_uid_cnt_14d, 0)    as max_uid_cnt_14d_pub,
        coalesce(tp.avg_uid_cnt_prevd, 0)  as avg_uid_cnt_prevd_pub,
        coalesce(tp.avg_uid_cnt_prev2d, 0) as avg_uid_cnt_prev2d_pub,
        coalesce(tp.avg_uid_cnt_prev3d, 0) as avg_uid_cnt_prev3d_pub
    from
        temp_job_result t
        left join temp_job_pub tp
                  on tp.country = t.country and
                     tp.date = t.record_date and
                     tp.publisher = t.publisher;

    alter table temp_pred_job_data
        add primary key (record_date, country_code, publisher, feed_cpc_usd);

    create temporary table temp_click_sample_data as
    select
        t.publisher,
        lower(t.country)           as country_code,
        t.click_date,
        t.uid_job,
        round(t.feed_cpc_usd, 2)   as feed_cpc_usd,
        count(distinct t.id_click) as click_cnt
    from
        affiliate.v_click_data t
        join affiliate.partner_settings p
             on p.partner = t.publisher and p.country = lower(t.country) and p.is_cpa_publisher = 0
    where
          t.click_type = 'certified'
      and t.feed_type = 'paid'
      and t.country in ('US', 'DE', 'FR', 'NL', 'UK', 'CA')
      and t.click_date between date_min - 14 and date_max
      and (lower(t.publisher) not like 'bebee_organic_%' or t.click_date >= '2024-01-01')
    group by
        t.publisher, lower(t.country), t.click_date, t.uid_job, round(t.feed_cpc_usd, 2);

    create temporary table temp_click_pub as
    select
        tc.date,
        csd.publisher,
        csd.country_code,
        sum(case when csd.click_date = tc.date_end then csd.click_cnt end)                            as click_cnt_prevd,
        sum(case
                when csd.click_date = (tc.date_end - '1 day'::interval)
                    then csd.click_cnt end)                                                           as click_cnt_prev2d,
        sum(case
                when csd.click_date = (tc.date_end - 2 * '1 day'::interval)
                    then csd.click_cnt end)                                                           as click_cnt_prev3d,
        sum(case when csd.click_date between tc.date_start_3d and tc.date_end then csd.click_cnt end) as click_cnt_3d,
        count(distinct case
                           when csd.click_date between tc.date_start_3d and tc.date_end
                               then csd.uid_job end)                                                  as job_click_cnt_3d,
        sum(case
                when csd.click_date between tc.date_start_7d and tc.date_end
                    then csd.click_cnt end)                                                           as click_cnt_7d,
        count(distinct case
                           when csd.click_date between tc.date_start_7d and tc.date_end
                               then csd.uid_job end)                                                  as job_click_cnt_7d,
        sum(csd.click_cnt)                                                                            as click_cnt_14d,
        count(distinct csd.uid_job)                                                                   as job_click_cnt_14d
    from
        temp_calendar tc
        join temp_click_sample_data csd
             on csd.click_date between tc.date_start_14d and tc.date_end
    group by
        tc.date,
        csd.publisher,
        csd.country_code;

    alter table temp_click_pub
        add primary key (date, publisher, country_code);

    create temporary table temp_click_pub_cpc as
    select
        tc.date,
        csd.publisher,
        csd.country_code,
        coalesce(csd.feed_cpc_usd, -1)                                                                as feed_cpc_usd,
        sum(case when csd.click_date = tc.date_end then csd.click_cnt end)                            as click_cnt_prevd,
        sum(case
                when csd.click_date = (tc.date_end - '1 day'::interval)
                    then csd.click_cnt end)                                                           as click_cnt_prev2d,
        sum(case
                when csd.click_date = (tc.date_end - 2 * '1 day'::interval)
                    then csd.click_cnt end)                                                           as click_cnt_prev3d,
        sum(case when csd.click_date between tc.date_start_3d and tc.date_end then csd.click_cnt end) as click_cnt_3d,
        count(distinct case
                           when csd.click_date between tc.date_start_3d and tc.date_end
                               then csd.uid_job end)                                                  as job_click_cnt_3d,
        sum(case
                when csd.click_date between tc.date_start_7d and tc.date_end
                    then csd.click_cnt end)                                                           as click_cnt_7d,
        count(distinct case
                           when csd.click_date between tc.date_start_7d and tc.date_end
                               then csd.uid_job end)                                                  as job_click_cnt_7d,
        sum(csd.click_cnt)                                                                            as click_cnt_14d,
        count(distinct csd.uid_job)                                                                   as job_click_cnt_14d
    from
        temp_calendar tc
        join temp_click_sample_data csd
             on csd.click_date between tc.date_start_14d and tc.date_end
    group by
        tc.date,
        csd.publisher,
        csd.country_code,
        coalesce(csd.feed_cpc_usd, -1);

    alter table temp_click_pub_cpc
        add primary key (date, publisher, country_code, feed_cpc_usd);

    create temporary table temp_click_pub_cpc2 as
    select
        t.date,
        t.publisher,
        t.country_code,
        t.feed_cpc_usd,
        t.click_cnt_3d,
        t.job_click_cnt_3d,
        t.click_cnt_7d,
        t.job_click_cnt_7d,
        t.click_cnt_14d,
        t.job_click_cnt_14d,
        sum(coalesce(t.click_cnt_3d, 0))
        over (partition by t.date, t.publisher, t.country_code order by t.feed_cpc_usd desc
            range between unbounded preceding and current row) as exp_click_cnt_3d_pub,
        sum(coalesce(t.click_cnt_7d, 0))
        over (partition by t.date, t.publisher, t.country_code order by t.feed_cpc_usd desc
            range between unbounded preceding and current row) as exp_click_cnt_7d_pub,
        sum(coalesce(t.click_cnt_14d, 0))
        over (partition by t.date, t.publisher, t.country_code order by t.feed_cpc_usd desc
            range between unbounded preceding and current row) as exp_click_cnt_14d_pub,
        t.click_cnt_prevd,
        t.click_cnt_prev2d,
        t.click_cnt_prev3d
    from
        temp_click_pub_cpc t
    order by
        t.date desc, t.publisher, t.feed_cpc_usd;

    alter table temp_click_pub_cpc2
        add primary key (date, publisher, country_code, feed_cpc_usd);

    create temporary table temp_click_current as
    select
        csd.click_date                 as date,
        csd.publisher,
        csd.country_code,
        coalesce(csd.feed_cpc_usd, -1) as feed_cpc_usd,
        sum(csd.click_cnt)             as click_cnt
    from
        temp_click_sample_data csd
        join temp_calendar tc
             on tc.date = csd.click_date
    group by
        csd.click_date,
        csd.publisher,
        csd.country_code,
        coalesce(csd.feed_cpc_usd, -1);

    alter table temp_click_current
        add primary key (date, publisher, country_code, feed_cpc_usd);

    create temporary table temp_click_result as
    select
        coalesce(tppc.date, tc.date)                     as date,
        coalesce(tppc.publisher, tc.publisher)           as publisher,
        coalesce(tppc.country_code, tc.country_code)     as country_code,
        coalesce(tppc.feed_cpc_usd, tc.feed_cpc_usd, -1) as feed_cpc_usd,
        coalesce(tc.click_cnt, 0)                        as click_cnt,
        coalesce(tppc.click_cnt_3d, 0)                   as click_cnt_3d,
        coalesce(tppc.job_click_cnt_3d, 0)               as job_click_cnt_3d,
        coalesce(tppc.click_cnt_7d, 0)                   as click_cnt_7d,
        coalesce(tppc.job_click_cnt_7d, 0)               as job_click_cnt_7d,
        coalesce(tppc.click_cnt_14d, 0)                  as click_cnt_14d,
        coalesce(tppc.job_click_cnt_14d, 0)              as job_click_cnt_14d,
        coalesce(tppc.exp_click_cnt_3d_pub, 0)           as exp_click_cnt_3d_pub,
        coalesce(tppc.exp_click_cnt_7d_pub, 0)           as exp_click_cnt_7d_pub,
        coalesce(tppc.exp_click_cnt_14d_pub, 0)          as exp_click_cnt_14d_pub,
        coalesce(tppc.click_cnt_prevd, 0)                as click_cnt_prevd,
        coalesce(tppc.click_cnt_prev2d, 0)               as click_cnt_prev2d,
        coalesce(tppc.click_cnt_prev3d, 0)               as click_cnt_prev3d
    from
        temp_click_pub_cpc2 tppc
        full join temp_click_current tc
                  on tppc.date = tc.date and
                     tppc.publisher = tc.publisher and
                     tppc.country_code = tc.country_code and
                     tppc.feed_cpc_usd = tc.feed_cpc_usd;

    alter table temp_click_result
        add primary key (date, publisher, country_code, feed_cpc_usd);

    create temporary table temp_prediction_dataset as
    select
        pjd.record_date,
        pjd.country_code,
        pjd.publisher,
        pjd.feed_cpc_usd,
        pjd.avg_uid_cnt,
        pjd.avg_uid_cnt_3d,
        pjd.avg_uid_cnt_7d,
        pjd.avg_uid_cnt_14d,
        pjd.avg_uid_cnt_prevd,
        pjd.avg_uid_cnt_prev2d,
        pjd.avg_uid_cnt_prev3d,
        pjd.avg_uid_cnt_3d_pub,
        pjd.min_uid_cnt_3d_pub,
        pjd.avg_uid_cnt_7d_pub,
        pjd.min_uid_cnt_7d_pub,
        pjd.avg_uid_cnt_14d_pub,
        pjd.min_uid_cnt_14d_pub,
        pjd.avg_job_cpc_percentile_3d_pub,
        pjd.avg_job_cpc_percentile_7d_pub,
        pjd.avg_job_cpc_percentile_14d_pub,
        pjd.avg_uid_cnt_prevd_pub,
        pjd.avg_uid_cnt_prev2d_pub,
        pjd.avg_uid_cnt_prev3d_pub,
        coalesce(tr.click_cnt, 0)             as click_cnt,
        coalesce(tr.click_cnt_3d, 0)          as click_cnt_3d,
        coalesce(tr.job_click_cnt_3d, 0)      as job_click_cnt_3d,
        coalesce(tr.click_cnt_7d, 0)          as click_cnt_7d,
        coalesce(tr.job_click_cnt_7d, 0)      as job_click_cnt_7d,
        coalesce(tr.click_cnt_14d, 0)         as click_cnt_14d,
        coalesce(tr.job_click_cnt_14d, 0)     as job_click_cnt_14d,
        coalesce(tr.click_cnt_prevd, 0)       as click_cnt_prevd,
        coalesce(tr.click_cnt_prev2d, 0)      as click_cnt_prev2d,
        coalesce(tr.click_cnt_prev3d, 0)      as click_cnt_prev3d,
        coalesce(tr.exp_click_cnt_3d_pub, 0)  as exp_click_cnt_3d_pub,
        coalesce(tr.exp_click_cnt_7d_pub, 0)  as exp_click_cnt_7d_pub,
        coalesce(tr.exp_click_cnt_14d_pub, 0) as exp_click_cnt_14d_pub,
        coalesce(tp.click_cnt_3d, 0)          as click_cnt_3d_pub,
        coalesce(tp.click_cnt_7d, 0)          as click_cnt_7d_pub,
        coalesce(tp.click_cnt_14d, 0)         as click_cnt_14d_pub,
        coalesce(tp.job_click_cnt_3d, 0)      as job_click_cnt_3d_pub,
        coalesce(tp.job_click_cnt_7d, 0)      as job_click_cnt_7d_pub,
        coalesce(tp.job_click_cnt_14d, 0)     as job_click_cnt_14d_pub,
        coalesce(tp.click_cnt_prevd, 0)       as click_cnt_prevd_pub,
        coalesce(tp.click_cnt_prev2d, 0)      as click_cnt_prev2d_pub,
        coalesce(tp.click_cnt_prev3d, 0)      as click_cnt_prev3d_pub
    from
        temp_pred_job_data pjd
        left join temp_click_result tr
                  on pjd.record_date = tr.date and
                     pjd.country_code = tr.country_code and
                     pjd.publisher = tr.publisher and
                     pjd.feed_cpc_usd = tr.feed_cpc_usd
        left join temp_click_pub tp
                  on tp.date = pjd.record_date and
                     tp.country_code = pjd.country_code and
                     tp.publisher = pjd.publisher
    where
        pjd.record_date >= date_min;

    alter table temp_prediction_dataset
        add primary key (record_date, country_code, publisher, feed_cpc_usd);

    return query
        select
            t.record_date,
            date_part('isodow', t.record_date)::smallint                                                                                        as day_of_week,
            t.publisher::varchar(255),
            t.country_code::varchar(2),
            t.feed_cpc_usd::numeric                                                                                                             as feed_cpc_usd,
            (extract(day from t.record_date::date) / extract(day from
                                                             (date_trunc('month', t.record_date) + interval '1 month - 1 day')::date))::numeric as month_completed,
            round(t.avg_uid_cnt_3d, 2)::numeric                                                                                                 as avg_uid_cnt_3d,
            round(t.avg_uid_cnt_7d, 2)::numeric                                                                                                 as avg_uid_cnt_7d,
            round(t.avg_uid_cnt_14d, 2)::numeric                                                                                                as avg_uid_cnt_14d,
            round(t.avg_uid_cnt_prevd, 2)::numeric                                                                                              as avg_uid_cnt_prevd,
            round(t.avg_uid_cnt_prev2d, 2)::numeric                                                                                             as avg_uid_cnt_prev2d,
            round(t.avg_uid_cnt_prev3d, 2)::numeric                                                                                             as avg_uid_cnt_prev3d,
            round(t.avg_uid_cnt_prevd_pub, 2)::numeric                                                                                          as avg_uid_cnt_prevd_pub,
            round(t.avg_uid_cnt_prev2d_pub, 2)::numeric                                                                                         as avg_uid_cnt_prev2d_pub,
            round(t.avg_uid_cnt_prev3d_pub, 2)::numeric                                                                                         as avg_uid_cnt_prev3d_pub,
            t.min_uid_cnt_3d_pub::integer                                                                                                       as min_uid_cnt_3d_pub,
            t.min_uid_cnt_7d_pub::integer                                                                                                       as min_uid_cnt_7d_pub,
            t.min_uid_cnt_14d_pub::integer                                                                                                      as min_uid_cnt_14d_pub,
            case
                when t.avg_uid_cnt_3d_pub = 0 then 0::numeric
                else round(t.avg_uid_cnt_3d / t.avg_uid_cnt_3d_pub, 4)::numeric end                                                             as uid_percent_pub_3d,
            case
                when t.avg_uid_cnt_7d_pub = 0 then 0::numeric
                else round(t.avg_uid_cnt_7d / t.avg_uid_cnt_7d_pub, 4)::numeric end                                                             as uid_percent_pub_7d,
            case
                when t.avg_uid_cnt_14d_pub = 0 then 0::numeric
                else round(t.avg_uid_cnt_14d / t.avg_uid_cnt_14d_pub, 4)::numeric end                                                           as uid_percent_pub_14d,
            round(t.avg_job_cpc_percentile_3d_pub, 4)::numeric                                                                                  as avg_job_cpc_percentile_3d_pub,
            round(t.avg_job_cpc_percentile_7d_pub, 4)::numeric                                                                                  as avg_job_cpc_percentile_7d_pub,
            round(t.avg_job_cpc_percentile_14d_pub, 4)::numeric                                                                                 as avg_job_cpc_percentile_14d_pub,
            (case
                 when coalesce(t.avg_uid_cnt_prev2d, 0) = 0 then 0
                 else (coalesce(t.avg_uid_cnt_prevd, 0) - coalesce(t.avg_uid_cnt_prev2d, 0)) /
                      coalesce(t.avg_uid_cnt_prev2d, 0) end)::numeric                                                                           as avg_uid_cnt_diff_12,
            (case
                 when coalesce(t.avg_uid_cnt_prev3d, 0) = 0 then 0
                 else (coalesce(t.avg_uid_cnt_prev2d, 0) - coalesce(t.avg_uid_cnt_prev3d, 0)) /
                      coalesce(t.avg_uid_cnt_prev3d, 0) end)::numeric                                                                           as avg_uid_cnt_diff_23,
            (case
                 when coalesce(t.avg_uid_cnt_prev2d_pub, 0) = 0 then 0
                 else (coalesce(t.avg_uid_cnt_prevd_pub, 0) - coalesce(t.avg_uid_cnt_prev2d_pub, 0)) /
                      coalesce(t.avg_uid_cnt_prev2d_pub, 0) end)::numeric                                                                       as avg_uid_cnt_diff_12_pub,
            (case
                 when coalesce(t.avg_uid_cnt_prev3d_pub, 0) = 0 then 0
                 else (coalesce(t.avg_uid_cnt_prev2d_pub, 0) - coalesce(t.avg_uid_cnt_prev3d_pub, 0)) /
                      coalesce(t.avg_uid_cnt_prev3d_pub, 0) end)::numeric                                                                       as avg_uid_cnt_diff_23_pub,
            coalesce(t.click_cnt, 0)::integer                                                                                                   as click_cnt,

            coalesce(t.click_cnt_3d, 0)::integer                                                                                                as click_cnt_3d,
            coalesce(t.click_cnt_7d, 0)::integer                                                                                                as click_cnt_7d,
            coalesce(t.click_cnt_14d, 0)::integer                                                                                               as click_cnt_14d,
            coalesce(t.click_cnt_3d_pub, 0)::integer                                                                                            as click_cnt_3d_pub,
            coalesce(t.click_cnt_7d_pub, 0)::integer                                                                                            as click_cnt_7d_pub,
            coalesce(t.click_cnt_14d_pub, 0)::integer                                                                                           as click_cnt_14d_pub,
            case
                when coalesce(t.click_cnt_3d_pub, 0) = 0 then 0::numeric
                else round(1.0 * coalesce(t.click_cnt_3d, 0) / coalesce(t.click_cnt_3d_pub, 0),
                           4)::numeric end                                                                                                      as click_percent_pub_3d,
            case
                when coalesce(t.click_cnt_7d_pub, 0) = 0 then 0::numeric
                else round(1.0 * coalesce(t.click_cnt_7d, 0) / coalesce(t.click_cnt_7d_pub, 0),
                           4)::numeric end                                                                                                      as click_percent_pub_7d,
            case
                when coalesce(t.click_cnt_14d_pub, 0) = 0 then 0::numeric
                else round(1.0 * coalesce(t.click_cnt_14d, 0) / coalesce(t.click_cnt_14d_pub, 0),
                           4)::numeric end                                                                                                      as click_percent_pub_14d,

            case
                when coalesce(t.click_cnt_3d_pub, 0) = 0 then 0::numeric
                else round(1 - coalesce(t.exp_click_cnt_3d_pub, 0) / coalesce(t.click_cnt_3d_pub, 0),
                           4)::numeric end                                                                                                      as click_cpc_percentile_3d_pub,
            case
                when coalesce(t.click_cnt_7d_pub, 0) = 0 then 0::numeric
                else round(1 - coalesce(t.exp_click_cnt_7d_pub, 0) / coalesce(t.click_cnt_7d_pub, 0),
                           4)::numeric end                                                                                                      as click_cpc_percentile_7d_pub,
            case
                when coalesce(t.click_cnt_14d_pub, 0) = 0 then 0::numeric
                else round(1 - coalesce(t.exp_click_cnt_14d_pub, 0) /
                               coalesce(t.click_cnt_14d_pub, 0),
                           4)::numeric end                                                                                                      as click_cpc_percentile_14d_pub,

            coalesce(t.job_click_cnt_3d, 0)::integer                                                                                            as job_click_cnt_3d,
            coalesce(t.job_click_cnt_7d, 0)::integer                                                                                            as job_click_cnt_7d,
            coalesce(t.job_click_cnt_14d, 0)::integer                                                                                           as job_click_cnt_14d,
            coalesce(t.job_click_cnt_3d_pub, 0)::integer                                                                                        as job_click_cnt_3d_pub,
            coalesce(t.job_click_cnt_7d_pub, 0)::integer                                                                                        as job_click_cnt_7d_pub,
            coalesce(t.job_click_cnt_14d_pub, 0)::integer                                                                                       as job_click_cnt_14d_pub,
            t.click_cnt_prevd::integer                                                                                                          as click_cnt_prevd,
            t.click_cnt_prevd_pub::integer                                                                                                      as click_cnt_prevd_pub,
            t.click_cnt_prev2d_pub::integer                                                                                                     as click_cnt_prev2d_pub,
            t.click_cnt_prev3d_pub::integer                                                                                                     as click_cnt_prev3d_pub,
            (case
                 when coalesce(t.click_cnt_prev2d, 0) = 0 then 0
                 else (coalesce(t.click_cnt_prevd, 0) - coalesce(t.click_cnt_prev2d, 0)) /
                      coalesce(t.click_cnt_prev2d, 0) end)::numeric                                                                             as click_cnt_diff_12,
            (case
                 when coalesce(t.click_cnt_prev3d, 0) = 0 then 0
                 else (coalesce(t.click_cnt_prev2d, 0) - coalesce(t.click_cnt_prev3d, 0)) /
                      coalesce(t.click_cnt_prev3d, 0) end)::numeric                                                                             as click_cnt_diff_23,
            (case
                 when coalesce(t.click_cnt_prev2d_pub, 0) = 0 then 0
                 else (coalesce(t.click_cnt_prevd_pub, 0) - coalesce(t.click_cnt_prev2d_pub, 0)) /
                      coalesce(t.click_cnt_prev2d_pub, 0) end)::numeric                                                                         as click_cnt_diff_12_pub,
            (case
                 when coalesce(t.click_cnt_prev3d_pub, 0) = 0 then 0
                 else (coalesce(t.click_cnt_prev2d_pub, 0) - coalesce(t.click_cnt_prev3d_pub, 0)) /
                      coalesce(t.click_cnt_prev3d_pub, 0) end)::numeric                                                                         as click_cnt_diff_23_pub,
            round(t.avg_uid_cnt, 2)::numeric                                                                                                    as avg_uid_cnt
        from
            temp_prediction_dataset t;

    drop table if exists temp_calendar;
    drop table if exists temp_job_sample_data;
    drop table if exists temp_job_sample_data1;
    drop table if exists temp_job_current;
    drop table if exists temp_pub_raw;
    drop table if exists temp_job_pub;
    drop table if exists temp_pub_cpc_raw;
    drop table if exists temp_job_pub_cpc;
    drop table if exists temp_job_result;
    drop table if exists temp_pred_job_data;
    drop table if exists temp_click_sample_data;
    drop table if exists temp_click_current;
    drop table if exists temp_click_pub_cpc;
    drop table if exists temp_click_pub_cpc2;
    drop table if exists temp_click_pub;
    drop table if exists temp_click_result;
    drop table if exists temp_prediction_dataset;

end;
$$;

alter function affiliate.get_model_train_data_v2(date, date) owner to ypr;