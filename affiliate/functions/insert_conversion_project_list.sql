create or replace function affiliate.insert_conversion_project_list(
                p_record_type character varying,
                p_id_project bigint,
                p_country character varying,
                p_date_from date
            )
    returns TABLE
            (
                status      text,
                description text
            )
    language plpgsql
as
$$
declare
    _record_type varchar  := p_record_type;
    _id_project  bigint   := p_id_project;
    _country     varchar  := p_country;
    _date_from   date     := p_date_from;

begin
    if _date_from <> date_trunc('month', now() + '1 month'::interval)::date and
       _date_from <> date_trunc('month', now())::date then
        return query select
                         'Error'                                                                        as status,
                         'The parameter date_from should be the 1st day of the current or next month' as description;
    elsif (_record_type = 'include' and (select count(1) from dimension.countries where alpha_2 = _country) = 0)
        or (_record_type = 'exclude' and _country <> 'ALL')
    then
        return query select
                         'Error'                                                                                  as status,
                         'The parameter ''country'' is invalid. For ''include'', accepted values are ''DE'', ''FR'', etc. ' ||
                         'For ''exclude'', only ''ALL'' is allowed' as description;
    elsif _record_type <> 'include'::varchar and _record_type <> 'exclude'::varchar then
        return query select
                         'Error'                                                                                       as status,
                         'The parameter ''record_type'' is invalid. Accepted values are: ''include'' or ''exclude''' as description;

    elsif _date_from < (select
                            max(coalesce(cpl.date_to, now()))::date
                        from
                            affiliate.conversion_project_list as cpl
                        where
                              cpl.country = _country
                          and cpl.id_project = _id_project
                          and cpl.record_type = _record_type) then
        return query select
                         'Error'                                                                   as status,
                         'The parameter date_from can''t be earlier than the max existing date_to' as description;

    elsif (select
               count(1)
           from
               affiliate.conversion_project_list as cpl
           where
                 cpl.country = _country
             and cpl.id_project = _id_project
             and cpl.record_type = _record_type
             and _date_from between cpl.date_from and coalesce(cpl.date_to,
                                                               date_trunc('month', now() + '1 month'::interval)::date)) =
          0 then

        insert into
            affiliate.conversion_project_list (country, id_project, record_type, date_from, created_at)
        values
            (_country,
             _id_project,
             _record_type,
             _date_from,
             now());

        return query select
                         'Success'                      as status,
                         'Record inserted successfully' as description;
    else
        return query select
                         'Error'                 as status,
                         'Record already exists' as description;
    end if;
end;
$$;


alter function affiliate.insert_conversion_project_list(varchar, bigint, varchar, date) owner to ypr;

grant execute on function affiliate.insert_conversion_project_list(varchar, bigint, varchar, date)  to cxt_devs;
