create or replace function affiliate.get_functionality_analysis()
    returns TABLE
            (
                country_code                    character varying,
                click_date                      date,
                publisher                       character varying,
                manager                         character varying,
                id_project                      integer,
                project_name                    character varying,
                is_optimal_cpc_ratio            integer,
                optimized_cpc_part              numeric,
                optimizer_job_part              numeric,
                is_reassembled_job              integer,
                reassembled_jobs_part           numeric,
                reassembler_job_part            numeric,
                total_revenue                   numeric,
                certified_cost                  numeric,
                conversion_click_count          bigint,
                conversion_click_revenue        numeric,
                conversions                     bigint,
                certified_click_count           bigint,
                id_local_partner                integer,
                project_reassembled_jobs_part   numeric,
                project_optimized_jobs_part     numeric,
                publisher_reassembled_jobs_part numeric,
                publisher_optimized_jobs_part   numeric
            )
    language plpgsql
as
$$
begin
    drop table if exists temp_click_data;
    drop table if exists temp_partner_settings_log;
    drop table if exists temp_project_settings_log;
    drop table if exists temp_unique_project_cpc_ratio;
    drop table if exists temp_click_result;

    create temporary table temp_unique_project_cpc_ratio as
    select distinct
        pcr.source_id,
        pcr.id_local,
        pcr.id_project,
        pcr.optimized_cpc_part,
        pcr.reassembled_jobs_part
    from
        affiliate.project_cpc_ratio as pcr;

    create temporary table temp_click_data as
    select
        t.id_country,
        t.id_click                             as click_id,
        t.publisher,
        lower(t.country)                       as country_code,
        m.code                                 as manager,
        t.id_project,
        ip.name                                as project_name,
        t.uid_job,
        t.feed_cpc_usd,
        coalesce(t.client_cpc_usd, 0::numeric) as client_cpc_usd,
        t.click_type,
        t.click_date,
        t.is_conversion_considered,
        t.conversion_click_count,
        t.conversion_count,
        t.conversion_revenue_usd,
        case
            when (t.affiliate_flags & 8) = 8 then 1
            else 0
            end                                as is_optimal_cpc_ratio,
        case
            when (t.affiliate_flags & 16) = 16 then 1
            else 0
            end                                as is_reassembled_job,
        t.feed_gather_datetime,
        ps.source_id,
        ps.id_local_partner,
        ps.reassembled_jobs_part               as publisher_reassembled_jobs_part,
        ps.optimized_cpc_part                  as publisher_optimized_cpc_part,
        t.feed_cpa_usd,
        t.url_extra_flags
    from
        affiliate.v_click_data t
        join affiliate.partner_settings ps
             on ps.country::text = lower(t.country::text) and ps.partner::text = t.publisher::text
        left join affiliate.dic_manager m
                  on m.id = ps.id_manager
        left join dimension.info_project ip
                  on ip.country = t.id_country and ip.id = t.id_project
    where
          t.click_date >= '2023-07-11'::date
      and ps.is_api_publisher = 0
      and not (t.is_redirected_click = 1 and coalesce(t.feed_id_project, 0) != coalesce(t.id_project, 0));

    create temporary table temp_partner_settings_log as
    select
        vpscl.source_id,
        vpscl.local_id,
        vpscl.min_gather_dt,
        vpscl.max_gather_dt,
        vpscl.field_name,
        vpscl.field_value
    from
        affiliate.v_partner_settings_change_log vpscl
    where
        vpscl.field_name in ('optimized_cpc_part', 'reassembled_jobs_part');

    create temporary table temp_project_settings_log as
    select
        vpcrcl.source_id,
        vpcrcl.local_id,
        vpcrcl.project_id,
        vpcrcl.min_gather_dt,
        vpcrcl.max_gather_dt,
        vpcrcl.field_name,
        vpcrcl.field_value
    from
        affiliate.v_project_cpc_ratio_change_log vpcrcl
    where
        vpcrcl.field_name in ('optimized_cpc_part', 'reassembled_jobs_part');

    create temporary table temp_click_result as
    select
        t.publisher,
        t.country_code,
        t.click_date,
        t.manager,
        t.id_project,
        t.project_name,
        case
            when t.feed_cpa_usd is not null and t.click_type::text = 'certified'::text
                then t.feed_cpa_usd * coalesce(t.conversion_count, 0)
            when t.click_type::text = 'certified'::text then coalesce(t.feed_cpc_usd, 0::numeric)
            else 0::numeric
            end                                                                                                  as certified_cost_usd,
        coalesce(t.client_cpc_usd, 0::numeric)                                                                   as total_revenue,
        case
            when t.click_type::text = 'certified'::text then 1::bigint
            else null::bigint
            end                                                                                                  as certified_click_cnt,
        case
            when t.is_conversion_considered = 1 then t.conversion_click_count
            else 0 end                                                                                           as conversion_click_count,
        case
            when t.is_conversion_considered = 1 then t.conversion_count
            else 0 end                                                                                           as conversion_count,
        case
            when t.is_conversion_considered = 1 then t.conversion_revenue_usd
            else 0::numeric
            end                                                                                                  as conversion_click_revenue,
        t.is_optimal_cpc_ratio,
        t.is_reassembled_job,
        coalesce(pr_cpc.field_value, par_cpc.field_value)                                                        as optimized_cpc_part,
        case
            when t.url_extra_flags & 256 = 256 then 0
            else coalesce(pr_jobad.field_value, par_jobad.field_value) end                                       as reassembled_jobs_part,
        t.id_local_partner,
        t.publisher_reassembled_jobs_part,
        t.publisher_optimized_cpc_part,
        tu_pcr.reassembled_jobs_part                                                                             as project_reassembled_jobs_part,
        tu_pcr.optimized_cpc_part                                                                                as project_optimized_cpc_part
    from
        temp_click_data t
        left join temp_partner_settings_log par_cpc
                  on t.source_id = par_cpc.source_id and
                     t.id_local_partner = par_cpc.local_id and
                     t.feed_gather_datetime >= par_cpc.min_gather_dt and
                     t.feed_gather_datetime < par_cpc.max_gather_dt and
                     par_cpc.field_name::text = 'optimized_cpc_part'::text
        left join temp_project_settings_log pr_cpc
                  on t.source_id = pr_cpc.source_id and
                     t.id_local_partner = pr_cpc.local_id and
                     t.id_project = pr_cpc.project_id and
                     t.feed_gather_datetime >= pr_cpc.min_gather_dt and
                     t.feed_gather_datetime < pr_cpc.max_gather_dt and
                     pr_cpc.field_name::text = 'optimized_cpc_part'::text
        left join temp_partner_settings_log par_jobad
                  on t.source_id = par_jobad.source_id and
                     t.id_local_partner = par_jobad.local_id and
                     t.feed_gather_datetime >= par_jobad.min_gather_dt and
                     t.feed_gather_datetime < par_jobad.max_gather_dt and
                     par_jobad.field_name::text = 'reassembled_jobs_part'::text
        left join temp_project_settings_log pr_jobad
                  on t.source_id = pr_jobad.source_id and
                     t.id_local_partner = pr_jobad.local_id and
                     t.id_project = pr_jobad.project_id and
                     t.feed_gather_datetime >= pr_jobad.min_gather_dt and
                     t.feed_gather_datetime < pr_jobad.max_gather_dt and
                     pr_jobad.field_name::text = 'reassembled_jobs_part'::text
        left join temp_unique_project_cpc_ratio as tu_pcr
                  on t.source_id = tu_pcr.source_id and
                     t.id_local_partner = tu_pcr.id_local and
                     t.id_project = tu_pcr.id_project
    where
        (coalesce(pr_cpc.field_value, par_cpc.field_value) > 0::numeric or
         coalesce(pr_jobad.field_value, par_jobad.field_value) > 0::numeric);

    return query
        select
            tcr.country_code::varchar                    as country_code,
            tcr.click_date,
            tcr.publisher::varchar                       as publisher,
            tcr.manager::varchar                         as manager,
            tcr.id_project,
            tcr.project_name,
            tcr.is_optimal_cpc_ratio,
            tcr.optimized_cpc_part,
            case
                when coalesce(tcr.optimized_cpc_part, 0::numeric) = 0::numeric then null::numeric
                when tcr.is_optimal_cpc_ratio = 1 then tcr.optimized_cpc_part
                else 1::numeric - tcr.optimized_cpc_part
                end                                      as optimizer_job_part,
            tcr.is_reassembled_job,
            tcr.reassembled_jobs_part,
            case
                when coalesce(tcr.reassembled_jobs_part, 0::numeric) = 0::numeric
                    then null::numeric
                when tcr.is_reassembled_job = 1
                    then tcr.reassembled_jobs_part
                else 1::numeric - tcr.reassembled_jobs_part
                end                                      as reassembler_job_part,
            sum(tcr.total_revenue)                       as total_revenue,
            sum(tcr.certified_cost_usd)                  as certified_cost,
            sum(tcr.conversion_click_count)::bigint      as conversion_click_count,
            sum(tcr.conversion_click_revenue)            as conversion_click_revenue,
            sum(tcr.conversion_count)::bigint            as conversions,
            sum(tcr.certified_click_cnt)::bigint         as certified_click_count,
            tcr.id_local_partner::integer                as id_local_partner,
            tcr.project_reassembled_jobs_part::numeric   as project_reassembled_jobs_part,
            tcr.project_optimized_cpc_part::numeric      as project_optimized_cpc_part,
            tcr.publisher_reassembled_jobs_part::numeric as publisher_reassembled_jobs_part,
            tcr.publisher_optimized_cpc_part::numeric    as publisher_optimized_cpc_part
        from
            temp_click_result tcr
        group by
            tcr.country_code, tcr.click_date, tcr.publisher, tcr.manager, tcr.id_project, tcr.project_name,
            tcr.is_optimal_cpc_ratio, tcr.optimized_cpc_part, tcr.id_local_partner,
            (
                case
                    when coalesce(tcr.optimized_cpc_part, 0::numeric) = 0::numeric then null::numeric
                    when tcr.is_optimal_cpc_ratio = 1 then tcr.optimized_cpc_part
                    else 1::numeric - tcr.optimized_cpc_part
                    end), tcr.is_reassembled_job, tcr.reassembled_jobs_part,
            (
                case
                    when coalesce(tcr.reassembled_jobs_part, 0::numeric) = 0::numeric then null::numeric
                    when tcr.is_reassembled_job = 1 then tcr.reassembled_jobs_part
                    else 1::numeric - tcr.reassembled_jobs_part
                    end),
            tcr.project_reassembled_jobs_part, tcr.project_optimized_cpc_part,
            tcr.publisher_reassembled_jobs_part, tcr.publisher_optimized_cpc_part;


    drop table if exists temp_click_data;
    drop table if exists temp_partner_settings_log;
    drop table if exists temp_project_settings_log;
    drop table if exists temp_unique_project_cpc_ratio;
    drop table if exists temp_click_result;

end
$$;

alter function affiliate.get_functionality_analysis() owner to ypr;

