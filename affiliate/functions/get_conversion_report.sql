create or replace function affiliate.get_conversion_report()
    returns TABLE
            (
                date                     date,
                partner                  character varying,
                sub_source               character varying,
                target_action            text,
                click_cnt                numeric,
                conversion_cnt           numeric,
                benchmark_click_cnt      numeric,
                benchmark_conversion_cnt numeric
            )
    language plpgsql
as
$$
begin

    drop table if exists temp_raw_data;
    drop table if exists temp_data;
    drop table if exists temp_project_conv;
    drop table if exists temp_prepared_data;

    create temporary table temp_raw_data as
    select
        vcd.click_date                           as date,
        vcd.publisher                            as partner,
        vcd.publisher_sub_source                 as sub_source,
        vcd.target_action,
        vcd.id_project,
        sum(vcd.conversion_click_count)::numeric as click_cnt,
        sum(vcd.conversion_count)                as conversion_cnt,
        vcd.country
    from
        affiliate.v_click_data vcd
    where
          vcd.is_conversion_considered = 1
      and vcd.click_date >= (current_date - 60)
      and (vcd.target_action = any (array ['Apply'::text, 'Click apply'::text]))
    group by
        vcd.click_date, vcd.publisher, vcd.publisher_sub_source, vcd.id_project, vcd.target_action, vcd.country;

    create temporary table temp_data as
    select
        d.date,
        d.partner,
        case
            when t.partner is not null then d.sub_source
            else null::character varying(200)
            end               as sub_source,
        d.target_action,
        sum(d.click_cnt)      as click_cnt,
        sum(d.conversion_cnt) as conversion_cnt,
        d.id_project,
        d.country
    from
        temp_raw_data d
        left join (select
                       rd.partner,
                       count(distinct rd.sub_source)::numeric / sum(rd.click_cnt) as ratio
                   from
                       temp_raw_data rd
                   where
                       rd.click_cnt > 0::numeric
                   group by rd.partner
                   having
                        (count(distinct rd.sub_source)::numeric / sum(rd.click_cnt)) < 0.6
                     or sum(rd.click_cnt) < 100::numeric) t
                  on d.partner::text = t.partner::text
    group by
        d.date, d.partner, d.target_action, d.country,
        (
            case
                when t.partner is not null then d.sub_source
                else null::character varying(200)
                end), d.id_project;

    create temporary table temp_project_conv as
    select
        record_date,
        id_project,
        country,
        traffic_source,
        aways,
        conversions
    from
        affiliate.v_project_conversions_by_source
    where
          record_date >= current_date - 60
      and is_conversion_considered = 1;

    create temporary table temp_prepared_data as
    select
        d.date,
        d.partner,
        d.sub_source,
        d.target_action,
        d.click_cnt,
        d.conversion_cnt,
        d.id_project,
        sum(vpcbs.aways)       as total_clicks,
        sum(vpcbs.conversions) as total_conversions,
        d.country
    from
        temp_data d
        left join temp_project_conv vpcbs
                  on vpcbs.record_date = d.date and d.partner::text <> vpcbs.traffic_source::text and
                     vpcbs.id_project = d.id_project and d.country::text = vpcbs.country::text
    group by
        d.date, d.partner, d.target_action, d.country, d.sub_source, d.id_project, d.click_cnt, d.conversion_cnt;

    return query
        with
            final_data as (
                select
                    pd.date,
                    pd.partner,
                    pd.sub_source,
                    pd.target_action,
                    pd.id_project,
                    pd.click_cnt,
                    pd.conversion_cnt,
                    sum(pd.total_clicks)      as total_clicks,
                    sum(pd.total_conversions) as total_conversions
                from
                    temp_prepared_data pd
                group by
                    pd.date, pd.partner, pd.country, pd.id_project, pd.sub_source, pd.target_action, pd.click_cnt,
                    pd.conversion_cnt
            )
        select
            fd.date,
            fd.partner,
            fd.sub_source,
            fd.target_action,
            sum(fd.click_cnt)      as click_cnt,
            sum(fd.conversion_cnt) as conversion_cnt,
            round(
                    case
                        when sum(fd.click_cnt) > 0::numeric then sum(fd.click_cnt * fd.total_clicks) / sum(fd.click_cnt)
                        else 0::numeric
                        end, 2)    as benchmark_click_cnt,
            round(
                    case
                        when sum(fd.click_cnt) > 0::numeric
                            then sum(fd.click_cnt * fd.total_conversions) / sum(fd.click_cnt)
                        else 0::numeric
                        end, 2)    as benchmark_conversion_cnt
        from
            final_data fd
        group by
            fd.date, fd.partner, fd.sub_source, fd.target_action;

    drop table if exists temp_raw_data;
    drop table if exists temp_data;
    drop table if exists temp_project_conv;
    drop table if exists temp_prepared_data;

end ;
$$;

alter function affiliate.get_conversion_report() owner to ypr;
