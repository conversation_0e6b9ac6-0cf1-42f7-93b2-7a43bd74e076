create or replace function affiliate.get_partner_project_revenue(publisher_var character varying, date_start_var date, date_end_var date)
    returns TABLE(id_project integer, affiliate_revenue_partner_project numeric, precent_from_publisher_total_revenue numeric)
    language plpgsql
as
$$
begin
    return query
        with
            t as (
                select
                    t.id_project,
                    sum(t.client_cpc_usd) as revenue_usd
                from
                    affiliate.v_click_data t
                where
                      t.click_date between date_start_var and date_end_var
                  and t.publisher = publisher_var
                group by t.id_project
            ),
            total_revenue as (
                select
                    sum(t.revenue_usd) as total_revenue
                from
                    t
            )
        select
            t.id_project,
            t.revenue_usd  as affiliate_revenue_partner_project,
            case
                when tr.total_revenue > 0 then t.revenue_usd / tr.total_revenue * 100
                else 0 end as precent_from_publisher_total_revenue
        from
            t,
            total_revenue tr
        where
            t.id_project is not null
        order by affiliate_revenue_partner_project desc;

end
$$;

alter function affiliate.get_partner_project_revenue(varchar, date, date) owner to ypr;