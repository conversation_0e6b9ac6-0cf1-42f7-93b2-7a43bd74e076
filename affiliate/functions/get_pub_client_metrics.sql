create or replace function affiliate.get_pub_client_metrics(_publisher character varying, _id_project integer, _date_start date, _date_end date)
    returns TABLE(away_click_cnt numeric, conversion_rate numeric, revenue_client_currency numeric, cpa_client_currency numeric, target_cpa_client_currency numeric, project_total_cpa_client_currency numeric, project_target_cpa_client_currency numeric)
    language plpgsql
as
$$
declare
    _country_id int = (select
                           country
                       from
                           dimension.u_traffic_source
                       where
                           name = _publisher);
begin
    drop table if exists internal_conversion_data;
    drop table if exists internal_conversions_final;
    drop table if exists temp_project_total_metrics;

    create temporary table internal_conversion_data as
    select
        pcd.country_id,
        pcd.record_date,
        pcd.id_campaign                       as campaign_id,
        pcd.traffic_source,
        sum(pcd.away_revenue)                 as away_revenue,
        sum(pcd.away_revenue_client_currency) as away_revenue_client_currency,
        sum(pcd.aways)                        as aways,
        sum(pcd.conversions)                  as conversions,
        avg(pcd.target_cpa_usd)               as target_cpa_usd,
        avg(pcd.target_cpa_client_currency)   as target_cpa_client_currency
    from
        affiliate.v_project_conversions_by_source pcd
    where
          pcd.record_date between _date_start and _date_end
      and pcd.id_project = _id_project
      and pcd.country_id = _country_id
    group by
        pcd.country_id,
        pcd.record_date,
        pcd.id_campaign,
        pcd.traffic_source;

    create temporary table internal_conversions_final as
    select
        t.country_id,
        t.record_date,
        t.campaign_id,
        t.traffic_source,
        t.away_revenue,
        t.away_revenue_client_currency,
        t.aways,
        t.conversions,
        sum(t.away_revenue)
        over (partition by t.record_date, t.country_id) as total_project_away_revenue,
        sum(t.away_revenue_client_currency)
        over (partition by t.record_date, t.country_id) as total_project_away_revenue_client_currency,
        sum(t.aways)
        over (partition by t.record_date, t.country_id) as total_project_aways,
        sum(t.conversions)
        over (partition by t.record_date, t.country_id) as total_project_conversions,
        sum(t.aways::double precision * t.target_cpa_client_currency)
        over (partition by t.record_date, t.country_id) as total_project_avg_cpa_numerator,
        sum(t.aways::double precision * t.target_cpa_usd)
        over (partition by t.record_date, t.country_id) as total_project_avg_cpa_usd_numerator,
        sum(
        case
            when t.target_cpa_client_currency > 0::double precision then t.aways
            else null::bigint
            end)
        over (partition by t.record_date, t.country_id) as total_project_cpa_denominator,
        coalesce(t.target_cpa_client_currency, 0)       as target_cpa_client_currency
    from
        internal_conversion_data t;

    create temporary table temp_project_total_metrics as
    with
        t as (
            select
                  record_date,
                total_project_aways,
                total_project_away_revenue_client_currency,
                total_project_conversions,
                case
                    when max(total_project_cpa_denominator) > 0 then max(total_project_avg_cpa_numerator) /
                                                                     max(total_project_cpa_denominator) end as project_target_cpa_client_currency
            from
                internal_conversions_final
            group by
                    record_date, total_project_aways, total_project_away_revenue_client_currency,
                    total_project_conversions
        )
    select
        case
            when sum(t.total_project_conversions) > 0 then
                    sum(t.total_project_away_revenue_client_currency) /
                    sum(t.total_project_conversions) end            as project_total_cpa_client_currency,
        case
            when sum(case when t.project_target_cpa_client_currency > 0 then t.total_project_aways end) > 0 then
                    sum(t.project_target_cpa_client_currency * t.total_project_aways) /
                    sum(case
                            when t.project_target_cpa_client_currency > 0
                                then t.total_project_aways end) end as project_target_cpa_client_currency
    from
        t;

    return query
        with
            t as (
                select
                    sum(r.aways)                                                                          as away_click_cnt,
                    case
                        when sum(r.aways) > 0
                            then 1.0 * sum(r.conversions) / sum(r.aways) end                              as conversion_rate,
                    sum(r.away_revenue_client_currency)                                                   as revenue_client_currency,
                    case
                        when sum(r.conversions) > 0
                            then sum(r.away_revenue_client_currency) / sum(r.conversions) end             as cpa_client_currency,

                    case
                          when sum(case when r.target_cpa_client_currency > 0 then r.aways end) > 0
                              then sum(r.target_cpa_client_currency * r.aways) /
                                   sum(case when r.target_cpa_client_currency > 0 then r.aways end) end as target_cpa_client_currency
                from
                    internal_conversions_final r
                where
                      r.traffic_source = _publisher
                )
        select
            t.away_click_cnt::numeric,
            t.conversion_rate::numeric,
            t.revenue_client_currency::numeric,
            t.cpa_client_currency::numeric,
            t.target_cpa_client_currency::numeric,
            temp_project_total_metrics.project_total_cpa_client_currency::numeric,
            temp_project_total_metrics.project_target_cpa_client_currency::numeric
        from
            t
            cross join temp_project_total_metrics;

    drop table if exists internal_conversion_data;
    drop table if exists internal_conversions_final;
    drop table if exists temp_project_total_metrics;

end;
$$;

alter function affiliate.get_pub_client_metrics(varchar, integer, date, date) owner to ypr;

