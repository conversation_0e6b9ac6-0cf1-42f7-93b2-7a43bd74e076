create or replace function affiliate.get_abtest_click_metrics_raw(_datediff integer)
    returns TABLE(publisher character varying, country_code text, test_id integer, iteration integer, is_control_group integer, test_group integer, record_date date, test_min_date date, test_max_date date, click_id character varying, avg_click_client_cpc numeric, avg_certified_click_cpc numeric, avg_profit_per_click numeric)
    language plpgsql
as
$$
begin

    drop table if exists test_list;
    drop table if exists clicks_raw;

    create temporary table test_list as
    with
        t as (
            select distinct
                val.source_id,
                val.ab_history_id,
                val.start_dt_local,
                val.start_dt_utc,
                coalesce(val.finish_dt_utc, timezone('utc'::text, now()))                    as finish_dt_utc,
                coalesce(val.finish_dt_local, now()::timestamp)                              as finish_dt_local,
                val.id_test,
                val.iteration,
                val.partner,
                val.group_cnt,
                val.offset_in_hour,
                min(val.start_dt_local::date) over (partition by val.id_test, val.iteration) as test_min_date,
                max(coalesce(val.finish_dt_local, current_date - 1)::date)
                over (partition by val.id_test, val.iteration)                               as test_max_date
            from
                affiliate.v_abtest_list val
        )
    select
        t.source_id,
        t.ab_history_id,
        t.start_dt_local,
        t.start_dt_utc,
        t.finish_dt_utc,
        t.finish_dt_local,
        t.id_test,
        t.iteration,
        t.partner,
        t.group_cnt,
        t.offset_in_hour,
        t.test_min_date,
        t.test_max_date
    from
        t
    where
        t.test_max_date >= public.fn_get_date_from_date_diff(_datediff - 1);

    create temporary table clicks_raw as
    with
        t as (
            select
                t.id_country,
                cast(t.id_click as varchar(100)) as click_id,
                t.publisher,
                lower(t.country)                 as country_code,
                t.uid_job,
                t.feed_cpc_usd,
                coalesce(t.client_cpc_usd, 0)    as client_cpc_usd,
                t.click_type                     as click_type,
                t.click_date                     as record_date,
                t.click_datetime
            from
                affiliate.v_click_data t
                left join dimension.info_project ip
                    on ip.country = t.id_country and ip.id = t.id_project
                left join dimension.info_project ipr
                    on ipr.country = t.id_country and ipr.id = t.feed_id_project
            where
                  t.publisher in (select distinct partner from test_list)
              and t.click_date between (select min(start_dt_local)::date from test_list) and (select max(finish_dt_local)::date from test_list)
              and lower(coalesce(ip.name, ipr.name, ''::text)) not like 'j-vers.%'::text
        )
    select
        tl.ab_history_id,
        tl.source_id,
        t.id_country,
        t.click_id,
        t.publisher,
        t.country_code,
        cast(t.uid_job as varchar(50))                             as uid_job,
        ((t.uid_job % tl.group_cnt) + tl.group_cnt) % tl.group_cnt as test_group,
        t.feed_cpc_usd,
        t.client_cpc_usd,
        t.click_type,
        t.record_date,
        tl.id_test,
        tl.iteration,
        tl.test_min_date,
        tl.test_max_date
    from
        t
        join test_list tl
             on tl.partner = t.publisher and
                t.click_datetime between tl.start_dt_local and tl.finish_dt_local;

    return query
        with
            clicks_raw_clean as (
                select
                    c.click_id,
                    c.id_country,
                    c.publisher,
                    c.country_code,
                    c.uid_job,
                    c.test_group,
                    tgl.is_control_group,
                    c.feed_cpc_usd,
                    c.client_cpc_usd,
                    c.click_type,
                    c.record_date,
                    c.id_test,
                    c.iteration,
                    c.test_min_date,
                    c.test_max_date
                from
                    clicks_raw c
                    join affiliate.v_abtest_list tgl
                         on tgl.source_id = c.source_id and tgl.ab_history_id = c.ab_history_id
                             and tgl.partner = c.publisher
                             and tgl.test_group = c.test_group
            )
        select distinct
            crc.publisher,
            crc.country_code,
            crc.id_test                                                     as test_id,
            crc.iteration,
            crc.is_control_group,
            crc.test_group::integer                                         as test_group,
            crc.record_date,
            crc.test_min_date,
            crc.test_max_date,
            crc.click_id,
            coalesce(crc.client_cpc_usd, 0)                                 as avg_click_client_cpc,
            coalesce(crc.feed_cpc_usd, 0)                                   as avg_certified_click_cpc,
            coalesce(crc.client_cpc_usd, 0) - coalesce(crc.feed_cpc_usd, 0) as avg_profit_per_click
        from
            clicks_raw_clean crc
        where
            crc.click_type = 'certified';

    drop table if exists test_list;
    drop table if exists clicks_raw;

end;
$$;

alter function affiliate.get_abtest_click_metrics_raw(integer) owner to ypr;