create materialized view if not exists affiliate.mv_project_jobs_by_category as
with
    max_date as (
       select
             jsh.id_country as country_id,
             max(jsh.date)  as max_date,
             jsh.id_project as project_id,
             cn.alpha_2     as country
       from
             aggregation.jobs_stat_hourly jsh
             join dimension.countries cn
                  on jsh.id_country = cn.id
       where
               paid_job_count > 0
           and jsh.job_category_id is not null
           and cn.alpha_2 in ('DE', 'UK', 'FR', 'NL', 'CA', 'US')
           and jsh.date::date >= (current_date - 13)
       group by
             jsh.id_country, jsh.id_project, cn.alpha_2
       )

select
    md.country,
    jsh.id_project           as project_id,
    vjkc.child_category_name as category_name,
    jsh.job_category_id      as category_id,
    sum(jsh.paid_job_count)  as jobs_count,
    case
        when sum(jsh.paid_job_count) > 0 then
            sum(jsh.paid_job_count * jsh.avg_cpc_for_paid_job_count_usd) / sum(jsh.paid_job_count)
        else 0 end           as avg_client_cpc_usd
from
    aggregation.jobs_stat_hourly jsh
    join max_date as md
         on md.max_date = jsh.date and md.country_id = jsh.id_country and md.project_id = jsh.id_project
    left join aggregation.v_job_kaiju_category vjkc
              on jsh.job_category_id = vjkc.child_category_id
where
      paid_job_count > 0
  and jsh.job_category_id is not null
group by
    md.country, jsh.id_project, vjkc.child_category_name,
    jsh.job_category_id;

alter materialized view affiliate.mv_project_jobs_by_category
    owner to ypr;

grant select on affiliate.mv_project_jobs_by_category to readonly;

grant delete, insert, select, update on affiliate.mv_project_jobs_by_category to writeonly_pyscripts;