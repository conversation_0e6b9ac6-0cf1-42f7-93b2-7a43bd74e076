create or replace procedure affiliate.insert_partner_settings_change_log(_datediff integer)
    language plpgsql
as
$$
begin
            delete from affiliate.partner_settings_change_log
            where update_datetime::date = current_date-1;

            --connect to NL
            perform dblink_connect('myconnNL', 'dbname=affiliate host=********* user= ''pentaho'' password= ''!faP@jhWYWb&3rwv'' options=-csearch_path=');

            insert into affiliate.partner_settings_change_log(source_id, id, local_id, feed_name, field_name, value_before, value_after, update_datetime)
            select source_id, id, local_id, feed_name, field_name, value_before, value_after, update_datetime
            from dblink('myconnNL',
            'select
                1                                                                              as source_id,
                lsc.id,
                id_local                                                                       as local_id,
                ps.feed_name,
                field_name,
                case when value_before not in ('''', ''Null'', ''None'') then value_before end as value_before,
                case when value_after not in ('''', ''Null'', ''None'') then value_after end   as value_after,
                datetime + co.offset_in_hour * ''1 hour''::interval                            as update_datetime
            from
                local_settings_change_log_nl lsc
                    join partner_settings_nl ps
                        on ps.id = lsc.id_local
                    join country_offset co
                        on ps.country = co.country
            where
                (datetime + co.offset_in_hour * ''1 hour''::interval)::date = current_date - 1;') AS partner_settings_change_log (source_id smallint, id integer, local_id integer, feed_name varchar(100), field_name varchar(100), value_before varchar, value_after varchar, update_datetime timestamp);

            perform dblink_disconnect('myconnNL');

            --connect to US
            perform dblink_connect('myconnUS', 'dbname=affiliate host=************ user= ''pentaho'' password= ''!faP@jhWYWb&3rwv'' options=-csearch_path=');

            insert into affiliate.partner_settings_change_log(source_id, id, local_id, feed_name, field_name, value_before, value_after, update_datetime)
            select source_id, id, local_id, feed_name, field_name, value_before, value_after, update_datetime
            from dblink('myconnUS',
            'select
                2                                                                              as source_id,
                lsc.id,
                id_local                                                                       as local_id,
                ps.feed_name,
                field_name,
                case when value_before not in ('''', ''Null'', ''None'') then value_before end as value_before,
                case when value_after not in ('''', ''Null'', ''None'') then value_after end   as value_after,
                datetime + co.offset_in_hour * ''1 hour''::interval                            as update_datetime
            from
                local_settings_change_log_us lsc
                    join partner_settings_us ps
                        on ps.id = lsc.id_local
                    join country_offset co
                        on ps.country = co.country
            where
                (datetime + co.offset_in_hour * ''1 hour''::interval)::date = current_date - 1;') AS partner_settings_change_log (source_id smallint, id integer, local_id integer, feed_name varchar(100), field_name varchar(100), value_before varchar, value_after varchar, update_datetime timestamp);

            perform dblink_disconnect('myconnUS');

end;

$$;

alter procedure affiliate.insert_partner_settings_change_log(integer) owner to ypr;
