create or replace procedure affiliate.insert_api_request_history(_datediff integer)
    language plpgsql
as
$$
begin
            delete from affiliate.api_request_history
            where request_datetime::date = current_date-1;

            --connect to NL
            perform dblink_connect('myconnNL', 'dbname=affiliate host=********* user= ''pentaho'' password= ''!faP@jhWYWb&3rwv'' options=-csearch_path=');

            insert into affiliate.api_request_history(source_id, request_id, partner_id, request_datetime, params)
            select source_id, request_id, partner_id, request_datetime, params
            from dblink('myconnNL',
            'select distinct on (arh.request_id, arh.partner_id)
                1 as source_id,
                arh.request_id,
                arh.partner_id,
                arh.date_created_utc + (co.offset_in_hour || ''hour'')::interval as request_datetime,
                arh.params
            from
                public.api_request_history_nl arh
                    join public.partner_settings_nl ps
                    on arh.partner_id = ps.id
                    join public.country_offset co
                    on ps.country = co.country
            where
                (arh.date_created_utc + (co.offset_in_hour || ''hour'')::interval)::date = current_date - 1
            order by arh.request_id, arh.partner_id, arh.date_created_utc + (co.offset_in_hour || ''hour'')::interval') AS api_history (source_id smallint, request_id varchar(32), partner_id integer, request_datetime timestamp, params json);


            perform dblink_disconnect('myconnNL');

            --connect to US
            perform dblink_connect('myconnUS', 'dbname=affiliate host=************ user= ''pentaho'' password= ''!faP@jhWYWb&3rwv'' options=-csearch_path=');

            insert into affiliate.api_request_history(source_id, request_id, partner_id, request_datetime, params)
            select source_id, request_id, partner_id, request_datetime, params
            from dblink('myconnUS',
            'select distinct on (arh.request_id, arh.partner_id)
                2 as source_id,
                arh.request_id,
                arh.partner_id,
                arh.date_created_utc + (co.offset_in_hour || ''hour'')::interval as request_datetime,
                arh.params
            from
                public.api_request_history_us arh
                    join public.partner_settings_us ps
                    on arh.partner_id = ps.id
                    join public.country_offset co
                    on ps.country = co.country
            where
                (arh.date_created_utc + (co.offset_in_hour || ''hour'')::interval)::date = current_date - 1
            order by arh.request_id, arh.partner_id, arh.date_created_utc + (co.offset_in_hour || ''hour'')::interval') AS api_history (source_id smallint, request_id varchar(32), partner_id integer, request_datetime timestamp, params json);

            perform dblink_disconnect('myconnUS');


end;

$$;

alter procedure affiliate.insert_api_request_history(integer) owner to rlu;
