create or replace procedure affiliate.insert_publisher_external_stat(_date_rec date)
    language plpgsql
as
$$
begin

    truncate affiliate.publisher_external_stat;

    perform dblink_connect('myconn',
                           'dbname=dwh host=********** user= ''pentaho'' password= ''!faP@jhWYWb&3rwv'' options=-csearch_path=');

    insert into
        affiliate.publisher_external_stat(id, publisher, click_cnt, cost, date)
    select
        id,
        publisher,
        click_cnt,
        cost,
        date
    from
        dblink('myconn',
               'select
                   id,
                   publisher,
                   click_cnt,
                   cost,
                   date
               from public.affiliate_publisher_external_stat') as pub_ext_stat (id integer, publisher varchar,
                                                                                  click_cnt integer,
                                                                                  cost double precision, date date);

    perform dblink_disconnect('myconn');


end;

$$;

alter procedure affiliate.insert_publisher_external_stat(date) owner to ypr;

