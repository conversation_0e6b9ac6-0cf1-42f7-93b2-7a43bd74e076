create or replace procedure affiliate.transfer_old_data_short_table(_date_rec date)
    language plpgsql
as
$$
begin

            insert into archive.short_job_in_feed_log (source_id, id_local_partner, id, job_uid, inactive, datetime, click_price)
            select source_id, id_local_partner, id, job_uid, inactive, datetime, click_price
            from affiliate.short_job_in_feed_log
            where datetime::date < _date_rec;

            insert into archive.short_click_price_change(source_id, id, country_code, job_uid, click_price_usd, datetime, id_campaign)
            select source_id, id, country_code, job_uid, click_price_usd, datetime, id_campaign
            from affiliate.short_click_price_change
            where datetime::date < _date_rec;



end;

$$;

alter procedure affiliate.transfer_old_data_short_table(date) owner to rlu;
