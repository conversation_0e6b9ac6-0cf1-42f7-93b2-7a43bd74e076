create or replace procedure affiliate.update_bot_clicks(_datediff integer)
    language plpgsql
as
$$
declare
    _date_diff integer := (select
                               max(date_diff)
                           from
                               affiliate.temp_bot_click);
begin
    drop table if exists temp_click;
    drop table if exists temp_duplicated_clicks;
    drop table if exists temp_duplicated_clicks_old_logic;

    create temporary table temp_click as
    select *
    from
        affiliate.statistics_daily_raw
    where
        date_diff between _date_diff - 1 and _date_diff;

    update temp_click
    set
        is_duplicated_old_logic = 0,
        is_duplicated           = 0
    where
        date_diff = _date_diff;

    update temp_click t
    set
        is_bot                 = b.is_bot,
        revenue                = 0,
        revenue_usd            = 0,
        affiliate_flags        = case
                                     when t.affiliate_flags & 4 != 4
                                         and b.is_internal_bot_method = 1 then t.affiliate_flags + 4
                                     else t.affiliate_flags end,
        bot_flags              = coalesce(b.bot_flags, 0),
        is_internal_duplicated = b.is_internal_duplicated
    from
        affiliate.temp_bot_click b
    where
          t.id_country = b.country
      and (b.is_bot = 1 or b.is_internal_duplicated = 1)
      and coalesce(t.id_external, t.id_external_init) = coalesce(b.id_external, b.id_external_init)
      and t.date_diff = _date_diff
      and t.date_diff = b.date_diff;

    create temporary table temp_duplicated_clicks as
    with
        t1 as (
            select
                date_diff,
                id_country,
                partner,
                id_external_init,
                id_external,
                click_datetime,
                uid_job,
                user_ip,
                is_bot,
                case
                    when url_extra_flags & 1 = 1 then coalesce(job_title_hash, '0')
                    else '0' end                                                                                                                           as job_title_hash,
                extract(epoch from (click_datetime - lag(click_datetime, 1)
                                                     over (partition by id_country, partner, uid_job, user_ip, case
                                                                                                                   when url_extra_flags & 1 = 1
                                                                                                                       then coalesce(job_title_hash, '0')
                                                                                                                   else '0' end order by click_datetime))) as diff
            from
                temp_click t
            where
                  is_bot = 0
              and is_project_accepted_location = 1
              and is_billable = 1
              and ((date_diff = _date_diff) or (is_duplicated = 0 and date_diff = _date_diff - 1))
        ),
        t2 as (
            select
                date_diff,
                id_country,
                partner,
                id_external_init,
                id_external,
                click_datetime,
                uid_job,
                user_ip,
                job_title_hash,
                diff,
                case
                    when sum(diff) over (partition by id_country, partner, uid_job, user_ip, job_title_hash
                        order by click_datetime) - (3600 * 24) >= 0
                        then 1
                    else 0
                    end as is_more_than_24h
            from
                t1
        ),
        t3 as (
            select
                t2.*,
                coalesce(is_more_than_24h -
                         lag(is_more_than_24h) over (partition by id_country, partner, uid_job, user_ip, job_title_hash
                             order by click_datetime), 0) as is_next_day_valid_click
            from
                t2
        )
    select
        id_country,
        date_diff,
        id_external_init,
        id_external
    from
        t3
    where
          diff <= (3600 * 24)
      and is_next_day_valid_click = 0;

    update temp_click t
    set
        is_duplicated = 1
    from
        temp_duplicated_clicks dc
    where
          t.date_diff = dc.date_diff
      and t.id_country = dc.id_country
      and coalesce(t.id_external, t.id_external_init) = coalesce(dc.id_external, dc.id_external_init)
      and t.date_diff = _date_diff;

    create temporary table temp_duplicated_clicks_old_logic as
    with
        t1 as (
            select
                date_diff,
                id_country,
                partner,
                id_external_init,
                id_external,
                click_datetime,
                uid_job,
                user_ip,
                is_bot,
                extract(epoch from (click_datetime - lag(click_datetime, 1)
                                                     over (partition by id_country, partner, uid_job, user_ip order by click_datetime))) as diff
            from
                temp_click t
            where
                  is_bot = 0
              and is_project_accepted_location = 1
              and is_billable = 1
              and ((date_diff = _date_diff) or (is_duplicated = 0 and date_diff = _date_diff - 1))
        ),
        t2 as (
            select
                date_diff,
                id_country,
                partner,
                id_external_init,
                id_external,
                click_datetime,
                uid_job,
                user_ip,
                diff,
                case
                    when sum(diff) over (partition by id_country, partner, uid_job, user_ip
                        order by click_datetime) - (3600 * 24) >= 0
                        then 1
                    else 0
                    end as is_more_than_24h
            from
                t1
        ),
        t3 as (
            select
                t2.*,
                coalesce(is_more_than_24h -
                         lag(is_more_than_24h) over (partition by id_country, partner, uid_job, user_ip
                             order by click_datetime), 0) as is_next_day_valid_click
            from
                t2
        )
    select
        id_country,
        date_diff,
        id_external_init,
        id_external
    from
        t3
    where
          diff <= (3600 * 24)
      and is_next_day_valid_click = 0;

    update temp_click t
    set
        is_duplicated_old_logic = 1
    from
        temp_duplicated_clicks_old_logic dc
    where
          t.date_diff = dc.date_diff
      and t.id_country = dc.id_country
      and coalesce(t.id_external, t.id_external_init) = coalesce(dc.id_external, dc.id_external_init)
      and t.date_diff = _date_diff;

    update temp_click t
    set
        click_type = case
                         when t.feed_cpa_usd is not null then 0
                         when t.affiliate_flags & 4 = 4
                             and not (t.user_country::text = lower(c.alpha_2::text)
                                 or t.user_country::text = 'gb'::text and lower(c.alpha_2::text) = 'uk'::text)
                             then 4
                         when t.is_bot = 1 then 1
                         when t.is_project_accepted_location = 0 then 4
                         when t.is_billable = 0 then 3
                         when t.is_duplicated = 1 then 2
                         else 0 end
    from
        dimension.countries c
    where
          c.id = t.id_country
      and t.date_diff = _date_diff;

    delete
    from
        affiliate.statistics_daily_raw
    where
        date_diff = _date_diff;

    insert into
        affiliate.statistics_daily_raw(date_diff, id_country, partner, user_country, is_bot, uid_job,
                                       id_project,  click_datetime, id_external,
                                       external_flags, inactivation_date, id_away, id_jdp, id_jdp_away,
                                       has_away_conversion, has_jdp_away_conversion, cpc_change_date, partner_cost,
                                       partner_cost_usd, feed_cost, feed_cost_usd, revenue, revenue_usd, user_ip,
                                       is_external_stat_client, id_external_init, id_campaign, is_duplicated,
                                       is_internal_duplicated, id_sub_client, affiliate_flags, is_billable,
                                       last_gather_id_campaign, last_gather_datetime, id_category, redirected_to_uid,
                                       click_type, is_project_accepted_location, pub_tracking_id, api_request_id,
                                       url_extra_flags, bot_flags, job_client_inactive_flags, job_client_inactive_date,
                                       is_paid_overflow, job_title_hash, user_agent_hash, is_duplicated_old_logic,
                                       feed_cpa, feed_cpa_usd, publisher_click_id, is_apply, apply_revenue_usd,
                                       client_cpa, client_cpa_usd)
    select
        date_diff,
        id_country,
        partner,
        user_country,
        is_bot,
        uid_job,
        id_project,
        click_datetime,
        id_external,
        external_flags,
        inactivation_date,
        id_away,
        id_jdp,
        id_jdp_away,
        has_away_conversion,
        has_jdp_away_conversion,
        cpc_change_date,
        partner_cost,
        partner_cost_usd,
        feed_cost,
        feed_cost_usd,
        revenue,
        revenue_usd,
        user_ip,
        is_external_stat_client,
        id_external_init,
        id_campaign,
        is_duplicated,
        is_internal_duplicated,
        id_sub_client,
        affiliate_flags,
        is_billable,
        last_gather_id_campaign,
        last_gather_datetime,
        id_category,
        redirected_to_uid,
        click_type,
        is_project_accepted_location,
        pub_tracking_id,
        api_request_id,
        url_extra_flags,
        bot_flags,
        job_client_inactive_flags,
        job_client_inactive_date,
        is_paid_overflow,
        job_title_hash,
        user_agent_hash,
        is_duplicated_old_logic,
        feed_cpa,
        feed_cpa_usd,
        publisher_click_id,
        is_apply,
        apply_revenue_usd,
        client_cpa,
        client_cpa_usd
    from
        temp_click
    where
        date_diff = _date_diff;

    call affiliate.insert_statistics_daily_agg(_date_diff, _date_diff);

    truncate affiliate.temp_bot_click;
    drop table if exists temp_click;
    drop table if exists temp_duplicated_clicks;
    drop table if exists temp_duplicated_clicks_old_logic;

end;

$$;

alter procedure affiliate.update_bot_clicks(integer) owner to ypr;
