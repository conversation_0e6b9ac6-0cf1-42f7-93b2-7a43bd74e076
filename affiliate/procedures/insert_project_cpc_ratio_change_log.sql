create or replace procedure affiliate.insert_project_cpc_ratio_change_log(_datediff integer)
    language plpgsql
as
$$
begin
            delete from affiliate.project_cpc_ratio_change_log
            where update_datetime::date = current_date-1;

            --connect to NL
            perform dblink_connect('myconnNL', 'dbname=affiliate host=********* user= ''pentaho'' password= ''!faP@jhWYWb&3rwv'' options=-csearch_path=');

            insert into affiliate.project_cpc_ratio_change_log(source_id, id, local_id, project_id, campaign_id, field_name, value_before, value_after, update_datetime)
            select source_id, id, local_id, project_id, campaign_id, field_name, value_before, value_after, update_datetime
            from dblink('myconnNL',
            'with
                t as (
                    select
                        pcrcl.id,
                        id_local,
                        id_project,
                        field_name,
                        case when value_before not in ('''', ''Null'', ''None'') then value_before end as value_before,
                        case when value_after not in ('''', ''Null'', ''None'') then value_after end as value_after,
                        datetime_utc + co.offset_in_hour * ''1 hour''::interval as update_datetime
                    from
                        affiliate.public.project_cpc_ratio_change_log_nl pcrcl
                            join affiliate.public.partner_settings_nl ps
                                on ps.id = pcrcl.id_local
                            join affiliate.public.country_offset co
                                on ps.country = co.country
                    where
                        field_name = ''id_campaign_cpc_ratio''
                        and (datetime_utc + co.offset_in_hour * ''1 hour''::interval)::date = current_date - 1
                    ),
                before_ as (
                    select
                        id,
                        id_local,
                        id_project,
                        field_name,
                        update_datetime,
                        json_each.key::integer as id_campaign_before,
                        json_each.value::varchar::numeric(14, 5) as id_campaign_cpc_ratio_before
                    from
                        t,
                        lateral json_each(t.value_before::json) json_each(key, value)
                    ),
                after_ as (
                    select
                        id,
                        id_local,
                        id_project,
                        field_name,
                        update_datetime,
                        json_each.key::integer as id_campaign_after,
                        json_each.value::varchar::numeric(14, 5) as id_campaign_cpc_ratio_after
                    from
                        t,
                        lateral json_each(t.value_after::json) json_each(key, value)
                    )
            select
                1                                                            as source_id,
                coalesce(b.id, a.id)                                         as id,
                coalesce(b.id_local, a.id_local)                             as local_id,
                coalesce(b.id_project, a.id_project)                         as project_id,
                coalesce(b.id_campaign_before, a.id_campaign_after)::integer as campaign_id,
                coalesce(b.field_name, a.field_name)                         as field_name,
                id_campaign_cpc_ratio_before::numeric(14, 5)                 as value_before,
                id_campaign_cpc_ratio_after::numeric(14, 5)                  as value_after,
                coalesce(b.update_datetime, a.update_datetime)               as update_datetime
            from
                before_ b
                    full join after_ a
                        on a.id = b.id and a.id_campaign_after = b.id_campaign_before
            where
                coalesce(id_campaign_cpc_ratio_before::numeric(14, 5), -1) != coalesce(id_campaign_cpc_ratio_after::numeric(14, 5), -1)

            union all

            select
                1                                                                                              as source_id,
                pcrcl.id,
                id_local                                                                                       as local_id,
                id_project                                                                                     as project_id,
                0                                                                                              as campaign_id,
                field_name,
                case when value_before not in ('''', ''Null'', ''None'') then value_before::numeric(14, 5) end as value_before,
                case when value_after not in ('''', ''Null'', ''None'') then value_after::numeric(14, 5) end   as value_after,
                datetime_utc + co.offset_in_hour * ''1 hour''::interval                                        as update_datetime
            from
                affiliate.public.project_cpc_ratio_change_log_nl pcrcl
                    join affiliate.public.partner_settings_nl ps
                        on ps.id = pcrcl.id_local
                    join affiliate.public.country_offset co
                        on ps.country = co.country
            where
                field_name in (''cpc_ratio'', ''reassembled_jobs_part'', ''optimized_cpc_part'')
                and (datetime_utc + co.offset_in_hour * ''1 hour''::interval)::date = current_date - 1;') AS project_cpc_ratio_change_log (source_id smallint, id integer, local_id integer, project_id integer, campaign_id integer, field_name varchar(100), value_before numeric(14,5), value_after numeric(14,5), update_datetime timestamp);

            perform dblink_disconnect('myconnNL');

            --connect to US
            perform dblink_connect('myconnUS', 'dbname=affiliate host=************ user= ''pentaho'' password= ''!faP@jhWYWb&3rwv'' options=-csearch_path=');

            insert into affiliate.project_cpc_ratio_change_log(source_id, id, local_id, project_id, campaign_id, field_name, value_before, value_after, update_datetime)
            select source_id, id, local_id, project_id, campaign_id, field_name, value_before, value_after, update_datetime
            from dblink('myconnUS',
            'with
                t as (
                    select
                        pcrcl.id,
                        id_local,
                        id_project,
                        field_name,
                        case when value_before not in ('''', ''Null'', ''None'') then value_before end as value_before,
                        case when value_after not in ('''', ''Null'', ''None'') then value_after end as value_after,
                        datetime_utc + co.offset_in_hour * ''1 hour''::interval as update_datetime
                    from
                        affiliate.public.project_cpc_ratio_change_log_us pcrcl
                            join affiliate.public.partner_settings_us ps
                                on ps.id = pcrcl.id_local
                            join affiliate.public.country_offset co
                                on ps.country = co.country
                    where
                        field_name = ''id_campaign_cpc_ratio''
                        and (datetime_utc + co.offset_in_hour * ''1 hour''::interval)::date = current_date - 1
                    ),
                before_ as (
                    select
                        id,
                        id_local,
                        id_project,
                        field_name,
                        update_datetime,
                        json_each.key::integer as id_campaign_before,
                        json_each.value::varchar::numeric(14, 5) as id_campaign_cpc_ratio_before
                    from
                        t,
                        lateral json_each(t.value_before::json) json_each(key, value)
                    ),
                after_ as (
                    select
                        id,
                        id_local,
                        id_project,
                        field_name,
                        update_datetime,
                        json_each.key::integer as id_campaign_after,
                        json_each.value::varchar::numeric(14, 5) as id_campaign_cpc_ratio_after
                    from
                        t,
                        lateral json_each(t.value_after::json) json_each(key, value)
                    )
            select
                2                                                            as source_id,
                coalesce(b.id, a.id)                                         as id,
                coalesce(b.id_local, a.id_local)                             as local_id,
                coalesce(b.id_project, a.id_project)                         as project_id,
                coalesce(b.id_campaign_before, a.id_campaign_after)::integer as campaign_id,
                coalesce(b.field_name, a.field_name)                         as field_name,
                id_campaign_cpc_ratio_before::numeric(14, 5)                 as value_before,
                id_campaign_cpc_ratio_after::numeric(14, 5)                  as value_after,
                coalesce(b.update_datetime, a.update_datetime)               as update_datetime
            from
                before_ b
                    full join after_ a
                        on a.id = b.id and a.id_campaign_after = b.id_campaign_before
            where
                coalesce(id_campaign_cpc_ratio_before::numeric(14, 5), -1) != coalesce(id_campaign_cpc_ratio_after::numeric(14, 5), -1)

            union all

            select
                2                                                                                              as source_id,
                pcrcl.id,
                id_local                                                                                       as local_id,
                id_project                                                                                     as project_id,
                0                                                                                              as campaign_id,
                field_name,
                case when value_before not in ('''', ''Null'', ''None'') then value_before::numeric(14, 5) end as value_before,
                case when value_after not in ('''', ''Null'', ''None'') then value_after::numeric(14, 5) end   as value_after,
                datetime_utc + co.offset_in_hour * ''1 hour''::interval                                        as update_datetime
            from
                affiliate.public.project_cpc_ratio_change_log_us pcrcl
                    join affiliate.public.partner_settings_us ps
                        on ps.id = pcrcl.id_local
                    join affiliate.public.country_offset co
                        on ps.country = co.country
            where
                field_name in (''cpc_ratio'', ''reassembled_jobs_part'', ''optimized_cpc_part'')
                and (datetime_utc + co.offset_in_hour * ''1 hour''::interval)::date = current_date - 1;') AS project_cpc_ratio_change_log (source_id smallint, id integer, local_id integer, project_id integer, campaign_id integer, field_name varchar(100), value_before numeric(14,5), value_after numeric(14,5), update_datetime timestamp);

            perform dblink_disconnect('myconnUS');


end;

$$;

alter procedure affiliate.insert_project_cpc_ratio_change_log(integer) owner to ypr;
