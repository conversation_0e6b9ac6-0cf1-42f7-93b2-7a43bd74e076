create or replace procedure affiliate.delete_old_data_api_request_history(_datediff integer)
    language plpgsql
as
$$
begin

    delete
    from
        affiliate.api_request_history as n
        using dimension.info_calendar as ic
    where
          ic.dt = n.request_datetime::date
      and ic.date_diff <= (_datediff - 120);
end;

$$;

alter procedure affiliate.delete_old_data_api_request_history(integer) owner to ypr;