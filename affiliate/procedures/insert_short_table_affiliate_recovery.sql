create or replace procedure affiliate.insert_short_table_affiliate_recovery(_date_rec date)
    language plpgsql
as
$$
begin

            delete from affiliate.short_click_price_change
            where cast(datetime as date) between (_date_rec - 2)::date and _date_rec;

            insert into affiliate.short_click_price_change(source_id, id, country_code, job_uid, click_price_usd, datetime, id_campaign)
            select source_id, id, country_code, job_uid, click_price_usd, datetime, id_campaign
            from affiliate.click_price_change
            where cast(datetime as date) between (_date_rec - 2)::date and _date_rec;


            delete from affiliate.short_job_in_feed_log
            where cast(datetime as date) between (_date_rec - 1)::date and _date_rec;

            insert into affiliate.short_job_in_feed_log(source_id, id_local_partner, id, job_uid, inactive, datetime, click_price)
            select source_id, id_local_partner, id, job_uid, inactive, datetime, click_price
            from affiliate.job_in_feed_log
            where cast(datetime as date) between (_date_rec - 1)::date and _date_rec;


            delete from affiliate.afg_row_cnt_snapshot
            where log_date = _date_rec
              and table_name = 'click_price_change';

            insert into affiliate.afg_row_cnt_snapshot(table_name, source_id, log_date, row_cnt)
            select
                'click_price_change' as table_name,
                source_id,
                _date_rec             as log_date,
                count(*)                 as row_cnt
            from affiliate.short_click_price_change
            where cast(datetime as date) = _date_rec
            group by source_id;


            delete from affiliate.afg_row_cnt_snapshot
            where log_date = _date_rec
              and table_name = 'job_in_feed_log';

            insert into affiliate.afg_row_cnt_snapshot(table_name, source_id, log_date, row_cnt)
            select
                'job_in_feed_log' as table_name,
                source_id,
                _date_rec  as log_date,
                count(*)          as row_cnt
            from affiliate.short_job_in_feed_log
            where cast(datetime as date) = _date_rec
            group by source_id;


end;

$$;

alter procedure affiliate.insert_short_table_affiliate_recovery(date) owner to rlu;
