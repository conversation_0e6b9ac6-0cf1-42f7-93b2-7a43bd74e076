create or replace procedure affiliate.insert_partner_settings(_datediff integer)
    language plpgsql
as
$$
begin

    truncate table affiliate.partner_settings;

    --connect to NL
    perform dblink_connect('myconnNL',
                           'dbname=affiliate host=********* user= ''pentaho'' password= ''!faP@jhWYWb&3rwv'' options=-csearch_path=');

    insert into
        affiliate.partner_settings(id_local_partner, partner, country, partner_id_currency, cpc_ratio, min_cpc_in_usd,
                             feed_is_only_easy_apply, feed_is_static_min_cpc, run_interval, feed_type, update_gap,
                             id_manager, is_rounded_cpc, offset_in_hour, local_flags, worst_cpc_ratio, source_id,
                             update_datetime, is_api_publisher, optimized_cpc_part, reassembled_jobs_part, max_cnt_jobs,
                             global_flags, is_cpa_publisher, max_cpc_in_usd)
    select
        id_local_partner,
        partner,
        country,
        partner_id_currency,
        cpc_ratio,
        min_cpc_in_usd,
        feed_is_only_easy_apply,
        feed_is_static_min_cpc,
        run_interval,
        feed_type,
        update_gap,
        id_manager,
        is_rounded_cpc,
        offset_in_hour,
        local_flags,
        worst_cpc_ratio,
        source_id,
        update_datetime,
        is_api_publisher,
        optimized_cpc_part,
        reassembled_jobs_part,
        max_cnt_jobs,
        global_flags,
        is_cpa_publisher,
        max_cpc_in_usd
    from
        dblink('myconnNL',
               'select
                   now() as update_datetime,
                   l.id as id_local_partner,
                   l.feed_name as partner,
                   lower(l.country) as country,
                   l.id_country_currency as partner_id_currency,
                   l.cpc_ratio,
                   l.min_cpc_in_usd,
                   case
                       when l.global_flags & 1 = 1 then 1
                       when l.global_flags is null then null
                       else 0
                       end as feed_is_only_easy_apply,
                   case
                       when l.static_min_cpc then 1
                       when l.static_min_cpc is null then null
                       else 0
                       end as feed_is_static_min_cpc,
                   l.run_interval,
                   case when cpc_ratio > 0 and (min_cpc_in_usd > 0 or max_cpc_in_usd > 0) then ''paid'' else ''organic'' end as feed_type,
                   l.update_gap,
                   l.id_soska_user as id_manager,
                   case
                       when l.local_flags & 8 = 8 then 1
                       else 0
                       end as is_rounded_cpc,
                   country_offset.offset_in_hour,
                   l.local_flags,
                   l.worst_cpc_ratio,
                   1 as source_id,
                   case
                       when l.local_flags & 64 = 64 then 1
                       else 0
                       end as is_api_publisher,
                   l.optimized_cpc_part,
                   l.reassembled_jobs_part,
                   l.max_cnt_jobs,
                   l.global_flags,
                   case when (select count(1)
                              from public.cpa_static cs
                              where cs.id_partner = l.id) > 0 or l.global_flags & 4096 = 4096
                        then 1
                        else 0
                        end as is_cpa_publisher,
                   l.max_cpc_in_usd
               from public.partner_settings_nl l
                   join public.country_offset
                       on l.country = country_offset.country;') as partner_settings (update_datetime timestamp,
                                                                                     id_local_partner integer,
                                                                                     partner varchar(100),
                                                                                     country varchar(10),
                                                                                     partner_id_currency integer,
                                                                                     cpc_ratio numeric(14, 5),
                                                                                     min_cpc_in_usd numeric(14, 5),
                                                                                     feed_is_only_easy_apply integer,
                                                                                     feed_is_static_min_cpc integer,
                                                                                     run_interval integer,
                                                                                     feed_type varchar(10),
                                                                                     update_gap integer,
                                                                                     id_manager integer,
                                                                                     is_rounded_cpc integer,
                                                                                     offset_in_hour integer,
                                                                                     local_flags integer,
                                                                                     worst_cpc_ratio numeric(14, 5),
                                                                                     source_id integer,
                                                                                     is_api_publisher smallint,
                                                                                     optimized_cpc_part numeric,
                                                                                     reassembled_jobs_part numeric,
                                                                                     max_cnt_jobs integer,
                                                                                     global_flags integer,
                                                                                     is_cpa_publisher smallint,
                                                                                     max_cpc_in_usd numeric(14, 5));

    perform dblink_disconnect('myconnNL');

    --connect to US
    perform dblink_connect('myconnUS',
                           'dbname=affiliate host=************ user= ''pentaho'' password= ''!faP@jhWYWb&3rwv'' options=-csearch_path=');


    insert into
        affiliate.partner_settings(id_local_partner, partner, country, partner_id_currency, cpc_ratio, min_cpc_in_usd,
                             feed_is_only_easy_apply, feed_is_static_min_cpc, run_interval, feed_type, update_gap,
                             id_manager, is_rounded_cpc, offset_in_hour, local_flags, worst_cpc_ratio, source_id,
                             update_datetime, is_api_publisher, optimized_cpc_part, reassembled_jobs_part, max_cnt_jobs,
                             global_flags, is_cpa_publisher, max_cpc_in_usd)
    select
        id_local_partner,
        partner,
        country,
        partner_id_currency,
        cpc_ratio,
        min_cpc_in_usd,
        feed_is_only_easy_apply,
        feed_is_static_min_cpc,
        run_interval,
        feed_type,
        update_gap,
        id_manager,
        is_rounded_cpc,
        offset_in_hour,
        local_flags,
        worst_cpc_ratio,
        source_id,
        update_datetime,
        is_api_publisher,
        optimized_cpc_part,
        reassembled_jobs_part,
        max_cnt_jobs,
        global_flags,
        is_cpa_publisher,
        max_cpc_in_usd
    from
        dblink('myconnUS',
               'select
                   now() as update_datetime,
                   l.id as id_local_partner,
                   l.feed_name as partner,
                   lower(l.country) as country,
                   l.id_country_currency as partner_id_currency,
                   l.cpc_ratio,
                   l.min_cpc_in_usd,
                   case
                       when l.global_flags & 1 = 1 then 1
                       when l.global_flags is null then null
                       else 0
                       end as feed_is_only_easy_apply,
                   case
                       when l.static_min_cpc then 1
                       when l.static_min_cpc is null then null
                       else 0
                       end as feed_is_static_min_cpc,
                   l.run_interval,
                   case when cpc_ratio > 0 and (min_cpc_in_usd > 0 or max_cpc_in_usd > 0) then ''paid'' else ''organic'' end as feed_type,
                   l.update_gap,
                   l.id_soska_user as id_manager,
                   case
                       when l.local_flags & 8 = 8 then 1
                       else 0
                       end as is_rounded_cpc,
                   country_offset.offset_in_hour,
                   l.local_flags,
                   l.worst_cpc_ratio,
                   2 as source_id,
                   case
                       when l.local_flags & 64 = 64 then 1
                       else 0
                       end as is_api_publisher,
                   l.optimized_cpc_part,
                   l.reassembled_jobs_part,
                   l.max_cnt_jobs,
                   l.global_flags,
                   case when (select count(1)
                              from public.cpa_static cs
                              where cs.id_partner = l.id) > 0 or l.global_flags & 4096 = 4096
                        then 1
                        else 0
                        end as is_cpa_publisher,
                   l.max_cpc_in_usd
               from public.partner_settings_us l
                   join public.country_offset
                       on l.country = country_offset.country;') as partner_settings (update_datetime timestamp,
                                                                                     id_local_partner integer,
                                                                                     partner varchar(100),
                                                                                     country varchar(10),
                                                                                     partner_id_currency integer,
                                                                                     cpc_ratio numeric(14, 5),
                                                                                     min_cpc_in_usd numeric(14, 5),
                                                                                     feed_is_only_easy_apply integer,
                                                                                     feed_is_static_min_cpc integer,
                                                                                     run_interval integer,
                                                                                     feed_type varchar(10),
                                                                                     update_gap integer,
                                                                                     id_manager integer,
                                                                                     is_rounded_cpc integer,
                                                                                     offset_in_hour integer,
                                                                                     local_flags integer,
                                                                                     worst_cpc_ratio numeric(14, 5),
                                                                                     source_id integer,
                                                                                     is_api_publisher smallint,
                                                                                     optimized_cpc_part numeric,
                                                                                     reassembled_jobs_part numeric,
                                                                                     max_cnt_jobs integer,
                                                                                     global_flags integer,
                                                                                     is_cpa_publisher smallint,
                                                                                     max_cpc_in_usd numeric(14, 5));

    perform dblink_disconnect('myconnUS');


end;

$$;

alter procedure affiliate.insert_partner_settings(integer) owner to ypr;

