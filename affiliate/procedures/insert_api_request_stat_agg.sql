create or replace procedure affiliate.insert_api_request_stat_agg(_datediff integer)
    language plpgsql
as
$$
begin

    delete
    from
        affiliate.api_request_stat_agg as n
        using dimension.info_calendar as ic
    where
          ic.dt = n.request_date
      and ic.date_diff = _datediff;

    insert into
        affiliate.api_request_stat_agg(id_source, id_partner, request_date, request_count)
    select
        arh.source_id,
        arh.partner_id             as id_partner,
        arh.request_datetime::date as request_date,
        count(1)                   as request_count
    from
        affiliate.api_request_history as arh
        join dimension.info_calendar as ic
             on ic.dt = arh.request_datetime::date
    where
        ic.date_diff = _datediff
    group by arh.source_id, arh.partner_id, arh.request_datetime::date;

end;

$$;

alter procedure affiliate.insert_api_request_stat_agg(integer) owner to ypr;