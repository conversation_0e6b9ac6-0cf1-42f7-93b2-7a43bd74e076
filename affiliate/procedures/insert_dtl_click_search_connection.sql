create or replace procedure affiliate.insert_dtl_click_search_connection(_datediff integer)
    language plpgsql
as
$$
begin

    delete from affiliate.dtl_click_search_connection
    where
         date_diff = _datediff
      or (date_diff = _datediff - 1 and search_id is null);

    --1. Перший раунд метчингу, дані якого зберігаються в temp_first_match
    drop table if exists temp_click_search_connection;
    drop table if exists temp_dtl_searches_not_matched;
    drop table if exists temp_dtl_click_log_not_matched;
    drop table if exists temp_dtl_duplicated_clicks;
    drop table if exists temp_dtl_searches;
    drop table if exists temp_dtl_click_log;


    create temporary table temp_dtl_searches as (
        --всі дані за вчора та незметчені дані за позавчора
        select
            s.country,
            s.date_diff,
            s.datetime,
            s.id_search,
            s.keyword_hash,
            s.keyword,
            s.region_name,
            s.id_region,
            s.results_total,
            s.traffic_source,
            s.id_traffic_source,
            s.user_ip,
            s.user_country,
            s.is_bot
        from
            affiliate.dtl_searches s
            left join affiliate.dtl_click_search_connection match
                      on s.country = match.country
                          and s.id_search = match.search_id
                          and s.date_diff = match.date_diff
        where
              match.search_id is null
          and (s.date_diff = _datediff or s.date_diff = _datediff - 1));

    create temporary table temp_dtl_click_log as (
        --всі дані за вчора та незметчені дані за позавчора
        select
            c.source_id,
            c.id,
            c.country,
            c.click_datetime,
            c.user_ip,
            c.id_traffic_source,
            c.keyword,
            c.keyword_hash,
            c.region_name,
            c.region_id,
            c.click_cpc
        from
            affiliate.dtl_click_log c
            join dimension.info_calendar ic
                 on ic.dt = c.click_datetime::date
            left join affiliate.dtl_click_search_connection match
                      on c.source_id = match.source_id
                          and c.id = match.click_log_id
        where
              match.click_log_id is null
          and (ic.date_diff = _datediff or ic.date_diff = _datediff - 1));

    create temporary table temp_click_search_connection
    (
        source_id         smallint,
        id                integer,
        date_diff         integer,
        click_datetime    timestamp,
        country           varchar(2),
        id_search         bigint,
        is_duplicated     smallint,
        is_bot            smallint,
        is_foreign        smallint,
        user_ip           varchar,
        keyword_hash      bigint,
        id_traffic_source integer,
        id_region         integer
    );


    insert into
        temp_click_search_connection(source_id, id, date_diff, click_datetime, country, id_search, is_duplicated,
                                     is_bot, is_foreign, user_ip, keyword_hash, id_traffic_source, id_region)
    with
        cte as (
            select
                c.source_id,
                c.id,
                s.date_diff,
                s.country,
                s.id_search,
                c.click_datetime,
                s.datetime                                                                                              as search_datetime,
                s.is_bot,
                c.user_ip,
                c.keyword_hash,
                c.id_traffic_source,
                c.region_id                                                                                             as id_region,
                s.user_country,
                row_number()
                over (partition by c.source_id, c.id order by abs(extract(epoch from (s.datetime - c.click_datetime)))) as row_num1
            from
                temp_dtl_searches s
                inner join temp_dtl_click_log c
                    -- match by country, ip, id_traffic_source, datetime
                           on lower(s.country) = lower(c.country)
                               and s.user_ip = c.user_ip
                               and s.id_traffic_source = c.id_traffic_source
                               and s.datetime between c.click_datetime - interval '60 seconds' and c.click_datetime + interval '60 seconds'
                               -- to match by keyword and region:
                               -- try to use keyword hash or keyword string (with replacing + and - by space) and region_id (with replacing nulls by -1)
                               and ((s.keyword_hash = c.keyword_hash or
                                   lower(replace(replace(s.keyword, '+', '-'), '-', ' ')) =
                                   lower(replace(replace(c.keyword, '+', '-'), '-', ' ')))
                                and s.id_region = coalesce(c.region_id, -1))
        ),
        cte2 as (
            select
                source_id,
                id,
                date_diff,
                click_datetime,
                country,
                id_search,
                is_bot,
                user_ip,
                keyword_hash,
                id_traffic_source,
                id_region,
                user_country,
                row_num1,
                row_number()
                over (partition by country, date_diff, id_search order by abs(extract(epoch from (search_datetime - click_datetime)))) as row_num2
            from
                cte
            where
                row_num1 = 1
        )
    select
        source_id,
        id,
        date_diff,
        click_datetime,
        country,
        id_search,
        0              as is_duplicated,
        is_bot,
        case
            when user_country = lower(country) or user_country = 'gb' and lower(country) = 'uk' then 0
            else 1 end as is_foreign,
        user_ip,
        keyword_hash,
        id_traffic_source,
        id_region
    from
        cte2
    where
        row_num2 = 1;

    --2. Зберігаємо в темповій табличці незметчені кліки з dtl_searches
    create temporary table temp_dtl_searches_not_matched as (
        select
            s.country,
            s.date_diff,
            s.datetime,
            s.id_search,
            s.keyword_hash,
            s.keyword,
            s.region_name,
            s.id_region,
            s.results_total,
            s.traffic_source,
            s.id_traffic_source,
            s.user_ip,
            s.user_country,
            s.is_bot
        from
            temp_dtl_searches s
            left join temp_click_search_connection match
                      on s.country = match.country
                          and s.id_search = match.id_search
                          and s.date_diff = match.date_diff
        where
            match.id_search is null);

--3.Зберігаємо в темповій табличці незметчені кліки з dtl_click_log
    create temporary table temp_dtl_click_log_not_matched as (
        select
            c.source_id,
            c.id,
            c.country,
            c.click_datetime,
            c.user_ip,
            c.id_traffic_source,
            c.keyword,
            c.keyword_hash,
            c.region_name,
            c.region_id,
            c.click_cpc
        from
            temp_dtl_click_log c
            left join temp_click_search_connection match
                      on c.id = match.id
                          and c.source_id = match.source_id
        where
            match.id is null);

--4. Другий раунд метчингу, де є метчу ті дані, які не вдалось зметчити одразу. Одразу додаємо ці колонки в таблицю з зметченими кліками.
    insert into
        temp_click_search_connection(source_id, id, date_diff, click_datetime, country, id_search, is_duplicated,
                                     is_bot, is_foreign, user_ip, keyword_hash, id_traffic_source, id_region)
    with
        second_round_cte as (
            select
                c.source_id,
                c.id,
                s.date_diff,
                s.country,
                s.id_search,
                c.click_datetime,
                s.datetime                                                                                              as search_datetime,
                s.is_bot,
                c.user_ip,
                c.keyword_hash,
                c.id_traffic_source,
                c.region_id                                                                                             as id_region,
                s.user_country,
                row_number()
                over (partition by c.source_id, c.id order by abs(extract(epoch from (s.datetime - c.click_datetime)))) as row_num1
            from
                temp_dtl_searches_not_matched s
                inner join temp_dtl_click_log_not_matched c
                    -- match by country, ip, id_traffic_source, datetime
                           on lower(s.country) = lower(c.country)
                               and s.user_ip = c.user_ip
                               and s.id_traffic_source = c.id_traffic_source
                               and s.datetime between c.click_datetime - interval '60 seconds' and c.click_datetime + interval '60 seconds'
                               -- to match by keyword and region:
                               -- try to use keyword hash or keyword string (with replacing + and - by space) and region_id (with replacing nulls by -1)
                               and ((s.keyword_hash = c.keyword_hash or
                                   lower(replace(replace(s.keyword, '+', '-'), '-', ' ')) =
                                   lower(replace(replace(c.keyword, '+', '-'), '-', ' ')))
                                and s.id_region = coalesce(c.region_id, -1))
        ),
        second_round_cte2 as (
            select
                source_id,
                id,
                date_diff,
                click_datetime,
                country,
                id_search,
                is_bot,
                user_ip,
                keyword_hash,
                id_traffic_source,
                id_region,
                user_country,
                row_num1,
                row_number()
                over (partition by country, date_diff, id_search order by abs(extract(epoch from (search_datetime - click_datetime)))) as row_num2
            from
                second_round_cte
            where
                row_num1 = 1
        )
    select
        source_id,
        id,
        date_diff,
        click_datetime,
        country,
        id_search,
        0              as is_duplicated,
        is_bot,
        case
            when user_country = lower(country) or user_country = 'gb' and lower(country) = 'uk' then 0
            else 1 end as is_foreign,
        user_ip,
        keyword_hash,
        id_traffic_source,
        id_region
    from
        second_round_cte2
    where
        row_num2 = 1;

    insert into
        temp_click_search_connection(source_id, id, date_diff, click_datetime, country, id_search, is_duplicated,
                                     is_bot, is_foreign, user_ip, keyword_hash, id_traffic_source, id_region)
    select
        c.source_id,
        c.id,
        ic.date_diff     as date_diff,
        c.click_datetime,
        lower(c.country) as country,
        null::bigint     as id_search,
        0                as is_duplicated,
        1                as is_bot,
        0                as is_foreign,
        c.user_ip,
        c.keyword_hash,
        c.id_traffic_source,
        c.region_id
    from
        temp_dtl_click_log c
        join dimension.info_calendar ic
             on ic.dt = c.click_datetime::date
        left join temp_click_search_connection match
                  on c.id = match.id
                      and c.source_id = match.source_id
    where
        match.id is null;
--РЕАЛІЗАЦІЯ ЛОГІКИ ДЛЯ ПРОСТАВЛЕННЯ ДУБЛІКАТІВ

    create temporary table temp_dtl_duplicated_clicks as
    with
        t as (
            select distinct
                a.source_id,
                a.click_log_id,
                a.date_diff,
                s.datetime as click_datetime,
                a.country,
                a.search_id,
                a.is_duplicated,
                s.user_ip,
                s.keyword_hash,
                s.is_bot,
                s.id_traffic_source,
                s.id_region
            from
                affiliate.dtl_click_search_connection a
                join affiliate.dtl_searches s
                     on a.date_diff = s.date_diff
                         and a.search_id = s.id_search
                         and a.country = s.country
            where
                  a.date_diff = _datediff - 1
              and a.click_type = 0

            union all

            select distinct
                source_id,
                id        as click_log_id,
                date_diff,
                click_datetime,
                country,
                id_search as search_id,
                is_duplicated,
                user_ip,
                keyword_hash,
                is_bot,
                id_traffic_source,
                id_region
            from
                temp_click_search_connection temp
            where
                  is_bot = 0
              and is_foreign = 0
        ),
        t1 as (
            select
                source_id,
                click_log_id,
                date_diff,
                click_datetime,
                country,
                search_id,
                is_duplicated,
                is_bot,
                user_ip,
                keyword_hash,
                id_traffic_source,
                id_region,
                extract(epoch from (click_datetime - lag(click_datetime, 1)
                                                     over (partition by source_id, country, user_ip, keyword_hash, id_traffic_source, id_region order by click_datetime))) as diff
            from
                t
        ),
        t2 as (
            select
                source_id,
                click_log_id,
                date_diff,
                click_datetime,
                country,
                search_id,
                is_duplicated,
                is_bot,
                user_ip,
                keyword_hash,
                id_traffic_source,
                id_region,
                diff,
                case
                    when sum(diff)
                         over (partition by source_id, country, user_ip, keyword_hash, id_traffic_source, id_region
                             order by click_datetime) - (3600 * 24) >= 0
                        then 1
                    else 0
                    end as is_more_than_24h
            from
                t1
        ),
        t3 as (
            select
                t2.*,
                coalesce(is_more_than_24h -
                         lag(is_more_than_24h)
                         over (partition by source_id, country, user_ip, keyword_hash, id_traffic_source, id_region
                             order by click_datetime), 0) as is_next_day_valid_click
            from
                t2
        )
    select
        t3.*
    from
        t3
    where
          diff <= (3600 * 24)
      and is_next_day_valid_click = 0;

    update temp_click_search_connection tmp
    set
        is_duplicated = 1
    from
        temp_dtl_duplicated_clicks dc
    where
          tmp.source_id = dc.source_id
      and tmp.id = dc.click_log_id
      and (tmp.date_diff = _datediff or tmp.date_diff = _datediff - 1);

    insert into
        affiliate.dtl_click_search_connection (source_id, click_log_id, country, date_diff, search_id, is_duplicated, click_type)
    select
        t.source_id,
        t.id        as click_log_id,
        t.country,
        t.date_diff,
        t.id_search as search_id,
        t.is_duplicated,
        case
            when t.is_bot = 1 or t.id_search is null then 1
            when t.is_duplicated = 1 then 2
            when t.is_foreign = 1 then 4
            else 0
            end     as click_type
    from
        temp_click_search_connection t;

end ;

$$;

alter procedure affiliate.insert_dtl_click_search_connection(integer) owner to ypr;

