create or replace procedure affiliate.insert_task_status_log(_datediff integer)
    language plpgsql
as
$$
begin
    delete
    from affiliate.task_status_log
    where task_datetime::date = current_date - 1;


    --connect to NL
    perform dblink_connect('myconnNL',
                           'dbname=affiliate host=********* user= ''pentaho'' password= ''!faP@jhWYWb&3rwv'' options=-csearch_path=');

    insert into affiliate.task_status_log(source_id, id, id_partner, task_datetime, start_gather_datetime, job_count, id_consumer_run)
    select source_id, id, id_partner, task_datetime, start_gather_datetime, job_count, id_consumer_run
    from dblink('myconnNL',
                'with
                    status_1 as (
                        select
                            tsl.id_partner,
                            tsl.datetime + (co.offset_in_hour || ''hour'')::interval as start_gather_datetime,
                            tsl.generation_id
                        from
                            public.task_status_log tsl
                                join public.partner_settings_nl ps
                                on tsl.id_partner = ps.id
                                join public.country_offset co
                                on ps.country = co.country
                        where
                            tsl.status = 1
                        ),
                    status_3 as (
                        select
                            tsl.id,
                            tsl.id_partner,
                            tsl.status,
                            tsl.datetime + (co.offset_in_hour || ''hour'')::interval as task_datetime,
                            tsl.generation_id,
                            tsl.jobs_count                                           as job_count,
                            tsl.id_consumer_run
                        from
                            public.task_status_log tsl
                                join public.partner_settings_nl ps
                                on tsl.id_partner = ps.id
                                join public.country_offset co
                                on ps.country = co.country
                        where
                            tsl.status = 3
                            and (tsl.datetime + (co.offset_in_hour || ''hour'')::interval)::date = current_date - 1
                            and tsl.jobs_count is not null
                        )
                select
                    1 as source_id,
                    t3.id,
                    t3.id_partner,
                    t3.task_datetime,
                    t1.start_gather_datetime,
                    t3.job_count,
                    t3.id_consumer_run
                from
                    status_3 t3
                        left join status_1 t1
                        on t1.generation_id = t3.generation_id
                            and t1.start_gather_datetime <= t3.task_datetime;') AS task_status_log (
                                source_id integer,
                                id integer,
                                id_partner integer,
                                task_datetime timestamp,
                                start_gather_datetime timestamp,
                                job_count integer,
                                id_consumer_run uuid);

    perform dblink_disconnect('myconnNL');

    --connect to US
    perform dblink_connect('conUS',
                           'dbname=affiliate host=************ user= ''pentaho'' password= ''!faP@jhWYWb&3rwv'' options=-csearch_path=');

    insert into affiliate.task_status_log(source_id, id, id_partner, task_datetime, start_gather_datetime, job_count, id_consumer_run)
    select source_id, id, id_partner, task_datetime, start_gather_datetime, job_count, id_consumer_run
    from dblink('conUS',
                'with
                    status_1 as (
                        select
                            tsl.id_partner,
                            tsl.datetime + (co.offset_in_hour || ''hour'')::interval as start_gather_datetime,
                            tsl.generation_id
                        from
                            public.task_status_log tsl
                                join public.partner_settings_us ps
                                on tsl.id_partner = ps.id
                                join public.country_offset co
                                on ps.country = co.country
                        where
                            tsl.status = 1
                        ),
                    status_3 as (
                        select
                            tsl.id,
                            tsl.id_partner,
                            tsl.status,
                            tsl.datetime + (co.offset_in_hour || ''hour'')::interval as task_datetime,
                            tsl.generation_id,
                            tsl.jobs_count                                           as job_count,
                            tsl.id_consumer_run
                        from
                            public.task_status_log tsl
                                join public.partner_settings_us ps
                                on tsl.id_partner = ps.id
                                join public.country_offset co
                                on ps.country = co.country
                        where
                            tsl.status = 3
                            and (tsl.datetime + (co.offset_in_hour || ''hour'')::interval)::date = current_date - 1
                            and tsl.jobs_count is not null
                        )
                select
                    2 as source_id,
                    t3.id,
                    t3.id_partner,
                    t3.task_datetime,
                    t1.start_gather_datetime,
                    t3.job_count,
                    t3.id_consumer_run
                from
                    status_3 t3
                        left join status_1 t1
                        on t1.generation_id = t3.generation_id
                            and t1.start_gather_datetime <= t3.task_datetime;') AS task_status_log (
                                source_id integer,
                                id integer,
                                id_partner integer,
                                task_datetime timestamp,
                                start_gather_datetime timestamp,
                                job_count integer,
                                id_consumer_run uuid);

    perform dblink_disconnect('conUS');

end;

$$;

alter procedure affiliate.insert_task_status_log(integer) owner to ypr;
