create or replace procedure affiliate.insert_job_stat_agg_daily()
    language plpgsql
as
$$
begin

    drop table if exists temp_gather_cnt_data;
    drop table if exists temp_job_stat;
    drop table if exists temp_job_stat_2;

    delete
    from
        affiliate.job_stat_agg_daily
    where
         date >= current_date - 5
      or date < current_date - 91;

    create temporary table temp_gather_cnt_data as
    select
        js1.id_source,
        js1.id_partner,
        c.id                                         as id_country,
        ic.dt                                        as date,
        js1.finish_gather_date_diff                  as date_diff,
        count(distinct js1.start_gather_dt)::integer as cnt_gather
    from
        affiliate.job_stat_agg js1
        join affiliate.partner_settings ps
             on ps.source_id = js1.id_source and ps.id_local_partner = js1.id_partner
        join dimension.countries c
             on c.alpha_2::text = upper(ps.country::text)
        join dimension.info_calendar ic
             on ic.date_diff = js1.finish_gather_date_diff
    where
        ic.dt >= current_date - 5
    group by
        js1.id_source, js1.id_partner, js1.finish_gather_date_diff, c.id, ic.dt;

    create temporary table temp_job_stat as
    select
        js.id,
        t.id_country,
        t.date,
        ac.id_project,
        t.cnt_gather,
        js.click_price_usd,
        js.feed_cpc_usd,
        js.is_multiplied,
        js.id_source,
        js.id_partner,
        round(js.click_price_usd, 2) as click_price_usd_round,
        round(js.feed_cpc_usd, 2)    as feed_cpc_usd_round
    from
        affiliate.job_stat_agg js
        join temp_gather_cnt_data t
             on t.id_source = js.id_source and t.id_partner = js.id_partner and
                t.date_diff = js.finish_gather_date_diff
        left join imp.auction_campaign ac
                  on ac.country_id = t.id_country and ac.id = js.id_campaign;

    alter table temp_job_stat
        add primary key (id);

    drop table temp_gather_cnt_data;

    create temporary table temp_job_stat_2 as
    select
        tjs.id_source,
        tjs.id_partner,
        tjs.id_country,
        tjs.date,
        tjs.id_project,
        tjs.cnt_gather,
        tjs.click_price_usd_round                                                as click_price_usd,
        tjs.feed_cpc_usd_round                                                   as feed_cpc_usd,
        jsc.id_category,
        coalesce(jsc.uid_count, 0)                                               as uid_count,
        coalesce(jsc.reassembled_job_count, 0)::integer                          as reassembled_jobs_count,
        coalesce(jsc.uid_count, 0) +
        coalesce(tjs.is_multiplied::int * jsc.reassembled_job_count, 0)::integer as total_jobs_count,
        ((coalesce(jsc.uid_count, 0) + coalesce(tjs.is_multiplied::int * jsc.reassembled_job_count, 0))::numeric *
         coalesce(tjs.click_price_usd, 0))                                       as cpc_job_mult,
        ((coalesce(jsc.uid_count, 0) + coalesce(tjs.is_multiplied::int * jsc.reassembled_job_count, 0))::numeric *
         coalesce(tjs.feed_cpc_usd, 0))                                          as feed_cpc_job_mult
    from
        temp_job_stat tjs
        join affiliate.job_stat_agg_category jsc
             on tjs.id = jsc.id_job_stat_agg;

    drop table temp_job_stat;

    insert into
        affiliate.job_stat_agg_daily(date, id_source, id_partner, id_country, id_project, id_category, cnt_gather,
                                     click_price_usd, feed_cpc_usd, uid_count, reassembled_jobs_count, total_jobs_count,
                                     avg_click_price_usd, avg_feed_cpc_usd)
    select
        tjs.date,
        tjs.id_source,
        tjs.id_partner,
        tjs.id_country,
        tjs.id_project,
        tjs.id_category,
        tjs.cnt_gather,
        tjs.click_price_usd,
        tjs.feed_cpc_usd,
        sum(tjs.uid_count)                                     as uid_count,
        sum(tjs.reassembled_jobs_count)                        as reassembled_jobs_count,
        sum(tjs.total_jobs_count)                              as total_jobs_count,
        sum(tjs.cpc_job_mult) / sum(tjs.total_jobs_count)      as avg_click_price_usd,
        sum(tjs.feed_cpc_job_mult) / sum(tjs.total_jobs_count) as avg_feed_cpc_usd
    from
        temp_job_stat_2 tjs
    group by
        tjs.date,
        tjs.id_source,
        tjs.id_partner,
        tjs.id_country,
        tjs.id_project,
        tjs.id_category,
        tjs.cnt_gather,
        tjs.click_price_usd,
        tjs.feed_cpc_usd;

    drop table temp_job_stat_2;
end;

$$;

alter procedure affiliate.insert_job_stat_agg_daily(integer) owner to ypr;