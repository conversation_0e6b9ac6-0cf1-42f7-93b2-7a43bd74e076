create or replace procedure affiliate.insert_underutilised_budget_category_log(_date_diff integer)
    language plpgsql
as
$$
declare
    _date date;

begin

    _date := (SELECT dt FROM dimension.info_calendar where date_diff = _date_diff);

    delete from affiliate.underutilised_budget_category_log
    where operation_timestamp::date = _date;


    --connect to NL
    perform dblink_connect('myconnNL',
                           'dbname=affiliate host=********* user= ''pentaho'' password= ''!faP@jhWYWb&3rwv'' options=-csearch_path=');

    insert into
        affiliate.underutilised_budget_category_log(id, source_id, operation_type, operation_timestamp, country, id_project,
                                                    id_job_category, underutilised_budget, category, old_country, old_id_project,
                                                    old_id_job_category, old_underutilised_budget, old_category)
    select
        id, source_id, operation_type, operation_timestamp, country, id_project, id_job_category, underutilised_budget,
        category, old_country, old_id_project, old_id_job_category, old_underutilised_budget, old_category
    from
        dblink('myconnNL',
               format('select
                        ubcl.id,
                        1 as source_id,
                        ubcl.operation_type,
                        ubcl.operation_timestamp + co.offset_in_hour * ''1 hour''::interval as operation_timestamp,
                        ubcl.country,
                        ubcl.id_project,
                        ubcl.id_job_category,
                        ubcl.underutilised_budget,
                        ubcl.category,
                        ubcl.old_country,
                        ubcl.old_id_project,
                        ubcl.old_id_job_category,
                        ubcl.old_underutilised_budget,
                        ubcl.old_category
                    from
                        public.underutilised_budget_category_log as ubcl
                        join public.country_offset co
                               on co.country = ubcl.country
                    where
                        (ubcl.operation_timestamp + co.offset_in_hour * ''1 hour''::interval)::date = %L',_date))
                            as underutilised_budget_category_log(id                       bigint,
                                                                 source_id                smallint,
                                                                 operation_type           text,
                                                                 operation_timestamp      timestamp,
                                                                 country                  text,
                                                                 id_project               integer,
                                                                 id_job_category          integer,
                                                                 underutilised_budget     numeric,
                                                                 category                 text,
                                                                 old_country              text,
                                                                 old_id_project           integer,
                                                                 old_id_job_category      integer,
                                                                 old_underutilised_budget numeric,
                                                                 old_category             text);

    perform dblink_disconnect('myconnNL');

    --connect to US
    perform dblink_connect('myconnUS',
                           'dbname=affiliate host=************ user= ''pentaho'' password= ''!faP@jhWYWb&3rwv'' options=-csearch_path=');


    insert into
        affiliate.underutilised_budget_category_log(id, source_id, operation_type, operation_timestamp, country, id_project,
                                                    id_job_category, underutilised_budget, category, old_country, old_id_project,
                                                    old_id_job_category, old_underutilised_budget, old_category)
    select
        id, source_id, operation_type, operation_timestamp, country, id_project, id_job_category, underutilised_budget,
        category, old_country, old_id_project, old_id_job_category, old_underutilised_budget, old_category
    from
        dblink('myconnUS',
               format('select
                        ubcl.id,
                        2 as source_id,
                        ubcl.operation_type,
                        ubcl.operation_timestamp + co.offset_in_hour * ''1 hour''::interval as operation_timestamp,
                        ubcl.country,
                        ubcl.id_project,
                        ubcl.id_job_category,
                        ubcl.underutilised_budget,
                        ubcl.category,
                        ubcl.old_country,
                        ubcl.old_id_project,
                        ubcl.old_id_job_category,
                        ubcl.old_underutilised_budget,
                        ubcl.old_category
                    from
                        public.underutilised_budget_category_log as ubcl
                        join public.country_offset co
                               on co.country = ubcl.country
                    where
                        (ubcl.operation_timestamp + co.offset_in_hour * ''1 hour''::interval)::date = %L',_date))
                            as underutilised_budget_category_log(id                       bigint,
                                                                 source_id                smallint,
                                                                 operation_type           text,
                                                                 operation_timestamp      timestamp,
                                                                 country                  text,
                                                                 id_project               integer,
                                                                 id_job_category          integer,
                                                                 underutilised_budget     numeric,
                                                                 category                 text,
                                                                 old_country              text,
                                                                 old_id_project           integer,
                                                                 old_id_job_category      integer,
                                                                 old_underutilised_budget numeric,
                                                                 old_category             text);

    perform dblink_disconnect('myconnUS');


end;

$$;

alter procedure affiliate.insert_underutilised_budget_category_log(integer) owner to ypr;
