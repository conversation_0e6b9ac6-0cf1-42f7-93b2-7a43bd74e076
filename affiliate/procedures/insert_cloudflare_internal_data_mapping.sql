create or replace procedure affiliate.insert_cloudflare_internal_data_mapping(p_date_diff integer)
    language plpgsql
as
$$
declare
    _date_diff integer := p_date_diff;
begin

    delete
    from
        affiliate.cloudflare_internal_data_mapping
    where
        date_diff = _date_diff;

    with
        data as (select
                     vcd.date_diff,
                     vcd.id_country,
                     vcd.id_external_init,
                     vcd.id_external,
                     cld.id                                                                                 as cloudflare_log_data_id,
                     row_number() over (partition by cld.record_type, vcd.id_external_init, vcd.id_external order by cld.id) as row
                 from
                     affiliate.v_click_data vcd
                     join affiliate.cloudflare_log_data cld
                          on cld.id_country = vcd.id_country and
                             lower(cld.utm_medium::text) = lower(vcd.publisher::text) and
                             vcd.date_diff = cld.date_diff and vcd.user_ip::text = cld.ip::text and
                             (cld.url_title_hash is null or vcd.job_title_hash::text = cld.url_title_hash::text) and
                             (cld.url_flags is null or vcd.url_extra_flags = cld.url_flags) and
                             (cld.url_request_id is null or vcd.api_request_id::text = cld.url_request_id::text) and
                             case
                                 when vcd.redirected_to_uid is null
                                     then cld.uid_job is null or vcd.uid_job = cld.uid_job
                                 else
                                     (cld.uid_job is null or vcd.redirected_to_uid = cld.uid_job) and
                                     (cld.url_prev_uid is null or vcd.uid_job = cld.url_prev_uid)
                                 end and (cld.url_pub_params is null or vcd.publisher_sub_source is null or
                                          lower(cld.url_pub_params::text) ~~
                                          (('%'::text || lower(vcd.publisher_sub_source::text)) || '%'::text)) and
                             (cld.url_pub_params is null or vcd.publisher_click_id is null or
                              lower(cld.url_pub_params::text) ~~
                              (('%'::text || lower(vcd.publisher_click_id::text)) || '%'::text))
                 where
                     vcd.date_diff = _date_diff)
    insert
    into
        affiliate.cloudflare_internal_data_mapping(date_diff, id_country, id_external_init, id_external,
                                             cloudflare_log_data_id)
    select
        d.date_diff,
        d.id_country,
        d.id_external_init,
        d.id_external,
        d.cloudflare_log_data_id
    from
        data d
    where
        d.row = 1;

end;
$$;

alter procedure affiliate.insert_cloudflare_internal_data_mapping(integer) owner to ypr;


