create or replace procedure affiliate.insert_cpa_static_settings_log(_datediff integer)
    language plpgsql
as
$$
begin
    delete
    from
        affiliate.cpa_static_settings_log
    where
        record_datetime_local::date = current_date - 1;


    --connect to NL
    perform dblink_connect('myconnNL',
                           'dbname=affiliate host=********* user= ''pentaho'' password= ''!faP@jhWYWb&3rwv'' options=-csearch_path=');

    insert into
        affiliate.cpa_static_settings_log(id_source, id, id_partner, id_project, cpa, datetime_utc,
                                          record_datetime_local)
    select
        id_source,
        id,
        id_partner,
        id_project,
        cpa,
        datetime_utc,
        record_datetime_local
    from
        dblink('myconnNL',
               'select
                   1 as id_source,
                   cs.id,
                   cs.id_partner,
                   cs.id_project,
                   cs.cpa,
                   cs.datetime_utc,
                   cs.datetime_utc + (co.offset_in_hour || ''hour'')::interval as record_datetime
               from
                   public.cpa_static cs
                       join public.partner_settings_nl ps
                       on cs.id_partner = ps.id
                       join public.country_offset co
                       on ps.country = co.country
               where (cs.datetime_utc + (co.offset_in_hour || ''hour'')::interval)::date = current_date - 1;'
            ) as cpa_static (
                             id_source integer,
                             id bigint,
                             id_partner integer,
                             id_project integer,
                             cpa double precision,
                             datetime_utc timestamp,
                             record_datetime_local timestamp);

    perform dblink_disconnect('myconnNL');

    --connect to US
    perform dblink_connect('conUS',
                           'dbname=affiliate host=************ user= ''pentaho'' password= ''!faP@jhWYWb&3rwv'' options=-csearch_path=');

    insert into
        affiliate.cpa_static_settings_log(id_source, id, id_partner, id_project, cpa, datetime_utc,
                                          record_datetime_local)
    select
        id_source,
        id,
        id_partner,
        id_project,
        cpa,
        datetime_utc,
        record_datetime_local
    from
        dblink('conUS',
               'select
                   2 as id_source,
                   cs.id,
                   cs.id_partner,
                   cs.id_project,
                   cs.cpa,
                   cs.datetime_utc,
                   cs.datetime_utc + (co.offset_in_hour || ''hour'')::interval as record_datetime
               from
                   public.cpa_static cs
                       join public.partner_settings_us ps
                       on cs.id_partner = ps.id
                       join public.country_offset co
                       on ps.country = co.country
               where (cs.datetime_utc + (co.offset_in_hour || ''hour'')::interval)::date = current_date - 1;'
            ) as cpa_static (
                             id_source integer,
                             id bigint,
                             id_partner integer,
                             id_project integer,
                             cpa double precision,
                             datetime_utc timestamp,
                             record_datetime_local timestamp);

    perform dblink_disconnect('conUS');

end;

$$;

alter procedure affiliate.insert_cpa_static_settings_log(integer) owner to ypr;
