create or replace procedure affiliate.insert_abtest_stat_agg(_datediff integer)
    language plpgsql
as
$$
begin

    drop table if exists test_list;
    drop table if exists clicks_raw;

    create temporary table test_list as
    with
        t as (
            select distinct
                val.source_id,
                val.ab_history_id,
                val.start_dt_local,
                val.start_dt_utc,
                coalesce(val.finish_dt_utc, timezone('utc'::text, now()))                    as finish_dt_utc,
                coalesce(val.finish_dt_local, now()::timestamp)                              as finish_dt_local,
                val.id_test,
                val.iteration,
                val.partner,
                val.group_cnt,
                val.offset_in_hour,
                min(val.start_dt_local::date) over (partition by val.id_test, val.iteration) as test_min_date,
                max(coalesce(val.finish_dt_local, current_date - 1)::date)
                over (partition by val.id_test, val.iteration)                               as test_max_date
            from
                affiliate.v_abtest_list val
        )
    select
        t.source_id,
        t.ab_history_id,
        t.start_dt_local,
        t.start_dt_utc,
        t.finish_dt_utc,
        t.finish_dt_local,
        t.id_test,
        t.iteration,
        t.partner,
        t.group_cnt,
        t.offset_in_hour,
        t.test_min_date,
        t.test_max_date
    from
        t
    where
        t.test_max_date >= public.fn_get_date_from_date_diff(_datediff - 1);

    delete
    from
        affiliate.abtest_stat_agg a
        using (select distinct id_test, iteration from test_list) t
    where
          a.test_id = t.id_test
      and a.iteration = t.iteration;

    create temporary table clicks_raw as
    with
        t as (
            select
                t.id_country,
                cast(t.id_click as varchar(100))                                         as click_id,
                t.publisher,
                lower(t.country)                                                         as country_code,
                t.id_project,
                t.feed_id_project,
                t.uid_job,
                t.feed_cpc_usd,
                coalesce(t.client_cpc_usd, 0)                                            as client_cpc_usd,
                t.click_type                                                             as click_type,
                t.click_date,
                t.click_datetime,
                case when t.has_postback = 1 then t.conversion_count else 0 end          as conversion_count,
                case when t.has_postback = 1 then t.conversion_click_count else 0 end    as conversion_click_count,
                case when t.has_postback = 1 then t.conversion_revenue_usd else 0 end    as conversion_click_revenue,
                t.is_conversion_considered,
                ip.name  as project_name,
                ipr.name as feed_project_name
            from
                affiliate.v_click_data t
                left join dimension.info_project ip
                    on ip.country = t.id_country and ip.id = t.id_project
                left join dimension.info_project ipr
                    on ipr.country = t.id_country and ipr.id = t.feed_id_project
            where
                  t.publisher in (select distinct partner from test_list)
              and t.click_date between (select min(start_dt_local)::date from test_list) and (select max(finish_dt_local)::date from test_list)
              and lower(coalesce(ip.name, ipr.name, ''::text)) not like 'j-vers.%'::text
        )
    select
        tl.ab_history_id,
        tl.source_id,
        t.id_country,
        t.click_id,
        t.publisher,
        t.country_code,
        t.id_project,
        t.feed_id_project,
        cast(t.uid_job as varchar(50))                             as uid_job,
        ((t.uid_job % tl.group_cnt) + tl.group_cnt) % tl.group_cnt as test_group,
        t.feed_cpc_usd,
        t.client_cpc_usd,
        t.click_type,
        t.click_date,
        t.conversion_count,
        t.conversion_click_count,
        t.conversion_click_revenue,
        t.is_conversion_considered,
        tl.id_test,
        tl.iteration,
        tl.test_min_date,
        tl.test_max_date,
        t.project_name,
        t.feed_project_name
    from
        t
        join test_list tl
             on tl.partner = t.publisher and
                t.click_datetime between tl.start_dt_local and tl.finish_dt_local;

    with
        clicks_raw_clean as (
            select
                click_id,
                id_country,
                publisher,
                country_code,
                id_project,
                project_name,
                feed_id_project,
                feed_project_name,
                uid_job,
                c.test_group,
                tgl.is_control_group,
                feed_cpc_usd,
                client_cpc_usd,
                click_type,
                click_date,
                c.id_test,
                c.iteration,
                conversion_count,
                conversion_click_count,
                conversion_click_revenue,
                is_conversion_considered
            from
                clicks_raw c
                join affiliate.v_abtest_list tgl
                     on tgl.source_id = c.source_id and tgl.ab_history_id = c.ab_history_id
                         and tgl.partner = c.publisher
                         and tgl.test_group = c.test_group
        ),
        result as (
            select
                country_code,
                publisher,
                coalesce(feed_id_project, id_project)     as id_project,
                coalesce(feed_project_name, project_name) as project_name,
                id_test,
                iteration,
                test_group,
                is_control_group,
                click_date                                as record_date,
                'count'                                   as metric_type,
                'total click count'                       as metric_name,
                count(distinct click_id)                  as metric_numerator,
                null::numeric                             as metric_denominator
            from
                clicks_raw_clean
            group by
                country_code,
                publisher,
                id_test,
                iteration,
                test_group,
                is_control_group,
                coalesce(feed_id_project, id_project),
                coalesce(feed_project_name, project_name),
                click_date

            union all

            select
                country_code,
                publisher,
                coalesce(feed_id_project, id_project)     as id_project,
                coalesce(feed_project_name, project_name) as project_name,
                id_test,
                iteration,
                test_group,
                is_control_group,
                click_date                                as record_date,
                'count'                                   as metric_type,
                'certified click count'                   as metric_name,
                count(distinct click_id)                  as metric_numerator,
                null::numeric                             as metric_denominator
            from
                clicks_raw_clean
            where
                click_type = 'certified'
            group by
                country_code,
                publisher,
                id_test,
                iteration,
                test_group,
                is_control_group,
                coalesce(feed_id_project, id_project),
                coalesce(feed_project_name, project_name),
                click_date

            union all

            select
                country_code,
                publisher,
                coalesce(feed_id_project, id_project)                                       as id_project,
                coalesce(feed_project_name, project_name)                                   as project_name,
                id_test,
                iteration,
                test_group,
                is_control_group,
                click_date                                                                  as record_date,
                'fraction'                                                                  as metric_type,
                'free click count, %'                                                       as metric_name,
                count(distinct case when coalesce(client_cpc_usd, 0) = 0 then click_id end) as metric_numerator,
                count(distinct click_id)                                                    as metric_denominator
            from
                clicks_raw_clean
            where
                click_type = 'certified'
            group by
                country_code,
                publisher,
                id_test,
                iteration,
                test_group,
                is_control_group,
                coalesce(feed_id_project, id_project),
                coalesce(feed_project_name, project_name),
                click_date

            union all

            select
                country_code,
                publisher,
                coalesce(feed_id_project, id_project)                                 as id_project,
                coalesce(feed_project_name, project_name)                             as project_name,
                id_test,
                iteration,
                test_group,
                is_control_group,
                click_date                                                            as record_date,
                'fraction'                                                            as metric_type,
                'unpaid click count, %'                                               as metric_name,
                count(distinct case when click_type != 'certified' then click_id end) as metric_numerator,
                count(distinct click_id)                                              as metric_denominator
            from
                clicks_raw_clean
            group by
                country_code,
                publisher,
                id_test,
                iteration,
                test_group,
                is_control_group,
                coalesce(feed_id_project, id_project),
                coalesce(feed_project_name, project_name),
                click_date

            union all

            select
                country_code,
                publisher,
                coalesce(feed_id_project, id_project)     as id_project,
                coalesce(feed_project_name, project_name) as project_name,
                id_test,
                iteration,
                test_group,
                is_control_group,
                click_date                                as record_date,
                'sum'                                     as metric_type,
                'total cost sum, $'                       as metric_name,
                sum(coalesce(feed_cpc_usd, 0))            as metric_numerator,
                null::numeric                             as metric_denominator
            from
                clicks_raw_clean
            group by
                country_code,
                publisher,
                id_test,
                iteration,
                test_group,
                is_control_group,
                coalesce(feed_id_project, id_project),
                coalesce(feed_project_name, project_name),
                click_date

            union all

            select
                country_code,
                publisher,
                coalesce(feed_id_project, id_project)     as id_project,
                coalesce(feed_project_name, project_name) as project_name,
                id_test,
                iteration,
                test_group,
                is_control_group,
                click_date                                as record_date,
                'sum'                                     as metric_type,
                'certified cost sum, $'                   as metric_name,
                sum(feed_cpc_usd)                         as metric_numerator,
                null::numeric                             as metric_denominator
            from
                clicks_raw_clean
            where
                click_type = 'certified'
            group by
                country_code,
                publisher,
                id_test,
                iteration,
                test_group,
                is_control_group,
                coalesce(feed_id_project, id_project),
                coalesce(feed_project_name, project_name),
                click_date

            union all

            select
                country_code,
                publisher,
                coalesce(feed_id_project, id_project)     as id_project,
                coalesce(feed_project_name, project_name) as project_name,
                id_test,
                iteration,
                test_group,
                is_control_group,
                click_date                                as record_date,
                'average'                                 as metric_type,
                'avg certified cost per job, $'           as metric_name,
                sum(feed_cpc_usd)                         as metric_numerator,
                count(distinct uid_job)                   as metric_denominator
            from
                clicks_raw_clean
            where
                click_type = 'certified'
            group by
                country_code,
                publisher,
                id_test,
                iteration,
                test_group,
                is_control_group,
                coalesce(feed_id_project, id_project),
                coalesce(feed_project_name, project_name),
                click_date

            union all

            select
                country_code,
                publisher,
                coalesce(feed_id_project, id_project)     as id_project,
                coalesce(feed_project_name, project_name) as project_name,
                id_test,
                iteration,
                test_group,
                is_control_group,
                click_date                                as record_date,
                'average'                                 as metric_type,
                'avg cost per job, $'                     as metric_name,
                sum(coalesce(feed_cpc_usd, 0))            as metric_numerator,
                count(distinct uid_job)                   as metric_denominator
            from
                clicks_raw_clean
            group by
                country_code,
                publisher,
                id_test,
                iteration,
                test_group,
                is_control_group,
                coalesce(feed_id_project, id_project),
                coalesce(feed_project_name, project_name),
                click_date

            union all

            select
                country_code,
                publisher,
                coalesce(feed_id_project, id_project)     as id_project,
                coalesce(feed_project_name, project_name) as project_name,
                id_test,
                iteration,
                test_group,
                is_control_group,
                click_date                                as record_date,
                'average'                                 as metric_type,
                'avg certified click count per job'       as metric_name,
                count(distinct click_id)                  as metric_numerator,
                count(distinct uid_job)                   as metric_denominator
            from
                clicks_raw_clean
            where
                click_type = 'certified'
            group by
                country_code,
                publisher,
                id_test,
                iteration,
                test_group,
                is_control_group,
                coalesce(feed_id_project, id_project),
                coalesce(feed_project_name, project_name),
                click_date

            union all

            select
                country_code,
                publisher,
                coalesce(feed_id_project, id_project)     as id_project,
                coalesce(feed_project_name, project_name) as project_name,
                id_test,
                iteration,
                test_group,
                is_control_group,
                click_date                                as record_date,
                'average'                                 as metric_type,
                'avg click count per job'                 as metric_name,
                count(distinct click_id)                  as metric_numerator,
                count(distinct uid_job)                   as metric_denominator
            from
                clicks_raw_clean
            group by
                country_code,
                publisher,
                id_test,
                iteration,
                test_group,
                is_control_group,
                coalesce(feed_id_project, id_project),
                coalesce(feed_project_name, project_name),
                click_date

            union all

            select
                country_code,
                publisher,
                coalesce(feed_id_project, id_project)     as id_project,
                coalesce(feed_project_name, project_name) as project_name,
                id_test,
                iteration,
                test_group,
                is_control_group,
                click_date                                as record_date,
                'average'                                 as metric_type,
                'avg certified click CPC, $'              as metric_name,
                sum(feed_cpc_usd)                         as metric_numerator,
                count(distinct click_id)                  as metric_denominator
            from
                clicks_raw_clean
            where
                click_type = 'certified'
            group by
                country_code,
                publisher,
                id_test,
                iteration,
                test_group,
                is_control_group,
                coalesce(feed_id_project, id_project),
                coalesce(feed_project_name, project_name),
                click_date

            union all

            select
                country_code,
                publisher,
                id_project,
                project_name,
                id_test,
                iteration,
                test_group,
                is_control_group,
                click_date                       as record_date,
                'sum'                            as metric_type,
                'total revenue sum, $'           as metric_name,
                sum(coalesce(client_cpc_usd, 0)) as metric_numerator,
                null::numeric                    as metric_denominator
            from
                clicks_raw_clean
            where
                click_type = 'certified'
            group by
                country_code,
                publisher,
                id_test,
                iteration,
                test_group,
                is_control_group,
                id_project,
                project_name,
                click_date

            union all

            select
                country_code,
                publisher,
                id_project,
                project_name,
                id_test,
                iteration,
                test_group,
                is_control_group,
                click_date                       as record_date,
                'average'                        as metric_type,
                'avg click client CPC, $'        as metric_name,
                sum(coalesce(client_cpc_usd, 0)) as metric_numerator,
                count(distinct click_id)         as metric_denominator
            from
                clicks_raw_clean
            where
                click_type = 'certified'
            group by
                country_code,
                publisher,
                id_test,
                iteration,
                test_group,
                is_control_group,
                id_project,
                project_name,
                click_date

            union all

            select
                country_code,
                publisher,
                id_project,
                project_name,
                id_test,
                iteration,
                test_group,
                is_control_group,
                click_date                       as record_date,
                'average'                        as metric_type,
                'avg revenue per job, $'         as metric_name,
                sum(coalesce(client_cpc_usd, 0)) as metric_numerator,
                count(distinct uid_job)          as metric_denominator
            from
                clicks_raw_clean
            where
                click_type = 'certified'
            group by
                country_code,
                publisher,
                id_test,
                iteration,
                test_group,
                is_control_group,
                id_project,
                project_name,
                click_date

            union all

            select
                country_code,
                publisher,
                coalesce(feed_id_project, id_project)     as id_project,
                coalesce(feed_project_name, project_name) as project_name,
                id_test,
                iteration,
                test_group,
                is_control_group,
                click_date                                as record_date,
                'count'                                   as metric_type,
                'total jobs with click count'             as metric_name,
                count(distinct uid_job)                   as metric_numerator,
                null::numeric                             as metric_denominator
            from
                clicks_raw_clean
            group by
                country_code,
                publisher,
                id_test,
                iteration,
                test_group,
                is_control_group,
                coalesce(feed_id_project, id_project),
                coalesce(feed_project_name, project_name),
                click_date

            union all

            select
                country_code,
                publisher,
                coalesce(feed_id_project, id_project)     as id_project,
                coalesce(feed_project_name, project_name) as project_name,
                id_test,
                iteration,
                test_group,
                is_control_group,
                click_date                                as record_date,
                'count'                                   as metric_type,
                'jobs with certified click count'         as metric_name,
                count(distinct uid_job)                   as metric_numerator,
                null::numeric                             as metric_denominator
            from
                clicks_raw_clean
            where
                click_type = 'certified'
            group by
                country_code,
                publisher,
                id_test,
                iteration,
                test_group,
                is_control_group,
                coalesce(feed_id_project, id_project),
                coalesce(feed_project_name, project_name),
                click_date

            union all

            select
                country_code,
                publisher,
                coalesce(feed_id_project, id_project)                             as id_project,
                coalesce(feed_project_name, project_name)                         as project_name,
                id_test,
                iteration,
                test_group,
                is_control_group,
                click_date                                                        as record_date,
                'sum'                                                             as metric_type,
                'profit sum, $'                                                   as metric_name,
                sum(coalesce(client_cpc_usd, 0)) - sum(coalesce(feed_cpc_usd, 0)) as metric_numerator,
                null::numeric                                                     as metric_denominator
            from
                clicks_raw_clean
            where
                click_type = 'certified'
            group by
                country_code,
                publisher,
                id_test,
                iteration,
                test_group,
                is_control_group,
                coalesce(feed_id_project, id_project),
                coalesce(feed_project_name, project_name),
                click_date

            union all

            select
                country_code,
                publisher,
                coalesce(feed_id_project, id_project)                               as id_project,
                coalesce(feed_project_name, project_name)                           as project_name,
                id_test,
                iteration,
                test_group,
                is_control_group,
                click_date                                                          as record_date,
                'fraction'                                                          as metric_type,
                'ROMI, %'                                                           as metric_name,
                (sum(coalesce(client_cpc_usd, 0)) - sum(coalesce(feed_cpc_usd, 0))) as metric_numerator,
                sum(coalesce(feed_cpc_usd, 0))                                      as metric_denominator
            from
                clicks_raw_clean
            where
                click_type = 'certified'
            group by
                country_code,
                publisher,
                id_test,
                iteration,
                test_group,
                is_control_group,
                coalesce(feed_id_project, id_project),
                coalesce(feed_project_name, project_name),
                click_date

            union all

            select
                country_code,
                publisher,
                coalesce(feed_id_project, id_project)                               as id_project,
                coalesce(feed_project_name, project_name)                           as project_name,
                id_test,
                iteration,
                test_group,
                is_control_group,
                click_date                                                          as record_date,
                'average'                                                           as metric_type,
                'avg profit per job, $'                                             as metric_name,
                (sum(coalesce(client_cpc_usd, 0)) - sum(coalesce(feed_cpc_usd, 0))) as metric_numerator,
                count(distinct uid_job)                                             as metric_denominator
            from
                clicks_raw_clean
            where
                click_type = 'certified'
            group by
                country_code,
                publisher,
                id_test,
                iteration,
                test_group,
                is_control_group,
                coalesce(feed_id_project, id_project),
                coalesce(feed_project_name, project_name),
                click_date

            union all

            select
                country_code,
                publisher,
                coalesce(feed_id_project, id_project)                               as id_project,
                coalesce(feed_project_name, project_name)                           as project_name,
                id_test,
                iteration,
                test_group,
                is_control_group,
                click_date                                                          as record_date,
                'average'                                                           as metric_type,
                'avg profit per click, $'                                           as metric_name,
                (sum(coalesce(client_cpc_usd, 0)) - sum(coalesce(feed_cpc_usd, 0))) as metric_numerator,
                count(distinct click_id)                                            as metric_denominator
            from
                clicks_raw_clean
            where
                click_type = 'certified'
            group by
                country_code,
                publisher,
                id_test,
                iteration,
                test_group,
                is_control_group,
                coalesce(feed_id_project, id_project),
                coalesce(feed_project_name, project_name),
                click_date

            union all

            select
                country_code,
                publisher,
                id_project,
                project_name,
                id_test,
                iteration,
                test_group,
                is_control_group,
                click_date                  as record_date,
                'fraction'                  as metric_type,
                'conversion rate, %'        as metric_name,
                sum(conversion_count)       as metric_numerator,
                sum(conversion_click_count) as metric_denominator
            from
                clicks_raw_clean
            group by
                country_code,
                publisher,
                id_test,
                iteration,
                test_group,
                is_control_group,
                id_project,
                project_name,
                click_date

            union all

            select
                country_code,
                publisher,
                id_project,
                project_name,
                id_test,
                iteration,
                test_group,
                is_control_group,
                click_date                   as record_date,
                'fraction'                   as metric_type,
                'conversion rate limited, %' as metric_name,
                sum(conversion_count)        as metric_numerator,
                sum(conversion_click_count)  as metric_denominator
            from
                clicks_raw_clean
            where is_conversion_considered = 1
            group by
                country_code,
                publisher,
                id_test,
                iteration,
                test_group,
                is_control_group,
                id_project,
                project_name,
                click_date

            union all

            select
                country_code,
                publisher,
                id_project,
                project_name,
                id_test,
                iteration,
                test_group,
                is_control_group,
                click_date                  as record_date,
                'count'                     as metric_type,
                'conversion click count'    as metric_name,
                sum(conversion_click_count) as metric_numerator,
                null::numeric               as metric_denominator
            from
                clicks_raw_clean
            group by
                country_code,
                publisher,
                id_test,
                iteration,
                test_group,
                is_control_group,
                id_project,
                project_name,
                click_date

            union all

            select
                country_code,
                publisher,
                id_project,
                project_name,
                id_test,
                iteration,
                test_group,
                is_control_group,
                click_date                       as record_date,
                'count'                          as metric_type,
                'conversion click count limited' as metric_name,
                sum(conversion_click_count)      as metric_numerator,
                null::numeric                    as metric_denominator
            from
                clicks_raw_clean
            where is_conversion_considered = 1
            group by
                country_code,
                publisher,
                id_test,
                iteration,
                test_group,
                is_control_group,
                id_project,
                project_name,
                click_date

            union all

            select
                country_code,
                publisher,
                id_project,
                project_name,
                id_test,
                iteration,
                test_group,
                is_control_group,
                click_date                    as record_date,
                'average'                     as metric_type,
                'CPA, $'                      as metric_name,
                sum(conversion_click_revenue) as metric_numerator,
                sum(conversion_count)         as metric_denominator
            from
                clicks_raw_clean
            group by
                country_code,
                publisher,
                id_test,
                iteration,
                test_group,
                is_control_group,
                id_project,
                project_name,
                click_date

            union all

            select
                country_code,
                publisher,
                id_project,
                project_name,
                id_test,
                iteration,
                test_group,
                is_control_group,
                click_date                    as record_date,
                'average'                     as metric_type,
                'CPA limited, $'              as metric_name,
                sum(conversion_click_revenue) as metric_numerator,
                sum(conversion_count)         as metric_denominator
            from
                clicks_raw_clean
            where is_conversion_considered = 1
            group by
                country_code,
                publisher,
                id_test,
                iteration,
                test_group,
                is_control_group,
                id_project,
                project_name,
                click_date
        )
    insert
    into
        affiliate.abtest_stat_agg(country_code, publisher, project_id, project_name, test_id, iteration, test_group,
                                  is_control_group, record_date, metric_type, metric_name, metric_numerator,
                                  metric_denominator)
    select
        country_code,
        publisher,
        id_project,
        project_name,
        id_test,
        iteration,
        test_group,
        is_control_group,
        record_date,
        metric_type,
        metric_name,
        metric_numerator,
        metric_denominator
    from
        result;


    with
        job_stat as (
            select
                jsa.id,
                upper(ps.country)                             as country,
                ps.partner                                    as feed_name,
                jsa.feed_cpc_usd,
                jsa.start_gather_dt,
                jsa.id_campaign,
                jsaa.id_ab_history,
                jsaa.test_group,
                jsaa.uid_cnt,
                jsaa.reassembled_uid_cnt,
                jsa.is_multiplied
            from
                affiliate.job_stat_agg jsa
                join affiliate.partner_settings ps
                     on ps.source_id = jsa.id_source and ps.id_local_partner = jsa.id_partner
                join affiliate.job_stat_agg_abtest as jsaa
                     on jsaa.id_job_stat_agg = jsa.id
        ),
        test_list_cte as (
            select distinct
                val.source_id,
                val.ab_history_id,
                val.start_dt_local,
                val.start_dt_utc,
                coalesce(val.finish_dt_utc, timezone('utc'::text, now()))            as finish_dt_utc,
                coalesce(val.finish_dt_local, now()::timestamp)                      as finish_dt_local,
                val.id_test,
                val.iteration,
                val.partner,
                val.group_cnt,
                val.offset_in_hour,
                val.test_group,
                val.is_control_group,
                min(val.start_dt_local::date) over (partition by id_test, iteration) as min_date,
                max(coalesce(val.finish_dt_local, now()::timestamp)::date)
                over (partition by id_test, iteration)                               as max_date
            from
                affiliate.v_abtest_list val
        ),
        job_stat_full as (
            select
                j.country,
                feed_name,
                feed_cpc_usd,
                start_gather_dt::date as record_date,
                id_campaign,
                ac.id_project,
                ip.name                                                        as project_name,
                j.test_group,
                uid_cnt,
                id_test,
                iteration,
                is_control_group,
                coalesce(j.uid_cnt, 0) +
                    case
                        when j.is_multiplied = false then 0
                        when j.is_multiplied = true then coalesce(j.reassembled_uid_cnt, 0)
                        else 0::integer
                        end                                                    as total_jobs_count
            from
                job_stat j
                join test_list_cte t
                     on t.partner = j.feed_name and
                        t.ab_history_id = j.id_ab_history and
                        t.test_group = j.test_group and
                        t.max_date >= public.fn_get_date_from_date_diff(_datediff - 1)
                left join dimension.countries c
                          on c.alpha_2 = j.country
                left join imp.auction_campaign ac
                          on ac.country_id = c.id and ac.id = j.id_campaign
                left join dimension.info_project ip
                          on ip.country = c.id and ip.id = ac.id_project
            where
                    start_gather_dt::date < current_date
                 and lower(coalesce(ip.name, ''::text)) not like 'j-vers.%'::text
        ),
        result as (
            select
                lower(country)    as country_code,
                feed_name         as publisher,
                id_project,
                project_name,
                id_test,
                iteration,
                test_group,
                is_control_group,
                record_date,
                'count'           as metric_type,
                'feed jobs count' as metric_name,
                sum(uid_cnt)      as metric_numerator,
                null::numeric     as metric_denominator
            from
                job_stat_full
            group by
                lower(country),
                feed_name,
                id_project,
                project_name,
                id_test,
                iteration,
                test_group,
                is_control_group,
                record_date

            union all

            select
                lower(country)             as country_code,
                feed_name                  as publisher,
                id_project,
                project_name,
                id_test,
                iteration,
                test_group,
                is_control_group,
                record_date,
                'count'                    as metric_type,
                'total feed jobs count'    as metric_name,
                sum(total_jobs_count)      as metric_numerator,
                null::numeric              as metric_denominator
            from
                job_stat_full
            group by
                lower(country),
                feed_name,
                id_project,
                project_name,
                id_test,
                iteration,
                test_group,
                is_control_group,
                record_date

            union all

            select
                lower(country)              as country_code,
                feed_name                   as publisher,
                id_project,
                project_name,
                id_test,
                iteration,
                test_group,
                is_control_group,
                record_date,
                'average'                   as metric_type,
                'avg job feed CPC, $'       as metric_name,
                sum(uid_cnt * feed_cpc_usd) as metric_numerator,
                sum(uid_cnt)                as metric_denominator
            from
                job_stat_full
            group by
                lower(country),
                feed_name,
                id_project,
                project_name,
                id_test,
                iteration,
                test_group,
                is_control_group,
                record_date

            union all

            select
                lower(country)                       as country_code,
                feed_name                            as publisher,
                id_project,
                project_name,
                id_test,
                iteration,
                test_group,
                is_control_group,
                record_date,
                'average'                            as metric_type,
                'avg total job feed CPC, $'          as metric_name,
                sum(total_jobs_count * feed_cpc_usd) as metric_numerator,
                sum(total_jobs_count)                as metric_denominator
            from
                job_stat_full
            group by
                lower(country),
                feed_name,
                id_project,
                project_name,
                id_test,
                iteration,
                test_group,
                is_control_group,
                record_date
        )
    insert
    into
        affiliate.abtest_stat_agg(country_code, publisher, project_id, project_name, test_id, iteration, test_group,
                                  is_control_group, record_date, metric_type, metric_name, metric_numerator,
                                  metric_denominator)
    select
        country_code,
        publisher,
        id_project,
        project_name,
        id_test,
        iteration,
        test_group,
        is_control_group,
        record_date,
        metric_type,
        metric_name,
        metric_numerator,
        metric_denominator
    from
        result;


end;

$$;

alter procedure affiliate.insert_abtest_stat_agg(integer) owner to ypr;

