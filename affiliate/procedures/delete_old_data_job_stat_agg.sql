create or replace procedure affiliate.delete_old_data_job_stat_agg(_datediff integer)
    language plpgsql
as
$$
begin

    create temporary table id_to_del as
    select id
    from affiliate.job_stat_agg jsa
    where jsa.start_gather_date_diff <= (_datediff - 210);

    delete from affiliate.job_stat_agg_category jsac
    using id_to_del
    where id_to_del.id = jsac.id_job_stat_agg;

    delete from affiliate.job_stat_agg_abtest jsaa
    using id_to_del
    where id_to_del.id = jsaa.id_job_stat_agg;

    delete from affiliate.job_stat_agg jsa
    using id_to_del
    where id_to_del.id = jsa.id;
end;

$$;

alter procedure affiliate.delete_old_data_job_stat_agg(integer) owner to ypr;
