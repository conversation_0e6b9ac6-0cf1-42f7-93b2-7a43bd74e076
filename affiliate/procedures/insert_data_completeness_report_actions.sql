create or replace procedure affiliate.insert_data_completeness_report_actions()
    language plpgsql
as
$$
begin
    drop table if exists temp_result;
    drop table if exists dic_cluster_source_id;
    drop table if exists temp_country_stat;
    drop table if exists temp_agg_metrics;
    drop table if exists ab_max_dt;
    drop table if exists temp_vmma;
    drop table if exists stat_daily_agg;
    drop table if exists temp_ab_metrics;

    create temporary table dic_cluster_source_id
    (
        source_id integer,
        cluster   varchar
    );
    insert into dic_cluster_source_id (source_id, cluster)
    values
        (1, 'nl'),
        (2, 'us'),
        (0, 'all');

    create temporary table temp_result
    (
        schema                   varchar,
        table_name               varchar,
        record_date              date,
        dimension_name           varchar,
        dimension_value          varchar,
        metric_name              varchar,
        metric_value             numeric,
        reference_metric_value   numeric,
        reference_source         varchar,
        check_type               varchar,
        is_incomplete            boolean,
        max_acceptable_deviation varchar
    );

    --------------------------------------------------------------------------------------------------------------------
    -- Perform most recent date check for tables with click statistics.
    --------------------------------------------------------------------------------------------------------------------

    insert into
        temp_result(schema, table_name, record_date, dimension_name, dimension_value, metric_name, metric_value,
                    reference_metric_value, reference_source, check_type, is_incomplete, max_acceptable_deviation)
    select
        'affiliate'                                                     as schema,
        'partner_daily_snapshot'                                        as table_name,
        current_date - 1                                                as record_date,
        null::varchar                                                   as dimension_name,
        null::varchar                                                   as dimension_value,
        null::varchar                                                   as metric_name,
        max(pds.date_diff)                                              as metric_value,
        public.fn_get_date_diff(current_date) - 1                       as reference_metric_value,
        null                                                            as reference_source,
        'max date in table'                                             as check_type,
        max(pds.date_diff) != public.fn_get_date_diff(current_date) - 1 as is_incomplete,
        '0'                                                             as max_acceptable_deviation
    from
        affiliate.partner_daily_snapshot pds;

    insert into
        temp_result(schema, table_name, record_date, dimension_name, dimension_value, metric_name, metric_value,
                    reference_metric_value, reference_source, check_type, is_incomplete, max_acceptable_deviation)
    select
        'affiliate'                                                     as schema,
        'statistics_daily_raw'                                          as table_name,
        current_date - 1                                                as record_date,
        null::varchar                                                   as dimension_name,
        null::varchar                                                   as dimension_value,
        null::varchar                                                   as metric_name,
        max(sdr.date_diff)                                              as metric_value,
        public.fn_get_date_diff(current_date) - 1                       as reference_metric_value,
        null                                                            as reference_source,
        'max date in table'                                             as check_type,
        max(sdr.date_diff) != public.fn_get_date_diff(current_date) - 1 as is_incomplete,
        '0'                                                             as max_acceptable_deviation
    from
        affiliate.statistics_daily_raw sdr;

    insert into
        temp_result(schema, table_name, record_date, dimension_name, dimension_value, metric_name, metric_value,
                    reference_metric_value, reference_source, check_type, is_incomplete, max_acceptable_deviation)
    select
        'affiliate'                                                     as schema,
        'statistics_daily_agg'                                          as table_name,
        current_date - 1                                                as record_date,
        null::varchar                                                   as dimension_name,
        null::varchar                                                   as dimension_value,
        null::varchar                                                   as metric_name,
        max(sda.date_diff)                                              as metric_value,
        public.fn_get_date_diff(current_date) - 1                       as reference_metric_value,
        null                                                            as reference_source,
        'max date in table'                                             as check_type,
        max(sda.date_diff) != public.fn_get_date_diff(current_date) - 1 as is_incomplete,
        '0'                                                             as max_acceptable_deviation
    from
        affiliate.statistics_daily_agg sda;

    insert into
        temp_result(schema, table_name, record_date, dimension_name, dimension_value, metric_name, metric_value,
                    reference_metric_value, reference_source, check_type, is_incomplete, max_acceptable_deviation)
    select
        'affiliate'                                                                            as schema,
        'prediction_features_v2'                                                               as table_name,
        current_date                                                                           as record_date,
        null::varchar                                                                          as dimension_name,
        null::varchar                                                                          as dimension_value,
        null::varchar                                                                          as metric_name,
        public.fn_get_date_diff(max(pf2.record_date))                                          as metric_value,
        public.fn_get_date_diff(current_date)                                                  as reference_metric_value,
        null                                                                                   as reference_source,
        'max date in table'                                                                    as check_type,
        public.fn_get_date_diff(max(pf2.record_date)) != public.fn_get_date_diff(current_date) as is_incomplete,
        '0'                                                                                    as max_acceptable_deviation
    from
        affiliate.prediction_features_v2 pf2;

    insert into
        temp_result(schema, table_name, record_date, dimension_name, dimension_value, metric_name, metric_value,
                    reference_metric_value, reference_source, check_type, is_incomplete, max_acceptable_deviation)
    select
        'affiliate'                                                                             as schema,
        'cloudflare_log_data'                                                                   as table_name,
        current_date                                                                            as record_date,
        null::varchar                                                                           as dimension_name,
        null::varchar                                                                           as dimension_value,
        null::varchar                                                                           as metric_name,
        public.fn_get_date_diff(max(cld.log_date))                                              as metric_value,
        public.fn_get_date_diff(current_date) - 1                                               as reference_metric_value,
        null                                                                                    as reference_source,
        'max date in table'                                                                     as check_type,
        public.fn_get_date_diff(max(cld.log_date)) != public.fn_get_date_diff(current_date) - 1 as is_incomplete,
        '0'                                                                                     as max_acceptable_deviation
    from
        affiliate.cloudflare_log_data cld;

    --------------------------------------------------------------------------------------------------------------------
    -- Perform most recent date check for tables with ab tests.
    --------------------------------------------------------------------------------------------------------------------

    create temporary table ab_max_dt as
    select
        max(case
                when v_abtest_list.finish_dt_local > current_date - 1 then current_date - 1
                else coalesce(v_abtest_list.finish_dt_local, current_date - 1) end)::date as max_dt
    from
        affiliate.v_abtest_list;

    insert
    into
        temp_result(schema, table_name, record_date, dimension_name, dimension_value, metric_name, metric_value,
                    reference_metric_value, reference_source, check_type, is_incomplete, max_acceptable_deviation)
    select
        'affiliate'                                                            as schema,
        'abtest_stat_agg'                                                      as table_name,
        current_date                                                           as record_date,
        null::varchar                                                          as dimension_name,
        null::varchar                                                          as dimension_value,
        null::varchar                                                          as metric_name,
        public.fn_get_date_diff(max(asa.record_date)::date)                    as metric_value,
        public.fn_get_date_diff((select max(ab_max_dt.max_dt) from ab_max_dt)) as reference_metric_value,
        'affiliate.v_abtest_list'                                              as reference_source,
        'max date in table'                                                    as check_type,
        public.fn_get_date_diff((select max(ab_max_dt.max_dt) from ab_max_dt)) !=
        public.fn_get_date_diff(max(asa.record_date)::date)                    as is_incomplete,
        '0'                                                                    as max_acceptable_deviation
    from
        affiliate.abtest_stat_agg asa;

    insert
    into
        temp_result(schema, table_name, record_date, dimension_name, dimension_value, metric_name, metric_value,
                    reference_metric_value, reference_source, check_type, is_incomplete, max_acceptable_deviation)
    select
        'affiliate'                                                            as schema,
        'abtest_significance_metrics'                                          as table_name,
        current_date                                                           as record_date,
        null::varchar                                                          as dimension_name,
        null::varchar                                                          as dimension_value,
        null::varchar                                                          as metric_name,
        public.fn_get_date_diff(max(asm.date_end)::date)                       as metric_value,
        public.fn_get_date_diff((select max(ab_max_dt.max_dt) from ab_max_dt)) as reference_metric_value,
        'affiliate.v_abtest_list'                                              as reference_source,
        'max date in table'                                                    as check_type,
        public.fn_get_date_diff((select max(ab_max_dt.max_dt) from ab_max_dt)) !=
        public.fn_get_date_diff(max(asm.date_end)::date)                       as is_incomplete,
        '0'                                                                    as max_acceptable_deviation
    from
        affiliate.abtest_significance_metrics asm;

    --------------------------------------------------------------------------------------------------------------------
    -- Check that yesterday clicks after 23:30 are available for top countries.
    --------------------------------------------------------------------------------------------------------------------

    create temporary table temp_country_stat as
    select
        c.alpha_2               as country,
        sum(sdr.revenue_usd)    as revenue_usd,
        max(sdr.click_datetime) as max_click_dt
    from
        affiliate.statistics_daily_raw sdr
        join dimension.countries c
             on c.id = sdr.id_country
    where
        sdr.date_diff = public.fn_get_date_diff(current_date) - 1
    group by
        c.alpha_2;

    insert into
        temp_result(schema, table_name, record_date, dimension_name, dimension_value, metric_name, metric_value,
                    reference_metric_value, reference_source, check_type, is_incomplete, max_acceptable_deviation)
    select
        'affiliate'                                 as schema,
        'statistics_daily_raw'                      as table_name,
        current_date - 1                            as record_date,
        'country'                                   as dimension_name,
        tcs.country                                 as dimension_value,
        'max click datetime'                        as metric_name,
        extract(epoch from tcs.max_click_dt)        as metric_value,
        extract(epoch from current_date::timestamp) as reference_metric_value,
        null                                        as reference_source,
        'metric value comparison'                   as check_type,
        (extract(epoch from current_date::timestamp) - extract(epoch from tcs.max_click_dt)) >
        1800                                        as is_incomplete,
        '1800'                                      as max_acceptable_deviation
    from
        temp_country_stat tcs
    where
        tcs.country in ('US', 'CA', 'UK', 'DE', 'FR', 'NL');

    --------------------------------------------------------------------------------------------------------------------
    -- Countries with affiliate revenue for yesterday in aggregation.adv_revenue_by_placement_and_src_analytics are
    -- present in affiliate.statistics_daily_raw.
    --------------------------------------------------------------------------------------------------------------------

    insert into
        temp_result(schema, table_name, record_date, dimension_name, dimension_value, metric_name, metric_value,
                    reference_metric_value, reference_source, check_type, is_incomplete, max_acceptable_deviation)
    with
        adv_rev as (
            select
                c.alpha_2                as country,
                sum(arbpasa.revenue_usd) as revenue_usd
            from
                aggregation.adv_revenue_by_placement_and_src_analytics arbpasa
                join dimension.countries c
                     on c.id = arbpasa.country_id
            where
                  arbpasa.placement in ('ad exchange', 'external')
              and arbpasa.date_diff = public.fn_get_date_diff(current_date) - 1
            group by
                c.alpha_2
            having
                sum(arbpasa.revenue_usd) > 0
        )
    select
        'affiliate'                                              as schema,
        'statistics_daily_raw'                                   as table_name,
        current_date - 1                                         as record_date,
        'country'                                                as dimension_name,
        adv_rev.country                                          as dimension_value,
        'affiliate revenue'                                      as metric_name,
        tcs.revenue_usd                                          as metric_value,
        adv_rev.revenue_usd                                      as reference_metric_value,
        'aggregation.adv_revenue_by_placement_and_src_analytics' as reference_source,
        'metric value comparison'                                as check_type,
        coalesce(tcs.revenue_usd, 0) = 0                         as is_incomplete,
        '99%'                                                    as max_acceptable_deviation
    from
        adv_rev
        left join temp_country_stat tcs
                  on tcs.country = adv_rev.country;

    --------------------------------------------------------------------------------------------------------------------
    -- Perform metric value check for tables recorded using other tables in DWH.
    --------------------------------------------------------------------------------------------------------------------

    with
        original_table as (
            select
                d.dt               as date,
                sum(j.revenue_usd) as revenue_usd
            from
                aggregation.click_data_agg j
                join dimension.countries c
                     on c.id = j.country_id
                join dimension.info_calendar d
                     on j.action_datediff = d.date_diff
                join dimension.u_traffic_source ts
                     on ts.country = j.country_id and ts.id = j.id_current_traf_source
            where
                  ts.channel::text = 'Affiliate'::text
              and d.dt between current_date - 7 and current_date - 2
              and j.placement not in ('external', 'ad exchange')
            group by
                d.dt
        ),
        recorded_data as (
            select
                date,
                sum(revenue_usd) as revenue_usd
            from
                affiliate.additional_revenue_agg
            where
                date between current_date - 7 and current_date - 2
            group by date
        )
    insert
    into
        temp_result(schema, table_name, record_date, dimension_name, dimension_value, metric_name, metric_value,
                    reference_metric_value, reference_source, check_type, is_incomplete, max_acceptable_deviation)
    select
        'affiliate'                                                                            as schema,
        'additional_revenue_agg'                                                               as table_name,
        dt::date                                                                               as record_date,
        null::varchar                                                                          as dimension_name,
        null::varchar                                                                          as dimension_value,
        'revenue_usd'                                                                          as metric_name,
        coalesce(r.revenue_usd, 0)                                                             as metric_value,
        coalesce(o.revenue_usd, 0)                                                             as reference_metric_value,
        'aggregation.click_data_agg'                                                           as reference_source,
        'metric value comparison'                                                              as check_type,
        case
            when coalesce(r.revenue_usd, 0) = 0 then coalesce(o.revenue_usd, 0) > 0
            else abs(1 - (coalesce(o.revenue_usd, 0) / coalesce(r.revenue_usd, 0))) > 0.01 end as is_incomplete,
        '1%'                                                                                   as max_acceptable_deviation
    from
        generate_series(current_date - 7, current_date - 2, '1 day'::interval) dt
        left join original_table o
                  on dt = o.date
        left join recorded_data r
                  on dt = r.date;

    --------------------------------------------------------------------------------------------------------------------
    -- Compare certified clicks, cost, revenue in different views and tables to affiliate.v_click_data.
    --------------------------------------------------------------------------------------------------------------------

    create temporary table temp_agg_metrics as
    select
        v_click_data.click_date,
        sum(case
                when v_click_data.click_type = 'certified' and v_click_data.feed_cpa_usd is not null
                    then v_click_data.feed_cpa_usd * coalesce(v_click_data.conversion_count, 0)
                when v_click_data.click_type = 'certified' then v_click_data.feed_cpc_usd end) as certified_cost_usd,
        sum(case when v_click_data.click_type = 'certified' then 1 end)                        as certified_click_cnt,
        sum(v_click_data.client_cpc_usd)                                                       as revenue_usd,
        sum(case when v_click_data.has_postback = 1 then v_click_data.conversion_count end)    as conversion_cnt,
        sum(case when v_click_data.has_postback = 1 then conversion_revenue_usd end)           as conversion_revenue_usd,
        sum(case when v_click_data.has_postback = 1 then conversion_click_count end)           as conversion_away_cnt
    from
        affiliate.v_click_data
    where
        v_click_data.click_date >= current_date - 7
    group by
        v_click_data.click_date;

    create temporary table temp_vmma as
    select
        v_main_metrics_agg.click_date,
        sum(v_main_metrics_agg.certified_cost_usd)     as certified_cost_usd,
        sum(v_main_metrics_agg.certified_click_cnt)    as certified_click_cnt,
        sum(v_main_metrics_agg.revenue_usd)            as revenue_usd,
        sum(v_main_metrics_agg.conversion_cnt)         as conversion_cnt,
        sum(v_main_metrics_agg.conversion_revenue_usd) as conversion_revenue_usd,
        sum(v_main_metrics_agg.conversion_away_cnt)    as conversion_away_cnt
    from
        affiliate.v_main_metrics_agg
    where
        v_main_metrics_agg.click_date >= current_date - 7
    group by
        v_main_metrics_agg.click_date;

    insert
    into
        temp_result(schema, table_name, record_date, dimension_name, dimension_value, metric_name, metric_value,
                    reference_metric_value, reference_source, check_type, is_incomplete, max_acceptable_deviation)
    select
        'affiliate'                                                 as schema,
        'v_main_metrics_agg'                                        as table_name,
        t.click_date                                                as record_date,
        null::varchar                                               as dimension_name,
        null::varchar                                               as dimension_value,
        'certified_click_cnt'                                       as metric_name,
        coalesce(v.certified_click_cnt, 0)                          as metric_value,
        coalesce(t.certified_click_cnt, 0)                          as reference_metric_value,
        'affiliate.v_click_data'                                    as reference_source,
        'metric value comparison'                                   as check_type,
        case
            when coalesce(t.certified_click_cnt, 0) = 0 then false
            else v.certified_click_cnt != t.certified_click_cnt end as is_incomplete,
        '0'                                                         as max_acceptable_deviation
    from
        temp_agg_metrics t
        left join temp_vmma v
                  on t.click_date = v.click_date;

    insert
    into
        temp_result(schema, table_name, record_date, dimension_name, dimension_value, metric_name, metric_value,
                    reference_metric_value, reference_source, check_type, is_incomplete, max_acceptable_deviation)
    select
        'affiliate'                                            as schema,
        'v_main_metrics_agg'                                   as table_name,
        t.click_date                                           as record_date,
        null::varchar                                          as dimension_name,
        null::varchar                                          as dimension_value,
        'certified_cost_usd'                                   as metric_name,
        coalesce(v.certified_cost_usd, 0)                      as metric_value,
        coalesce(t.certified_cost_usd, 0)                      as reference_metric_value,
        'affiliate.v_click_data'                               as reference_source,
        'metric value comparison'                              as check_type,
        case
            when coalesce(t.certified_cost_usd, 0) = 0 then false
            else abs(coalesce(v.certified_cost_usd, 0) - coalesce(t.certified_cost_usd, 0)) /
                 coalesce(t.certified_cost_usd, 0) > 0.001 end as is_incomplete,
        '0.1%'                                                 as max_acceptable_deviation
    from
        temp_agg_metrics t
        left join temp_vmma v
                  on t.click_date = v.click_date;

    insert
    into
        temp_result(schema, table_name, record_date, dimension_name, dimension_value, metric_name, metric_value,
                    reference_metric_value, reference_source, check_type, is_incomplete, max_acceptable_deviation)
    select
        'affiliate'                                     as schema,
        'v_main_metrics_agg'                            as table_name,
        t.click_date                                    as record_date,
        null::varchar                                   as dimension_name,
        null::varchar                                   as dimension_value,
        'revenue_usd'                                   as metric_name,
        coalesce(v.revenue_usd, 0)                      as metric_value,
        coalesce(t.revenue_usd, 0)                      as reference_metric_value,
        'affiliate.v_click_data'                        as reference_source,
        'metric value comparison'                       as check_type,
        case
            when coalesce(t.revenue_usd, 0) = 0 then false
            else abs(coalesce(v.revenue_usd, 0) - coalesce(t.revenue_usd, 0)) /
                 coalesce(t.revenue_usd, 0) > 0.001 end as is_incomplete,
        '0.1%'                                          as max_acceptable_deviation
    from
        temp_agg_metrics t
        left join temp_vmma v
                  on t.click_date = v.click_date;

    insert
    into
        temp_result(schema, table_name, record_date, dimension_name, dimension_value, metric_name, metric_value,
                    reference_metric_value, reference_source, check_type, is_incomplete, max_acceptable_deviation)
    select
        'affiliate'                                       as schema,
        'v_main_metrics_agg'                              as table_name,
        t.click_date                                      as record_date,
        null::varchar                                     as dimension_name,
        null::varchar                                     as dimension_value,
        'conversion_cnt'                                  as metric_name,
        coalesce(v.conversion_cnt, 0)                     as metric_value,
        coalesce(t.conversion_cnt, 0)                     as reference_metric_value,
        'affiliate.v_click_data'                          as reference_source,
        'metric value comparison'                         as check_type,
        case
            when coalesce(t.conversion_cnt, 0) = 0 then false
            else v.conversion_cnt != t.conversion_cnt end as is_incomplete,
        '0'                                               as max_acceptable_deviation
    from
        temp_agg_metrics t
        left join temp_vmma v
                  on t.click_date = v.click_date;

    insert
    into
        temp_result(schema, table_name, record_date, dimension_name, dimension_value, metric_name, metric_value,
                    reference_metric_value, reference_source, check_type, is_incomplete, max_acceptable_deviation)
    select
        'affiliate'                                                as schema,
        'v_main_metrics_agg'                                       as table_name,
        t.click_date                                               as record_date,
        null::varchar                                              as dimension_name,
        null::varchar                                              as dimension_value,
        'conversion_revenue_usd'                                   as metric_name,
        coalesce(v.conversion_revenue_usd, 0)                      as metric_value,
        coalesce(t.conversion_revenue_usd, 0)                      as reference_metric_value,
        'affiliate.v_click_data'                                   as reference_source,
        'metric value comparison'                                  as check_type,
        case
            when coalesce(t.conversion_revenue_usd, 0) = 0 then false
            else abs(coalesce(v.conversion_revenue_usd, 0) - coalesce(t.conversion_revenue_usd, 0)) /
                 coalesce(t.conversion_revenue_usd, 0) > 0.001 end as is_incomplete,
        '0.1%'                                                     as max_acceptable_deviation
    from
        temp_agg_metrics t
        left join temp_vmma v
                  on t.click_date = v.click_date;

    insert
    into
        temp_result(schema, table_name, record_date, dimension_name, dimension_value, metric_name, metric_value,
                    reference_metric_value, reference_source, check_type, is_incomplete, max_acceptable_deviation)
    select
        'affiliate'                                                 as schema,
        'v_main_metrics_agg'                                        as table_name,
        t.click_date                                                as record_date,
        null::varchar                                               as dimension_name,
        null::varchar                                               as dimension_value,
        'conversion_away_cnt'                                       as metric_name,
        coalesce(v.conversion_away_cnt, 0)                          as metric_value,
        coalesce(t.conversion_away_cnt, 0)                          as reference_metric_value,
        'affiliate.v_click_data'                                    as reference_source,
        'metric value comparison'                                   as check_type,
        case
            when coalesce(t.conversion_away_cnt, 0) = 0 then false
            else v.conversion_away_cnt != t.conversion_away_cnt end as is_incomplete,
        '0'                                                         as max_acceptable_deviation
    from
        temp_agg_metrics t
        left join temp_vmma v
                  on t.click_date = v.click_date;

    with
        fin_report as (
            select
                finance_report.date,
                sum(finance_report.cost_usd) as cost_usd
            from
                aggregation.finance_report
            where
                  finance_report.date >= current_date - 7
              and finance_report.cost_type in ('Affiliate', 'J-Vers')
            group by
                finance_report.date
        )
    insert
    into
        temp_result(schema, table_name, record_date, dimension_name, dimension_value, metric_name, metric_value,
                    reference_metric_value, reference_source, check_type, is_incomplete, max_acceptable_deviation)
    select
        'aggregation'                                          as schema,
        'finance_report'                                       as table_name,
        t.click_date                                           as record_date,
        null::varchar                                          as dimension_name,
        null::varchar                                          as dimension_value,
        'certified_cost_usd'                                   as metric_name,
        coalesce(fr.cost_usd, 0)                               as metric_value,
        coalesce(t.certified_cost_usd, 0)                      as reference_metric_value,
        'affiliate.v_click_data'                               as reference_source,
        'metric value comparison'                              as check_type,
        case
            when coalesce(t.certified_cost_usd, 0) = 0 then false
            else abs(coalesce(fr.cost_usd, 0) - coalesce(t.certified_cost_usd, 0)) /
                 coalesce(t.certified_cost_usd, 0) > 0.001 end as is_incomplete,
        '0.1%'                                                 as max_acceptable_deviation
    from
        temp_agg_metrics t
        left join fin_report fr
                  on t.click_date = fr.date;

    create temporary table stat_daily_agg as
    select
        vsda.date                                                                     as click_date,
        sum(case
                when vsda.feed_cpa_usd is not null and vsda.click_type = 'certified'
                    then (coalesce(vsda.jdp_away_conversion_cnt, 0) + coalesce(vsda.away_conversion_cnt, 0)
                         + coalesce(vsda.apply_cnt, 0)) * vsda.feed_cpa_usd
                when vsda.click_type = 'certified'
                    then vsda.external_click_cnt * vsda.feed_cpc_usd end)             as certified_cost_usd,
        sum(case when vsda.click_type = 'certified' then vsda.external_click_cnt end) as certified_click_cnt,
        sum(case
                when vsda.is_bot = 0 and vsda.is_internal_duplicated = 0 and vsda.is_external_stat_client = 1
                    then
                        vsda.client_cpc_usd *
                        (coalesce(vsda.away_click_cnt, 0) + coalesce(vsda.jdp_click_cnt, 0))
                when vsda.is_bot = 0 and vsda.is_internal_duplicated = 0 and vsda.is_external_stat_client = 0
                    then
                        vsda.client_cpc_usd *
                        (coalesce(vsda.away_click_cnt, 0) +
                         coalesce(vsda.jdp_away_click_cnt, 0)) end)                   as revenue_usd,
        sum(case
                when vsda.has_postback = 1 and vsda.is_bot = 0 and vsda.is_internal_duplicated = 0 and
                     vsda.is_external_stat_client = 1 and vsda.tracking_type = 'Postback'
                    then
                        vsda.client_cpc_usd *
                        (coalesce(vsda.away_click_cnt, 0) + coalesce(vsda.jdp_click_cnt, 0))
                when vsda.has_postback = 1 and vsda.is_bot = 0 and vsda.is_internal_duplicated = 0 and
                     vsda.is_external_stat_client = 0 and vsda.tracking_type = 'Postback'
                    then
                        vsda.client_cpc_usd *
                        (coalesce(vsda.away_click_cnt, 0) +
                         coalesce(vsda.jdp_away_click_cnt, 0))
                when vsda.has_postback = 1 and vsda.is_bot = 0 and vsda.is_internal_duplicated = 0 and
                     vsda.tracking_type <> 'Postback'
                    then vsda.client_cpc_usd * coalesce(vsda.jdp_click_cnt, 0)
            end)                                                                      as conversion_revenue_usd,
        sum(case
                when vsda.has_postback = 1 and vsda.is_bot = 0 and vsda.tracking_type = 'Postback' then
                            coalesce(vsda.jdp_away_click_cnt, 0) + coalesce(vsda.away_click_cnt, 0) -
                            coalesce(vsda.non_local_away_attempt_cnt, 0)
                when vsda.has_postback = 1 and vsda.is_bot = 0 and vsda.tracking_type <> 'Postback' then
                    coalesce(vsda.jdp_click_cnt, 0)
            end)                                                                      as conversion_away_cnt,
        sum(case
                when vsda.has_postback = 1 and vsda.is_bot = 0 and vsda.tracking_type = 'Postback' then
                        coalesce(vsda.away_conversion_cnt, 0) + coalesce(vsda.jdp_away_conversion_cnt, 0)
                when vsda.has_postback = 1 and vsda.is_bot = 0 and vsda.tracking_type <> 'Postback'
                    then coalesce(vsda.apply_cnt, 0)
            end)                                                                      as conversion_cnt
    from
        affiliate.v_statistics_daily_agg vsda
    where
        vsda.date >= current_date - 7
    group by
        vsda.date;

    insert
    into
        temp_result(schema, table_name, record_date, dimension_name, dimension_value, metric_name, metric_value,
                    reference_metric_value, reference_source, check_type, is_incomplete, max_acceptable_deviation)
    select
        'affiliate'                                                 as schema,
        'v_statistics_daily_agg'                                    as table_name,
        t.click_date                                                as record_date,
        null::varchar                                               as dimension_name,
        null::varchar                                               as dimension_value,
        'certified_click_cnt'                                       as metric_name,
        coalesce(s.certified_click_cnt, 0)                          as metric_value,
        coalesce(t.certified_click_cnt, 0)                          as reference_metric_value,
        'affiliate.v_click_data'                                    as reference_source,
        'metric value comparison'                                   as check_type,
        case
            when coalesce(t.certified_click_cnt, 0) = 0 then false
            else s.certified_click_cnt != t.certified_click_cnt end as is_incomplete,
        '0'                                                         as max_acceptable_deviation
    from
        temp_agg_metrics t
        left join stat_daily_agg s
                  on t.click_date = s.click_date;

    insert
    into
        temp_result(schema, table_name, record_date, dimension_name, dimension_value, metric_name, metric_value,
                    reference_metric_value, reference_source, check_type, is_incomplete, max_acceptable_deviation)
    select
        'affiliate'                                            as schema,
        'v_statistics_daily_agg'                               as table_name,
        t.click_date                                           as record_date,
        null::varchar                                          as dimension_name,
        null::varchar                                          as dimension_value,
        'certified_cost_usd'                                   as metric_name,
        coalesce(s.certified_cost_usd, 0)                      as metric_value,
        coalesce(t.certified_cost_usd, 0)                      as reference_metric_value,
        'affiliate.v_click_data'                               as reference_source,
        'metric value comparison'                              as check_type,
        case
            when coalesce(t.certified_cost_usd, 0) = 0 then false
            else abs(coalesce(s.certified_cost_usd, 0) - coalesce(t.certified_cost_usd, 0)) /
                 coalesce(t.certified_cost_usd, 0) > 0.001 end as is_incomplete,
        '0.1%'                                                 as max_acceptable_deviation
    from
        temp_agg_metrics t
        left join stat_daily_agg s
                  on t.click_date = s.click_date;

    insert
    into
        temp_result(schema, table_name, record_date, dimension_name, dimension_value, metric_name, metric_value,
                    reference_metric_value, reference_source, check_type, is_incomplete, max_acceptable_deviation)
    select
        'affiliate'                                     as schema,
        'v_statistics_daily_agg'                        as table_name,
        t.click_date                                    as record_date,
        null::varchar                                   as dimension_name,
        null::varchar                                   as dimension_value,
        'revenue_usd'                                   as metric_name,
        coalesce(s.revenue_usd, 0)                      as metric_value,
        coalesce(t.revenue_usd, 0)                      as reference_metric_value,
        'affiliate.v_click_data'                        as reference_source,
        'metric value comparison'                       as check_type,
        case
            when coalesce(t.revenue_usd, 0) = 0 then false
            else abs(coalesce(s.revenue_usd, 0) - coalesce(t.revenue_usd, 0)) /
                 coalesce(t.revenue_usd, 0) > 0.001 end as is_incomplete,
        '0.1%'                                          as max_acceptable_deviation
    from
        temp_agg_metrics t
        left join stat_daily_agg s
                  on t.click_date = s.click_date;

    insert
    into
        temp_result(schema, table_name, record_date, dimension_name, dimension_value, metric_name, metric_value,
                    reference_metric_value, reference_source, check_type, is_incomplete, max_acceptable_deviation)
    select
        'affiliate'                                                as schema,
        'v_statistics_daily_agg'                                   as table_name,
        t.click_date                                               as record_date,
        null::varchar                                              as dimension_name,
        null::varchar                                              as dimension_value,
        'conversion_revenue_usd'                                   as metric_name,
        coalesce(s.conversion_revenue_usd, 0)                      as metric_value,
        coalesce(t.conversion_revenue_usd, 0)                      as reference_metric_value,
        'affiliate.v_click_data'                                   as reference_source,
        'metric value comparison'                                  as check_type,
        case
            when coalesce(t.conversion_revenue_usd, 0) = 0 then false
            else abs(coalesce(s.conversion_revenue_usd, 0) - coalesce(t.conversion_revenue_usd, 0)) /
                 coalesce(t.conversion_revenue_usd, 0) > 0.001 end as is_incomplete,
        '0.1%'                                                     as max_acceptable_deviation
    from
        temp_agg_metrics t
        left join stat_daily_agg s
                  on t.click_date = s.click_date;

    insert
    into
        temp_result(schema, table_name, record_date, dimension_name, dimension_value, metric_name, metric_value,
                    reference_metric_value, reference_source, check_type, is_incomplete, max_acceptable_deviation)
    select
        'affiliate'                                                 as schema,
        'v_statistics_daily_agg'                                    as table_name,
        t.click_date                                                as record_date,
        null::varchar                                               as dimension_name,
        null::varchar                                               as dimension_value,
        'conversion_away_cnt'                                       as metric_name,
        coalesce(s.conversion_away_cnt, 0)                          as metric_value,
        coalesce(t.conversion_away_cnt, 0)                          as reference_metric_value,
        'affiliate.v_click_data'                                    as reference_source,
        'metric value comparison'                                   as check_type,
        case
            when coalesce(t.conversion_away_cnt, 0) = 0 then false
            else s.conversion_away_cnt != t.conversion_away_cnt end as is_incomplete,
        '0'                                                         as max_acceptable_deviation
    from
        temp_agg_metrics t
        left join stat_daily_agg s
                  on t.click_date = s.click_date;

    insert
    into
        temp_result(schema, table_name, record_date, dimension_name, dimension_value, metric_name, metric_value,
                    reference_metric_value, reference_source, check_type, is_incomplete, max_acceptable_deviation)
    select
        'affiliate'                                       as schema,
        'v_statistics_daily_agg'                          as table_name,
        t.click_date                                      as record_date,
        null::varchar                                     as dimension_name,
        null::varchar                                     as dimension_value,
        'conversion_cnt'                                  as metric_name,
        coalesce(s.conversion_cnt, 0)                     as metric_value,
        coalesce(t.conversion_cnt, 0)                     as reference_metric_value,
        'affiliate.v_click_data'                          as reference_source,
        'metric value comparison'                         as check_type,
        case
            when coalesce(t.conversion_cnt, 0) = 0 then false
            else s.conversion_cnt != t.conversion_cnt end as is_incomplete,
        '0'                                               as max_acceptable_deviation
    from
        temp_agg_metrics t
        left join stat_daily_agg s
                  on t.click_date = s.click_date;

    --------------------------------------------------------------------------------------------------------------------
    -- Compare ab test metrics between abtest_significance_metrics and abtest_stat_agg
    --------------------------------------------------------------------------------------------------------------------

    create temporary table temp_ab_metrics as
    with
        max_data_raw_asm as (
            select
                mdasm.test_id,
                mdasm.iteration,
                max(mdasm.date_end)             as max_date_end,
                min(mdasm.date_start)           as min_date_start,
                count(distinct mdasm.publisher) as cnt_pubs
            from
                affiliate.abtest_significance_metrics as mdasm
            group by mdasm.test_id, mdasm.iteration
        ),
        max_data_clean_asm as (
            select
                mrdasm.*
            from
                max_data_raw_asm as mrdasm
            where
                mrdasm.cnt_pubs > 3
            order by mrdasm.max_date_end desc, mrdasm.min_date_start desc
            limit 1
        ),
        clean_asm as (
            select
                asm.test_id,
                asm.iteration,
                asm.test_group,
                asm.metric_name,
                asm.metric_value as asm_metric,
                asm.date_end
            from
                affiliate.abtest_significance_metrics as asm
                join max_data_clean_asm as mdk
                     on asm.test_id = mdk.test_id and asm.iteration = mdk.iteration and
                        asm.date_start = mdk.min_date_start and asm.date_end = mdk.max_date_end and
                        asm.country_code = 'Total' and asm.publisher = 'Total'
        ),
        unique_metrics as (
            select distinct
                cs.metric_name as metric_name
            from
                clean_asm as cs
        ),
        clean_asa as (
            select
                asa.test_id,
                asa.iteration,
                asa.test_group,
                asa.metric_name,
                case
                    when coalesce(sum(asa.metric_denominator), 0) = 0 then 0
                    else round(
                                coalesce(sum(asa.metric_numerator), 0) /
                                coalesce(sum(asa.metric_denominator), 0),
                                4)
                    end as asa_metric,
                mdka.max_date_end
            from
                affiliate.abtest_stat_agg as asa
                join unique_metrics as um
                     on um.metric_name = asa.metric_name
                join max_data_clean_asm as mdka
                     on asa.test_id = mdka.test_id and asa.iteration = mdka.iteration and
                        asa.record_date::date between mdka.min_date_start and mdka.max_date_end
            group by
                asa.test_id, asa.iteration, asa.test_group, asa.metric_name, um.metric_name,
                mdka.max_date_end
        )

    select
        casm.metric_name                                                    as metric_name,
        casm.test_id || ' - ' || casm.iteration || ' - ' || casm.test_group as dimension_value,
        casm.date_end                                                       as record_date,
        casa.asa_metric,
        casm.asm_metric
    from
        clean_asm as casm
        left join clean_asa as casa
                  on casa.test_id = casm.test_id and casa.iteration = casm.iteration and
                     casa.test_group = casm.test_group and
                     casa.metric_name = casm.metric_name;

    insert
    into
        temp_result(schema, table_name, record_date, dimension_name, dimension_value, metric_name, metric_value,
                    reference_metric_value, reference_source, check_type, is_incomplete, max_acceptable_deviation)
    select
        'affiliate'                   as schema,
        'abtest_significance_metrics' as table_name,
        tam.record_date               as record_date,
        'test - iteration - group'    as dimension_name,
        tam.dimension_value           as dimension_value,
        tam.metric_name               as metric_name,
        coalesce(tam.asm_metric, 0)   as metric_value,
        coalesce(tam.asa_metric, 0)   as reference_metric_value,
        'affiliate.abtest_stat_agg'   as reference_source,
        'metric value comparison'     as check_type,
        case
            when coalesce(tam.asa_metric, 0) = 0
                then coalesce(tam.asa_metric, 0) != coalesce(tam.asm_metric, 0)
            else abs(coalesce(tam.asm_metric, 0) - coalesce(tam.asa_metric, 0)) /
                 coalesce(tam.asa_metric, 0) > 0.001
            end                       as is_incomplete,
        '0.1%'                        as max_acceptable_deviation
    from
        temp_ab_metrics tam;

    delete
    from
        affiliate.data_completeness_report
    where
        record_part_number = 2;

    insert into
        affiliate.data_completeness_report(record_part_number, update_date, schema, table_name, record_date,
                                           dimension_name, dimension_value, metric_name, metric_value,
                                           reference_metric_value, reference_source, check_type, is_incomplete,
                                           max_acceptable_deviation)
    select
        2                  as record_part_number,
        current_date::date as update_date,
        t.schema,
        t.table_name,
        t.record_date,
        t.dimension_name,
        t.dimension_value,
        t.metric_name,
        t.metric_value,
        t.reference_metric_value,
        t.reference_source,
        t.check_type,
        t.is_incomplete,
        t.max_acceptable_deviation
    from
        temp_result t;

    drop table temp_result;
    drop table dic_cluster_source_id;
    drop table temp_country_stat;
    drop table temp_agg_metrics;
    drop table ab_max_dt;
    drop table temp_vmma;
    drop table stat_daily_agg;
    drop table temp_ab_metrics;
end
$$;

alter procedure affiliate.insert_data_completeness_report_actions() owner to ypr;

