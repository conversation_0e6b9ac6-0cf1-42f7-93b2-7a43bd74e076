create or replace procedure affiliate.insert_short_click_price_change_all_scheduler_run(_datediff integer)
    language plpgsql
as
$$
begin

            -- when yesterday data is recorded to affiliate.click_price_change, insert it to short table
            delete from affiliate.short_click_price_change
            where datetime::date = current_date - 1;
            
            insert into affiliate.short_click_price_change(source_id, country_code, id, job_uid, click_price_usd, datetime, id_campaign)
            select
                source_id,
                country_code,
                id,
                job_uid,
                click_price_usd,
                datetime,
                id_campaign
            from
                affiliate.click_price_change
            where datetime::date = current_date - 1;

            delete from affiliate.afg_row_cnt_snapshot
            where log_date = current_date - 1
              and table_name = 'click_price_change';

            insert into affiliate.afg_row_cnt_snapshot(table_name, source_id, log_date, row_cnt)
            select
                'click_price_change' as table_name,
                source_id,
                current_date - 1         as log_date,
                count(*)                 as row_cnt
            from affiliate.short_click_price_change
            where cast(datetime as date) = current_date - 1
            group by source_id;


end;

$$;

alter procedure affiliate.insert_short_click_price_change_all_scheduler_run(integer) owner to rlu;

