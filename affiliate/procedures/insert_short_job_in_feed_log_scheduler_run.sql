create or replace procedure affiliate.insert_short_job_in_feed_log_scheduler_run(_datediff integer)
    language plpgsql
as
$$
begin

            -- insert yesterday data after it is written to job_in_feed_log
            delete from affiliate.short_job_in_feed_log
            where cast(datetime as date) = current_date-1;
            
            insert into affiliate.short_job_in_feed_log(source_id, id_local_partner, id, job_uid, inactive, datetime, click_price)
            select
                source_id,
                id_local_partner,
                id,
                job_uid,
                inactive,
                datetime,
                click_price
            from affiliate.job_in_feed_log
            where cast(datetime as date) = current_date-1;

            delete from affiliate.afg_row_cnt_snapshot
            where log_date = current_date - 1
              and table_name = 'job_in_feed_log';

            insert into affiliate.afg_row_cnt_snapshot(table_name, source_id, log_date, row_cnt)
            select
                'job_in_feed_log' as table_name,
                source_id,
                current_date - 1  as log_date,
                count(*)          as row_cnt
            from affiliate.short_job_in_feed_log
            where cast(datetime as date) = current_date - 1
            group by source_id;

end;

$$;

alter procedure affiliate.insert_short_job_in_feed_log_scheduler_run(integer) owner to rlu;
