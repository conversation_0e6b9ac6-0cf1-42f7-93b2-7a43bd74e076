create or replace procedure affiliate.insert_optimal_cpc_ratio(_datediff integer)
    language plpgsql
as
$$
begin

    drop table if exists temp_result;
    drop table if exists temp_job_stat_raw;

    delete
    from
        affiliate.optimal_cpc_ratio
    where
        task_datetime::date = public.fn_get_date_from_date_diff(_datediff);


    create temporary table temp_job_stat_raw as
    select
        jsa.id_source                                                                                              as source_id,
        jsa.id_partner,
        jsa.click_price_usd,
        jsa.cpc_ratio,
        jsa.start_gather_dt,
        tsl.task_datetime,
        count(*)
        over (partition by jsa.id_source, jsa.id_partner, jsa.click_price_usd, jsa.start_gather_dt, jsa.cpc_ratio) as cnt_cpc_ratio
    from
        affiliate.job_stat_agg jsa
        join affiliate.task_status_log tsl
             on tsl.source_id = jsa.id_source and
                tsl.id_partner = jsa.id_partner and
                tsl.id_consumer_run = jsa.id_consumer_run
    where
          jsa.uid_count > 0
      and jsa.is_optimized_cpc is true
      and tsl.task_datetime::date = public.fn_get_date_from_date_diff(_datediff);

    insert into
        affiliate.optimal_cpc_ratio(source_id, id_local_partner, task_datetime, click_price_usd, cpc_ratio, record_type)
    select distinct on (t.source_id, t.id_partner, t.task_datetime, t.click_price_usd)
        t.source_id,
        t.id_partner,
        t.task_datetime,
        t.click_price_usd,
        cpc_ratio,
        0 as record_type
    from
        temp_job_stat_raw t
    order by
        t.source_id, t.id_partner, t.task_datetime, t.click_price_usd, record_type, cnt_cpc_ratio desc,
        cpc_ratio desc;

    drop table if exists temp_result;
    drop table if exists temp_job_stat_raw;


end;

$$;

alter procedure affiliate.insert_optimal_cpc_ratio(integer) owner to ypr;
