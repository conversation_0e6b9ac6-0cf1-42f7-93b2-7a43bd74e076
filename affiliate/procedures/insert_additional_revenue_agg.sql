create or replace procedure affiliate.insert_additional_revenue_agg(input_date character varying)
    language plpgsql
as
$$
declare _start_date date := input_date::date;
begin

    delete from affiliate.additional_revenue_agg
    where date between (_start_date - '5 days'::interval) and _start_date;

    insert into affiliate.additional_revenue_agg(country, date, traffic_source, placement, project_id, campaign_id, revenue_usd)
    select lower(c.alpha_2::text) as country,
           d.dt                   as date,
           ts.name                as traffic_source,
           j.placement            as placement,
           j.id_project           as project_id,
           j.id_campaign          as campaign_id,
           sum(j.revenue_usd)     as revenue_usd
    from aggregation.click_data_agg j
             join dimension.countries c
                  on c.id = j.country_id
             join dimension.info_calendar d
                  on j.action_datediff = d.date_diff
             join dimension.u_traffic_source ts
                  on ts.country = j.country_id and ts.id = j.id_current_traf_source
    where ts.channel::text = 'Affiliate'::text
      and d.dt between (_start_date - '5 days'::interval) and _start_date
      and j.placement not in ('external', 'ad exchange')
      and j.revenue_usd > 0
    group by lower(c.alpha_2::text),
             d.dt,
             ts.name,
             j.placement,
             j.id_project,
             j.id_campaign;

end;
$$;

alter procedure affiliate.insert_additional_revenue_agg(varchar) owner to ypr;
