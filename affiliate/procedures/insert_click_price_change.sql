create or replace procedure affiliate.insert_click_price_change()
    language plpgsql
as
$$
begin
    drop table if exists max_ids;

    create temporary table max_ids as
    select
        max(case when source_id = 1 then id end) as max_id_nl,
        max(case when source_id = 2 then id end) as max_id_us
    from
        affiliate.click_price_change
    where
        part_num = 2;

    --connect to NL
    perform dblink_connect('myconnNL'::text,
                           'dbname=affiliate host=********* user= ''pentaho'' password= ''!faP@jhWYWb&3rwv'' options=-csearch_path=');

    insert into
        affiliate.click_price_change(source_id, id, country_code, job_uid, click_price_usd, datetime, part_num,
                                     id_campaign)
    select
        source_id,
        id,
        country_code,
        job_uid,
        click_price_usd,
        datetime,
        part_num,
        id_campaign
    from
        dblink('myconnNL',
               format('select
                   1 as source_id,
                   id,
                   country as country_code,
                   job_uid,
                   click_price_usd,
                   datetime,
                   2 as part_num,
                   id_campaign
               from public.click_price_change
               where id > %s;', (select max_id_nl from max_ids))) as (source_id smallint, id bigint,
                                                                      country_code varchar(2),
                                                                      job_uid bigint,
                                                                      click_price_usd numeric(14, 5),
                                                                      datetime timestamp, part_num smallint,
                                                                      id_campaign integer);


    perform dblink_disconnect('myconnNL');

    --connect to US
    perform dblink_connect('myconnUS'::text,
                           'dbname=affiliate host=************ user= ''pentaho'' password= ''!faP@jhWYWb&3rwv'' options=-csearch_path=');

    insert into
        affiliate.click_price_change(source_id, id, country_code, job_uid, click_price_usd, datetime, part_num,
                                     id_campaign)
    select
        source_id,
        id,
        country_code,
        job_uid,
        click_price_usd,
        datetime,
        part_num,
        id_campaign
    from
        dblink('myconnUS',
               format('select
                   2 as source_id,
                   id,
                   country as country_code,
                   job_uid,
                   click_price_usd,
                   datetime,
                   2 as part_num,
                   id_campaign
               from public.click_price_change
               where id > %s;', (select max_id_us from max_ids))) as (source_id smallint, id bigint,
                                                                      country_code varchar(2),
                                                                      job_uid bigint,
                                                                      click_price_usd numeric(14, 5),
                                                                      datetime timestamp, part_num smallint,
                                                                      id_campaign integer);

    perform dblink_disconnect('myconnUS');

    drop table if exists max_ids;
end ;

$$;

alter procedure affiliate.insert_click_price_change() owner to ypr;

