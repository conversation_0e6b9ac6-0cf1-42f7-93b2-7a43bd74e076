create or replace procedure affiliate.insert_underutilised_budget_category()
    language plpgsql
as
$$
begin

    truncate table affiliate.underutilised_budget_category;

    --connect to NL
    perform dblink_connect('myconnNL',
                           'dbname=affiliate host=********* user= ''pentaho'' password= ''!faP@jhWYWb&3rwv'' options=-csearch_path=');

    insert into
        affiliate.underutilised_budget_category(id, source_id, country, id_project, id_job_category,
                                                underutilised_budget, category)
    select
        id,
        source_id,
        country,
        id_project,
        id_job_category,
        underutilised_budget,
        category
    from
        dblink('myconnNL',
               'select id,
                       1 as source_id,
                       country,
                       id_project,
                       id_job_category,
                       underutilised_budget,
                       category
                from public.underutilised_budget_category;') as underutilised_budget_category (id                   bigint,
                                                                                               source_id            smallint,
                                                                                               country              text,
                                                                                               id_project           integer,
                                                                                               id_job_category      integer,
                                                                                               underutilised_budget numeric,
                                                                                               category             text);

    perform dblink_disconnect('myconnNL');

    --connect to US
    perform dblink_connect('myconnUS',
                           'dbname=affiliate host=************ user= ''pentaho'' password= ''!faP@jhWYWb&3rwv'' options=-csearch_path=');


    insert into
        affiliate.underutilised_budget_category(id, source_id, country, id_project, id_job_category,
                                                underutilised_budget, category)
    select
        id,
        source_id,
        country,
        id_project,
        id_job_category,
        underutilised_budget,
        category
    from
        dblink('myconnUS',
               'select id,
                       2 as source_id,
                       country,
                       id_project,
                       id_job_category,
                       underutilised_budget,
                       category
                from public.underutilised_budget_category;') as underutilised_budget_category (id                   bigint,
                                                                                               source_id            smallint,
                                                                                               country              text,
                                                                                               id_project           integer,
                                                                                               id_job_category      integer,
                                                                                               underutilised_budget numeric,
                                                                                               category             text);

    perform dblink_disconnect('myconnUS');


end;

$$;

alter procedure affiliate.insert_underutilised_budget_category() owner to ypr;
