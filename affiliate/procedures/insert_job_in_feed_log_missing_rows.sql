create or replace procedure affiliate.insert_job_in_feed_log_missing_rows(_date_rec date, _login character varying, _password character varying)
    language plpgsql
as
$$
declare
    connstr          varchar;
    cnt_missing_rows bigint;
begin
    drop table if exists temp_missing_rows;

    --connect to NL
    connstr := 'dbname=affiliate host=********* user=' || quote_ident(_login) || ' password=' ||
               quote_literal(_password) || ' options=-csearch_path=';
    perform dblink_connect('myconnNL', connstr);

    create temporary table temp_missing_rows as
    select
        tt.source_id,
        tt.id,
        tt.id_local_partner,
        tt.job_uid,
        tt.click_price,
        tt.inactive,
        tt.datetime
    from
        dblink('myconnNL',
               format('select
                          source_id,
                          id,
                          id_local_partner,
                          job_uid,
                          case when id_local_partner = 407 then click_price/100 else click_price end as click_price,
                          inactive,
                          datetime
                       from public.check_load_job_in_feed_log(%L, %L, %L);',
                      _date_rec, quote_ident('pentaho'), quote_ident('!faP@jhWYWb&3rwv'))) as tt (
                                                                                                  source_id smallint,
                                                                                                  id bigint,
                                                                                                  id_local_partner integer,
                                                                                                  job_uid bigint,
                                                                                                  click_price numeric(14, 5),
                                                                                                  inactive boolean,
                                                                                                  datetime timestamp without time zone
            );

    perform dblink_disconnect('myconnNL');

    --connect to US
    connstr := 'dbname=affiliate host=************ user=' || quote_ident(_login) || ' password=' ||
               quote_literal(_password) || ' options=-csearch_path=';
    perform dblink_connect('myconnUS', connstr);

    insert into temp_missing_rows
    select
        tt1.source_id,
        tt1.id,
        tt1.id_local_partner,
        tt1.job_uid,
        tt1.click_price,
        tt1.inactive,
        tt1.datetime
    from
        dblink('myconnUS',
               format('select
                          source_id,
                          id,
                          id_local_partner,
                          job_uid,
                          click_price,
                          inactive,
                          datetime
                       from public.check_load_job_in_feed_log(%L, %L, %L);',
                      _date_rec, quote_ident('pentaho'), quote_ident('!faP@jhWYWb&3rwv'))) as tt1 (
                                                                                                   source_id smallint,
                                                                                                   id bigint,
                                                                                                   id_local_partner integer,
                                                                                                   job_uid bigint,
                                                                                                   click_price numeric(14, 5),
                                                                                                   inactive boolean,
                                                                                                   datetime timestamp without time zone
            );

    perform dblink_disconnect('myconnUS');

    insert into
        affiliate.job_in_feed_log(source_id, id, id_local_partner, job_uid, click_price, inactive, datetime)
    select
        source_id,
        id,
        id_local_partner,
        job_uid,
        click_price,
        inactive,
        datetime
    from
        temp_missing_rows;

    insert into
        affiliate.short_job_in_feed_log(source_id, id, id_local_partner, job_uid, click_price, inactive, datetime)
    select
        source_id,
        id,
        id_local_partner,
        job_uid,
        click_price,
        inactive,
        datetime
    from
        temp_missing_rows;

    cnt_missing_rows := (select count(*)::bigint from temp_missing_rows);

    if cnt_missing_rows > 0 then
        delete
        from
            affiliate.afg_row_cnt_snapshot
        where
              log_date = _date_rec
          and table_name = 'job_in_feed_log';

        insert into
            affiliate.afg_row_cnt_snapshot(table_name, source_id, log_date, row_cnt)
        select
            'job_in_feed_log' as table_name,
            source_id,
            _date_rec         as log_date,
            count(*)          as row_cnt
        from
            affiliate.short_job_in_feed_log
        where
            cast(datetime as date) = _date_rec
        group by source_id, _date_rec;

    end if;
end ;

$$;

alter procedure affiliate.insert_job_in_feed_log_missing_rows(date, varchar, varchar) owner to ypr;

