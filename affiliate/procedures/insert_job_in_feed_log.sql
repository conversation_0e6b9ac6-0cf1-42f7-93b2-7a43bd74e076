create or replace procedure affiliate.insert_job_in_feed_log()
    language plpgsql
as
$$
begin
    drop table if exists max_ids;

    create temporary table max_ids as
    select
        max(case when source_id = 1 then id end) as max_id_nl,
        max(case when source_id = 2 then id end) as max_id_us
    from
        affiliate.job_in_feed_log;

    --connect to NL
    perform dblink_connect('myconnNL'::text,
                           'dbname=affiliate host=********* user= ''pentaho'' password= ''!faP@jhWYWb&3rwv'' options=-csearch_path=');

    insert into
        affiliate.job_in_feed_log(source_id, id, id_local_partner, job_uid, click_price, inactive, datetime)
    select
        source_id,
        id,
        id_local_partner,
        job_uid,
        click_price,
        inactive,
        datetime
    from
        dblink('myconnNL',
               format('select
                           1 as source_id,
                           id,
                           id_local_partner,
                           job_uid,
                           case when id_local_partner = 407 then click_price/100 else click_price end as click_price,
                           inactive,
                           datetime
                       from public.job_in_feed_log
                       where id > %s', (select max_id_nl from max_ids))) as (source_id smallint, id bigint,
                                                                             id_local_partner integer, job_uid bigint,
                                                                             click_price numeric(14, 5),
                                                                             inactive boolean,
                                                                             datetime timestamp);


    perform dblink_disconnect('myconnNL');

    --connect to US
    perform dblink_connect('myconnUS'::text,
                           'dbname=affiliate host=************ user= ''pentaho'' password= ''!faP@jhWYWb&3rwv'' options=-csearch_path=');

    insert into
        affiliate.job_in_feed_log(source_id, id, id_local_partner, job_uid, click_price, inactive, datetime)
    select
        source_id,
        id,
        id_local_partner,
        job_uid,
        click_price,
        inactive,
        datetime
    from
        dblink('myconnUS',
               format('select
                           2 as source_id,
                           id,
                           id_local_partner,
                           job_uid,
                           click_price,
                           inactive,
                           datetime
                       from public.job_in_feed_log
                       where id > %s', (select max_id_us from max_ids))) as (source_id smallint, id bigint,
                                                                             id_local_partner integer, job_uid bigint,
                                                                             click_price numeric(14, 5),
                                                                             inactive boolean,
                                                                             datetime timestamp);

    perform dblink_disconnect('myconnUS');

    drop table if exists max_ids;
end ;

$$;

alter procedure affiliate.insert_job_in_feed_log() owner to ypr;

