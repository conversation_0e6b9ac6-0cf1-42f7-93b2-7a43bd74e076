with daily_revenue as (select CAST(date as DATE), 
                              sum(total_value) as daily_revenue, 
                              sum(total_value_origin_currency) as daily_revenue_origin_currency,
                              country_id, 
                              id_project, 
                              id_campaign
                       from aggregation.auction_click_statistic_analytics
                       group by country_id, 
                                id_project, 
                                id_campaign, 
                                date)
select CAST(agg.site_url as varchar) as site,
       CAST(agg.user_id as integer) as user_id,
       CAST(c.alpha_2 as varchar) as country_code,
       CAST(action_date as DATE) as date,
       CAST(c.name_country_eng as varchar) as country,
       CAST(project_id as INTEGER),
       CAST(agg.campaign as varchar) as campaign_name,
       CAST(COALESCE(stat.daily_revenue, 0) as NUMERIC) as campaign_cost,
       CAST(COALESCE(stat.daily_revenue_origin_currency, 0) as NUMERIC) as campaign_cost_origin_currency,
       CAST(user_budget_month_usd as NUMERIC) as user_budget_month_usd,
       CAST(campaign_budget_month_usd as NUMERIC),
       CAST(user_budget_month as NUMERIC) as user_budget_month,
       CAST(campaign_budget_month as NUMERIC) as campaign_budget_month,
       CAST(COALESCE(au.daily_budget, 0) as NUMERIC) as user_daily_budget,
       CAST(c.alpha_2 || '_' || project_id as VARCHAR) as client_hash
from aggregation.budget_revenue_daily_agg as agg
         inner join imp.auction_user as au on au.id = agg.user_id and au.country = agg.country_id
         inner join dimension.countries c
                    on agg.country_id = c.id
         left join daily_revenue as stat on
            CAST(stat.date as DATE) = agg.action_date and stat.country_id = agg.country_id and
            stat.id_project = agg.project_id and CAST(agg.id_campaign AS integer) = stat.id_campaign
where agg.country_id in %(country_ids)s
  and action_date BETWEEN %(action_date_start)s and %(action_date_end)s
order by action_date desc;
