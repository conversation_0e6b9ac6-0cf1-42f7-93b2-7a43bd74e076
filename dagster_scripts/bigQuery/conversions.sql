select CAST(dc.name_country_eng as VARCHAR)                                                       as country,
       CAST(dt.date as DATE),
       CAST(dt.project_id as INTEGER)                                                             as id_project,
       CAST(dt.campaign_id as INTEGER)                                                            as id_campaign,
       CAST(case when ac.name is not null then ac.name else max(dt.campaign_name) end as VARCHAR) as campaign_name,
       CAST(sum(dt.away_revenue_origin_currency) as NUMERIC)                                      as cost,
       CAST(sum(dt.aways) as INTEGER)                                                             as clicks,
       CAST(sum(dt.conversions) as INTEGER)                                                       as conversions,
       CAST(sum(dt.avg_cpc_for_paid_job_count * dt.paid_job_count::numeric) /
            nullif(sum(dt.paid_job_count), 0)::numeric as NUMERIC)                                AS avg_cpc,
       CAST(case when crm.benchmark_cr > 0 then crm.benchmark_cr else null end as NUMERIC)        as target_cr,
       CAST(case when crm.benchmark_cpa > 0 then crm.benchmark_cpa else null end as NUMERIC)      as target_cpa,
       CAST(dc.alpha_2 || '_' || dt.project_id as VARCHAR)                                        as client_hash,
       dt.job_category_id                                                                         as child_category_id,
       job_kaiju_category.child_name                                                              as child_category_name,
       coalesce(v_job_kaiju_category.parent_category_name,
                job_kaiju_category.child_name)                                                    as parent_category_name

from (select country_id,
             project_id,
             campaign_id,
             campaign_name,
             session_date as date,
             away_revenue_origin_currency,
             aways,
             conversions,
             0            as avg_cpc_for_paid_job_count,
             0            as paid_job_count,
             pcd.job_category_id
      from aggregation.project_conversions_daily pcd
      union all
      select id_country                      as country_id,
             jsd.id_project                  as project_id,
             id_campaign                     as campaign_id,
             ''                              as campaign_name,
             to_date(jsd.date, 'YYYY-MM-DD') as date,
             0                               as away_revenue_origin_currency,
             0                               as aways,
             0                               as conversions,
             jsd.avg_cpc_for_paid_job_count,
             jsd.paid_job_count,
             jsd.job_category_id
      from aggregation.jobs_stat_daily jsd) dt
         inner join dimension.countries dc on dt.country_id = dc.id
         left join imp.campaign as ac on ac.country = dt.country_id and dt.campaign_id = ac.id
         left join (select crm_client_account.soska_project_link,
                           "left"(crm_client_account.soska_project_link::text, 2)                                                AS country,
                           substr(crm_client_account.soska_project_link::text, 3,
                                  length(crm_client_account.soska_project_link::text) -
                                  2)                                                                                             AS id_project,
                           crm_client_account.benchmark_cr,
                           crm_client_account.benchmark_cpa,
                           crm_client_account.target_action,
                           row_number()
                           over (partition by crm_client_account.soska_project_link order by crm_client_account.created_on desc) AS num
                    from aggregation.crm_client_account
                    where (crm_client_account.benchmark_cr > 0::numeric or
                           crm_client_account.benchmark_cpa > 0::numeric)
                      and crm_client_account.soska_project_link is not null) crm
                   on dc.alpha_2::text = crm.country and
                      dt.project_id = nullif(crm.id_project, ''::text)::integer and crm.num = 1
         left join (Select job_kaiju_category.id_child, child_name
                    from dimension.job_kaiju_category
                    union
                    Select job_kaiju_category.id_parent, parent_name
                    from dimension.job_kaiju_category) as job_kaiju_category
                   on dt.job_category_id = job_kaiju_category.id_child
         left join aggregation.v_job_kaiju_category
                   on job_kaiju_category.id_child = v_job_kaiju_category.child_category_id
where dc.id in %(country_ids)s
  and dt.campaign_id > 0
  and dt.date BETWEEN %(date_start)s and %(date_end)s
group by dc.name_country_eng,
         dt.date,
         dt.project_id,
         dt.campaign_id,
         ac.name,
         crm.benchmark_cr,
         crm.benchmark_cpa,
         dc.alpha_2,
         dt.job_category_id,
         job_kaiju_category.child_name,
         coalesce(v_job_kaiju_category.parent_category_name, job_kaiju_category.child_name);
