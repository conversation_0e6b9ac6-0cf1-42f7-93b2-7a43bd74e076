create table cube.employer_job_history as
-- івент публікації вакансії
select distinct
       j.sources as database_source_id,
       j.id as job_id,
       j.id_employer as employer_id,
       e.id_cdp as cdp_id,
       'https://ua.jooble.org/jdp/' || j.uid_current::varchar(30) as job_link,
       1 /*job_creation*/ as event_type_id,
       (j.date_created::timestamp at time zone 'UTC') at time zone 'Europe/Kiev'  as event_datetime,
       0 as apply_id,
       0::int as profile_id,
       ''::varchar(50) as profile_link,
       ''::text   as     additional_info
from imp_employer.job j
join imp_employer.employer e
  on j.sources = e.sources and
     j.id_employer = e.id and
     e.country_code = 'ua'
where j.moderation_status in (1) and
      j.date_created >= '2021-01-01' and
      j.sources = 1

union all

-- івент надісланого профілю у відгуки
-- івент надісланого профілю у ЦР
select distinct
       j.sources as database_source_id,
       j.id as job_id,
       j.id_employer as employer_id,
        e.id_cdp as cdp_id,
       'https://ua.jooble.org/jdp/' || j.uid_current::varchar(30) as job_link,
       case when ja.flags& 1024 = 0 then 2 /*apply_recieved*/ else 3 /*DR_profile_recieved*/ end as event_type_id,
       (ja.date::timestamp at time zone 'UTC') at time zone 'Europe/Kiev' as event_datetime,
       ja.id as apply_id,
       jap.id_profile as profile_id,
       'https://ua.jooble.org/profilePreview/'||p.id_public as profile_link,
       ''::text   as     additional_info
from imp_employer.job j
join imp_employer.employer e
  on j.sources = e.sources and
     j.id_employer = e.id and
     e.country_code = 'ua'
join imp_employer.job_apply ja
  on j.sources = ja.sources and
     j.id = ja.id_job
join imp_employer.job_apply_profile jap
  on jap.sources = ja.sources and
     jap.id_apply = ja.id
join imp.profiles p
  on p.id = jap.id_profile
where j.moderation_status in (1) and
      j.date_created >= '2021-01-01' and
      j.sources = 1


union all

-- івент перегляду надісланого профілю
select distinct
       j.sources as database_source_id,
       j.id as job_id,
       j.id_employer as employer_id,
        e.id_cdp as cdp_id,
       'https://ua.jooble.org/jdp/' || j.uid_current::varchar(30) as job_link,
       case when ja.flags& 1024 = 0 then 4 /*apply_seen*/ else 5 /*DR_profile_seen*/ end as event_type_id,
       (ja.date_seen::timestamp at time zone 'UTC') at time zone 'Europe/Kiev' as event_datetime,
       ja.id as apply_id,
       jap.id_profile as profile_id,
       'https://ua.jooble.org/profilePreview/'||p.id_public as profile_link,
       ''::text   as     additional_info
from imp_employer.job j
join imp_employer.employer e
  on j.sources = e.sources and
     j.id_employer = e.id and
     e.country_code = 'ua'
join imp_employer.job_apply ja
  on j.sources = ja.sources and
     j.id = ja.id_job
join imp_employer.job_apply_profile jap
  on jap.sources = ja.sources and
     jap.id_apply = ja.id
join imp.profiles p
  on p.id = jap.id_profile
where j.moderation_status in (1) and
      j.date_created >= '2021-01-01' and
      ja.date_seen is not null and
      j.sources = 1

union all
-- івент відкриття контактів надісланого профілю
select distinct
       j.sources as database_source_id,
       j.id as job_id,
       j.id_employer as employer_id,
        e.id_cdp as cdp_id,
       'https://ua.jooble.org/jdp/' || j.uid_current::varchar(30) as job_link,
       case when ja.flags& 1024 = 0 then 6 /*apply_open_contact*/ else 7 /*DR_profile_open_contact*/ end as event_type_id,
       (poc.date::timestamp at time zone 'UTC') at time zone 'Europe/Kiev' as event_datetime,
       ja.id as apply_id,
       jap.id_profile as profile_id,
       'https://ua.jooble.org/profilePreview/'||p.id_public as profile_link,
       ''::text   as     additional_info
from imp_employer.job j
join imp_employer.employer e
  on j.sources = e.sources and
     j.id_employer = e.id and
     e.country_code = 'ua'
join imp_employer.job_apply ja
  on j.sources = ja.sources and
     j.id = ja.id_job
join imp_employer.job_apply_profile jap
  on jap.sources = ja.sources and
     jap.id_apply = ja.id
join imp.profiles p
  on p.id = jap.id_profile
join imp_employer.profile_open_contact poc
  on poc.sources = ja.sources and
     poc.id_apply = ja.id
where j.moderation_status in (1) and
      j.date_created >= '2021-01-01' and
      ja.date_seen is not null and poc.date is not null and
      j.sources = 1
union all


-- messages to and from apply profile (8, 9) or DG profile (10,11)
select j.sources as database_source_id,
       j.id as job_id,
       j.id_employer as employer_id,
       e.id_cdp as cdp_id,
       'https://ua.jooble.org/jdp/' || j.uid_current::varchar(30) as job_link,
       case when ja.flags& 1024 = 0 and jam.author_type = 0 /*employer*/ then 8 /*apply_message_from_employer*/
            when ja.flags& 1024 = 0 and jam.author_type = 1 /*job seeker*/ then 9 /*apply_message_from_job_seeker*/
            when ja.flags& 1024 = 1024 and jam.author_type = 0 /*employer*/ then 10  /*DG_message_from_employer*/
            when ja.flags& 1024 = 1024 and jam.author_type = 1 /*job seeker*/ then 11 /*DG_message_from_job_seeker*/
           end as event_type_id,
       (jam.date_created::timestamp at time zone 'UTC') at time zone 'Europe/Kiev' as event_datetime,
       ja.id as apply_id,
       jap.id_profile as profile_id,
       'https://ua.jooble.org/profilePreview/'||p.id_public as profile_link,
        string_agg(jam.text, chr(10)) as additional_info
from imp_employer.job j
join imp_employer.employer e
  on j.sources = e.sources and
     j.id_employer = e.id and
     e.country_code = 'ua'
join imp_employer.job_apply ja
  on j.sources = ja.sources and
     j.id = ja.id_job
join imp_employer.job_apply_profile jap
  on jap.sources = ja.sources and
     jap.id_apply = ja.id
join imp.profiles p
  on p.id = jap.id_profile
join imp_employer.job_apply_message jam
  on jam.sources = jap.sources and
     jam.id_apply = jap.id_apply
where j.moderation_status in (1) and
      j.date_created >= '2021-01-01' and
      ja.date_seen is not null and
      j.sources = 1
group by j.sources ,
       j.id ,
       j.id_employer,
       e.id_cdp,
       'https://ua.jooble.org/jdp/' || j.uid_current::varchar(30),
       case when ja.flags& 1024 = 0 and jam.author_type = 0 /*employer*/ then 8 /*apply_message_from_employer*/
            when ja.flags& 1024 = 0 and jam.author_type = 1 /*job seeker*/ then 9 /*apply_message_from_job_seeker*/
            when ja.flags& 1024 = 1024 and jam.author_type = 0 /*employer*/ then 10  /*DG_message_from_employer*/
            when ja.flags& 1024 = 1024 and jam.author_type = 1 /*job seeker*/ then 11 /*DG_message_from_job_seeker*/
           end,
       (jam.date_created::timestamp at time zone 'UTC') at time zone 'Europe/Kiev',
       ja.id ,
       jap.id_profile,
       'https://ua.jooble.org/profilePreview/'||p.id_public
;


alter table cube.employer_job_history
	add constraint employer_job_history_pk
		primary key (database_source_id, job_id, apply_id, event_type_id, event_datetime );

grant select on cube.employer_job_history to readonly;
grant select on cube.employer_job_history to readonly_ds;

