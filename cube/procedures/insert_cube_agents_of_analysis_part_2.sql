create or replace procedure cube.insert_cube_agents_of_analysis_part_2(_datediff integer)
    language plpgsql
as
$$
begin

        truncate table employer.job_blue_collar_score;

        insert into employer.job_blue_collar_score (database_source_id, employer_id, job_id, blue_score_avg)
        select 1 as database_source_id,
               j.id_employer as employer_id,
               id_job_employer as job_id,
               avg(coalesce(pbs.blue_score, pbs1.blue_score)) as blue_score_avg
        from job.job_profession jp
        left join dimension.professions_blue_score pbs on pbs.profession = jp.profession
        left join kpav.parrent_profession pp on pp.profession_name = jp.profession
        left join dimension.info_profession ip on ip.id = pp.parrent_id
        left join dimension.professions_blue_score pbs1 on pbs1.profession = ip.name
        left join imp_employer.job j on j.sources = 1 and j.id = jp.id_job_employer
        where jp.id_project = -1 and id_job_employer is not null
        group by j.id_employer,
                 id_job_employer;

        truncate table employer.job_packet;

        insert into employer.job_packet (database_source_id, subscription_id, packet_rank, employer_id, packet_start_datetime,
                                         packet_end_datetime, job_id, first_impression_datediff, last_impression_datediff, job_active_day_cnt)
        select msp.database_source_id,
               msp.subscription_id,
               msp.packet_rank,
               msp.employer_id,
               msp.packet_start_datetime,
               msp.packet_end_datetime,
               j.id                         as job_id,
               min(js.date_diff)            as first_impression_datediff,
               max(js.date_diff)            as last_impression_datediff,
               count(distinct js.date_diff) as job_active_day_cnt
        from imp_employer.job_statistics_detailed js
        join imp_employer.job j on js.sources = j.sources and js.id_job = j.id
        join employer.subscription_active_date sda on sda.database_source_id = j.sources and sda.employer_id = j.id_employer and js.date_diff = sda.subscription_datediff
        join employer.m_subscription_packet msp on msp.database_source_id = sda.database_source_id and msp.employer_id = sda.employer_id
                 and msp.subscription_id = sda.subscription_id
                 and js.date_diff between fn_get_date_diff(msp.packet_start_datetime)
                                      and fn_get_date_diff(msp.packet_end_datetime)
        group by msp.database_source_id,
                 msp.subscription_id,
                 msp.packet_rank,
                 msp.employer_id,
                 msp.packet_start_datetime,
                 msp.packet_end_datetime,
                 j.id;

        -- в разі потреби переходу на щоденний допис потрібно залигати лише ті в яких закінчився час пакету, тобто додати в умову "msp.packet_end_datetime::date = '2021-12-02'"


        truncate table employer.packet_account_activity_agg;

        insert into employer.packet_account_activity_agg(database_source_id, employer_id, subscription_id, packet_rank, packet_start_datetime, packet_end_datetime, account_id, session_cnt)
        select msp.database_source_id,
               msp.employer_id,
               msp.subscription_id,
               msp.packet_rank,
               msp.packet_start_datetime,
               msp.packet_end_datetime,
               eaa.id_account as account_id,
               count(distinct eas.id) as session_cnt
        from employer.m_subscription_packet msp
        join imp_employer.employer_account_account eaa on msp.database_source_id = eaa.sources and msp.employer_id = eaa.id_employer
        left join imp_employer.employer_account_session eas on eas.sources = eaa.sources and eas.id_account = eaa.id_account
                                                            and eas.date_started between msp.packet_start_datetime and msp.packet_end_datetime and eas.using_secret_key = false
        group by msp.database_source_id,
                 msp.employer_id,
                 msp.subscription_id,
                 msp.packet_rank,
                 msp.packet_start_datetime,
                 msp.packet_end_datetime,
                 eaa.id_account;

end;

$$;

alter procedure cube.insert_cube_agents_of_analysis_part_2(integer) owner to rlu;

