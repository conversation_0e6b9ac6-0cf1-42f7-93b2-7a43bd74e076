create or replace view cube.v_employer_job_history as
select ejh.database_source_id,
       ejh.job_id,
       ejh.employer_id,
       ejh.cdp_id,
       ejh.job_link,
       ejh.event_type_id,
       ejh.event_datetime,
       ejh.apply_id,
       ejh.profile_id,
       ejh.profile_link,
       ejh.additional_info,
       iet.name as event_type_name,
       j.title as job_title,
       j.region_list as job_region_list,
       j.address as job_address,
       j.additional_questions_json as job_additional_questions_json,
       ec.company_name,
       j.html_desc as job_html_desc
from cube.employer_job_history ejh
         left join cube.info_employer_job_history_event_type iet
                   on iet.id = ejh.event_type_id
         join imp_employer.job j
              on j.sources = ejh.database_source_id and
                 j.id = ejh.job_id
         join imp_employer.employer_cdp ec
              on ec.sources = ejh.database_source_id and
                 ec.id = ejh.cdp_id
;

grant select on cube.v_employer_job_history to readonly;
grant select on cube.v_employer_job_history to readonly_ds;
