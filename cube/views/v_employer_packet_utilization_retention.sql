drop view if exists cube.v_employer_packet_utilization_retention;
create or replace view cube.v_employer_packet_utilization_retention
            (database_source_id, subscription_id, packet_rank, employer_id, packet_jcoin_cnt, packet_start_datetime,
             packet_end_datetime, packet_plan_end_datetime, packet_day_cnt, packet_price,
             packet_id, subscription_month_cnt, subscription_order_datetime,
             packet_type_id, payment_id, payment_rank, payment_start_datetime, payment_end_datetime,
             packet_type_paid_result_id, packet_next_start_datetime, jcoin_utilized_cnt, jcoin_plan_cnt, call_cnt,
             call_answered_cnt, call_answered_30_sec_free_packet_cnt, call_answered_30_sec_paid_packet_cnt, apply_cnt,
             apply_profile_viewed_cnt, apply_profile_open_contact_free_packet_cnt,
             apply_profile_open_contact_paid_packet_cnt, apply_profile_message_cnt, profile_base_search_cnt,
             profile_base_profile_viewed_cnt, profile_base_profile_open_contact_free_packet_cnt,
             profile_base_profile_open_contact_paid_packet_cnt, profile_base_profile_message_cnt,
             digital_recruiter_profile_cnt, digital_recruiter_profile_viewed_cnt,
             digital_recruiter_profile_open_contact_free_packet_cnt,
             digital_recruiter_profile_open_contact_paid_packet_cnt, digital_recruiter_profile_message_cnt,
             job_active_avg, packet_short_name, retention_type, packet_type, packet_type_size,
             company_name, is_retained, apply_revenue, call_revenue, digital_recruiter_revenue, profile_base_revenue,
             packet_payment_without_vat_uah, next_packet_type, next_packet_jcoin_cnt, packet_type_transition,
             next_packet_payment_without_vat_uah, next_packet_start_datetime, jcoin_spent_packet_day,
             industry, is_active_packet, is_active_next_packet, profile_submitted_cnt, job_abroad_prc, blue_score_avg, payment_method, acquisition_channel, registration_source,
              account_cnt,active_account_cnt, session_cnt, employer_paid_packet_cnt  )
as
-- агрегую до пакету (збираю додаткові дані по пакету з датасету утилізації)
WITH packet_data AS (
    SELECT msp.database_source_id,
           msp.subscription_id,
           msp.packet_rank,
           msp.employer_id,
           msp.packet_jcoin_cnt,
           msp.packet_start_datetime,
           msp.packet_end_datetime,
           msp.packet_plan_end_datetime,
           max(jmd.packet_day_cnt) as packet_day_cnt,
           msp.packet_price,
           msp.packet_id,
           msp.subscription_month_cnt,
           msp.subscription_order_datetime,
           msp.packet_type_id,
           msp.payment_id,
           msp.payment_rank,
           msp.payment_start_datetime,
           msp.payment_end_datetime,
           msp.packet_type_paid_result_id,
           msp.packet_next_start_datetime,
           sum(jmd.jcoin_utilized_cnt)                                     AS jcoin_utilized_cnt,
           sum(jmd.jcoin_plan_cnt)                                         AS jcoin_plan_cnt,
           sum(jmd.call_cnt)                                               AS call_cnt,
           sum(jmd.call_answered_cnt)                                      AS call_answered_cnt,
           sum(jmd.call_answered_30_sec_free_packet_cnt)                   AS call_answered_30_sec_free_packet_cnt,
           sum(jmd.call_answered_30_sec_paid_packet_cnt)                   AS call_answered_30_sec_paid_packet_cnt,
           sum(jmd.apply_cnt)                                              AS apply_cnt,
           sum(jmd.apply_profile_viewed_cnt)                               AS apply_profile_viewed_cnt,
           sum(jmd.apply_profile_open_contact_free_packet_cnt)             AS apply_profile_open_contact_free_packet_cnt,
           sum(jmd.apply_profile_open_contact_paid_packet_cnt)             AS apply_profile_open_contact_paid_packet_cnt,
           sum(jmd.apply_profile_message_cnt)                              AS apply_profile_message_cnt,
           sum(jmd.profile_base_search_cnt)                                AS profile_base_search_cnt,
           sum(jmd.profile_base_profile_viewed_cnt)                        AS profile_base_profile_viewed_cnt,
           sum(jmd.profile_base_profile_open_contact_free_packet_cnt)      AS profile_base_profile_open_contact_free_packet_cnt,
           sum(jmd.profile_base_profile_open_contact_paid_packet_cnt)      AS profile_base_profile_open_contact_paid_packet_cnt,
           sum(jmd.profile_base_profile_message_cnt)                       AS profile_base_profile_message_cnt,
           sum(jmd.digital_recruiter_profile_cnt)                          AS digital_recruiter_profile_cnt,
           sum(jmd.digital_recruiter_profile_viewed_cnt)                   AS digital_recruiter_profile_viewed_cnt,
           sum(jmd.digital_recruiter_profile_open_contact_free_packet_cnt) AS digital_recruiter_profile_open_contact_free_packet_cnt,
           sum(jmd.digital_recruiter_profile_open_contact_paid_packet_cnt) AS digital_recruiter_profile_open_contact_paid_packet_cnt,
           sum(jmd.digital_recruiter_profile_message_cnt)                  AS digital_recruiter_profile_message_cnt,
           avg(jmd.job_active_cnt)                                         AS job_active_avg,
           vspi.packet_short_name,

           CASE
               WHEN msp.packet_type_id = 1 THEN 'free'::text
               WHEN msp.packet_type_paid_result_id = ANY (ARRAY [2, 3]) THEN 'unpaid'::text
               WHEN msp.packet_type_paid_result_id = 1 THEN 'paid'::text
               ELSE NULL::text
               END                                                         AS packet_type,
           CASE
               WHEN msp.packet_type_id = 1 THEN 'free'::text
               WHEN msp.packet_type_paid_result_id = ANY (ARRAY [2, 3]) THEN 'unpaid '::text || vspi.packet_short_name::text
               WHEN msp.packet_type_paid_result_id = 1 THEN 'paid '::text || vspi.packet_short_name::text
               ELSE NULL::text
               END                                                         AS packet_type_size,

           sum(jmd.apply_revenue)                                          AS apply_revenue,
           sum(jmd.call_revenue)                                           AS call_revenue,
           sum(jmd.digital_recruiter_revenue)                              AS digital_recruiter_revenue,
           sum(jmd.profile_base_revenue)                                   AS profile_base_revenue,
           round(COALESCE(jmrc.payment_without_vat_uah, 0::numeric) / jmrc.subscription_payment_month_cnt::numeric,
                 2)                                                        AS packet_payment_without_vat_uah,
           vspi.payment_method

    FROM employer.jcoin_utilization_daily jmd
        join employer.m_subscription_packet msp on
            msp.database_source_id = jmd.database_source_id and
            msp.subscription_id = jmd.subscription_id and
            msp.packet_rank = jmd.packet_rank

             JOIN employer.m_subscription_packet_info vspi ON vspi.database_source_id = jmd.database_source_id AND
                                                                   vspi.subscription_id = jmd.subscription_id
             LEFT JOIN employer.jcoin_model_revenue_cashflow jmrc
                       ON jmrc.database_source_id = msp.database_source_id AND jmrc.payment_id = msp.payment_id
    GROUP BY msp.database_source_id, msp.subscription_id, msp.packet_rank, msp.employer_id, msp.packet_jcoin_cnt,
             vspi.packet_short_name,
             msp.packet_start_datetime,
           msp.packet_end_datetime,
           msp.packet_plan_end_datetime,
           msp.packet_price,
           msp.packet_id,
           msp.subscription_month_cnt,
           msp.subscription_order_datetime,
           msp.packet_type_id,
           msp.payment_id,
           msp.payment_rank,
           msp.payment_start_datetime,
           msp.payment_end_datetime,
           msp.packet_type_paid_result_id,
             msp.packet_next_start_datetime,
             (
                 CASE
                     WHEN msp.packet_type_id = 1 THEN 'free'::text
                     WHEN msp.packet_type_paid_result_id = ANY (ARRAY [2, 3]) THEN 'unpaid'::text
                     WHEN msp.packet_type_paid_result_id = 1 THEN 'paid'::text
                     ELSE NULL::text
                     END),
             (
                 CASE
                     WHEN msp.packet_type_id = 1 THEN 'free'::text
                     WHEN msp.packet_type_paid_result_id = ANY (ARRAY [2, 3])
                         THEN 'unpaid '::text || vspi.packet_short_name::text
                     WHEN msp.packet_type_paid_result_id = 1 THEN 'paid '::text || vspi.packet_short_name::text
                     ELSE NULL::text
                     END),
             (round(COALESCE(jmrc.payment_without_vat_uah, 0::numeric) / jmrc.subscription_payment_month_cnt::numeric,
                    2)),
             vspi.payment_method
),
     -- додаю дані про retention в наступний пакет та дані про перехід в наступний пакет та активність роботодавця
     packet_data_with_next_packet_data AS (
         SELECT packet_data.database_source_id,
                packet_data.subscription_id,
                packet_data.packet_rank,
                packet_data.employer_id,
                packet_data.packet_jcoin_cnt,
                packet_data.packet_start_datetime,
                packet_data.packet_end_datetime,
                packet_data.packet_plan_end_datetime,
                packet_data.packet_day_cnt,
                packet_data.packet_price,
                packet_data.packet_id,
                packet_data.subscription_month_cnt,
                packet_data.subscription_order_datetime,
                packet_data.packet_type_id,
                packet_data.payment_id,
                packet_data.payment_rank,
                packet_data.payment_start_datetime,
                packet_data.payment_end_datetime,
                packet_data.packet_type_paid_result_id,
                packet_data.packet_next_start_datetime,
                packet_data.jcoin_utilized_cnt,
                packet_data.jcoin_plan_cnt,
                packet_data.call_cnt,
                packet_data.call_answered_cnt,
                packet_data.call_answered_30_sec_free_packet_cnt,
                packet_data.call_answered_30_sec_paid_packet_cnt,
                packet_data.apply_cnt,
                packet_data.apply_profile_viewed_cnt,
                packet_data.apply_profile_open_contact_free_packet_cnt,
                packet_data.apply_profile_open_contact_paid_packet_cnt,
                packet_data.apply_profile_message_cnt,
                packet_data.profile_base_search_cnt,
                packet_data.profile_base_profile_viewed_cnt,
                packet_data.profile_base_profile_open_contact_free_packet_cnt,
                packet_data.profile_base_profile_open_contact_paid_packet_cnt,
                packet_data.profile_base_profile_message_cnt,
                packet_data.digital_recruiter_profile_cnt,
                packet_data.digital_recruiter_profile_viewed_cnt,
                packet_data.digital_recruiter_profile_open_contact_free_packet_cnt,
                packet_data.digital_recruiter_profile_open_contact_paid_packet_cnt,
                packet_data.digital_recruiter_profile_message_cnt,
                packet_data.job_active_avg,
                packet_data.packet_short_name,
                packet_data.packet_type,
                packet_data.packet_type_size,
                packet_data.apply_revenue,
                packet_data.call_revenue,
                packet_data.digital_recruiter_revenue,
                packet_data.profile_base_revenue,
                packet_data.packet_payment_without_vat_uah,
                lead(packet_data.packet_type)
                OVER (PARTITION BY packet_data.database_source_id, packet_data.employer_id ORDER BY packet_data.packet_start_datetime) AS next_packet_type,
                lead(packet_data.packet_jcoin_cnt)
                OVER (PARTITION BY packet_data.database_source_id, packet_data.employer_id ORDER BY packet_data.packet_start_datetime) AS next_packet_jcoin_cnt,
                lead(packet_data.packet_payment_without_vat_uah)
                OVER (PARTITION BY packet_data.database_source_id, packet_data.employer_id ORDER BY packet_data.packet_start_datetime) AS next_packet_payment_without_vat_uah,
                lead(packet_data.packet_start_datetime)
                OVER (PARTITION BY packet_data.database_source_id, packet_data.employer_id ORDER BY packet_data.packet_start_datetime) AS next_packet_start_datetime,
                lead(packet_data.payment_start_datetime)
                OVER (PARTITION BY packet_data.database_source_id, packet_data.employer_id ORDER BY packet_data.packet_start_datetime) as next_payment_start_datetime,
                sps.jcoin_spent_datediff-public.fn_get_date_diff(packet_data.packet_start_datetime) as jcoin_spent_packet_day,
           (SELECT ec.company_name
            FROM imp_employer.employer e
                     JOIN imp_employer.employer_cdp ec ON ec.sources = e.sources AND ec.id = e.id_cdp
            WHERE e.sources = packet_data.database_source_id
              AND e.id = packet_data.employer_id)                                  AS company_name,
           (SELECT ec.industry
            FROM imp_employer.employer e
                     JOIN imp_employer.employer_cdp ec ON ec.sources = e.sources AND ec.id = e.id_cdp
            WHERE e.sources = packet_data.database_source_id
              AND e.id = packet_data.employer_id)                                  AS industry,
           CASE
               WHEN vpper.next_packet_start_datetime IS NOT NULL THEN 1
               ELSE 0
               END                                                         AS is_retained,
           CASE
               WHEN vpper.next_packet_start_datetime = vpper.next_payment_start_datetime AND
                    vpper.next_packet_start_datetime <= (vpper.packet_start_datetime + '1 mon 5 days'::interval)
                   THEN 'retained'::text
               WHEN vpper.next_packet_start_datetime = vpper.next_payment_start_datetime
                   THEN 'retained after 1 month 5 days'::text
               WHEN vpper.next_packet_start_datetime IS NOT NULL THEN 'autoretained'::text
               WHEN vpper.packet_start_datetime IS NOT NULL THEN 'dropped'::text
               ELSE NULL::text
               END                                                         AS retention_type,
                -- дані про активність роботодавця (для фільтру неактивних роботодавців)
             max(case when packet_type_id = 1 and job_active_avg = 0 and jcoin_utilized_cnt = 0 and profile_base_profile_message_cnt = 0 then 0 else 1 end) over (partition by packet_data.database_source_id, packet_data.employer_id) as is_active_employer,
            case when packet_type_id = 1 and job_active_avg = 0 and jcoin_utilized_cnt = 0 and profile_base_profile_message_cnt = 0 then 0 else 1 end as is_active_packet,
            lead(case when packet_type_id = 1 and job_active_avg = 0 and jcoin_utilized_cnt = 0 and profile_base_profile_message_cnt = 0 then 0 else 1 end) over (PARTITION BY packet_data.database_source_id, packet_data.employer_id ORDER BY packet_data.packet_start_datetime) as is_active_next_packet,
                payment_method

         FROM packet_data
         left join employer.subscription_packet_spent sps
                on packet_data.database_source_id = sps.database_source_id and
                   packet_data.subscription_id = sps.subscription_id and
                   packet_data.packet_rank = sps.packet_rank
         LEFT JOIN pbi.v_paid_packet_employer_retention vpper
                ON vpper.database_source_id= packet_data.database_source_id and
                   vpper.subscription_id = packet_data.subscription_id AND
                   vpper.packet_rank = packet_data.packet_rank
     )

-- фільтрую активні пакети (виключаю їх), фільтрую роботодавців, пакети яких взагалі ніяк не використовувалися
SELECT packet_data_with_next_packet_data.database_source_id,
       packet_data_with_next_packet_data.subscription_id,
       packet_data_with_next_packet_data.packet_rank,
       packet_data_with_next_packet_data.employer_id,
       packet_data_with_next_packet_data.packet_jcoin_cnt,
       packet_data_with_next_packet_data.packet_start_datetime,
       packet_data_with_next_packet_data.packet_end_datetime,
       packet_data_with_next_packet_data.packet_plan_end_datetime,
       packet_data_with_next_packet_data.packet_day_cnt,
       packet_data_with_next_packet_data.packet_price,
       packet_data_with_next_packet_data.packet_id,
       packet_data_with_next_packet_data.subscription_month_cnt,
       packet_data_with_next_packet_data.subscription_order_datetime,
       packet_data_with_next_packet_data.packet_type_id,
       packet_data_with_next_packet_data.payment_id,
       packet_data_with_next_packet_data.payment_rank,
       packet_data_with_next_packet_data.payment_start_datetime,
       packet_data_with_next_packet_data.payment_end_datetime,
       packet_data_with_next_packet_data.packet_type_paid_result_id,
       packet_data_with_next_packet_data.packet_next_start_datetime,
       packet_data_with_next_packet_data.jcoin_utilized_cnt,
       packet_data_with_next_packet_data.jcoin_plan_cnt,
       packet_data_with_next_packet_data.call_cnt,
       packet_data_with_next_packet_data.call_answered_cnt,
       packet_data_with_next_packet_data.call_answered_30_sec_free_packet_cnt,
       packet_data_with_next_packet_data.call_answered_30_sec_paid_packet_cnt,
       packet_data_with_next_packet_data.apply_cnt,
       packet_data_with_next_packet_data.apply_profile_viewed_cnt,
       packet_data_with_next_packet_data.apply_profile_open_contact_free_packet_cnt,
       packet_data_with_next_packet_data.apply_profile_open_contact_paid_packet_cnt,
       packet_data_with_next_packet_data.apply_profile_message_cnt,
       packet_data_with_next_packet_data.profile_base_search_cnt,
       packet_data_with_next_packet_data.profile_base_profile_viewed_cnt,
       packet_data_with_next_packet_data.profile_base_profile_open_contact_free_packet_cnt,
       packet_data_with_next_packet_data.profile_base_profile_open_contact_paid_packet_cnt,
       packet_data_with_next_packet_data.profile_base_profile_message_cnt,
       packet_data_with_next_packet_data.digital_recruiter_profile_cnt,
       packet_data_with_next_packet_data.digital_recruiter_profile_viewed_cnt,
       packet_data_with_next_packet_data.digital_recruiter_profile_open_contact_free_packet_cnt,
       packet_data_with_next_packet_data.digital_recruiter_profile_open_contact_paid_packet_cnt,
       packet_data_with_next_packet_data.digital_recruiter_profile_message_cnt,
       packet_data_with_next_packet_data.job_active_avg,
       packet_data_with_next_packet_data.packet_short_name,
       packet_data_with_next_packet_data.retention_type,
       packet_data_with_next_packet_data.packet_type,
       packet_data_with_next_packet_data.packet_type_size,
       packet_data_with_next_packet_data.company_name,
       packet_data_with_next_packet_data.is_retained,
       packet_data_with_next_packet_data.apply_revenue,
       packet_data_with_next_packet_data.call_revenue,
       packet_data_with_next_packet_data.digital_recruiter_revenue,
       packet_data_with_next_packet_data.profile_base_revenue,
       packet_data_with_next_packet_data.packet_payment_without_vat_uah,
       packet_data_with_next_packet_data.next_packet_type,
       packet_data_with_next_packet_data.next_packet_jcoin_cnt,
       CASE
           WHEN packet_type = 'paid'::text AND next_packet_type = 'paid'::text
               THEN 'paid -> paid '::text ||
                    CASE
                        WHEN next_packet_jcoin_cnt >  packet_jcoin_cnt
                            THEN 'bigger'::text
                        WHEN next_packet_jcoin_cnt = packet_jcoin_cnt AND
                             next_payment_start_datetime = payment_start_datetime
                            THEN 'same (auto)'::text
                        WHEN next_packet_jcoin_cnt = packet_jcoin_cnt AND
                             next_payment_start_datetime <> payment_start_datetime
                            THEN 'same (new payment)'::text
                        WHEN next_packet_jcoin_cnt < packet_jcoin_cnt
                            THEN 'smaller'::text
                        ELSE NULL::text
                        END
           ELSE (packet_type
                     || case when packet_type = 'free' and is_active_packet = 0  then ' (inactive)'
                             when packet_type = 'free' and is_active_packet = 1  then ' (active)'
                         else '' end
                     || ' -> '::text)
                     || next_packet_type
                     || case when next_packet_type = 'free' and is_active_next_packet = 0  then ' (inactive)'
                             when next_packet_type = 'free' and is_active_next_packet = 1  then ' (active)'
                         else '' end
           END  AS packet_type_transition,
       packet_data_with_next_packet_data.next_packet_payment_without_vat_uah,
       packet_data_with_next_packet_data.next_packet_start_datetime,
       jcoin_spent_packet_day,
       industry,
       is_active_packet,
       is_active_next_packet,
       pjps.profile_submitted_cnt,
       round(vpjaa.job_abroad_prc,2) as job_abroad_prc,
       round(vpjbcsa.blue_score_avg,2) as blue_score_avg,
       packet_data_with_next_packet_data.payment_method,
       ec.channel as acquisition_channel,
       ec.registration_source,
       -- блок активності роботодавця в пакеті
       paaa.account_cnt,
       paaa.active_account_cnt,
       paaa.session_cnt,
       case when sum(is_retained) over (partition by packet_data_with_next_packet_data.database_source_id, packet_data_with_next_packet_data.employer_id) > 0
            then sum(is_retained) over (partition by packet_data_with_next_packet_data.database_source_id, packet_data_with_next_packet_data.employer_id) +1
        else 0
        end as employer_paid_packet_cnt
FROM packet_data_with_next_packet_data
-- додаю створені профілі з jdp вакансій пакету
left join employer.v_packet_job_profile_submission_agg pjps
       on pjps.database_source_id = packet_data_with_next_packet_data.database_source_id and
          pjps.subscription_id = packet_data_with_next_packet_data.subscription_id and
          pjps.packet_rank = packet_data_with_next_packet_data.packet_rank
-- додаю % вакансій за кордоном
left join employer.v_packet_job_abroad_agg vpjaa
       on vpjaa.database_source_id = packet_data_with_next_packet_data.database_source_id and
          vpjaa.subscription_id = packet_data_with_next_packet_data.subscription_id and
          vpjaa.packet_rank = packet_data_with_next_packet_data.packet_rank
-- додаю синьокомірцевість компанії
left join employer.v_packet_job_blue_collar_score_agg vpjbcsa
       on vpjbcsa.database_source_id = packet_data_with_next_packet_data.database_source_id and
          vpjbcsa.subscription_id = packet_data_with_next_packet_data.subscription_id and
          vpjbcsa.packet_rank = packet_data_with_next_packet_data.packet_rank
-- додаю канал acquisition
left join pbi.employer_category ec
       on ec.database_source_id = packet_data_with_next_packet_data.database_source_id and
          ec.employer_id = packet_data_with_next_packet_data.employer_id
left join employer.v_packet_employer_activity_agg paaa
       on paaa.database_source_id = packet_data_with_next_packet_data.database_source_id and
          paaa.subscription_id = packet_data_with_next_packet_data.subscription_id and
          paaa.packet_rank = packet_data_with_next_packet_data.packet_rank
-- прибираю поточні пакети та ті, по яким ще може перерахуватися retention.
-- прибираю компанії з неактивними фрі пакетами
WHERE (packet_data_with_next_packet_data.next_packet_start_datetime + '5 days'::interval) < CURRENT_DATE and
      is_active_employer = 1
;

alter table cube.v_employer_packet_utilization_retention
    owner to dap;

grant select on cube.v_employer_packet_utilization_retention to readonly;
