create procedure profile.insert_traffic_profile_arpu_7_days()
	language plpgsql
as $$
declare _var_dd_start int := (select fn_get_date_diff(current_date) - 15);
    declare _var_dd_stop int := (select fn_get_date_diff(current_date) - 7);
begin

	--truncate table profile.traffic_profile_arpu_7_days;
	delete from profile.traffic_profile_arpu_7_days where submission_datediff >= _var_dd_start;

	insert into profile.traffic_profile_arpu_7_days(country_id, profile_id, submission_datediff,
	                                                session_traffic_source_id, session_traffic_source_group_id,
	                                                profile_base_open_contact_price,
	                                                profile_base_open_contact_jcoin_cnt, apply_open_contact_price,
	                                                apply_open_contact_jcoin_cnt, digital_recruiter_open_contact_price,
	                                                digital_recruiter_open_contact_jcoin_cnt, call_open_contact_price,
	                                                call_open_contact_jcoin_cnt)
    select pss.country_id,
           pss.profile_id,
           pss.submission_datediff,
           pss.session_traffic_source_id,
           pss.session_traffic_source_group_id,
           (select coalesce(sum(pboc.open_contact_price), 0)
            from profile_base.profile_base_open_contact pboc
            where pss.country_id = 1
              and pboc.database_source_id = 1
              and pss.profile_id = pboc.profile_id
              and pboc.open_contact_datediff - pss.submission_datediff between 0 and 7
           )                                               as profile_base_open_contact_price,
           (select coalesce(count(pboc.profile_id), 0)
            from profile_base.profile_base_open_contact pboc
            where pss.country_id = 1
              and pboc.database_source_id = 1
              and pss.profile_id = pboc.profile_id
              and pboc.open_contact_datediff - pss.submission_datediff between 0 and 7
           )                                               as profile_base_open_contact_jcoin_cnt,
           (select coalesce(sum(aop.open_contact_price), 0)
            from employer.apply_open_contact aop
            where aop.database_source_id = 1
              and aop.profile_id = pss.profile_id
              and fn_get_date_diff(aop.open_contact_datetime) - pss.submission_datediff between 0 and 7)
                                                           as apply_open_contact_price,
           (select coalesce(count(aop.profile_id), 0)
            from employer.apply_open_contact aop
            where aop.database_source_id = 1
              and aop.profile_id = pss.profile_id
              and fn_get_date_diff(aop.open_contact_datetime) - pss.submission_datediff between 0 and 7)
                                                           as apply_open_contact_jcoin_cnt,
           (select coalesce(sum(droc.open_contact_price), 0)
            from employer.digital_recruiter_open_contact droc
            where droc.database_source_id = 1
              and droc.profile_id = pss.profile_id
              and fn_get_date_diff(droc.open_contact_datetime) -
                  pss.submission_datediff between 0 and 7) as digital_recruiter_open_contact_price,
           (select coalesce(count(droc.profile_id), 0)
            from employer.digital_recruiter_open_contact droc
            where droc.database_source_id = 1
              and droc.profile_id = pss.profile_id
              and fn_get_date_diff(droc.open_contact_datetime) -
                  pss.submission_datediff between 0 and 7) as digital_recruiter_open_contact_jcoin_cnt,

           (select coalesce(sum(cop.open_contact_price), 0)
            from profile.call_open_contact cop
            where cop.country_id = 1
              and cop.profile_id = pss.profile_id
              and cop.call_datediff - pss.submission_datediff between 0 and 7
           )                                               as call_open_contact_price,
           (select coalesce(count(cop.profile_id), 0)
            from profile.call_open_contact cop
            where cop.country_id = 1
              and cop.profile_id = pss.profile_id
              and cop.call_datediff - pss.submission_datediff between 0 and 7
           )                                               as call_open_contact_jcoin_cnt
    from profile.profile_submission_traffic_source as pss
    where pss.submission_datediff between _var_dd_start and _var_dd_stop
    group by 1, 2, 3, 4, 5;


end
$$;

alter procedure profile.insert_traffic_profile_arpu_7_days() owner to yiv;

