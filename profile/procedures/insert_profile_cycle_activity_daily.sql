create procedure profile.insert_profile_cycle_activity_daily()
	language plpgsql
as $$
begin

	truncate profile.profile_cycle_activity_daily;

	insert into profile.profile_cycle_activity_daily(country_id, profile_id, profile_blue_collar_type_id,
                                          seeker_cycle_start_type_id, seeker_cycle_first_datediff, action_datediff,
                                          day_from_cycle_start, action_to_contact_cnt, session_cnt, search_cnt,
                                          jdp_dte_view_cnt, jdp_agg_view_cnt)
	with
		job_seeker_cycle as (
			select jscs.country_id,
				   jscs.profile_id,
				   bs.profile_blue_collar_type_id,

				   jscs.seeker_cycle_start_type_id,
				   jscs.seeker_cycle_first_datediff,
				   jscs.seeker_cycle_first_datediff + day_from_cycle_start as action_datediff,
				   day_from_cycle_start                                    as day_from_cycle_start
			from profile.job_seeker_cycle_start jscs
				 cross join generate_series(0, 27) as day_from_cycle_start
				 left join  profile.profile_blue_score bs
				 on jscs.country_id = bs.country_id
					 and jscs.profile_id = bs.profile_id
			where seeker_cycle_first_datediff >= 44195 /* 2021-01-01 */
			  and jscs.seeker_cycle_first_datediff + day_from_cycle_start <= fn_get_date_diff(current_date - 1)
		),
		session_agg as (
			select country_id,
				   session_datediff           as action_datediff,
				   profile_id,
				   count(distinct session_id) as session_cnt
			from profile.profile_session
			where session_datediff >= 44195 /* 2021-01-01 */
			group by country_id, session_datediff, profile_id
		),
		atc_agg as (
			select country_id,
				   action_datediff,
				   profile_id,
				   sum(action_to_contact_prob) as action_to_contact_cnt
			from profile.job_seeker_action_to_contact atc
			where action_datediff >= 44195 /* 2021-01-01 */
			group by country_id, action_datediff, profile_id
		),
		search_agg as (
			select country_id,
				   search_datediff           as action_datediff,
				   profile_id,
				   count(distinct search_id) as search_cnt
			from profile.profile_search
			where search_datediff >= 44195 /* 2021-01-01 */
			group by country_id, search_datediff, profile_id
		),
		jdp_view_agg as (
			select country_id,
				   jdp_viewed_datediff                                        as action_datediff,
				   profile_id,
				   count(distinct case when jdp_flag & 1 = 1 then jdp_id end) as jdp_dte_view_cnt,
				   count(distinct case when jdp_flag & 1 = 0 then jdp_id end) as jdp_agg_view_cnt
			from profile.profile_jdp
			where jdp_viewed_datediff >= 44195 /* 2021-01-01 */
			group by country_id, jdp_viewed_datediff, profile_id
		)
	select jscs.country_id,
		   jscs.profile_id,
		   jscs.profile_blue_collar_type_id,
		   jscs.seeker_cycle_start_type_id,
		   jscs.seeker_cycle_first_datediff,
		   jscs.action_datediff,
		   jscs.day_from_cycle_start,

		   coalesce(atc.action_to_contact_cnt, 0) as action_to_contact_cnt,
		   coalesce(s.session_cnt, 0)             as session_cnt,
		   coalesce(sr.search_cnt, 0)             as search_cnt,
		   coalesce(j.jdp_dte_view_cnt, 0)        as jdp_dte_view_cnt,
		   coalesce(j.jdp_agg_view_cnt, 0)        as jdp_agg_view_cnt
	from job_seeker_cycle jscs
		 left join atc_agg atc
		 on jscs.country_id = atc.country_id
			 and jscs.profile_id = atc.profile_id
			 and jscs.action_datediff = atc.action_datediff
		 left join session_agg s
		 on jscs.country_id = s.country_id
			 and jscs.profile_id = s.profile_id
			 and jscs.action_datediff = s.action_datediff
		 left join search_agg sr
		 on jscs.country_id = sr.country_id
			 and jscs.profile_id = sr.profile_id
			 and jscs.action_datediff = sr.action_datediff
		 left join jdp_view_agg j
		 on jscs.country_id = j.country_id
			 and jscs.profile_id = j.profile_id
			 and jscs.action_datediff = j.action_datediff;

end;
$$;

alter procedure profile.insert_profile_cycle_activity_daily() owner to yiv;

