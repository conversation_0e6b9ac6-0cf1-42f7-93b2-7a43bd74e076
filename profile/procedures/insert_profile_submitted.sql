create or replace procedure profile.insert_profile_submitted()
	language sql
as $$
truncate table profile.profile_submitted;

        insert into profile.profile_submitted (country_id, id, email, phone, creation_datediff, data, date_created, date_updated, submission_datediff, first_input_datediff)
        select country_id, id, email, phone, creation_datediff, data, date_created, date_updated, submission_datediff, first_input_datediff
        from profile.v_profile_submitted;

$$;

alter procedure profile.insert_profile_submitted() owner to rlu;

