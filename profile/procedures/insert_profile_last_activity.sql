create procedure profile.insert_profile_last_activity()
	language plpgsql
as $$
begin

	truncate profile.profile_last_activity;


	insert into profile.profile_last_activity(country_id, profile_id, submission_datediff, last_atc_datediff,
                                           last_session_datediff, last_cycle_start_datediff)
		select p.country                             as country_id,
       p.id                                  as profile_id,
       max(p.date_submitted)                 as submission_datediff,
       max(jatc.action_datediff)             as last_atc_datediff,
       max(session_datediff)                 as last_session_datediff,
       max(jscs.seeker_cycle_first_datediff) as last_cycle_start_datediff
	from imp.profiles p
		 left join profile.job_seeker_action_to_contact jatc
		 on p.country = jatc.country_id
			 and p.id = jatc.profile_id
		 left join profile.profile_session ps
		 on p.country = ps.country_id
			 and p.id = ps.profile_id
		 left join profile.job_seeker_cycle_start jscs
		 on p.country = jscs.country_id
			 and p.id = jscs.profile_id
	group by p.country, p.id
	having fn_get_date_from_date_diff(max(session_datediff)) >= '2021-08-01';


end;
$$;

alter procedure profile.insert_profile_last_activity() owner to postgres;

