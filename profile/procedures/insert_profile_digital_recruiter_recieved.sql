create or replace procedure profile.insert_profile_digital_recruiter_recieved(_datediff integer)
    language plpgsql
as
$$
begin

            truncate table profile.profile_digital_recruiter_recieved;

            insert into profile.profile_digital_recruiter_recieved(country_id, profile_id, submission_datediff, submission_datetime,
                                                                   digital_recruiter_profile_recieved_1_day_cnt, digital_recruiter_profile_recieved_7_days_cnt,
                                                                   digital_recruiter_profile_recieved_14_days_cnt, digital_recruiter_profile_recieved_28_days_cnt)
            select vps.country_id,
                   vps.profile_id,
                   vps.submission_datediff,
                   vps.submission_datetime,
                   count(distinct case when ja.date between vps.submission_datetime and vps.submission_datetime + interval '1 day' then ja.id end) as digital_recruiter_profile_recieved_1_day_cnt,
                   count(distinct case when ja.date between vps.submission_datetime and vps.submission_datetime + interval '7 days' then ja.id end) as digital_recruiter_profile_recieved_7_days_cnt,
                   count(distinct case when ja.date between vps.submission_datetime and vps.submission_datetime + interval '14 days' then ja.id end) as digital_recruiter_profile_recieved_14_days_cnt,
                   count(distinct case when ja.date between vps.submission_datetime and vps.submission_datetime + interval '28 days' then ja.id end) as digital_recruiter_profile_recieved_28_days_cnt
            from  profile.profile_submitted_with_double vps
            left join imp_employer.job_apply_profile jap on jap.sources = 1 and jap.id_profile = vps.profile_id
            left join imp_employer.job_apply ja on ja.sources = jap.sources and ja.id = jap.id_apply and ja.flags &1024 = 1024 /*digital recruiter*/ and
                                                   ja.date between vps.submission_datetime and vps.submission_datetime + interval '28 days'
            where vps.country_id = 1
            group by vps.country_id,
                     vps.profile_id,
                     vps.submission_datediff,
                     vps.submission_datetime;

end;

$$;

alter procedure profile.insert_profile_digital_recruiter_recieved(integer) owner to rlu;
