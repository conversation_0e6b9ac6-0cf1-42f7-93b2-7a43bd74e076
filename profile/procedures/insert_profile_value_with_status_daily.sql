create procedure profile.insert_profile_value_with_status_daily()
	language plpgsql
as $$
begin

	truncate profile.profile_value_with_status_daily;

	insert into profile.profile_value_with_status_daily (country_id, action_datediff, profile_id, employer_account_id,
                                             platform_user_type_id, feature_type_id, status_flags,
                                             next_7_days_max_status_flags, value_type, value_cnt)
	select v.country_id,
       v.action_datediff,
       v.profile_id,
       v.employer_account_id,
       v.platform_user_type_id,
       v.feature_type_id,
       v.status_flags,
       v.next_7_days_max_status_flags,
       v.value_type,
       coalesce(sum(v.value), 0) as value_cnt
	from profile.profile_value_with_status v
	group by country_id, action_datediff, profile_id, employer_account_id, platform_user_type_id, feature_type_id, status_flags,
			 next_7_days_max_status_flags, value_type;




end;
$$;

alter procedure profile.insert_profile_value_with_status_daily() owner to yiv;

