create procedure profile.insert_profile_value_with_status_cycle_agg()
	language plpgsql
as $$
begin


	truncate profile.profile_value_with_status_cycle_agg;

	insert into profile.profile_value_with_status_cycle_agg (country_id, seeker_cycle_first_datediff, profile_id,
                                                 seeker_cycle_start_type_id, employer_account_id, platform_user_type_id,
                                                 feature_type_id, status_flags, next_7_days_max_status_flags,
                                                 value_type, value_7_days_cnt, value_15_days_cnt, value_20_days_cnt,
                                                 value_30_days_cnt, value_60_days_cnt)
	with
    job_seaker_cycle as (
        select jscs.country_id,
               jscs.profile_id,
               jscs.seeker_cycle_start_type_id,
               jscs.seeker_cycle_first_datediff,
               jscs.seeker_cycle_first_datediff + day_from_cycle_start as action_datediff,
               day_from_cycle_start
        from profile.job_seeker_cycle_start jscs
             cross join generate_series(0, 59) as day_from_cycle_start
        where seeker_cycle_start_type_id in (1, 2)
    )
	select jsc.country_id,
		   jsc.seeker_cycle_first_datediff,
		   jsc.profile_id,
		   jsc.seeker_cycle_start_type_id,
		   v.employer_account_id,
		   v.platform_user_type_id,
		   v.feature_type_id,

		   v.status_flags,
		   v.next_7_days_max_status_flags,
		   v.value_type,
		   coalesce(sum(case when day_from_cycle_start <= 6 then v.value end), 0)  as value_7_days_cnt,
		   coalesce(sum(case when day_from_cycle_start <= 14 then v.value end), 0) as value_15_days_cnt,
		   coalesce(sum(case when day_from_cycle_start <= 19 then v.value end), 0) as value_20_days_cnt,
		   coalesce(sum(case when day_from_cycle_start <= 29 then v.value end), 0) as value_30_days_cnt,
		   coalesce(sum(case when day_from_cycle_start <= 59 then v.value end), 0) as value_60_days_cnt
	from job_seaker_cycle jsc
		 join profile.profile_value_with_status v
		 on jsc.profile_id = v.profile_id
			 and jsc.seeker_cycle_first_datediff + day_from_cycle_start = v.action_datediff
	group by jsc.country_id, jsc.seeker_cycle_first_datediff, v.employer_account_id, jsc.profile_id, jsc.seeker_cycle_start_type_id, v.platform_user_type_id, v.feature_type_id, v.status_flags,
			 v.next_7_days_max_status_flags, v.value_type;




end;
$$;

alter procedure profile.insert_profile_value_with_status_cycle_agg() owner to yiv;

