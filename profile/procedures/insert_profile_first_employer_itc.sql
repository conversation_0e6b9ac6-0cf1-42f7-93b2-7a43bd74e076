create or replace procedure profile.insert_profile_first_employer_itc(_datediff integer)
    language plpgsql
as
$$
begin

            truncate table profile.profile_first_employer_itc;

            insert into profile.profile_first_employer_itc(database_source_id, country_id, profile_id, submission_datetime, first_itc_datetime)
            select database_source_id,
                   1 as country_id,
                   ps.profile_id,
                   ps.submission_datetime,
                   min(action_datetime) as first_itc_datetime
            from employer.v_profile_open_contact_with_packet v
            join profile.profile_submitted_with_double ps
             on ps.country_id = 1 and
                ps.profile_id = v.profile_id
            where action_datediff is not null
            group by database_source_id,
                     ps.submission_datetime,
                     ps.profile_id;

end;

$$;

alter procedure profile.insert_profile_first_employer_itc(integer) owner to rlu;
