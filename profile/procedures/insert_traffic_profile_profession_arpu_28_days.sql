create procedure profile.insert_traffic_profile_profession_arpu_28_days()
	language plpgsql
as $$
begin

    truncate table profile.traffic_profile_profession_arpu_28_days;

    insert into profile.traffic_profile_profession_arpu_28_days(country_id, profile_id, profession_id, profession_name,
                                                                parrent_profession_id, parrent_profession_name,
                                                                submission_datediff, session_traffic_source_id,
                                                                session_traffic_source_group_id,
                                                                profile_base_open_contact_price,
                                                                profile_base_open_contact_jcoin_cnt,
                                                                apply_open_contact_price, apply_open_contact_jcoin_cnt,
                                                                digital_recruiter_open_contact_price,
                                                                digital_recruiter_open_contact_jcoin_cnt,
                                                                call_open_contact_price, call_open_contact_jcoin_cnt)
    select pp.country_id,
           pp.profile_id,
           pap.profession_id,
           pap.profession_name,
           pap.parrent_id as parrent_profession_id,
           h.name         as parrent_profession_name,
           pa.submission_datediff,
           pa.session_traffic_source_id,
           pa.session_traffic_source_group_id,
           pa.profile_base_open_contact_price,
           pa.profile_base_open_contact_jcoin_cnt,
           pa.apply_open_contact_price,
           pa.apply_open_contact_jcoin_cnt,
           pa.digital_recruiter_open_contact_price,
           pa.digital_recruiter_open_contact_jcoin_cnt,
           pa.call_open_contact_price,
           pa.call_open_contact_jcoin_cnt
    from profile.profile_profession pp
    -- до ід_професії додаю parrent_id
             join profile.parrent_profession pap
                  on pap.country_id = pp.country_id
                      and pap.profession_id = pp.profession_id
    -- до parrent_id додаю parrent_profession_name
             join imp.hashtag h
                  on h.country = pap.country_id
                      and pap.parrent_id = h.id
    -- додаю інформацію по arpu
             join profile.traffic_profile_arpu_28_days pa
                  on pp.country_id = pa.country_id
                      and pp.profile_id = pa.profile_id
    where pp.country_id = 1;


end;
$$;

alter procedure profile.insert_traffic_profile_profession_arpu_28_days() owner to mb;

