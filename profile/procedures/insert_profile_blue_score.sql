create procedure profile.insert_profile_blue_score(_datediff integer)
	language plpgsql
as $$
begin

            truncate table profile.profile_blue_score;

            insert into profile.profile_blue_score(country_id, profile_id, blue_score, profile_blue_collar_type_id)
            SELECT pdj.country_id,
                   pdj.profile_id,
                   max(case when pbs.blue_score>= 0.5 then 1 else 0 end) AS blue_score,
                   CASE
                       WHEN max(case when pbs.blue_score>= 0.5 then 1 else 0 end) = 0 THEN 1
                       WHEN max(case when pbs.blue_score>= 0.5 then 1 else 0 end) = 1 THEN 3
                       ELSE 0
                       END::smallint   AS profile_blue_collar_type_id
            FROM profile.profile_profession pdj
                     LEFT JOIN dimension.info_profession ip ON ip.id = pdj.profession_id AND ip.country = pdj.country_id
                     LEFT JOIN dimension.professions_blue_score pbs ON pbs.profession::text = ip.name::text
            WHERE pdj.country_id = 1
            GROUP BY pdj.country_id, pdj.profile_id;


end;

$$;

alter procedure profile.insert_profile_blue_score(integer) owner to rlu;

