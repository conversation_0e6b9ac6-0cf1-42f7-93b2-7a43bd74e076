create or replace view profile.v_profile_liveliness_level_daily(id, report_datediff, registration_source_id, profile_blue_collar_type_id, profile_cnt, alive_profile_cnt, only_created_profile_cnt, only_returned_profile_cnt, only_activated_profile_cnt, only_activated_and_returned_profile_cnt, apply_returned_profile_cnt, country_id, country_name, registration_source_name, profile_blue_collar_type_name, submission_datediff, creation_datediff) as
	SELECT lld.id,
       lld.report_datediff,
       lld.registration_source_id,
       lld.profile_blue_collar_type_id,
       lld.profile_cnt,
       lld.alive_profile_cnt,
       lld.only_created_profile_cnt,
       lld.only_returned_profile_cnt,
       lld.only_activated_profile_cnt,
       lld.only_activated_and_returned_profile_cnt,
       lld.apply_returned_profile_cnt,
       lld.country_id,
       (SELECT c.alpha_2
        FROM dimension.countries c
        WHERE c.id = lld.country_id)                     AS country_name,
       (SELECT a.source_name
        FROM dimension.auth_source a
        WHERE a.id = lld.registration_source_id)         AS registration_source_name,
       (SELECT pbct.type_name
        FROM dimension.profile_blue_collar_type pbct
        WHERE pbct.id = lld.profile_blue_collar_type_id) AS profile_blue_collar_type_name,
       lld.submission_datediff,
       lld.creation_datediff
FROM profile.profile_liveliness_level_daily lld;

alter table profile.v_profile_liveliness_level_daily owner to postgres;

grant select on profile.v_profile_liveliness_level_daily to npo;

grant select on profile.v_profile_liveliness_level_daily to readonly;

grant select on profile.v_profile_liveliness_level_daily to nsh;

grant select on profile.v_profile_liveliness_level_daily to writeonly_product;

grant select on profile.v_profile_liveliness_level_daily to readonly_ds;

