create or replace view profile.v_sudoku_effect(initial_datediff, has_call_cnt, has_call_atc_per_profile, has_no_call_atc_per_profile, action_spread_per_profile) as
	WITH sudoku_effect_data AS (
    SELECT spp.initial_datediff,
           count(DISTINCT
                 CASE
                     WHEN spp.has_sudoku_call = 1 THEN spp.profile_id
                     ELSE NULL::integer
                     END)                                        AS has_call_cnt,
           sum(
                   CASE
                       WHEN spp.has_sudoku_call = 1 THEN atcs.action_to_contact_prob
                       ELSE 0::numeric
                       END) / NULLIF(count(DISTINCT
                                           CASE
                                               WHEN spp.has_sudoku_call = 1 THEN spp.profile_id
                                               ELSE NULL::integer
                                               END), 0)::numeric AS has_call_atc_per_profile,
           sum(
                   CASE
                       WHEN spp.has_sudoku_call = 0 THEN atcs.action_to_contact_prob
                       ELSE 0::numeric
                       END) / NULLIF(count(DISTINCT
                                           CASE
                                               WHEN spp.has_sudoku_call = 0 THEN spp.profile_id
                                               ELSE NULL::integer
                                               END), 0)::numeric AS has_no_call_atc_per_profile
    FROM profile.sudoku_profile spp
             LEFT JOIN profile.action_to_contact_structure atcs ON atcs.profile_id = spp.profile_id
    GROUP BY spp.initial_datediff
)
SELECT sudoku_effect_data.initial_datediff,
       sudoku_effect_data.has_call_cnt,
       sudoku_effect_data.has_call_atc_per_profile,
       sudoku_effect_data.has_no_call_atc_per_profile,
       (sudoku_effect_data.has_call_atc_per_profile - sudoku_effect_data.has_no_call_atc_per_profile) *
       sudoku_effect_data.has_call_cnt::numeric AS action_spread_per_profile
FROM sudoku_effect_data;

alter table profile.v_sudoku_effect owner to rlu;

grant select on profile.v_sudoku_effect to readonly;

grant select on profile.v_sudoku_effect to writeonly_product;

grant select on profile.v_sudoku_effect to readonly_ds;

