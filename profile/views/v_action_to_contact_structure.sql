create or replace view profile.v_action_to_contact_structure
            (country_id, profile_id, platform_user_type_id, feature_id, action_type_id, action_datediff,
             action_datetime, action_to_contact_prob, blue_collar_type_name, action_to_contact_type_name,
             platform_user_type_name, feature_type_name, action_from_seeker_cycle_start_day_interval, initiator_name,
             is_action_from_active_profile_base_user, employer_account_id)
as
SELECT atcs.country_id,
       atcs.profile_id,
       atcs.platform_user_type_id,
       atcs.feature_id,
       atcs.action_type_id,
       atcs.action_datediff,
       atcs.action_datetime,
       atcs.action_to_contact_prob,
       pbct.type_name                                                                            AS blue_collar_type_name,
       (SELECT atct.type_name
        FROM dimension.v_action_to_contact_type atct
        WHERE atct.type_id = atcs.action_type_id
          AND atct.platform_user_type_id = atcs.platform_user_type_id)                           AS action_to_contact_type_name,
       (SELECT put.type_name
        FROM dimension.platform_user_type put
        WHERE put.id = atcs.platform_user_type_id)                                               AS platform_user_type_name,
       (SELECT ft.feature_type_name
        FROM dimension.v_feature_type ft
        WHERE ft.feature_type_id = atcs.feature_id
          AND ft.platform_user_type_id = atcs.platform_user_type_id)                             AS feature_type_name,
       atcs.action_datediff - ((SELECT max(jscs.seeker_cycle_first_datediff) AS max
                                FROM profile.job_seeker_cycle_start jscs
                                WHERE jscs.country_id = atcs.country_id
                                  AND jscs.profile_id = atcs.profile_id
                                  AND jscs.seeker_cycle_first_datediff <= atcs.action_datediff)) AS action_from_seeker_cycle_start_day_interval,
       (SELECT jscs.initiator_name
        FROM profile.v_job_seeker_cycle_start jscs
        WHERE jscs.country_id = atcs.country_id
          AND jscs.profile_id = atcs.profile_id
          AND jscs.seeker_cycle_first_datediff <= atcs.action_datediff
        ORDER BY jscs.seeker_cycle_first_datediff DESC
        LIMIT 1)                                                                                 AS initiator_name,
       CASE
           WHEN (aew.active_vacancy_cnt = 0::numeric OR
                 (aew.employer_action_cnt / aew.active_vacancy_cnt) >= 150::numeric) AND
                atcs.platform_user_type_id = 2 AND atcs.feature_id = 3 THEN 1
           ELSE 0
           END                                                                                   AS is_action_from_active_profile_base_user,
       atcs.employer_account_id
FROM profile.action_to_contact_structure atcs
         LEFT JOIN profile.profile_blue_score pbs
                   ON pbs.country_id = atcs.country_id AND pbs.profile_id = atcs.profile_id
         LEFT JOIN dimension.profile_blue_collar_type pbct ON pbs.profile_blue_collar_type_id = pbct.id
         LEFT JOIN profile_base.active_employer_weekly aew ON aew.employer_account_id = atcs.employer_account_id AND
                                                              atcs.action_datediff >= aew.week_start_datediff AND
                                                              atcs.action_datediff <= (aew.week_start_datediff + 6) AND
                                                              atcs.country_id = 1;
