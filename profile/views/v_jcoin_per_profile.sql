with cycle_jcoin_agg as (
    select jscs.country_id,
           jscs.seeker_cycle_first_datediff,
           jscs.seeker_cycle_start_type_id,

           jscs.profile_id,
           jps.feature_type_id,
           coalesce(sum(
                            case
                                when (jps.action_datediff - jscs.seeker_cycle_first_datediff) = 0 then jps.jcoin_cnt
                                else null::bigint
                                end), 0::numeric) as jcoin_1_days_cnt,
           coalesce(sum(
                            case
                                when (jps.action_datediff - jscs.seeker_cycle_first_datediff) <= 1
                                    then jps.jcoin_cnt
                                else null::bigint
                                end), 0::numeric) as jcoin_2_days_cnt,
           coalesce(sum(
                            case
                                when (jps.action_datediff - jscs.seeker_cycle_first_datediff) <= 2
                                    then jps.jcoin_cnt
                                else null::bigint
                                end), 0::numeric) as jcoin_3_days_cnt,
           coalesce(sum(
                            case
                                when (jps.action_datediff - jscs.seeker_cycle_first_datediff) <= 6
                                    then jps.jcoin_cnt
                                else null::bigint
                                end), 0::numeric) as jcoin_7_days_cnt,
           coalesce(sum(
                            case
                                when (jps.action_datediff - jscs.seeker_cycle_first_datediff) <= 27
                                    then jps.jcoin_cnt
                                else null::bigint
                                end), 0::numeric) as jcoin_28_days_cnt
    from profile.job_seeker_cycle_start_js_only jscs
             left join profile.jcoin_profile_structure jps
                       on jscs.country_id = jps.country_id and jscs.profile_id = jps.profile_id and
                          (jps.action_datediff - jscs.seeker_cycle_first_datediff) >= 0 and
                          (jps.action_datediff - jscs.seeker_cycle_first_datediff) <= 27

    where jscs.seeker_cycle_first_datediff >= 44407
      and jscs.country_id = 1
    group by jscs.country_id,
             jscs.seeker_cycle_first_datediff,
             jscs.seeker_cycle_start_type_id,
             jscs.profile_id,
             jps.feature_type_id
)
select cycle_jcoin_agg.country_id,
       seeker_cycle_first_datediff,
       seeker_cycle_start_type_id,
       jscs_name.type_name  as seeker_cycle_start_type_name,
       pbs.profile_blue_collar_type_id,
       pbs_name.type_name   as profile_blue_collar_type_name,
       prs.registration_source_id,
       prs_name.source_name as registration_source_name,
       cycle_jcoin_agg.profile_id,
       feature_type_id,
       ft.type_name         as feature_type_name,
       jcoin_1_days_cnt,
       jcoin_2_days_cnt,
       jcoin_3_days_cnt,
       jcoin_7_days_cnt,
       jcoin_28_days_cnt
from cycle_jcoin_agg
         left join dimension.seeker_cycle_start_type jscs_name
                   on cycle_jcoin_agg.seeker_cycle_start_type_id = jscs_name.id

         left join profile.profile_blue_score pbs
                   on pbs.country_id = cycle_jcoin_agg.country_id and pbs.profile_id = cycle_jcoin_agg.profile_id
         left join dimension.profile_blue_collar_type pbs_name
                   on pbs.profile_blue_collar_type_id = pbs_name.id

         left join profile.profile_registration_source prs
                   on cycle_jcoin_agg.country_id = prs.country_id
                       and cycle_jcoin_agg.profile_id = prs.profile_id
         left join dimension.auth_source prs_name
                   on prs.registration_source_id = prs_name.id

         left join dimension.employer_feature_type ft
                   on feature_type_id = ft.id
where cycle_jcoin_agg.country_id = 1;
