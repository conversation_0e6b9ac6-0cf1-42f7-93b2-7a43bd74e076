create or replace view profile.v_sudoku_profile_submitted_retention_14days(date_submitted, retention_1_14days) as
	SELECT v_sudoku_profile_submitted_return.date_submitted,
       count(DISTINCT v_sudoku_profile_submitted_return.profile_id) AS retention_1_14days
FROM profile.v_sudoku_profile_submitted_return
WHERE v_sudoku_profile_submitted_return.jdp_view_on_day >= 1
  AND v_sudoku_profile_submitted_return.jdp_view_on_day <= 14
  AND v_sudoku_profile_submitted_return.date_submitted <=
      (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) - 15)
GROUP BY v_sudoku_profile_submitted_return.date_submitted;

alter table profile.v_sudoku_profile_submitted_retention_14days owner to rlu;

grant select on profile.v_sudoku_profile_submitted_retention_14days to readonly;

grant select on profile.v_sudoku_profile_submitted_retention_14days to writeonly_product;

grant select on profile.v_sudoku_profile_submitted_retention_14days to readonly_ds;

