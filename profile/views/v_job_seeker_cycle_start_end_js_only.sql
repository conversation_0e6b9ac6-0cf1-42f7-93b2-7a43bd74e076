create or replace view profile.v_job_seeker_cycle_start_end_js_only(country_id, profile_id, seeker_cycle_first_datediff, seeker_cycle_start_type_id, seeker_cycle_last_datediff) as
	SELECT s.country_id,
       s.profile_id,
       s.seeker_cycle_first_datediff,
       s.seeker_cycle_start_type_id,
       e.seeker_cycle_last_datediff
FROM profile.job_seeker_cycle_start_js_only s
         LEFT JOIN profile.job_seeker_cycle_end_js_only e
                   ON s.country_id = e.country_id AND s.profile_id = e.profile_id AND
                      s.seeker_cycle_first_datediff = e.seeker_cycle_first_datediff;

alter table profile.v_job_seeker_cycle_start_end_js_only owner to dap;

grant select on profile.v_job_seeker_cycle_start_end_js_only to readonly;

grant select on profile.v_job_seeker_cycle_start_end_js_only to "pavlo.kvasnii";

