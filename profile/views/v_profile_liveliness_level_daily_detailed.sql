create or replace view profile.v_profile_liveliness_level_daily_detailed(country_name, report_datediff, profile_id, registration_source_name, profile_blue_collar_type_name, is_created_last_7_days, is_session_after_creation_last_7_days, is_activated_last_7_days, is_session_after_activation_last_7_days, is_returned_apply_last_7_days, is_alive_profile, is_only_created_profile, is_only_returned_profile, is_only_activated_profile, is_only_activated_and_returned_profile, is_apply_returned_profile, creation_datediff, activation_datediff, country_id, registration_source_id, submission_datediff) as
	SELECT (SELECT c.alpha_2
        FROM dimension.countries c
        WHERE c.id = lldd.country_id)                         AS country_name,
       lldd.report_datediff,
       lldd.profile_id,
       (SELECT a.source_name
        FROM dimension.auth_source a
        WHERE a.id = lldd.registration_source_id)             AS registration_source_name,
       (SELECT pbct.type_name
        FROM dimension.profile_blue_collar_type pbct
        WHERE pbct.id = lldd.profile_blue_collar_type_id)     AS profile_blue_collar_type_name,
       lldd.is_created_last_7_days::smallint                  AS is_created_last_7_days,
       lldd.is_session_after_creation_last_7_days::smallint   AS is_session_after_creation_last_7_days,
       lldd.is_activated_last_7_days::smallint                AS is_activated_last_7_days,
       lldd.is_session_after_activation_last_7_days::smallint AS is_session_after_activation_last_7_days,
       lldd.is_returned_apply_last_7_days::smallint           AS is_returned_apply_last_7_days,
       CASE
           WHEN (lldd.is_created_last_7_days + lldd.is_session_after_creation_last_7_days +
                 lldd.is_activated_last_7_days + lldd.is_session_after_activation_last_7_days +
                 lldd.is_returned_apply_last_7_days) > 0 THEN 1
           ELSE 0
           END::smallint                                      AS is_alive_profile,
       CASE
           WHEN lldd.is_created_last_7_days = 1 AND
                (lldd.is_session_after_creation_last_7_days + lldd.is_activated_last_7_days +
                 lldd.is_session_after_activation_last_7_days + lldd.is_returned_apply_last_7_days) = 0 THEN 1
           ELSE 0
           END::smallint                                      AS is_only_created_profile,
       CASE
           WHEN lldd.is_session_after_creation_last_7_days = 1 AND
                (lldd.is_activated_last_7_days + lldd.is_session_after_activation_last_7_days +
                 lldd.is_returned_apply_last_7_days) = 0 THEN 1
           ELSE 0
           END::smallint                                      AS is_only_returned_profile,
       CASE
           WHEN lldd.is_activated_last_7_days = 1 AND
                (lldd.is_session_after_activation_last_7_days + lldd.is_returned_apply_last_7_days) = 0 THEN 1
           ELSE 0
           END::smallint                                      AS is_only_activated_profile,
       CASE
           WHEN lldd.is_session_after_activation_last_7_days = 1 AND lldd.is_returned_apply_last_7_days = 0 THEN 1
           ELSE 0
           END::smallint                                      AS is_only_activated_and_returned_profile,
       CASE
           WHEN lldd.is_returned_apply_last_7_days = 1 THEN 1
           ELSE 0
           END                                                AS is_apply_returned_profile,
       vpca.creation_datediff,
       vpca.activation_datediff,
       lldd.country_id,
       lldd.registration_source_id,
       vpca.submission_datediff
FROM profile.profile_liveliness_level_daily_detailed lldd
         LEFT JOIN profile.v_profile_creation_activation vpca
                   ON vpca.country_id = lldd.country_id AND vpca.profile_id = lldd.profile_id;

alter table profile.v_profile_liveliness_level_daily_detailed owner to postgres;

grant select on profile.v_profile_liveliness_level_daily_detailed to npo;

grant select on profile.v_profile_liveliness_level_daily_detailed to readonly;

grant select on profile.v_profile_liveliness_level_daily_detailed to nsh;

grant select on profile.v_profile_liveliness_level_daily_detailed to writeonly_product;

grant select on profile.v_profile_liveliness_level_daily_detailed to readonly_ds;

