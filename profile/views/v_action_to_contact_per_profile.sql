SELECT jscs.country_id,
       jscs.profile_id,
       jscs.seeker_cycle_first_datediff,
       COALESCE((SELECT sum(vpatcfjs.action_to_contact_prob) AS sum
                 FROM profile.v_action_to_contact_structure vpatcfjs
                 WHERE vpatcfjs.country_id = jscs.country_id
                   AND vpatcfjs.profile_id = jscs.profile_id
                   AND vpatcfjs.is_action_from_active_profile_base_user = 0
                   AND vpatcfjs.action_datediff >= jscs.seeker_cycle_first_datediff
                   AND vpatcfjs.action_datediff <= (jscs.seeker_cycle_first_datediff + 27)),
                0::numeric)                     AS action_to_contact_28_days_cnt,
       COALESCE((SELECT sum(vpatcfjs.action_to_contact_prob) AS sum
                 FROM profile.job_seeker_action_to_contact vpatcfjs
                 WHERE vpatcfjs.country_id = jscs.country_id
                   AND vpatcfjs.profile_id = jscs.profile_id
                   AND vpatcfjs.action_datediff >= jscs.seeker_cycle_first_datediff
                   AND vpatcfjs.action_datediff <= (jscs.seeker_cycle_first_datediff + 27)
                   AND vpatcfjs.action_type_id = 1),
                0::numeric)                     AS action_to_contact_call_28_days_cnt,
       COALESCE((SELECT sum(vpatcfjs.action_to_contact_prob) AS sum
                 FROM profile.job_seeker_action_to_contact vpatcfjs
                 WHERE vpatcfjs.country_id = jscs.country_id
                   AND vpatcfjs.profile_id = jscs.profile_id
                   AND vpatcfjs.action_datediff >= jscs.seeker_cycle_first_datediff
                   AND vpatcfjs.action_datediff <= (jscs.seeker_cycle_first_datediff + 27)
                   AND vpatcfjs.action_type_id = 2),
                0::numeric)                     AS action_to_contact_apply_28_days_cnt,
       COALESCE((SELECT sum(vpatcfjs.action_to_contact_prob) AS sum
                 FROM profile.v_action_to_contact_structure vpatcfjs
                 WHERE vpatcfjs.country_id = jscs.country_id
                   AND vpatcfjs.profile_id = jscs.profile_id
                   AND vpatcfjs.action_datediff >= jscs.seeker_cycle_first_datediff
                   AND vpatcfjs.action_datediff <= (jscs.seeker_cycle_first_datediff + 27)
                   AND vpatcfjs.platform_user_type_id = 2),
                0::numeric)                     AS action_to_contact_employer_28_days_cnt,
       COALESCE((SELECT sum(vpatcfjs.action_to_contact_prob) AS sum
                 FROM profile.v_action_to_contact_structure vpatcfjs
                 WHERE vpatcfjs.country_id = jscs.country_id
                   AND vpatcfjs.profile_id = jscs.profile_id
                   AND vpatcfjs.is_action_from_active_profile_base_user = 0
                   AND vpatcfjs.action_datediff >= jscs.seeker_cycle_first_datediff
                   AND vpatcfjs.action_datediff <= (jscs.seeker_cycle_first_datediff + 13)),
                0::numeric)                     AS action_to_contact_14_days_cnt,
       COALESCE((SELECT sum(vpatcfjs.action_to_contact_prob) AS sum
                 FROM profile.job_seeker_action_to_contact vpatcfjs
                 WHERE vpatcfjs.country_id = jscs.country_id
                   AND vpatcfjs.profile_id = jscs.profile_id
                   AND vpatcfjs.action_datediff <= (jscs.seeker_cycle_first_datediff + 13)
                   AND vpatcfjs.action_type_id = 1),
                0::numeric)                     AS action_to_contact_call_14_days_cnt,
       COALESCE((SELECT sum(vpatcfjs.action_to_contact_prob) AS sum
                 FROM profile.job_seeker_action_to_contact vpatcfjs
                 WHERE vpatcfjs.country_id = jscs.country_id
                   AND vpatcfjs.profile_id = jscs.profile_id
                   AND vpatcfjs.action_datediff >= jscs.seeker_cycle_first_datediff
                   AND vpatcfjs.action_datediff <= (jscs.seeker_cycle_first_datediff + 13)
                   AND vpatcfjs.action_type_id = 2),
                0::numeric)                     AS action_to_contact_apply_14_days_cnt,
       COALESCE((SELECT sum(vpatcfjs.action_to_contact_prob) AS sum
                 FROM profile.v_action_to_contact_structure vpatcfjs
                 WHERE vpatcfjs.country_id = jscs.country_id
                   AND vpatcfjs.profile_id = jscs.profile_id
                   AND vpatcfjs.is_action_from_active_profile_base_user = 0
                   AND vpatcfjs.action_datediff >= jscs.seeker_cycle_first_datediff
                   AND vpatcfjs.action_datediff <= (jscs.seeker_cycle_first_datediff + 13)
                   AND vpatcfjs.platform_user_type_id = 2),
                0::numeric)                     AS action_to_contact_employer_14_days_cnt,
       COALESCE((SELECT sum(vpatcfjs.action_to_contact_prob) AS sum
                 FROM profile.v_action_to_contact_structure vpatcfjs
                 WHERE vpatcfjs.country_id = jscs.country_id
                   AND vpatcfjs.profile_id = jscs.profile_id
                   AND vpatcfjs.is_action_from_active_profile_base_user = 0
                   AND vpatcfjs.action_datediff >= jscs.seeker_cycle_first_datediff
                   AND vpatcfjs.action_datediff <= (jscs.seeker_cycle_first_datediff + 6)),
                0::numeric)                     AS action_to_contact_7_days_cnt,
       COALESCE((SELECT sum(vpatcfjs.action_to_contact_prob) AS sum
                 FROM profile.job_seeker_action_to_contact vpatcfjs
                 WHERE vpatcfjs.country_id = jscs.country_id
                   AND vpatcfjs.profile_id = jscs.profile_id
                   AND vpatcfjs.action_datediff >= jscs.seeker_cycle_first_datediff
                   AND vpatcfjs.action_datediff <= (jscs.seeker_cycle_first_datediff + 6)
                   AND vpatcfjs.action_type_id = 1),
                0::numeric)                     AS action_to_contact_call_7_days_cnt,
       COALESCE((SELECT sum(vpatcfjs.action_to_contact_prob) AS sum
                 FROM profile.job_seeker_action_to_contact vpatcfjs
                 WHERE vpatcfjs.country_id = jscs.country_id
                   AND vpatcfjs.profile_id = jscs.profile_id
                   AND vpatcfjs.action_datediff >= jscs.seeker_cycle_first_datediff
                   AND vpatcfjs.action_datediff <= (jscs.seeker_cycle_first_datediff + 6)
                   AND vpatcfjs.action_type_id = 2),
                0::numeric)                     AS action_to_contact_apply_7_days_cnt,
       COALESCE((SELECT sum(vpatcfjs.action_to_contact_prob) AS sum
                 FROM profile.v_action_to_contact_structure vpatcfjs
                 WHERE vpatcfjs.country_id = jscs.country_id
                   AND vpatcfjs.profile_id = jscs.profile_id
                   AND vpatcfjs.is_action_from_active_profile_base_user = 0
                   AND vpatcfjs.action_datediff >= jscs.seeker_cycle_first_datediff
                   AND vpatcfjs.action_datediff <= (jscs.seeker_cycle_first_datediff + 6)
                   AND vpatcfjs.platform_user_type_id = 2),
                0::numeric)                     AS action_to_contact_employer_7_days_cnt,
       COALESCE((SELECT sum(vpatcfjs.action_to_contact_prob) AS sum
                 FROM profile.v_action_to_contact_structure vpatcfjs
                 WHERE vpatcfjs.country_id = jscs.country_id
                   AND vpatcfjs.profile_id = jscs.profile_id
                   AND vpatcfjs.is_action_from_active_profile_base_user = 0
                   AND vpatcfjs.action_datediff = jscs.seeker_cycle_first_datediff),
                0::numeric)                     AS action_to_contact_1_days_cnt,
       COALESCE((SELECT sum(vpatcfjs.action_to_contact_prob) AS sum
                 FROM profile.job_seeker_action_to_contact vpatcfjs
                 WHERE vpatcfjs.country_id = jscs.country_id
                   AND vpatcfjs.profile_id = jscs.profile_id
                   AND vpatcfjs.action_datediff = jscs.seeker_cycle_first_datediff
                   AND vpatcfjs.action_type_id = 1),
                0::numeric)                     AS action_to_contact_call_1_days_cnt,
       COALESCE((SELECT sum(vpatcfjs.action_to_contact_prob) AS sum
                 FROM profile.job_seeker_action_to_contact vpatcfjs
                 WHERE vpatcfjs.country_id = jscs.country_id
                   AND vpatcfjs.profile_id = jscs.profile_id
                   AND vpatcfjs.action_datediff = jscs.seeker_cycle_first_datediff
                   AND vpatcfjs.action_type_id = 2),
                0::numeric)                     AS action_to_contact_apply_1_days_cnt,
       COALESCE((SELECT sum(vpatcfjs.action_to_contact_prob) AS sum
                 FROM profile.v_action_to_contact_structure vpatcfjs
                 WHERE vpatcfjs.country_id = jscs.country_id
                   AND vpatcfjs.profile_id = jscs.profile_id
                   AND vpatcfjs.is_action_from_active_profile_base_user = 0
                   AND vpatcfjs.action_datediff = jscs.seeker_cycle_first_datediff
                   AND vpatcfjs.platform_user_type_id = 2),
                0::numeric)                     AS action_to_contact_employer_1_days_cnt,
       (SELECT (SELECT pbct.type_name
                FROM dimension.profile_blue_collar_type pbct
                WHERE pbs.profile_blue_collar_type_id = pbct.id) AS blue_collar_type_name
        FROM profile.profile_blue_score pbs
        WHERE pbs.country_id = jscs.country_id
          AND pbs.profile_id = jscs.profile_id) AS blue_collar_type_name,
       scst.type_name                           AS seeker_cycle_start_type_name,
       put.type_name                            AS initiator_name,
       COALESCE((SELECT sum(vpatcfjs.action_to_contact_prob) AS sum
                 FROM profile.v_action_to_contact_structure vpatcfjs
                 WHERE vpatcfjs.country_id = jscs.country_id
                   AND vpatcfjs.profile_id = jscs.profile_id
                   AND vpatcfjs.is_action_from_active_profile_base_user = 0
                   AND vpatcfjs.action_datediff >= jscs.seeker_cycle_first_datediff
                   AND vpatcfjs.action_datediff <= (jscs.seeker_cycle_first_datediff + 1)),
                0::numeric)                     AS action_to_contact_2_days_cnt,
       jscs.seeker_cycle_start_type_id,
       COALESCE((SELECT sum(vpatcfjs.action_to_contact_prob) AS sum
                 FROM profile.v_action_to_contact_structure vpatcfjs
                 WHERE vpatcfjs.country_id = jscs.country_id
                   AND vpatcfjs.profile_id = jscs.profile_id
                   AND vpatcfjs.action_datediff >= jscs.seeker_cycle_first_datediff
                   AND vpatcfjs.action_datediff <= (jscs.seeker_cycle_first_datediff + 6)
                   AND vpatcfjs.platform_user_type_id = 1
                   AND (vpatcfjs.feature_id = ANY (ARRAY [2, 3, 4]))),
                0::numeric)                     AS action_to_contact_job_seeker_message_7_days_cnt,
       COALESCE((SELECT sum(vpatcfjs.action_to_contact_prob) AS sum
                 FROM profile.v_action_to_contact_structure vpatcfjs
                 WHERE vpatcfjs.country_id = jscs.country_id
                   AND vpatcfjs.profile_id = jscs.profile_id
                   AND vpatcfjs.action_datediff = jscs.seeker_cycle_first_datediff
                   AND vpatcfjs.platform_user_type_id = 1
                   AND (vpatcfjs.feature_id = ANY (ARRAY [2, 3, 4]))),
                0::numeric)                     AS action_to_contact_job_seeker_message_1_days_cnt,
       COALESCE((SELECT sum(vpatcfjs.action_to_contact_prob) AS sum
                 FROM profile.v_action_to_contact_structure vpatcfjs
                 WHERE vpatcfjs.country_id = jscs.country_id
                   AND vpatcfjs.profile_id = jscs.profile_id
                   AND vpatcfjs.action_datediff >= jscs.seeker_cycle_first_datediff
                   AND vpatcfjs.action_datediff <= (jscs.seeker_cycle_first_datediff + 27)
                   AND vpatcfjs.platform_user_type_id = 1
                   AND (vpatcfjs.feature_id = ANY (ARRAY [2, 3, 4]))),
                0::numeric)                     AS action_to_contact_job_seeker_message_28_days_cnt,
       COALESCE((SELECT sum(vpatcfjs.action_to_contact_prob) AS sum
                 FROM profile.v_action_to_contact_structure vpatcfjs
                 WHERE vpatcfjs.country_id = jscs.country_id
                   AND vpatcfjs.profile_id = jscs.profile_id
                   AND vpatcfjs.is_action_from_active_profile_base_user = 0
                   AND vpatcfjs.action_datediff >= jscs.seeker_cycle_first_datediff + 28
                   AND vpatcfjs.action_datediff <= (jscs.seeker_cycle_first_datediff + 98)),
                0::numeric)                     AS action_to_contact_28_98_days_cnt,
       COALESCE((SELECT sum(vpatcfjs.action_to_contact_prob) AS sum
                 FROM profile.job_seeker_action_to_contact vpatcfjs
                 WHERE vpatcfjs.country_id = jscs.country_id
                   AND vpatcfjs.profile_id = jscs.profile_id
                   AND vpatcfjs.action_datediff >= jscs.seeker_cycle_first_datediff + 28
                   AND vpatcfjs.action_datediff <= (jscs.seeker_cycle_first_datediff + 98)
                   AND vpatcfjs.action_type_id = 1),
                0::numeric)                     AS action_to_contact_call_28_98_days_cnt,
       COALESCE((SELECT sum(vpatcfjs.action_to_contact_prob) AS sum
                 FROM profile.job_seeker_action_to_contact vpatcfjs
                 WHERE vpatcfjs.country_id = jscs.country_id
                   AND vpatcfjs.profile_id = jscs.profile_id
                   AND vpatcfjs.action_datediff >= jscs.seeker_cycle_first_datediff + 28
                   AND vpatcfjs.action_datediff <= (jscs.seeker_cycle_first_datediff + 98)
                   AND vpatcfjs.action_type_id = 2),
                0::numeric)                     AS action_to_contact_apply_28_98_days_cnt,
       COALESCE((SELECT sum(vpatcfjs.action_to_contact_prob) AS sum
                 FROM profile.v_action_to_contact_structure vpatcfjs
                 WHERE vpatcfjs.country_id = jscs.country_id
                   AND vpatcfjs.profile_id = jscs.profile_id
                   AND vpatcfjs.action_datediff >= jscs.seeker_cycle_first_datediff + 28
                   AND vpatcfjs.action_datediff <= (jscs.seeker_cycle_first_datediff + 98)
                   AND vpatcfjs.platform_user_type_id = 1
                   AND (vpatcfjs.feature_id = ANY (ARRAY [2, 3, 4]))),
                0::numeric)                     AS action_to_contact_job_seeker_message_28_98_days_cnt
FROM profile.job_seeker_cycle_start jscs
         LEFT JOIN dimension.seeker_cycle_start_type scst ON scst.id = jscs.seeker_cycle_start_type_id
         LEFT JOIN dimension.platform_user_type put ON put.id = scst.initiator_type_id
WHERE jscs.country_id in (1,10);
