create or replace view profile.v_sudoku_profiles_submitted_0day(date_submitted, profiles_submitted_cnt) as
	SELECT pr.date_submitted,
       count(DISTINCT pr.id) AS profiles_submitted_cnt
FROM imp.profiles pr
WHERE pr.country = 1
  AND pr.date_submitted > 44292
GROUP BY pr.date_submitted;

alter table profile.v_sudoku_profiles_submitted_0day owner to rlu;

grant select on profile.v_sudoku_profiles_submitted_0day to readonly;

grant select on profile.v_sudoku_profiles_submitted_0day to writeonly_product;

grant select on profile.v_sudoku_profiles_submitted_0day to readonly_ds;

