create or replace view profile.v_profile_blue_score(country_id, profile_id, blue_score, profile_blue_collar_type_id) as
	SELECT pdj.country_id,
       pdj.profile_id,
       avg(pbs.blue_score) AS blue_score,
       CASE
           WHEN avg(pbs.blue_score)::double precision >= 0::double precision AND
                avg(pbs.blue_score)::double precision <= 0.34::double precision THEN 1
           WHEN avg(pbs.blue_score)::double precision >= 0.35::double precision AND
                avg(pbs.blue_score)::double precision <= 0.64::double precision THEN 2
           WHEN avg(pbs.blue_score)::double precision >= 0.65::double precision AND
                avg(pbs.blue_score)::double precision <= 1::double precision THEN 3
           ELSE 0
           END::smallint   AS profile_blue_collar_type_id
FROM profile.profile_profession pdj
         LEFT JOIN dimension.info_profession ip ON ip.id = pdj.profession_id AND ip.country = pdj.country_id
         LEFT JOIN dimension.professions_blue_score pbs ON pbs.profession::text = ip.name::text
WHERE pdj.country_id = 1
GROUP BY pdj.country_id, pdj.profile_id;

alter table profile.v_profile_blue_score owner to postgres;

grant select on profile.v_profile_blue_score to npo;

grant select on profile.v_profile_blue_score to readonly;

grant select on profile.v_profile_blue_score to nsh;

grant select on profile.v_profile_blue_score to writeonly_product;

grant select on profile.v_profile_blue_score to readonly_ds;

