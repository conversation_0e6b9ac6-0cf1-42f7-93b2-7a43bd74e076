create or replace view profile.v_sudoku_profile_submitted_return(profile_id, date_submitted, jdp_view_on_day) as
	SELECT pr.id                                      AS profile_id,
       pr.date_submitted,
       pj.jdp_viewed_datediff - pr.date_submitted AS jdp_view_on_day
FROM imp.profiles pr
         LEFT JOIN profile.profile_jdp pj ON pj.country_id = pr.country AND pj.profile_id = pr.id
WHERE pr.country = 1
  AND pr.date_submitted > 44292
  AND (pj.jdp_viewed_datediff - pr.date_submitted) >= 0
GROUP BY pr.id, (pj.jdp_viewed_datediff - pr.date_submitted), pr.date_submitted;

alter table profile.v_sudoku_profile_submitted_return owner to rlu;

grant select on profile.v_sudoku_profile_submitted_return to readonly;

grant select on profile.v_sudoku_profile_submitted_return to writeonly_product;

grant select on profile.v_sudoku_profile_submitted_return to readonly_ds;

