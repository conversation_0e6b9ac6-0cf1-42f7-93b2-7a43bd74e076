create or replace view profile.v_action_to_contact_per_profile_structure(country_id, profile_id, seeker_cycle_first_datediff, blue_collar_type_name, seeker_cycle_start_type_name, initiator_name, platform_user_type_id, feature_id, platform_user_type_name, feature_type_name, action_to_contact_1_days_cnt, action_to_contact_2_days_cnt, action_to_contact_7_days_cnt, action_type_id, action_type_name) as
	WITH structure_dic AS (
    SELECT DISTINCT action_to_contact_structure.platform_user_type_id,
                    action_to_contact_structure.feature_id,
                    action_to_contact_structure.action_type_id
    FROM profile.action_to_contact_structure
)
SELECT jscs.country_id,
       jscs.profile_id,
       jscs.seeker_cycle_first_datediff,
       (SELECT (SELECT pbct.type_name
                FROM dimension.profile_blue_collar_type pbct
                WHERE pbs.profile_blue_collar_type_id = pbct.id) AS blue_collar_type_name
        FROM profile.profile_blue_score pbs
        WHERE pbs.country_id = jscs.country_id
          AND pbs.profile_id = jscs.profile_id)                                                     AS blue_collar_type_name,
       scst.type_name                                                                               AS seeker_cycle_start_type_name,
       put.type_name                                                                                AS initiator_name,
       sd.platform_user_type_id,
       sd.feature_id,
       (SELECT put_1.type_name
        FROM dimension.platform_user_type put_1
        WHERE put_1.id = sd.platform_user_type_id)                                                  AS platform_user_type_name,
       (SELECT ft.feature_type_name
        FROM dimension.v_feature_type ft
        WHERE ft.feature_type_id = sd.feature_id
          AND ft.platform_user_type_id = sd.platform_user_type_id)                                  AS feature_type_name,
       COALESCE((SELECT sum(atcs.action_to_contact_prob) AS sum
                 FROM profile.v_action_to_contact_structure atcs
                 WHERE atcs.country_id = jscs.country_id
                   AND atcs.profile_id = jscs.profile_id
                   AND atcs.platform_user_type_id = sd.platform_user_type_id
                   AND atcs.feature_id = sd.feature_id
                   AND atcs.action_type_id = sd.action_type_id
                   AND atcs.is_action_from_active_profile_base_user = 0
                   AND atcs.action_datediff >= jscs.seeker_cycle_first_datediff
                   AND atcs.action_datediff <= (jscs.seeker_cycle_first_datediff + 0)),
                0::numeric)                                                                         AS action_to_contact_1_days_cnt,
       COALESCE((SELECT sum(atcs.action_to_contact_prob) AS sum
                 FROM profile.v_action_to_contact_structure atcs
                 WHERE atcs.country_id = jscs.country_id
                   AND atcs.profile_id = jscs.profile_id
                   AND atcs.platform_user_type_id = sd.platform_user_type_id
                   AND atcs.feature_id = sd.feature_id
                   AND atcs.action_type_id = sd.action_type_id
                   AND atcs.is_action_from_active_profile_base_user = 0
                   AND atcs.action_datediff >= jscs.seeker_cycle_first_datediff
                   AND atcs.action_datediff <= (jscs.seeker_cycle_first_datediff + 1)),
                0::numeric)                                                                         AS action_to_contact_2_days_cnt,
       COALESCE((SELECT sum(atcs.action_to_contact_prob) AS sum
                 FROM profile.v_action_to_contact_structure atcs
                 WHERE atcs.country_id = jscs.country_id
                   AND atcs.profile_id = jscs.profile_id
                   AND atcs.platform_user_type_id = sd.platform_user_type_id
                   AND atcs.feature_id = sd.feature_id
                   AND atcs.action_type_id = sd.action_type_id
                   AND atcs.is_action_from_active_profile_base_user = 0
                   AND atcs.action_datediff >= jscs.seeker_cycle_first_datediff
                   AND atcs.action_datediff <= (jscs.seeker_cycle_first_datediff + 6)),
                0::numeric)                                                                         AS action_to_contact_7_days_cnt,
       sd.action_type_id,
       (SELECT vatct.type_name
        FROM dimension.v_action_to_contact_type vatct
        WHERE vatct.type_id = sd.action_type_id
          AND vatct.platform_user_type_id = sd.platform_user_type_id)                               AS action_type_name
FROM profile.job_seeker_cycle_start jscs
         JOIN structure_dic sd ON 1 = 1
         LEFT JOIN dimension.seeker_cycle_start_type scst ON scst.id = jscs.seeker_cycle_start_type_id
         LEFT JOIN dimension.platform_user_type put ON put.id = scst.initiator_type_id
WHERE jscs.seeker_cycle_first_datediff >= 44407;

alter table profile.v_action_to_contact_per_profile_structure owner to dap;

grant select on profile.v_action_to_contact_per_profile_structure to readonly;

grant select on profile.v_action_to_contact_per_profile_structure to readonly_ds;

