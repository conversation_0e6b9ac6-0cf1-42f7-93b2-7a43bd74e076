create or replace view profile.v_profile_info
            (country_id, profile_id, creation_date, first_input_date, submission_date, phone, email, first_name,
             last_name, about_me, salary, education, work_experience, is_add_photo, driver_licenses,
             additional_questions_answers, gender, regions, year_of_birth, profession_tags, is_visible_for_employers,
             profile_blue_collar_type_id, profile_blue_score, profession_cnt, packet_type_name)
as
SELECT vps.country_id,
       vps.id                                                           AS profile_id,
       '1900-01-01'::date + vps.creation_datediff                       AS creation_date,
       '1900-01-01'::date + vps.first_input_datediff                    AS first_input_date,
       '1900-01-01'::date + vps.submission_datediff                     AS submission_date,
       vps.phone,
       vps.email,
       pgi.first_name,
       pgi.last_name,
       pr.aboutme                                                       AS about_me,
       pr.salary::double precision * COALESCE(((SELECT ic.currency_rate
                                                FROM dimension.info_currency ic
                                                WHERE ic.name::text = pr.currency::text
                                                  AND ic.country = pr.country))::double precision,
                                              1::double precision)      AS salary,
       pr.education,
       pr.workexperience                                                AS work_experience,
       pr.is_add_photo,
       pr.driverlicenses                                                AS driver_licenses,
       pr.additionalquestionsanswers                                    AS additional_questions_answers,
       (SELECT ig.gender
        FROM dimension.info_gender ig
        WHERE ig.id = pgi.gender)                                       AS gender,
       pgi.regions,
       pgi.yearofbirth                                                  AS year_of_birth,
       string_agg(((SELECT ip.name
                    FROM dimension.info_profession ip
                    WHERE ip.id = pp.profession_id))::text, ', '::text) AS profession_tags,
       pgi.isvisibleforemployers                                        AS is_visible_for_employers,
       pbs.profile_blue_collar_type_id,
       pbs.blue_score                                                   AS profile_blue_score,
       count((SELECT DISTINCT ip.name
              FROM dimension.info_profession ip
              WHERE ip.id = pp.profession_id))                          AS profession_cnt,
       CASE
           WHEN pjeps.source_type_id = 1 THEN 'free packet job'::text
           WHEN pjeps.source_type_id = 2 THEN 'paid packet job'::text
           ELSE 'other'::text
           END                                                          AS packet_type_name
FROM product.profile_parse pr
         JOIN profile.v_profile_submitted vps ON pr.country = vps.country_id AND pr.id = vps.id
         LEFT JOIN profile.profile_general_info pgi ON pgi.country_id = vps.country_id AND pgi.profile_id = vps.id
         LEFT JOIN profile.profile_profession pp ON pp.country_id = vps.country_id AND pp.profile_id = vps.id
         LEFT JOIN profile.profile_blue_score pbs ON pbs.country_id = vps.country_id AND pbs.profile_id = vps.id
         LEFT JOIN profile.v_profile_jdp_submission_source_unique pjeps
                   ON pjeps.country_id = pr.country AND pjeps.profile_id = pr.id

GROUP BY vps.country_id, vps.id, vps.creation_datediff, vps.first_input_datediff, vps.submission_datediff, vps.phone,
         vps.email, pgi.first_name, pgi.last_name, pr.aboutme, pr.salary, pr.education, pr.workexperience,
         pr.is_add_photo, pr.driverlicenses, pr.additionalquestionsanswers, pgi.gender, pgi.regions, pgi.yearofbirth,
         pr.currency, pr.country, pgi.isvisibleforemployers, pbs.profile_blue_collar_type_id, pbs.blue_score,
         (
             CASE
                 WHEN pjeps.source_type_id = 1 THEN 'free packet job'::text
                 WHEN pjeps.source_type_id = 2 THEN 'paid packet job'::text
                 ELSE 'other'::text
                 END);

alter table v_profile_info
    owner to postgres;

grant select on v_profile_info to readonly;

grant select on v_profile_info to writeonly_product;

grant select on v_profile_info to readonly_ds;

