create or replace view profile.v_user_activation(country_id, user_type_id, user_id, profile_created_datediff, activation_datediff) as
	SELECT upc.country_id,
       upc.user_type_id,
       upc.user_id,
       min(upc.usage_first_datediff) AS profile_created_datediff,
       min(ua.jdp_viewed_datediff)   AS activation_datediff
FROM job_seeker.user_profile_created upc
         LEFT JOIN job_seeker.user_apply ua
                   ON ua.country_id = upc.country_id AND ua.user_id::text = upc.user_id::text AND
                      ua.user_type_id = upc.user_type_id
WHERE upc.country_id = 1
  AND upc.usage_cnt = 1
GROUP BY upc.country_id, upc.user_type_id, upc.user_id;

alter table profile.v_user_activation owner to postgres;

grant select on profile.v_user_activation to npo;

grant select on profile.v_user_activation to readonly;

grant select on profile.v_user_activation to nsh;

grant select on profile.v_user_activation to writeonly_product;

grant select on profile.v_user_activation to readonly_ds;

