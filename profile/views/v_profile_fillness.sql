create view profile.v_profile_fillness(country, profile_id, date_created, is_submitted, funnel_step, is_filled, is_submitted_field) as
	SELECT p.country,
       p.id    AS profile_id,
       p.date_created,
       p.is_submitted,
       1       AS funnel_step,
       CASE
           WHEN (p.data::json -> 'telephone'::text) IS NULL OR (p.data::json ->> 'telephone'::text) IS NULL OR
                (p.data::json ->> 'telephone'::text) = ''::text OR (p.data::json ->> 'telephone'::text) = '[]'::text
               THEN 0
           ELSE 1
           END AS is_filled,
       1       AS is_submitted_field
FROM imp.profiles p
WHERE p.country = 1
  AND p.date_created >= '2021-08-01'::date
UNION ALL
SELECT p.country,
       p.id    AS profile_id,
       p.date_created,
       p.is_submitted,
       2       AS funnel_step,
       CASE
           WHEN (p.data::json -> 'email'::text) IS NULL OR (p.data::json ->> 'email'::text) IS NULL OR
                (p.data::json ->> 'email'::text) = ''::text OR (p.data::json ->> 'email'::text) = '[]'::text THEN 0
           ELSE 1
           END AS is_filled,
       0       AS is_submitted_field
FROM imp.profiles p
WHERE p.country = 1
  AND p.date_created >= '2021-08-01'::date
UNION ALL
SELECT p.country,
       p.id    AS profile_id,
       p.date_created,
       p.is_submitted,
       3       AS funnel_step,
       CASE
           WHEN
                       CASE
                           WHEN (p.data::json #> '{generalInfo,lastName}'::text[]) IS NULL OR
                                (p.data::json #>> '{generalInfo,lastName}'::text[]) IS NULL OR
                                (p.data::json #>> '{generalInfo,lastName}'::text[]) = ''::text THEN 0
                           ELSE 1
                           END = 1 AND
                       CASE
                           WHEN (p.data::json #> '{generalInfo,firstName}'::text[]) IS NULL OR
                                (p.data::json #>> '{generalInfo,firstName}'::text[]) IS NULL OR
                                (p.data::json #>> '{generalInfo,firstName}'::text[]) = ''::text THEN 0
                           ELSE 1
                           END = 1 THEN 1
           ELSE 0
           END AS is_filled,
       1       AS is_submitted_field
FROM imp.profiles p
WHERE p.country = 1
  AND p.date_created >= '2021-08-01'::date
UNION ALL
SELECT p.country,
       p.id    AS profile_id,
       p.date_created,
       p.is_submitted,
       4       AS funnel_step,
       CASE
           WHEN (p.data::json #> '{generalInfo,gender}'::text[]) IS NULL OR
                (p.data::json #>> '{generalInfo,gender}'::text[]) IS NULL THEN 0
           ELSE 1
           END AS is_filled,
       1       AS is_submitted_field
FROM imp.profiles p
WHERE p.country = 1
  AND p.date_created >= '2021-08-01'::date
UNION ALL
SELECT p.country,
       p.id    AS profile_id,
       p.date_created,
       p.is_submitted,
       5       AS funnel_step,
       CASE
           WHEN
                       CASE
                           WHEN (p.data::json #> '{generalInfo,fullBirthDate}'::text[]) IS NULL OR
                                (p.data::json #>> '{generalInfo,fullBirthDate}'::text[]) IS NULL OR
                                (p.data::json #>> '{generalInfo,fullBirthDate}'::text[]) = ''::text THEN 0
                           ELSE 1
                           END = 1 OR
                       CASE
                           WHEN (p.data::json #> '{generalInfo,yearOfBirth}'::text[]) IS NULL OR
                                (p.data::json #>> '{generalInfo,yearOfBirth}'::text[]) IS NULL OR
                                (p.data::json #>> '{generalInfo,yearOfBirth}'::text[]) = ''::text THEN 0
                           ELSE 1
                           END = 1 THEN 1
           ELSE 0
           END AS is_filled,
       1       AS is_submitted_field
FROM imp.profiles p
WHERE p.country = 1
  AND p.date_created >= '2021-08-01'::date
UNION ALL
SELECT p.country,
       p.id    AS profile_id,
       p.date_created,
       p.is_submitted,
       6       AS funnel_step,
       CASE
           WHEN
                       CASE
                           WHEN (p.data::json #> '{generalInfo,regions}'::text[]) IS NULL OR
                                (p.data::json #>> '{generalInfo,regions}'::text[]) IS NULL OR
                                (p.data::json #>> '{generalInfo,regions}'::text[]) = '[""]'::text OR
                                (p.data::json #>> '{generalInfo,regions}'::text[]) = '[]'::text THEN 0
                           ELSE 1
                           END = 1 OR
                       CASE
                           WHEN (p.data::json #> '{generalInfo,region}'::text[]) IS NULL OR
                                (p.data::json #>> '{generalInfo,region}'::text[]) IS NULL OR
                                (p.data::json #>> '{generalInfo,region}'::text[]) = '[""]'::text OR
                                (p.data::json #>> '{generalInfo,region}'::text[]) = '[]'::text THEN 0
                           ELSE 1
                           END = 1 THEN 1
           ELSE 0
           END AS is_filled,
       1       AS is_submitted_field
FROM imp.profiles p
WHERE p.country = 1
  AND p.date_created >= '2021-08-01'::date
UNION ALL
SELECT p.country,
       p.id    AS profile_id,
       p.date_created,
       p.is_submitted,
       7       AS funnel_step,
       CASE
           WHEN (p.data::json #> '{generalInfo,isReadyWorkAbroad}'::text[]) IS NULL OR
                (p.data::json #>> '{generalInfo,isReadyWorkAbroad}'::text[]) IS NULL THEN 0
           ELSE 1
           END AS is_filled,
       0       AS is_submitted_field
FROM imp.profiles p
WHERE p.country = 1
  AND p.date_created >= '2021-08-01'::date
UNION ALL
SELECT p.country,
       p.id    AS profile_id,
       p.date_created,
       p.is_submitted,
       8       AS funnel_step,
       CASE
           WHEN (p.data::json #> '{generalInfo,isReadyForRelocate}'::text[]) IS NULL OR
                (p.data::json #>> '{generalInfo,isReadyForRelocate}'::text[]) IS NULL THEN 0
           ELSE 1
           END AS is_filled,
       0       AS is_submitted_field
FROM imp.profiles p
WHERE p.country = 1
  AND p.date_created >= '2021-08-01'::date
UNION ALL
SELECT p.country,
       p.id    AS profile_id,
       p.date_created,
       p.is_submitted,
       9       AS funnel_step,
       CASE
           WHEN (p.data::json -> 'desiredSentinels'::text) IS NULL OR
                (p.data::json ->> 'desiredSentinels'::text) IS NULL OR
                (p.data::json ->> 'desiredSentinels'::text) = ''::text OR
                (p.data::json ->> 'desiredSentinels'::text) = '[]'::text THEN 0
           ELSE 1
           END AS is_filled,
       1       AS is_submitted_field
FROM imp.profiles p
WHERE p.country = 1
  AND p.date_created >= '2021-08-01'::date
UNION ALL
SELECT p.country,
       p.id    AS profile_id,
       p.date_created,
       p.is_submitted,
       10      AS funnel_step,
       CASE
           WHEN (p.data::json -> 'workExperience'::text) IS NULL OR (p.data::json ->> 'workExperience'::text) IS NULL OR
                (p.data::json ->> 'workExperience'::text) = ''::text OR (p.data::json ->> 'workExperience'::text) = '[]'::text
               THEN 0
           ELSE 1
           END AS is_filled,
       1       AS is_submitted_field
FROM imp.profiles p
WHERE p.country = 1
  AND p.date_created >= '2021-08-01'::date
UNION ALL
SELECT p.country,
       p.id    AS profile_id,
       p.date_created,
       p.is_submitted,
       11      AS funnel_step,
       CASE
           WHEN (p.data::json -> 'driverLicenses'::text) IS NULL OR (p.data::json ->> 'driverLicenses'::text) IS NULL OR
                (p.data::json ->> 'driverLicenses'::text) = ''::text OR (p.data::json ->> 'driverLicenses'::text) = '[]'::text
               THEN 0
           ELSE 1
           END AS is_filled,
       0       AS is_submitted_field
FROM imp.profiles p
WHERE p.country = 1
  AND p.date_created >= '2021-08-01'::date
UNION ALL
SELECT p.country,
       p.id    AS profile_id,
       p.date_created,
       p.is_submitted,
       12      AS funnel_step,
       CASE
           WHEN (p.data::json -> 'languages'::text) IS NULL OR (p.data::json ->> 'languages'::text) IS NULL OR
                (p.data::json ->> 'languages'::text) = ''::text OR (p.data::json ->> 'languages'::text) = '[]'::text
               THEN 0
           ELSE 1
           END AS is_filled,
       0       AS is_submitted_field
FROM imp.profiles p
WHERE p.country = 1
  AND p.date_created >= '2021-08-01'::date
UNION ALL
SELECT p.country,
       p.id    AS profile_id,
       p.date_created,
       p.is_submitted,
       13      AS funnel_step,
       CASE
           WHEN (p.data::json -> 'aboutMe'::text) IS NULL OR (p.data::json ->> 'aboutMe'::text) IS NULL OR
                (p.data::json ->> 'aboutMe'::text) = ''::text OR (p.data::json ->> 'aboutMe'::text) = '[]'::text THEN 0
           ELSE 1
           END AS is_filled,
       0       AS is_submitted_field
FROM imp.profiles p
WHERE p.country = 1
  AND p.date_created >= '2021-08-01'::date
UNION ALL
SELECT p.country,
       p.id    AS profile_id,
       p.date_created,
       p.is_submitted,
       14      AS funnel_step,
       CASE
           WHEN (p.data::json -> 'employmentTypes'::text) IS NULL OR
                (p.data::json ->> 'employmentTypes'::text) IS NULL OR
                (p.data::json ->> 'employmentTypes'::text) = ''::text OR
                (p.data::json ->> 'employmentTypes'::text) = '[]'::text THEN 0
           ELSE 1
           END AS is_filled,
       1       AS is_submitted_field
FROM imp.profiles p
WHERE p.country = 1
  AND p.date_created >= '2021-08-01'::date
UNION ALL
SELECT p.country,
       p.id    AS profile_id,
       p.date_created,
       p.is_submitted,
       15      AS funnel_step,
       CASE
           WHEN (p.data::json -> 'isVisibleForEmployers'::text) IS NULL OR
                (p.data::json ->> 'isVisibleForEmployers'::text) IS NULL OR
                (p.data::json ->> 'isVisibleForEmployers'::text) = ''::text OR
                (p.data::json ->> 'isVisibleForEmployers'::text) = '[]'::text THEN 0
           ELSE 1
           END AS is_filled,
       0       AS is_submitted_field
FROM imp.profiles p
WHERE p.country = 1
  AND p.date_created >= '2021-08-01'::date
UNION ALL
SELECT p.country,
       p.id    AS profile_id,
       p.date_created,
       p.is_submitted,
       16      AS funnel_step,
       CASE
           WHEN (p.data::json -> 'salary'::text) IS NULL OR (p.data::json ->> 'salary'::text) IS NULL OR
                (p.data::json ->> 'salary'::text) = ''::text OR (p.data::json ->> 'salary'::text) = '[]'::text THEN 0
           ELSE 1
           END AS is_filled,
       0       AS is_submitted_field
FROM imp.profiles p
WHERE p.country = 1
  AND p.date_created >= '2021-08-01'::date
UNION ALL
SELECT p.country,
       p.id    AS profile_id,
       p.date_created,
       p.is_submitted,
       17      AS funnel_step,
       CASE
           WHEN (p.data::json -> 'photoId'::text) IS NULL OR (p.data::json ->> 'photoId'::text) IS NULL OR
                (p.data::json ->> 'photoId'::text) = ''::text OR (p.data::json ->> 'photoId'::text) = '[]'::text THEN 0
           ELSE 1
           END AS is_filled,
       0       AS is_submitted_field
FROM imp.profiles p
WHERE p.country = 1
  AND p.date_created >= '2021-08-01'::date
UNION ALL
SELECT p.country,
       p.id    AS profile_id,
       p.date_created,
       p.is_submitted,
       18      AS funnel_step,
       CASE
           WHEN (p.data::json -> 'education'::text) IS NULL OR (p.data::json ->> 'education'::text) IS NULL OR
                (p.data::json ->> 'education'::text) = ''::text OR (p.data::json ->> 'education'::text) = '[]'::text
               THEN 0
           ELSE 1
           END AS is_filled,
       0       AS is_submitted_field
FROM imp.profiles p
WHERE p.country = 1
  AND p.date_created >= '2021-08-01'::date
UNION ALL
SELECT p.country,
       p.id    AS profile_id,
       p.date_created,
       p.is_submitted,
       19      AS funnel_step,
       CASE
           WHEN (p.data::json -> 'certificates'::text) IS NULL OR (p.data::json ->> 'certificates'::text) IS NULL OR
                (p.data::json ->> 'certificates'::text) = ''::text OR (p.data::json ->> 'certificates'::text) = '[]'::text
               THEN 0
           ELSE 1
           END AS is_filled,
       0       AS is_submitted_field
FROM imp.profiles p
WHERE p.country = 1
  AND p.date_created >= '2021-08-01'::date;

alter table profile.v_profile_fillness owner to ypi;

grant select on profile.v_profile_fillness to readonly;

grant select on profile.v_profile_fillness to "pavlo.kvasnii";

