create or replace view profile.v_job_seeker_action_to_contact_call_click(country_id, profile_id, jdp_viewed_datediff, job_uid, jdp_viewed_datetime, feature_id) as
	SELECT pjr.country_id,
       pjr.profile_id,
       pjr.jdp_viewed_datediff,
       sj.uid_job AS job_uid,
       sj.date    AS jdp_viewed_datetime,
       1          AS feature_id
FROM profile.profile_jdp_respond pjr
         JOIN imp.session_jdp sj
              ON sj.country = pjr.country_id AND sj.id = pjr.jdp_id AND sj.date_diff = pjr.jdp_viewed_datediff
         JOIN profile.profile_submitted vps ON vps.country_id = pjr.country_id AND vps.id = pjr.profile_id AND
                                               pjr.jdp_viewed_datediff >= vps.submission_datediff
WHERE pjr.is_call_click = 1
  AND (pjr.jdp_flag & 1) = 1;

alter table profile.v_job_seeker_action_to_contact_call_click owner to dap;

grant select on profile.v_job_seeker_action_to_contact_call_click to readonly;

grant select on profile.v_job_seeker_action_to_contact_call_click to readonly_ds;

