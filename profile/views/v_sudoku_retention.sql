create or replace view profile.v_sudoku_retention(initial_datediff, day_0_profile_cnt, day_14_profile_cnt, day_7_profile_cnt, group_id) as
	SELECT srp.initial_datediff,
       srp.day_0_profile_cnt,
       v14.day_14_profile_cnt,
       v7.day_7_profile_cnt,
       1 AS group_id
FROM profile.v_sudoku_profile_retention_day_0 srp
         LEFT JOIN profile.v_sudoku_profile_retention_day_14 v14
                   ON srp.initial_datediff = v14.initial_datediff AND srp.has_sudoku_call = v14.has_sudoku_call
         LEFT JOIN profile.v_sudoku_profile_retention_day_7 v7
                   ON v14.initial_datediff = v7.initial_datediff AND v14.has_sudoku_call = v7.has_sudoku_call;

alter table profile.v_sudoku_retention owner to rlu;

grant select on profile.v_sudoku_retention to readonly;

grant select on profile.v_sudoku_retention to writeonly_product;

grant select on profile.v_sudoku_retention to readonly_ds;

