create or replace view profile.v_profile_jdp_submission_source_min(country_id, submission_datediff, profile_id, submission_datetime, uid_job, source_type_id, min_submission_datetime) as
	SELECT v_profile_jdp_submission_source.country_id,
       v_profile_jdp_submission_source.submission_datediff,
       v_profile_jdp_submission_source.profile_id,
       v_profile_jdp_submission_source.submission_datetime,
       v_profile_jdp_submission_source.uid_job,
       v_profile_jdp_submission_source.source_type_id,
       min(v_profile_jdp_submission_source.submission_datetime)
       OVER (PARTITION BY v_profile_jdp_submission_source.country_id, v_profile_jdp_submission_source.profile_id) AS min_submission_datetime
FROM profile.v_profile_jdp_submission_source;

alter table profile.v_profile_jdp_submission_source_min owner to rlu;

grant select on profile.v_profile_jdp_submission_source_min to readonly;

grant select on profile.v_profile_jdp_submission_source_min to writeonly_product;

grant select on profile.v_profile_jdp_submission_source_min to readonly_ds;

