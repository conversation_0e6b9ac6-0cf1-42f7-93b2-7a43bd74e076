create or replace view profile.v_sudoku_profile_submitted_retention_7days(date_submitted, retention_1_7days) as
	SELECT v_sudoku_profile_submitted_return.date_submitted,
       count(DISTINCT v_sudoku_profile_submitted_return.profile_id) AS retention_1_7days
FROM profile.v_sudoku_profile_submitted_return
WHERE v_sudoku_profile_submitted_return.jdp_view_on_day >= 1
  AND v_sudoku_profile_submitted_return.jdp_view_on_day <= 7
  AND v_sudoku_profile_submitted_return.date_submitted <=
      (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) - 8)
GROUP BY v_sudoku_profile_submitted_return.date_submitted;

alter table profile.v_sudoku_profile_submitted_retention_7days owner to rlu;

grant select on profile.v_sudoku_profile_submitted_retention_7days to readonly;

grant select on profile.v_sudoku_profile_submitted_retention_7days to writeonly_product;

grant select on profile.v_sudoku_profile_submitted_retention_7days to readonly_ds;

