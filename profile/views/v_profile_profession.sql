create or replace view profile.v_profile_profession(country_id, profile_id, profession_id, profession_name, type, flags) as
	SELECT pp.country_id,
       pp.profile_id,
       pp.profession_id,
       ip.name AS profession_name,
       ip.type,
       ip.flags
FROM profile.profile_profession pp
         LEFT JOIN dimension.info_profession ip ON pp.country_id = ip.country AND pp.profession_id = ip.id;

alter table profile.v_profile_profession owner to dap;

grant select on profile.v_profile_profession to readonly;

