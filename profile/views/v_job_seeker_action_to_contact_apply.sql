create or replace view profile.v_job_seeker_action_to_contact_apply(country_id, employer_apply_id, job_seeker_apply_id, apply_datediff, apply_datetime, profile_id, employer_account_id, job_uid, first_apply_datetime, registration_source_id, feature_id) as
	SELECT sub.country_id,
       sub.employer_apply_id,
       sub.job_seeker_apply_id,
       sub.apply_datediff,
       sub.apply_datetime,
       sub.profile_id,
       sub.employer_account_id,
       sub.job_uid,
       sub.first_apply_datetime,
       sub.registration_source_id,
       sub.feature_id
FROM (SELECT pa.country_id,
             pa.employer_apply_id,
             pa.job_seeker_apply_id,
             pa.apply_datediff,
             pa.apply_datetime,
             pa.profile_id,
             pa.employer_account_id,
             pa.job_uid,
             min(pa.apply_datetime) OVER (PARTITION BY pa.country_id, pa.profile_id) AS first_apply_datetime,
             (SELECT prs.registration_source_id
              FROM profile.profile_registration_source prs
              WHERE prs.country_id = pa.country_id
                AND prs.profile_id = pa.profile_id)                                  AS registration_source_id,
             1                                                                       AS feature_id
      FROM profile.profile_apply pa
               JOIN profile.profile_submitted ps ON ps.country_id = pa.country_id AND ps.id = pa.profile_id AND
                                                    ps.submission_datediff >= pa.apply_datediff) sub
WHERE CASE
          WHEN sub.apply_datetime = sub.first_apply_datetime AND sub.registration_source_id = 24 THEN false
          ELSE true
          END;

alter table profile.v_job_seeker_action_to_contact_apply owner to dap;

grant select on profile.v_job_seeker_action_to_contact_apply to readonly;

grant select on profile.v_job_seeker_action_to_contact_apply to readonly_ds;

