create or replace view profile.v_sudoku_profile_count_by_initial_date(initial_datediff, has_sudoku_call, profile_cnt) as
	SELECT sudoku_profile.initial_datediff,
       sudoku_profile.has_sudoku_call,
       count(DISTINCT sudoku_profile.profile_id) AS profile_cnt
FROM profile.sudoku_profile
GROUP BY sudoku_profile.initial_datediff, sudoku_profile.has_sudoku_call;

alter table profile.v_sudoku_profile_count_by_initial_date owner to rlu;

grant select on profile.v_sudoku_profile_count_by_initial_date to readonly;

grant select on profile.v_sudoku_profile_count_by_initial_date to writeonly_product;

grant select on profile.v_sudoku_profile_count_by_initial_date to readonly_ds;

