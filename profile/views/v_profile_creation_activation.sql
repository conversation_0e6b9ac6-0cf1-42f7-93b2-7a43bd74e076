create or replace view profile.v_profile_creation_activation(country_id, profile_id, creation_datediff, activation_datediff, last_active_datediff, lt_apply_cnt, lt7_apply_cnt, submission_datediff) as
	SELECT p.country_id,
       p.id                       AS profile_id,
       p.creation_datediff,
       min(sa.date_diff)          AS activation_datediff,
       max(sa.date_diff)          AS last_active_datediff,
       count(DISTINCT sa.id)      AS lt_apply_cnt,
       count(DISTINCT
             CASE
                 WHEN sa.date_diff::numeric < (p.creation_datediff::numeric + 7::numeric) THEN sa.id
                 ELSE NULL::bigint
                 END)             AS lt7_apply_cnt,
       min(p.submission_datediff) AS submission_datediff
FROM profile.profile_submitted p
         LEFT JOIN imp.profile_accounts pa ON pa.country = p.country_id AND pa.id_profile = p.id
         LEFT JOIN imp.session_apply sa ON sa.country = pa.country AND sa.id_account = pa.id_accounts AND
                                           sa.date_diff::numeric >= p.creation_datediff::numeric
GROUP BY p.country_id, p.id, p.creation_datediff;

alter table profile.v_profile_creation_activation owner to postgres;