create or replace view profile.v_job_seeker_action_to_contact(country_id, profile_id, jdp_viewed_datediff, action_to_contact_prob) as
	SELECT job_seeker_action_to_contact.country_id,
       job_seeker_action_to_contact.profile_id,
       job_seeker_action_to_contact.action_datediff AS jdp_viewed_datediff,
       CASE
           WHEN sum(job_seeker_action_to_contact.action_to_contact_prob) > 1::numeric THEN 1::numeric
           ELSE sum(job_seeker_action_to_contact.action_to_contact_prob)
           END                                      AS action_to_contact_prob
FROM profile.job_seeker_action_to_contact
GROUP BY job_seeker_action_to_contact.country_id, job_seeker_action_to_contact.profile_id,
         job_seeker_action_to_contact.action_datediff;

alter table profile.v_job_seeker_action_to_contact owner to dap;

grant select on profile.v_job_seeker_action_to_contact to readonly;

grant select on profile.v_job_seeker_action_to_contact to writeonly_product;

grant select on profile.v_job_seeker_action_to_contact to readonly_ds;

