create view profile.v_profile_region_residence(country_id, profile_id, region_residence_id, region_residence_name) as
	SELECT pr.country_id,
       pr.profile_id,
       pr.region_residence_id,
       ir.name AS region_residence_name
FROM profile.profile_region_residence pr
         LEFT JOIN dimension.info_region ir ON pr.country_id = ir.country AND pr.region_residence_id = ir.id;

alter table profile.v_profile_region_residence owner to dap;

grant select on profile.v_profile_region_residence to readonly;

grant select on profile.v_profile_region_residence to "pavlo.kvasnii";

