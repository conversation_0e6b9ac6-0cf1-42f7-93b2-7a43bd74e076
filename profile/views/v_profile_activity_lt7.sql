create or replace view profile.v_profile_activity_lt7
            (country_id, profile_id, creation_datediff, activation_datediff, last_active_datediff,
             registration_source_id, registration_source_name, profile_blue_collar_type_id,
             profile_blue_collar_type_name, lt_apply_cnt, lt7_session_cnt, lt7_dte_jdp_view_cnt, lt7_apply_cnt,
             submission_datediff)
as
SELECT psv.country_id,
       psv.profile_id,
       psv.creation_datediff,
       vpca.activation_datediff,
       vpca.last_active_datediff,
       COALESCE(pcs.registration_source_id, 0)::smallint      AS registration_source_id,
       (SELECT a.source_name AS registration_source_name
        FROM dimension.auth_source a
        WHERE a.id = COALESCE(pcs.registration_source_id, 0)) AS registration_source_name,
       pbs.profile_blue_collar_type_id,
       (SELECT pbct.type_name AS profile_blue_collar_type_name
        FROM dimension.profile_blue_collar_type pbct
        WHERE pbct.id = pbs.profile_blue_collar_type_id)      AS profile_blue_collar_type_name,
       vpca.lt_apply_cnt::integer                             AS lt_apply_cnt,
       psv.lt7_session_cnt,
       psv.lt7_dte_jdp_view_cnt,
       vpca.lt7_apply_cnt::integer                            AS lt7_apply_cnt,
       vpca.submission_datediff
FROM profile.v_profile_creation_activation vpca
         LEFT JOIN profile.profile_session_and_view_lt7 psv
                   ON vpca.country_id = psv.country_id AND vpca.profile_id = psv.profile_id
         LEFT JOIN profile.v_profile_blue_score pbs
                   ON pbs.country_id = psv.country_id AND pbs.profile_id = psv.profile_id
         LEFT JOIN profile.profile_registration_source pcs
                   ON pcs.country_id = psv.country_id AND pcs.profile_id = psv.profile_id
WHERE vpca.country_id = 1;

alter table profile.v_profile_activity_lt7
    owner to rlu;

grant select on profile.v_profile_activity_lt7 to npo;

grant select on profile.v_profile_activity_lt7 to readonly;

grant select on profile.v_profile_activity_lt7 to nsh;

grant select on profile.v_profile_activity_lt7 to writeonly_product;

grant select on profile.v_profile_activity_lt7 to readonly_ds;
