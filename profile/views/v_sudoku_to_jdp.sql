create or replace view profile.v_sudoku_to_jdp(initial_datediff, profile_id, has_sudoku_call, jdp_view_on_day) as
	SELECT spp.initial_datediff,
       spp.profile_id,
       spp.has_sudoku_call,
       pj.jdp_viewed_datediff - spp.initial_datediff AS jdp_view_on_day
FROM profile.sudoku_profile spp
         JOIN profile.profile_jdp pj ON pj.country_id = 1 AND pj.profile_id = spp.profile_id
WHERE pj.jdp_viewed_datediff > 44292
  AND (pj.jdp_viewed_datediff - spp.initial_datediff) >= 1
  AND (pj.jdp_viewed_datediff - spp.initial_datediff) <= 14
  AND spp.has_sudoku_call = 1;

alter table profile.v_sudoku_to_jdp owner to rlu;

grant select on profile.v_sudoku_to_jdp to readonly;

grant select on profile.v_sudoku_to_jdp to writeonly_product;

grant select on profile.v_sudoku_to_jdp to readonly_ds;

