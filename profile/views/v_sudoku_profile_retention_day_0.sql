create or replace view profile.v_sudoku_profile_retention_day_0(initial_datediff, has_sudoku_call, day_0_profile_cnt, jdp_view_on_day) as
	SELECT spp.initial_datediff,
       spp.has_sudoku_call,
       count(DISTINCT spp.profile_id) AS day_0_profile_cnt,
       0                              AS jdp_view_on_day
FROM profile.sudoku_profile spp
WHERE spp.has_sudoku_call = 1
GROUP BY spp.initial_datediff, spp.has_sudoku_call;

alter table profile.v_sudoku_profile_retention_day_0 owner to rlu;

grant select on profile.v_sudoku_profile_retention_day_0 to readonly;

grant select on profile.v_sudoku_profile_retention_day_0 to writeonly_product;

grant select on profile.v_sudoku_profile_retention_day_0 to readonly_ds;

