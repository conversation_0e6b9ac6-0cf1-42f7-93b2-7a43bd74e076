create or replace view profile.v_profile_driver_flow_registration(country_id, profile_id, profile_parent_profession_cnt) as
WITH registration_profile_parent_profession AS (
    SELECT p.id   AS profile_id,
           pp_1.profession_id,
           pap.profession_name,
           h.name AS parent_profession
    FROM imp.profiles p
             JOIN profile.profile_profession pp_1 ON p.country = pp_1.country_id AND p.id = pp_1.profile_id
             JOIN profile.parrent_profession pap
                  ON p.country = pap.country_id AND pap.profession_id = pp_1.profession_id
             JOIN imp.hashtag h ON h.country = p.country AND pap.parrent_id = h.id
    WHERE p.country = 1
      AND p.date_created >= '2022-01-01'::date
),
     profile_parent_profession_count AS (
         SELECT registration_profile_parent_profession.profile_id,
                registration_profile_parent_profession.parent_profession,
                count(registration_profile_parent_profession.profile_id) AS profile_parent_profession_cnt
         FROM registration_profile_parent_profession
         WHERE registration_profile_parent_profession.parent_profession::text = 'Водитель'::text
         GROUP BY registration_profile_parent_profession.profile_id,
                  registration_profile_parent_profession.parent_profession
         ORDER BY registration_profile_parent_profession.profile_id
     ),
     profile_any_profession_count AS (
         SELECT registration_profile_parent_profession.profile_id,
                count(registration_profile_parent_profession.profile_id) AS profile_all_profession_cnt
         FROM registration_profile_parent_profession
         GROUP BY registration_profile_parent_profession.profile_id
     )
SELECT DISTINCT prs.country_id,
                pp.profile_id,
                pp.profile_parent_profession_cnt
FROM profile_parent_profession_count pp
         JOIN profile_any_profession_count pa
              ON pp.profile_id = pa.profile_id AND pp.profile_parent_profession_cnt = pa.profile_all_profession_cnt
         JOIN ypic.v_profile_registration_source prs
              ON prs.country_id = 1 AND pp.profile_id = prs.profile_id AND prs.registration_source_id = 24;

alter table profile.v_profile_driver_flow_registration
    owner to ypi;
