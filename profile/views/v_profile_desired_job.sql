create or replace view profile.v_profile_desired_job(country_id, profile_id, profession_name, blue_score) as
	SELECT pdj.country_id,
       pdj.profile_id,
       ip.name AS profession_name,
       pbs.blue_score
FROM profile.profile_profession pdj
         JOIN profile.profile_submitted p ON p.country_id = pdj.country_id AND p.id = pdj.profile_id
         LEFT JOIN dimension.info_profession ip ON ip.id = pdj.profession_id AND ip.country = pdj.country_id
         LEFT JOIN dimension.professions_blue_score pbs ON pbs.profession::text = ip.name::text
WHERE pdj.country_id = 1;

alter table profile.v_profile_desired_job owner to dap;

grant select on profile.v_profile_desired_job to npo;

grant select on profile.v_profile_desired_job to readonly;

grant select on profile.v_profile_desired_job to nsh;

grant select on profile.v_profile_desired_job to writeonly_product;

grant select on profile.v_profile_desired_job to readonly_ds;

