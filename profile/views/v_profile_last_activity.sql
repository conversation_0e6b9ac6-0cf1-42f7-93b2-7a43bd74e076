create or replace view profile.v_profile_last_activity as
select vps.country_id,
       vps.id                                as profile_id,
       max(vps.submission_datediff)          as submission_datediff,
       max(jatc.action_datediff)             as last_atc_datediff,
       max(session_datediff)                 as last_session_datediff,
       max(jscs.seeker_cycle_first_datediff) as last_cycle_start_datediff
from profile.v_profile_submitted vps
     left join profile.job_seeker_action_to_contact jatc
     on vps.country_id = jatc.country_id
         and vps.id = jatc.profile_id
     left join profile.profile_session ps
     on vps.country_id = ps.country_id
         and vps.id = ps.profile_id
     left join profile.job_seeker_cycle_start jscs
     on vps.country_id = jscs.country_id
         and vps.id = jscs.profile_id
group by vps.country_id, vps.id;
