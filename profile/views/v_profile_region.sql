create or replace view profile.v_profile_region(country_id, profile_id, region_id, name) as
	SELECT pr.country_id,
       pr.profile_id,
       pr.region_id,
       ir.name
FROM profile.profile_region pr
         LEFT JOIN dimension.info_region ir ON pr.country_id = ir.country AND pr.region_id = ir.id;

alter table profile.v_profile_region owner to dap;

grant select on profile.v_profile_region to readonly;

