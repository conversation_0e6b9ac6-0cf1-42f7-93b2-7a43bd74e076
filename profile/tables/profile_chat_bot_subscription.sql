select 1 as country_id,
       profile_id,
       case when chat_bot_source_name = 'Viber' then 1
            when chat_bot_source_name = 'Telegram' then 2
        else 0 end as subscription_chanel_id,
        min(date_diff) as first_subscription_datediff
from job_seeker.chat_bot_user_change_subscribe_status
where profile_id is not null and
      user_subscribe_status = 1 /*subsribe*/
group by profile_id,
         case when chat_bot_source_name = 'Viber' then 1
            when chat_bot_source_name = 'Telegram' then 2
         else 0 end;
