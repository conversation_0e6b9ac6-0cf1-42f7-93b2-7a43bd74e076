-- insert procedure - profile.insert_sudoku_profile

select initial_datediff,
       p.profile_id as profile_id,
       p.submission_datediff,
       p.has_sudoku_call,
       p.sudoku_call_datetime,
      --7 day action count
       (select sum(atcs.action_to_contact_prob)
           from profile.action_to_contact_structure atcs
           where atcs.country_id = 1
           and atcs.profile_id = p.profile_id
            and atcs.action_datediff between p.initial_datediff and p.initial_datediff+6) as atc_1_7_days,
        --7 day action emp
       (select sum(atcs.action_to_contact_prob)
           from profile.action_to_contact_structure atcs
           where atcs.country_id = 1 and atcs.platform_user_type_id = 2
           and atcs.profile_id = p.profile_id
            and atcs.action_datediff between p.initial_datediff and p.initial_datediff+6) as atc_emp_1_7_days,
      -- action by js kinda 7day
       (select sum(atcs.action_to_contact_prob)
           from profile.action_to_contact_structure atcs
           where atcs.country_id = 1 and atcs.platform_user_type_id = 1
           and atcs.profile_id = p.profile_id
            and atcs.action_datediff between p.initial_datediff and p.initial_datediff+6) as atc_js_1_7_days,
       --14 day:
       --js+emp
       (select sum(atcs.action_to_contact_prob)
           from profile.action_to_contact_structure atcs
           where atcs.country_id = 1
           and atcs.profile_id = p.profile_id
            and atcs.action_datediff between p.initial_datediff and p.initial_datediff+13) as atc_1_14_days,
       --emp
       (select sum(atcs.action_to_contact_prob)
           from profile.action_to_contact_structure atcs
           where atcs.country_id = 1 and atcs.platform_user_type_id = 2
           and atcs.profile_id = p.profile_id
            and atcs.action_datediff between p.initial_datediff and p.initial_datediff+13) as atc_emp_1_14_days,
        -- js
        (select sum(atcs.action_to_contact_prob)
           from profile.action_to_contact_structure atcs
           where atcs.country_id = 1 and atcs.platform_user_type_id = 1
           and atcs.profile_id = p.profile_id
            and atcs.action_datediff between p.initial_datediff and p.initial_datediff+13) as atc_js_1_14_days,
        --28 days actions:
       -- total actions
        (select sum(atcs.action_to_contact_prob)
           from profile.action_to_contact_structure atcs
           where atcs.country_id = 1
           and atcs.profile_id = p.profile_id
            and atcs.action_datediff between p.initial_datediff and p.initial_datediff+27) as atc_1_28_days,
        --emp actions
         (select sum(atcs.action_to_contact_prob)
           from profile.action_to_contact_structure atcs
           where atcs.country_id = 1 and atcs.platform_user_type_id = 2
           and atcs.profile_id = p.profile_id
            and atcs.action_datediff between p.initial_datediff and p.initial_datediff+27) as atc_emp_1_28_days,
       -- js actions
         (select sum(atcs.action_to_contact_prob)
           from profile.action_to_contact_structure atcs
           where atcs.country_id = 1 and atcs.platform_user_type_id = 1
           and atcs.profile_id = p.profile_id
            and atcs.action_datediff between p.initial_datediff and p.initial_datediff+27)
             --and initial_datediff < fn_get_date_diff(current_date)-27)
             as atc_js_1_28_days
from profile.sudoku_profile p
where initial_datediff between 44296 and fn_get_date_diff(current_date)-8 and initial_datediff not in (44320, 44321, 44322);
