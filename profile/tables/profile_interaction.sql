WITH profile_seen(interact_datediff, profile_id, employer_account_id) AS (
    SELECT psa.date_diff  AS interact_datediff,
           psa.id_profile AS profile_id,
           psa.id_account AS employer_account_id
    FROM imp_employer.profile_search_action psa
    WHERE psa.action = ANY (ARRAY [1, 2, 3, 4, 6])
),
     apply_seen(interact_datediff, profile_id, employer_account_id) AS (
         SELECT public.fn_get_date_diff(aaa.date_created::timestamp without time zone) AS interact_datediff,
                pa.profile_id,
                pa.employer_account_id
         FROM public.v_nsh_attr_apply_action aaa
                  JOIN profile.profile_apply pa ON pa.employer_apply_id = aaa.id_apply
         WHERE aaa.sources = 1
           AND aaa.country_code = 'UA'::text
           AND (aaa.type_action = ANY (ARRAY ['phone'::text, 'invitation'::text, 'question'::text, 'viber'::text]))
     ),
     apply_all(apply_datediff, apply_cnt) AS (
         SELECT pa.apply_datediff,
                count(DISTINCT (pa.profile_id::character varying::text || '_'::text) ||
                               pa.employer_account_id::character varying::text) AS apply_cnt
         FROM public.v_nsh_attr_apply_action aaa
                  JOIN profile.profile_apply pa ON pa.employer_apply_id = aaa.id_apply
         WHERE aaa.sources = 1
           AND aaa.country_code = 'UA'::text
         GROUP BY pa.apply_datediff
     ),
     interact_daily(interact_datediff, interaction_cnt) AS (
         SELECT interaction.interact_datediff,
                count(DISTINCT (interaction.profile_id::character varying::text || '_'::text) ||
                               interaction.employer_account_id::character varying::text) AS interaction_cnt
         FROM (SELECT profile_seen.interact_datediff,
                      profile_seen.profile_id,
                      profile_seen.employer_account_id
               FROM profile_seen
               UNION ALL
               SELECT apply_seen.interact_datediff,
                      apply_seen.profile_id,
                      apply_seen.employer_account_id
               FROM apply_seen) interaction
         GROUP BY interaction.interact_datediff
     )
SELECT 1 as country_id,
       apply_all.apply_datediff,
       apply_all.apply_cnt,
       interact_daily.interaction_cnt
FROM apply_all
         LEFT JOIN interact_daily ON apply_all.apply_datediff = interact_daily.interact_datediff
where apply_all.apply_datediff = ${DT_NOW} - 1
;

