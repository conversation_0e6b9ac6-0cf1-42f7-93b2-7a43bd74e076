insert into profile.traffic_profile_arpu_7_days
select pss.country_id,
       pss.profile_id,
       pss.submission_datediff,
       pss.session_traffic_source_id,
       pss.session_traffic_source_group_id,
       (select coalesce(sum(pboc.open_contact_price), 0)
        from profile_base.profile_base_open_contact pboc
        where pss.country_id = 1
          and pboc.database_source_id = 1
          and pss.profile_id = pboc.profile_id
          and pboc.open_contact_datediff - pss.submission_datediff between 0 and 7
       )                                               as profile_base_open_contact_price,
       (select coalesce(count(pboc.profile_id), 0)
        from profile_base.profile_base_open_contact pboc
        where pss.country_id = 1
          and pboc.database_source_id = 1
          and pss.profile_id = pboc.profile_id
          and pboc.open_contact_datediff - pss.submission_datediff between 0 and 7
       )                                               as profile_base_open_contact_jcoin_cnt,
       (select coalesce(sum(aop.open_contact_price), 0)
        from employer.apply_open_contact aop
        where aop.database_source_id = 1
          and aop.profile_id = pss.profile_id
          and fn_get_date_diff(aop.open_contact_datetime) - pss.submission_datediff between 0 and 7)
                                                       as apply_open_contact_price,
       (select coalesce(count(aop.profile_id), 0)
        from employer.apply_open_contact aop
        where aop.database_source_id = 1
          and aop.profile_id = pss.profile_id
          and fn_get_date_diff(aop.open_contact_datetime) - pss.submission_datediff between 0 and 7)
                                                       as apply_open_contact_jcoin_cnt,
       (select coalesce(sum(droc.open_contact_price), 0)
        from employer.digital_recruiter_open_contact droc
        where droc.database_source_id = 1
          and droc.profile_id = pss.profile_id
          and fn_get_date_diff(droc.open_contact_datetime) -
              pss.submission_datediff between 0 and 7) as digital_recruiter_open_contact_price,
       (select coalesce(count(droc.profile_id), 0)
        from employer.digital_recruiter_open_contact droc
        where droc.database_source_id = 1
          and droc.profile_id = pss.profile_id
          and fn_get_date_diff(droc.open_contact_datetime) -
              pss.submission_datediff between 0 and 7) as digital_recruiter_open_contact_jcoin_cnt,

       (select coalesce(sum(cop.open_contact_price), 0)
        from profile.call_open_contact cop
        where cop.country_id = 1
          and cop.profile_id = pss.profile_id
          and cop.call_datediff - pss.submission_datediff between 0 and 7
       )                                               as call_open_contact_price,
       (select coalesce(count(cop.profile_id), 0)
        from profile.call_open_contact cop
        where cop.country_id = 1
          and cop.profile_id = pss.profile_id
          and cop.call_datediff - pss.submission_datediff between 0 and 7
       )                                               as call_open_contact_jcoin_cnt
from profile.profile_submission_traffic_source as pss
where fn_get_date_diff(current_date) - pss.submission_datediff >= 7
  and pss.submission_datediff >= 44407
group by 1, 2, 3, 4, 5;
