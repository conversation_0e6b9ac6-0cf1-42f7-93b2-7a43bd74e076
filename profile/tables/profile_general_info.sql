truncate profile.profile_general_info;

create temp table temp_profiles as
select *
from imp.profiles
where country in (1, 10) and data is not null;


insert into profile.profile_general_info(country_id, profile_id, is_submitted, gender, region, regions, yearofbirth, last_name,
                                        first_name, isreadyforrelocate, isvisibleforemployers, isreadyworkabroad, full_birth_date,
                                             workexperience_cnt, desiredsentinel_cnt, certificate_cnt, language_cnt, education_cnt,
                                             submitted_datediff, created_date)
with temp_parce_prof as (
     select country,
               id,
               is_submitted,
               cast(data as jsonb) ->> 'generalInfo' as generalInfo,
               cast(data as jsonb) ->> 'desiredJobs' as desiredJobs,
               cast(data as jsonb) ->> 'isVisibleForEmployers' as isVisibleForEmployers,
               cast(data as jsonb) ->> 'workExperience' as workExperience,
          cast(data as jsonb) ->> 'desiredSentinels' as desiredSentinels,
          cast(data as jsonb) ->> 'certificates' as certificates,
          cast(data as jsonb) ->> 'languages' as languages,
          cast(data as jsonb) ->> 'education' as education,
          date_submitted as submitted_datediff,
          date_created as created_date
     from temp_profiles
     where id <> 4562407)
select country as country_id,
          id as profile_id,
          is_submitted,
          cast(cast(generalInfo as jsonb) ->> 'gender' as smallint) as gender,
          cast(generalInfo as jsonb) ->> 'region' as region,
          trim(both (replace(replace(replace(cast(generalInfo as jsonb) ->> 'regions', '"', ''), '[', ''), ']', '')) ) as regions,
          cast(cast(generalInfo as jsonb) ->> 'yearOfBirth' as int) as yearOfBirth,
          nullif(trim(both cast(cast(generalInfo as jsonb) ->> 'lastName' as varchar(255)) ), '') as last_name,
          nullif(trim(both cast(cast(generalInfo as jsonb) ->> 'firstName' as varchar(255)) ), '') as first_name,
          cast(cast(generalInfo as jsonb) ->> 'isReadyForRelocate' as boolean) as isReadyForRelocate,
          cast(isVisibleForEmployers as boolean) as isVisibleForEmployers,
          cast(cast(generalInfo as jsonb) ->> 'isReadyWorkAbroad' as boolean) as isReadyWorkAbroad,
          cast(case when length(substring(cast(generalInfo as jsonb) ->> 'fullBirthDate',1,10)) = 10
                    then cast(generalInfo as jsonb) ->> 'fullBirthDate'
               end as date) as full_birth_date,
          jsonb_array_length(workExperience::jsonb) as workExperience_cnt,
          jsonb_array_length(desiredSentinels::jsonb) as desiredSentinel_cnt,
          jsonb_array_length(certificates::jsonb) as certificate_cnt,
          jsonb_array_length(languages::jsonb) as language_cnt,
          jsonb_array_length(education::jsonb) as education_cnt,
          submitted_datediff,
          created_date
from temp_parce_prof
order by id;

drop table temp_profiles;
