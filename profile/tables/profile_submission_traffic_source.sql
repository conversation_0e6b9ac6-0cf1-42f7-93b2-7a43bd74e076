-- insert procedure - traffic.insert_paid_traffic_dashboard_1

select sps.country_id,
       sps.session_datediff as submission_datediff,
       sps.session_id,
       sps.profile_id,
       sm.session_traffic_source_id,
       sm.session_traffic_source_group_id
from profile.session_profile_submission sps
join traffic.session_metric sm on sps.country_id = sm.country_id and sps.session_id = sm.session_id and sps.session_datediff = sm.session_datediff
where sm.country_id = 1 and sps.session_datediff = _datediff;