select vpa.country_id                                                                       as country_id,
       vpa.profile_id                                                                       as profile_id,
       s.cookie_label as session_cookie_label,
       s.id                                                                                 as session_id,
       s.date_diff                                                                          as session_datediff,
       sa.date                                                                              as sign_in_datetime,
       lead(sa.date) over (partition by vpa.country_id, s.id, s.date_diff order by sa.date) as sign_out_datetime
from imp.session s
     inner join imp.session_account sa
     on s.country = sa.country
        and s.date_diff = sa.date_diff
        and s.id = sa.id_session
     inner join profile.v_profile_account vpa
     on vpa.country_id = sa.country
        and vpa.account_id = sa.id_account
where s.country in (1, 10) and s.date_diff = ${DT_NOW} - 1 and s.is_bot = 0;
