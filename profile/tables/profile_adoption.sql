select session.date_diff as session_datediff,
       count(distinct session.id) as session_cnt,
       count(distinct case when profiles.id is not null then session.id end) as session_with_profile_cnt,
       count(distinct case when application_form_conversion.is_success =1 then application_form_conversion.jdp_id end) as apply_cnt,
       count(distinct case when application_form_conversion.is_success =1 and profiles.id is not null then application_form_conversion.jdp_id end) as apply_with_profile_cnt
from imp.session
         left join imp.profile_cookie_labels on profile_cookie_labels.cookie_labels = session.cookie_label and session.country = profile_cookie_labels.country
         left join imp.profiles on profiles.id = profile_cookie_labels.id_profile and profiles.country = profile_cookie_labels.country
                                and profiles.data is not null and session.date_diff >= profiles.date_diff and profiles.is_submitted = true
        left join apply.jdp on session.country = jdp.country_id and session.id = jdp.session_id and session.date_diff = jdp.jdp_viewed_datediff
        left join apply.application_form_conversion on application_form_conversion.country_id = jdp.country_id and application_form_conversion.jdp_id = jdp.jdp_id
                                                    and application_form_conversion.jdp_viewed_datediff = jdp.jdp_viewed_datediff
                                                    and application_form_conversion.is_success = 1
where session.country = 1 and session.date_diff = ${DT_NOW} - 1 and session.is_bot = 0
group by session.date_diff;