SELECT  p.country                                        AS country_id,
        p.id as profile_id,
        max(p.id) OVER (PARTITION BY p.phone)            AS last_profile_id,
        p.email,
        p.phone,
        p.date_diff::integer                             AS creation_datediff,
        p.data as profile_data,
        p.date_created as created_date,
        p.date_updated as updated_date,
        COALESCE(p.date_submitted, p.date_diff::integer) AS submission_datediff,
        p.date_first_json_update                         AS first_input_datediff,
        p.date_is_submitted as submission_datetime,
        p.date_last_activity as last_activity_date
FROM imp.profiles p
WHERE p.country = 1 AND p.data IS NOT NULL AND p.is_submitted = true;
