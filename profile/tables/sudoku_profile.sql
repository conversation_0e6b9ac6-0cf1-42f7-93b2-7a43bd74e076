select date_first_json_update as initial_datediff,
           id as profile_id,
           date_diff as create_profile_datediff,
           date_submitted,
           is_deleted,
           max(case when cjsc.call_datetime is not null then 1 else 0 end) as has_sudoku_call,
           min(cjsc.call_datetime) as sudoku_call_datetime,
           call_status_name,
       cjsc.source
    from imp.profiles p
    left join imp_statistic.crm_job_seeker_call cjsc on cjsc.country_id = p.country and cjsc.profile_id = p.id
    where country = 1 and date_first_json_update <= fn_get_date_diff(current_date)-1 --between 44293 and fn_get_date_diff(current_date)-1

    group by date_first_json_update,
             id, date_diff,
             date_submitted,
             is_deleted,
             call_status_name,
             cjsc.source;
