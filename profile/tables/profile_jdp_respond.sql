select sj.country_id                                 as country_id,
       sj.profile_id,
       sj.jdp_id                                     as jdp_id,
       sj.jdp_viewed_datediff                        as jdp_viewed_datediff,
       sj.jdp_flag,
       max(case when sj.jdp_flag & 4 = 4 and sja.type = 1 then 1 else 0 end) as is_apply_click,
       max(case when sj.jdp_flag & 4 != 4 and sja.type = 1 then 1 else 0 end) as is_away_click,
       max(case when sja.type in (13, 19, 21, 34) then 1 else 0 end)as is_call_click
from profile.profile_jdp sj
join imp.session_jdp_action sja on sj.country_id = sja.country and sj.jdp_viewed_datediff = sja.date_diff AND sj.jdp_id = sja.id_jdp and
                                   sja.type in (1, 13, 19, 21, 34)
where sj.country_id = 1 and sj.jdp_viewed_datediff = ${DT_NOW} - 1
group by sj.country_id,
       sj.profile_id,
       sj.jdp_id,
       sj.jdp_viewed_datediff,
       sj.jdp_flag
;