create table profile.job_seeker_cycle_start_js_only as
-- циклы поиска работы: старт - создание профиля
select ps.country_id,
       ps.profile_id          as profile_id,
       ps.submission_datediff as seeker_cycle_first_datediff,
       1 /*profile creation*/ as seeker_cycle_start_type_id
from profile.profile_submitted_with_double ps
where ps.submission_datediff >= 44195 /* 2021-01-01 */

union
-- старт - первое обращение за последние 28 дней со стороны соискателя
select distinct
       country_id,
       profile_id,
       action_datediff                         as seeker_cycle_first_datediff,
       2 /*action to contact from job seeker*/ as seeker_cycle_start_type_id
from profile.job_seeker_action_to_contact patc
where patc.action_datediff >= 44195 /* 2021-01-01 */
  and not (patc.feature_id in (2, 3) and patc.action_type_id in (3, 4, 5)) /* messages from job seeker are excluded (Apply Cha<PERSON>, Digital Recruiter Chat) */
  and not (patc.feature_id = 4 and patc.action_type_id = 3) /* messages from job seeker are excluded (Profile Base Chat) */
  and not exists
    (select 1
     from profile.job_seeker_action_to_contact patc_lag
     where patc.country_id = patc_lag.country_id
       and patc.profile_id = patc_lag.profile_id
       and patc_lag.action_datediff between patc.action_datediff - 28 and patc.action_datediff - 1
       and not (patc_lag.feature_id in (2, 3) and patc_lag.action_type_id in (3, 4, 5)) /* message is not an activation */
       and not (patc_lag.feature_id = 4 and patc_lag.action_type_id = 3)
    )
  and not exists
    (select 1
     from profile.profile_submitted_with_double ps
     where patc.country_id = ps.country_id
       and patc.profile_id = ps.profile_id
       and ps.submission_datediff between patc.action_datediff - 28 and patc.action_datediff - 0
    )
;

alter table profile.job_seeker_cycle_start_js_only
    add primary key (country_id, profile_id, seeker_cycle_first_datediff);