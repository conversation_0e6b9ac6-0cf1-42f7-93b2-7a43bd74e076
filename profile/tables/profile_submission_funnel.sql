select sa.country                                                                     as country_id,
       date(sa.date)                                                                  as registration_date,
       case when s.flags & 16 = 16 then 1 else 0 end                                  as is_mobile,
       -- session_auth_source
       sa.source                                                                      as registration_sourse_id,
       -- визначаємо чи це воронка відгуку
       case
           when sa.source in (24 /*apply*/, 39 /*apply agg*/, 43 /*popup jdp*/) then 1
           else 0 end                                                                 as is_apply_registration_source_id,
       aus.source_name                                                                as registration_source_name,
       case
           when sj.flags & 131072 = 131072 then 1
           else 0 end                                                                 as has_additional_questions,
       case when pdfr.profile_id is null then 0 else 1 end                            as has_driver_flow,
       count(distinct
             case when saa.flags & 5 = 5 and saa.type = 2 then saa.id end)            as success_registartion_cnt,
       coalesce(count(distinct case
                                   when s.country = 10 and spa.screen = 2022 /*email*/ and spa.type = 4 and
                                        spa.flags & 0 = 0 then sp.id_profile end), 0) as profile_email_cnt,
       count(distinct case
                          when spa.screen = 2016 and spa.type = 4 and spa.flags & 0 = 0
                              then sp.id_profile end)                                 as input_name_cnt,
       count(distinct case
                          when spa.screen = 2003 and spa.type = 4 and spa.flags & 0 = 0
                              then sp.id_profile end)                                 as summary_about_you_cnt,
       count(distinct case
                          when spa.screen = 2004 and spa.type = 4 and spa.flags & 0 = 0
                              then sp.id_profile end)                                 as desired_job_cnt,
       count(distinct case
                          when spa.screen = 2010 and spa.type = 4 and spa.flags & 0 = 0
                              then sp.id_profile end)                                 as driver_license_cnt,
       count(distinct case
                          when spa.screen = 2017 and spa.type = 4 and spa.flags & 0 = 0
                              then sp.id_profile end)                                 as driver_experience_cnt,
       count(distinct case
                          when (s.country = 1 and spa.screen = 2018 /*Work experience*/ and spa.type = 4 and
                                spa.flags & 0 = 0)
                              or (s.country = 1 and spa.screen = 2005 /*About me ProfileSkillsAndExperience*/ and
                                  spa.type = 4 and spa.flags & 0 = 0)
                              then sp.id_profile

                          when (s.country = 10 and spa.date_diff < 44717 and spa.screen = 2018 /*Work experience*/ and
                                spa.type = 4 and spa.flags & 0 = 0)
                              or (s.country = 10 and spa.date_diff < 44717 and
                                  spa.screen = 2005 /*About me ProfileSkillsAndExperience*/ and spa.type = 4 and
                                  spa.flags & 0 = 0)
                              then sp.id_profile
                          when (s.country = 10 and spa.date_diff >= 44717 and spa.screen = 2021 /*Work experience*/ and
                                spa.type = 4 and spa.flags & 0 = 0)
                              or (s.country = 10 and spa.date_diff >= 44717 and
                                  spa.screen = 2005 /*About me ProfileSkillsAndExperience*/ and spa.type = 4 and
                                  spa.flags & 0 = 0)
                              then sp.id_profile
           end)                                                                       as work_experience_info_cnt,
       coalesce(count(distinct case
                                   when (spa.date_diff >= 44709 and s.country = 10 and spa.type = 4 and
                                         spa.screen = 2009 and
                                         spa.flags & 0 = 0) or
                                        (spa.date_diff >= 44709 and s.country = 10 and spa.screen = 2013 and
                                         spa.type = 200 and
                                         spa.flags & 0 = 0)
                                       then sp.id_profile end), 0)                    as profile_education_cnt,
       count(distinct case
                          when spa.screen = 2011 and spa.type = 4 and spa.flags & 0 = 0
                              then sp.id_profile end)                                 as languages_cnt,
       count(distinct case
                          when spa.screen = 2008 /* зайнятість */ and spa.type = 4 and spa.flags & 0 = 0
                              then sp.id_profile end)                                 as employment_type_cnt,
       count(distinct case
                          when ((spa.screen = 2013 and spa.type = 4 and
                                 (spa.flags & 256 = 256 or spa.flags & 512 = 512))
                              or (spa.screen = 2006 and spa.type = 200 and spa.flags & 0 = 0)
                              )
                              then sp.id_profile end)                                 as visibility_cnt,
       count(distinct case
                          when sa.source in (24 /*apply*/, 39 /*apply agg*/, 43 /*popup jdp*/) and
                               ((spa.screen = 2006 and spa.type = 4 and spa.flags & 0 = 0) or
                                (spa.screen = 2014 and spa.type = 200 and spa.flags & 0 = 0))
                              then sp.id_profile end)                                 as apply_cnt

from imp.session_auth sa /*Нажатие на apply*/
         join imp.session_auth_action saa /*Ввод телефона*/
              on sa.country = saa.country
                  and sa.id = saa.id_auth
                  and sa.date_diff = saa.date_diff
                  and sa.method = 1 /*  via phone number */
                  and saa.type = 2 /* SEND_CODE */
                  and saa.flags & 5 = 5 /* SUCCESS REGISTRATION */
         join imp.session_profile sp
              on sa.country = sp.country
                  and sa.id = sp.id_auth
                  and sa.id_session = sp.id_session
                  and sa.date_diff = sp.date_diff
                  and sp.source = 7 /* source that the Job Seeker came from Auth */
         join imp.session_profile_action spa
              on sa.country = spa.country
                  and sp.id = spa.id_session_profile
                  and sa.date_diff = spa.date_diff
         join imp.session s
              on s.country = spa.country
                  and s.date_diff = spa.date_diff
                  and s.id = spa.id_session
         left join dimension.auth_source aus
                   on aus.id = sa.source
         left join imp.session_jdp sj
                   on sa.country = sj.country
                       and sa.date_diff = sj.date_diff
                       and sp.id_jdp = sj.id
         left join profile.v_profile_driver_flow_registration pdfr
                   on sa.country = pdfr.country_id
                       and sp.id_profile = pdfr.profile_id
where sa.country in (1, 10)
  and sa.date_diff = public.fn_get_date_diff(current_date - 1)
group by 1, 2, 3, 4, 5, 6, 7, 8;
