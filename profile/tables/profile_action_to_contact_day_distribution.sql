select jscs.country_id,
       jscs.profile_id,
       jscs.seeker_cycle_first_datediff,
       vpatcfjs.action_datediff-jscs.seeker_cycle_first_datediff as action_from_seeker_cycle_start_day_interval,
       sum(vpatcfjs.action_to_contact_prob) as action_to_contact_28_days_cnt,
       jscs.seeker_cycle_start_type_id
from profile.job_seeker_cycle_start jscs
join profile.action_to_contact vpatcfjs on vpatcfjs.country_id = jscs.country_id and vpatcfjs.profile_id = jscs.profile_id
     and vpatcfjs.action_datediff between jscs.seeker_cycle_first_datediff and jscs.seeker_cycle_first_datediff + 27
where jscs.seeker_cycle_first_datediff = ${DT_NOW} - 1
group by jscs.country_id,
         jscs.profile_id,
         jscs.seeker_cycle_first_datediff,
         vpatcfjs.action_datediff-jscs.seeker_cycle_first_datediff,
         jscs.seeker_cycle_start_type_id;
