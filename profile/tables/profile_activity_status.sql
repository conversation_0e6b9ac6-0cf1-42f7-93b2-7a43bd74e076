create temp table profile_action as
select distinct
       s.country_id,
       s.profile_id,
       s.session_datediff as action_datediff,
       s.sign_in_datetime as action_datetime,
       2                  as action_type
from profile.profile_session s

union all

select distinct
       country_id,
       ps.profile_id,
       submission_datediff                                                                 as action_datediff,
       coalesce(submission_datetime, fn_get_timestamp_from_date_diff(submission_datediff)) as action_datetime,
       1                                                                                   as action_type
from profile.profile_submitted_with_double ps

union all

select distinct
       country_id,
       profile_id,
       action_datediff,
       action_datetime,
       3 as action_type
from profile.job_seeker_action_to_contact jatc;



create table profile.profile_activity_status as
with
    profile_action as (
        select ps.country_id,
               ps.profile_id,
               a.action_datetime,
               age(coalesce(lead(a.action_datetime) over (partition by ps.profile_id order by a.action_datetime),
                            current_date - 1)
                   , a.action_datetime) as period_to_next_action
        from profile.profile_submitted_with_double ps
             left join profile_action a
             on ps.country_id = a.country_id
                 and ps.profile_id = a.profile_id
    ),
    profile_action_status as (
        select country_id,
               profile_id,
               action_datetime + '1 day'::interval * t.inactive_day_cnt as status_change_datetime, /* має пройти n днів неактивності, на n+1 день змінюється статус */
               t.new_status_flags                                       as status_flags,
               period_to_next_action,
               inactive_day_cnt
        from profile_action a
             join (select 7           as inactive_day_cnt,
                          (2 + 4 + 8) as new_status_flags
                   union all

                   select 14      as inactive_day_cnt,
                          (4 + 8) as new_status_flags
                   union all

                   select 19  as inactive_day_cnt,
                          (8) as new_status_flags
                   union all

                   select 28 as inactive_day_cnt,
                          0  as new_status_flags
                  ) t
             on (a.period_to_next_action >= '28 days'::interval and inactive_day_cnt in (7, 14, 19, 28))
                 or
                (a.period_to_next_action between '19'::interval and '27'::interval and inactive_day_cnt in (7, 14, 19))
                 or
                ((a.period_to_next_action between '14'::interval and '18'::interval) and inactive_day_cnt in (7, 14))
                 or
                ((a.period_to_next_action between '7'::interval and '13'::interval) and inactive_day_cnt = 7)

        union all

        select country_id,
               profile_id,
               action_datetime as status_change_datetime,
               (1 + 2 + 4 + 8) as status_flags, /* bit 0 - active on DR, bit 1 - active on Profile Base*/
               period_to_next_action,
               0               as inactive_day_cnt
        from profile_action a),
    c as (
        select country_id,
               profile_id,
               status_change_datetime,
               period_to_next_action,
               inactive_day_cnt,
               status_flags,
               lag(status_flags) over (partition by profile_id order by status_change_datetime) as prev_status_flags
        from profile_action_status
    )
select country_id,
       profile_id,
       status_change_datetime,
       status_flags,
       lag(status_flags) over (partition by profile_id order by status_change_datetime)  as prev_status_flags,
       lead(status_flags) over (partition by profile_id order by status_change_datetime) as next_status_flags,
       lead(status_change_datetime)
       over (partition by profile_id order by status_change_datetime)                    as next_status_change_datetime,
       lag(status_change_datetime)
       over (partition by profile_id order by status_change_datetime)                    as prev_status_change_datetime
from c
where status_flags <> coalesce(prev_status_flags, -1);


alter table profile.profile_activity_status
    add primary key (country_id, profile_id, status_change_datetime);