create table profile.m_profile_field_fullness as
-- code for ua
select country          as country_id,
       p.id             as profile_id,
       p.date_created   as created_date,
       p.date_submitted as submission_datediff,
       p.is_submitted,
       1::int           as funnel_step, /*Telephone*/
       case
           when data::json -> 'telephone' is null
               or data::json ->> 'telephone' is null
               or data::json ->> 'telephone' = ''
               or data::json ->> 'telephone' = '[]'
               then 0
           else 1 end   as is_filled,
       1::int           as is_required_field
from imp.profiles p
         join profile.profile_last_activity pls
              on p.country = pls.country_id
                  and p.id = pls.profile_id
where country = 1
      --and date(date_created) >= '2021-08-01'
union all
select country,
       p.id             as profile_id,
       p.date_created,
       p.date_submitted as submitted_datediff,
       p.is_submitted,
       2::int           as funnel_step, /*Email*/
       case
           when data::json -> 'email' is null
               or data::json ->> 'email' is null
               or data::json ->> 'email' = ''
               or data::json ->> 'email' = '[]'
               then 0
           else 1 end   as is_filled,
       0::int           as is_required_field
from imp.profiles p
         join profile.profile_last_activity pls
              on p.country = pls.country_id
                  and p.id = pls.profile_id
where country = 1
union all
select country,
       p.id             as profile_id,
       p.date_created,
       p.date_submitted as submitted_datediff,
       p.is_submitted,
       3::int           as funnel_step, /*first and last name*/
       case
           when (case
                     when data::json #> '{generalInfo,lastName}' is null or
                          data::json #>> '{generalInfo,lastName}' is null or
                          data::json #>> '{generalInfo,lastName}' = '' then 0
                     else 1 end) = 1 and
                (case
                     when data::json #> '{generalInfo,firstName}' is null or
                          data::json #>> '{generalInfo,firstName}' is null or
                          data::json #>> '{generalInfo,firstName}' = '' then 0
                     else 1 end) = 1 then 1
           else 0 end   as is_filled,
       1::int           as is_required_field
from imp.profiles p
         join profile.profile_last_activity pls
              on p.country = pls.country_id
                  and p.id = pls.profile_id
where country = 1
union all
/*General info start*/
select country,
       p.id             as profile_id,
       p.date_created,
       p.date_submitted as submitted_datediff,
       p.is_submitted,
       4::int           as funnel_step, /*gender*/
       case
           when data::json #> '{generalInfo,gender}' is null or data::json #>> '{generalInfo,gender}' is null
               then 0
           else 1 end   as is_filled,
       1::int           as is_required_field
from imp.profiles p
         join profile.profile_last_activity pls
              on p.country = pls.country_id
                  and p.id = pls.profile_id
where country = 1
      --and date(date_created) >= '2021-08-01'
union all
select country,
       p.id             as profile_id,
       p.date_created,
       p.date_submitted as submitted_datediff,
       p.is_submitted,
       5::int           as funnel_step, /*Birth date*/
       case
           when (
                    case
                        when data::json #> '{generalInfo,fullBirthDate}' is null or
                             data::json #>> '{generalInfo,fullBirthDate}' is null or
                             data::json #>> '{generalInfo,fullBirthDate}' = '' then 0
                        else 1 end
                    ) = 1
               or (
                      case
                          when data::json #> '{generalInfo,yearOfBirth}' is null or
                               data::json #>> '{generalInfo,yearOfBirth}' is null or
                               data::json #>> '{generalInfo,yearOfBirth}' = '' then 0
                          else 1 end
                      ) = 1 then 1
           else 0 end   as is_filled,
       1::int           as is_required_field
from imp.profiles p
         join profile.profile_last_activity pls
              on p.country = pls.country_id
                  and p.id = pls.profile_id
where country = 1
      --and date(date_created) >= '2021-08-01'
union all
select country,
       p.id             as profile_id,
       p.date_created,
       p.date_submitted as submitted_datediff,
       p.is_submitted,
       6::int           as funnel_step, /*Regions*/
       case
           when (case
                     when data::json #> '{generalInfo,regions}' is null or
                          data::json #>> '{generalInfo,regions}' is null or
                          data::json #>> '{generalInfo,regions}' = '[""]' or
                          data::json #>> '{generalInfo,regions}' = '[]'
                         then 0
                     else 1 end) = 1
               or (case
                       when data::json #> '{generalInfo,region}' is null or
                            data::json #>> '{generalInfo,region}' is null or
                            data::json #>> '{generalInfo,region}' = '[""]' or
                            data::json #>> '{generalInfo,region}' = '[]'
                           then 0
                       else 1 end) = 1 then 1
           else 0 end   as is_filled,
       1::int           as is_required_field
from imp.profiles p
         join profile.profile_last_activity pls
              on p.country = pls.country_id
                  and p.id = pls.profile_id
where country = 1
      --and date(date_created) >= '2021-08-01'
union all
select country,
       p.id             as profile_id,
       p.date_created,
       p.date_submitted as submitted_datediff,
       p.is_submitted,
       7::int           as funnel_step, /*Is Ready Work Abroad*/
       case
           when data::json #> '{generalInfo, isReadyWorkAbroad}' is null or
                data::json #>> '{generalInfo,isReadyWorkAbroad}' is null then 0
           else 1 end   as is_filled,
       0::int           as is_required_field
from imp.profiles p
         join profile.profile_last_activity pls
              on p.country = pls.country_id
                  and p.id = pls.profile_id
where country = 1
      --and date(date_created) >= '2021-08-01'
union all
select country,
       p.id             as profile_id,
       p.date_created,
       p.date_submitted as submitted_datediff,
       p.is_submitted,
       8::int           as funnel_step, /*Is Ready For Relocate*/
       case
           when data::json #> '{generalInfo,isReadyForRelocate}' is null or
                data::json #>> '{generalInfo,isReadyForRelocate}' is null then 0
           else 1 end   as is_filled,
       0::int           as is_required_field
from imp.profiles p
         join profile.profile_last_activity pls
              on p.country = pls.country_id
                  and p.id = pls.profile_id
where country = 1
      --and date(date_created) >= '2021-08-01'
    /*GI end*/
union all
select country,
       p.id             as profile_id,
       p.date_created,
       p.date_submitted as submitted_datediff,
       p.is_submitted,
       9::int           as funnel_step, /*What job are you searching == desired Sentinels*/
       case
           when data::json -> 'desiredSentinels' is null
               or data::json ->> 'desiredSentinels' is null
               or data::json ->> 'desiredSentinels' = ''
               or data::json ->> 'desiredSentinels' = '[]'
               or pp.is_profession is null
               then 0
           else 1 end   as is_filled,
       1::int           as is_required_field
from imp.profiles p
         join profile.profile_last_activity pls
              on p.country = pls.country_id
                  and p.id = pls.profile_id
         left join (select distinct profile_id,
                                    case
                                        when count(id) over (partition by country_id, profile_id) > 0 then 1
                                        else 0 end as is_profession
                    from profile.profile_profession
                    where country_id = 1) pp
                   on p.id = pp.profile_id
where country = 1
      --and date(date_created) >= '2021-08-01'
union all
select country,
       p.id             as profile_id,
       p.date_created,
       p.date_submitted as submitted_datediff,
       p.is_submitted,
       10::int          as funnel_step, /*work Experience*/
       case
           when data::json -> 'workExperience' is null
               or data::json ->> 'workExperience' is null
               or data::json ->> 'workExperience' = ''
               or data::json ->> 'workExperience' = '[]'
               then 0
           else 1 end   as is_filled,
       1::int           as is_required_field
from imp.profiles p
         join profile.profile_last_activity pls
              on p.country = pls.country_id
                  and p.id = pls.profile_id
where country = 1
      --and date(date_created) >= '2021-08-01'
    /*Additional questions start*/
union all
select country,
       p.id             as profile_id,
       p.date_created,
       p.date_submitted as submitted_datediff,
       p.is_submitted,
       11::int          as funnel_step, /*driver Licenses*/
       case
           when data::json -> 'driverLicenses' is null
               or data::json ->> 'driverLicenses' is null
               or data::json ->> 'driverLicenses' = ''
               or data::json ->> 'driverLicenses' = '[]'
               then 0
           else 1 end   as is_filled,
       0::int           as is_required_field
from imp.profiles p
         join profile.profile_last_activity pls
              on p.country = pls.country_id
                  and p.id = pls.profile_id
where country = 1
      --and date(date_created) >= '2021-08-01'
union all
select country,
       p.id             as profile_id,
       p.date_created,
       p.date_submitted as submitted_datediff,
       p.is_submitted,
       12::int          as funnel_step, /*languages*/
       case
           when data::json -> 'languages' is null
               or data::json ->> 'languages' is null
               or data::json ->> 'languages' = ''
               or data::json ->> 'languages' = '[]'
               then 0
           else 1 end   as is_filled,
       0::int           as is_required_field
from imp.profiles p
         join profile.profile_last_activity pls
              on p.country = pls.country_id
                  and p.id = pls.profile_id
where country = 1
      --and date(date_created) >= '2021-08-01'
    /*AQ end*/
union all
select country,
       p.id             as profile_id,
       p.date_created,
       p.date_submitted as submitted_datediff,
       p.is_submitted,
       13::int          as funnel_step, /*About me*/
       case
           when data::json -> 'aboutMe' is null
               or data::json ->> 'aboutMe' is null
               or data::json ->> 'aboutMe' = ''
               or data::json ->> 'aboutMe' = '[]'
               then 0
           else 1 end   as is_filled,
       0::int           as is_required_field
from imp.profiles p
         join profile.profile_last_activity pls
              on p.country = pls.country_id
                  and p.id = pls.profile_id
where country = 1
      --and date(date_created) >= '2021-08-01'
union all
select country,
       p.id             as profile_id,
       p.date_created,
       p.date_submitted as submitted_datediff,
       p.is_submitted,
       14::int          as funnel_step, /*employment Types*/
       case
           when data::json -> 'employmentTypes' is null
               or data::json ->> 'employmentTypes' is null
               or data::json ->> 'employmentTypes' = ''
               or data::json ->> 'employmentTypes' = '[]'
               then 0
           else 1 end   as is_filled,
       1::int           as is_required_field
from imp.profiles p
         join profile.profile_last_activity pls
              on p.country = pls.country_id
                  and p.id = pls.profile_id
where country = 1
      --and date(date_created) >= '2021-08-01'
union all
select country,
       p.id             as profile_id,
       p.date_created,
       p.date_submitted as submitted_datediff,
       p.is_submitted,
       15::int          as funnel_step, /*Is Visible For Employers*/
       case
           when data::json -> 'isVisibleForEmployers' is null
               or data::json ->> 'isVisibleForEmployers' is null
               or data::json ->> 'isVisibleForEmployers' = ''
               or data::json ->> 'isVisibleForEmployers' = '[]'
               then 0
           else 1 end   as is_filled,
       0::int           as is_required_field
from imp.profiles p
         join profile.profile_last_activity pls
              on p.country = pls.country_id
                  and p.id = pls.profile_id
where country = 1
      --and date(date_created) >= '2021-08-01'
union all
select country,
       p.id             as profile_id,
       p.date_created,
       p.date_submitted as submitted_datediff,
       p.is_submitted,
       16::int          as funnel_step, /*Salary*/
       case
           when data::json -> 'salary' is null
               or data::json ->> 'salary' is null
               or data::json ->> 'salary' = ''
               or data::json ->> 'salary' = '[]'
               then 0
           else 1 end   as is_filled,
       0::int           as is_required_field
from imp.profiles p
         join profile.profile_last_activity pls
              on p.country = pls.country_id
                  and p.id = pls.profile_id
where country = 1
      --and date(date_created) >= '2021-08-01'
union all
select p.country,
       p.id             as profile_id,
       p.date_created,
       p.date_submitted as submitted_datediff,
       p.is_submitted,
       17::int          as funnel_step, /*Currency*/
       case
           when data::json -> 'currency' is null
               or data::json ->> 'currency' is null
               or data::json ->> 'currency' = ''
               or data::json ->> 'currency' = '[]'
               then 0
           else 1 end   as is_filled,
       0::int           as is_required_field
from imp.profiles p
where country = 1
  and date(date_created) >= '2021-08-01'
union all
select country,
       p.id             as profile_id,
       p.date_created,
       p.date_submitted as submitted_datediff,
       p.is_submitted,
       17::int          as funnel_step, /*Photo*/
       case
           when data::json -> 'photoId' is null
               or data::json ->> 'photoId' is null
               or data::json ->> 'photoId' = ''
               or data::json ->> 'photoId' = '[]'
               then 0
           else 1 end   as is_filled,
       0::int           as is_required_field
from imp.profiles p
         join profile.profile_last_activity pls
              on p.country = pls.country_id
                  and p.id = pls.profile_id
where country = 1
      --and date(date_created) >= '2021-08-01'
union all
select country,
       p.id             as profile_id,
       p.date_created,
       p.date_submitted as submitted_datediff,
       p.is_submitted,
       18::int          as funnel_step, /*Education*/
       case
           when data::json -> 'education' is null
               or data::json ->> 'education' is null
               or data::json ->> 'education' = ''
               or data::json ->> 'education' = '[]'
               then 0
           else 1 end   as is_filled,
       0::int           as is_required_field
from imp.profiles p
         join profile.profile_last_activity pls
              on p.country = pls.country_id
                  and p.id = pls.profile_id
where country = 1
      --and date(date_created) >= '2021-08-01'
union all
select country,
       p.id             as profile_id,
       p.date_created,
       p.date_submitted as submitted_datediff,
       p.is_submitted,
       19::int          as funnel_step, /*Certificates*/
       case
           when data::json -> 'certificates' is null
               or data::json ->> 'certificates' is null
               or data::json ->> 'certificates' = ''
               or data::json ->> 'certificates' = '[]'
               then 0
           else 1 end   as is_filled,
       0::int           as is_required_field
from imp.profiles p
         join profile.profile_last_activity pls
              on p.country = pls.country_id
                  and p.id = pls.profile_id
where country = 1
      --and date(date_created) >= '2021-08-01'
union all
select country,
       p.id             as profile_id,
       p.date_created,
       p.date_submitted as submitted_datediff,
       p.is_submitted,
       20::int          as funnel_step, /*Work experience + About me = Work experience info*/
       case
           when (case
                     when data::json -> 'workExperience' is null
                         or data::json ->> 'workExperience' is null
                         or data::json ->> 'workExperience' = ''
                         or data::json ->> 'workExperience' = '[]'
                         then 0
                     else 1 end) = 1
               or (case
                       when data::json -> 'aboutMe' is null
                           or data::json ->> 'aboutMe' is null
                           or data::json ->> 'aboutMe' = ''
                           or data::json ->> 'aboutMe' = '[]'
                           then 0
                       else 1 end) = 1 then 1
           else 0 end   as is_filled,
       1::int           as is_required_field
from imp.profiles p
         join profile.profile_last_activity pls
              on p.country = pls.country_id
                  and p.id = pls.profile_id
where country = 1
      --and date(date_created) >= '2021-08-01'
union all
select country,
       p.id             as profile_id,
       p.date_created,
       p.date_submitted as submitted_datediff,
       p.is_submitted,
       21::int          as funnel_step, /*General info*/
       case
           when (case
                     when data::json #> '{generalInfo,lastName}' is null or
                          data::json #>> '{generalInfo,lastName}' is null or
                          data::json #>> '{generalInfo,lastName}' = '' then 0
                     else 1 end) = 1 and
                (case
                     when data::json #> '{generalInfo,firstName}' is null or
                          data::json #>> '{generalInfo,firstName}' is null or
                          data::json #>> '{generalInfo,firstName}' = '' then 0
                     else 1 end) = 1 and
                (case
                     when data::json #> '{generalInfo,gender}' is null or data::json #>> '{generalInfo,gender}' is null
                         then 0
                     else 1 end) = 1 and
                (case
                     when (case
                               when data::json #> '{generalInfo,regions}' is null or
                                    data::json #>> '{generalInfo,regions}' is null or
                                    data::json #>> '{generalInfo,regions}' = '[""]' or
                                    data::json #>> '{generalInfo,regions}' = '[]'
                                   then 0
                               else 1 end) = 1
                         or (case
                                 when data::json #> '{generalInfo,region}' is null or
                                      data::json #>> '{generalInfo,region}' is null or
                                      data::json #>> '{generalInfo,region}' = '[""]' or
                                      data::json #>> '{generalInfo,region}' = '[]'
                                     then 0
                                 else 1 end) = 1 then 1
                     else 0 end) = 1 and
                (case
                     when (
                              case
                                  when data::json #> '{generalInfo,fullBirthDate}' is null or
                                       data::json #>> '{generalInfo,fullBirthDate}' is null or
                                       data::json #>> '{generalInfo,fullBirthDate}' = '' then 0
                                  else 1 end
                              ) = 1
                         or (
                                case
                                    when data::json #> '{generalInfo,yearOfBirth}' is null or
                                         data::json #>> '{generalInfo,yearOfBirth}' is null or
                                         data::json #>> '{generalInfo,yearOfBirth}' = '' then 0
                                    else 1 end
                                ) = 1 then 1
                     else 0 end) = 1 then 1
           else 0 end   as is_filled,
       1::int           as is_required_field
from imp.profiles p
         join profile.profile_last_activity pls
              on p.country = pls.country_id
                  and p.id = pls.profile_id
where country = 1
union all
select country,
       p.id             as profile_id,
       p.date_created,
       p.date_submitted as submitted_datediff,
       p.is_submitted,
       22::int          as funnel_step, /*Salary period*/
       case
           when data::json -> 'salaryPeriod' is null
               or data::json ->> 'salaryPeriod' is null
               or data::json ->> 'salaryPeriod' = ''
               or data::json ->> 'salaryPeriod' = '[]'
               then 0
           else 1 end   as is_filled,
       0::int           as is_required_field
from imp.profiles p
         join profile.profile_last_activity pls
              on p.country = pls.country_id
                  and p.id = pls.profile_id
where country = 1
      -- code for HU
union all
select country          as country_id,
       p.id             as profile_id,
       p.date_created   as created_date,
       p.date_submitted as submission_datediff,
       p.is_submitted,
       1::int           as funnel_step, /*Telephone*/
       case
           when data::json -> 'telephone' is null
               or data::json ->> 'telephone' is null
               or data::json ->> 'telephone' = ''
               or data::json ->> 'telephone' = '[]'
               then 0
           else 1 end   as is_filled,
       1::int           as is_required_field
from imp.profiles p
where country = 10
union all
select country,
       p.id                                                          as profile_id,
       p.date_created,
       p.date_submitted                                              as submitted_datediff,
       p.is_submitted,
       2::int                                                        as funnel_step, /*Email*/
       case
           when data::json -> 'email' is null
               or data::json ->> 'email' is null
               or data::json ->> 'email' = ''
               or data::json ->> 'email' = '[]'
               then 0
           else 1 end                                                as is_filled,
       case when date_submitted >= 44725 then 1::int else 0::int end as is_required_field
from imp.profiles p
where country = 10
union all
select country,
       p.id             as profile_id,
       p.date_created,
       p.date_submitted as submitted_datediff,
       p.is_submitted,
       3::int           as funnel_step, /*first and last name*/
       case
           when (case
                     when data::json #> '{generalInfo,lastName}' is null or
                          data::json #>> '{generalInfo,lastName}' is null or
                          data::json #>> '{generalInfo,lastName}' = '' then 0
                     else 1 end) = 1 and
                (case
                     when data::json #> '{generalInfo,firstName}' is null or
                          data::json #>> '{generalInfo,firstName}' is null or
                          data::json #>> '{generalInfo,firstName}' = '' then 0
                     else 1 end) = 1 then 1
           else 0 end   as is_filled,
       1::int           as is_required_field
from imp.profiles p
where country = 10
union all
/*General info start*/
select country,
       p.id             as profile_id,
       p.date_created,
       p.date_submitted as submitted_datediff,
       p.is_submitted,
       4::int           as funnel_step, /*gender*/
       case
           when data::json #> '{generalInfo,gender}' is null or data::json #>> '{generalInfo,gender}' is null
               then 0
           else 1 end   as is_filled,
       1::int           as is_required_field
from imp.profiles p
where country = 10
union all
select country,
       p.id             as profile_id,
       p.date_created,
       p.date_submitted as submitted_datediff,
       p.is_submitted,
       5::int           as funnel_step, /*Birth date*/
       case
           when (
                    case
                        when data::json #> '{generalInfo,fullBirthDate}' is null or
                             data::json #>> '{generalInfo,fullBirthDate}' is null or
                             data::json #>> '{generalInfo,fullBirthDate}' = '' then 0
                        else 1 end
                    ) = 1
               or (
                      case
                          when data::json #> '{generalInfo,yearOfBirth}' is null or
                               data::json #>> '{generalInfo,yearOfBirth}' is null or
                               data::json #>> '{generalInfo,yearOfBirth}' = '' then 0
                          else 1 end
                      ) = 1 then 1
           else 0 end   as is_filled,
       1::int           as is_required_field
from imp.profiles p
where country = 10
union all
select country,
       p.id             as profile_id,
       p.date_created,
       p.date_submitted as submitted_datediff,
       p.is_submitted,
       6::int           as funnel_step, /*Regions*/
       case
           when (case
                     when data::json #> '{generalInfo,regions}' is null or
                          data::json #>> '{generalInfo,regions}' is null or
                          data::json #>> '{generalInfo,regions}' = '[""]' or
                          data::json #>> '{generalInfo,regions}' = '[]'
                         then 0
                     else 1 end) = 1
               or (case
                       when data::json #> '{generalInfo,region}' is null or
                            data::json #>> '{generalInfo,region}' is null or
                            data::json #>> '{generalInfo,region}' = '[""]' or
                            data::json #>> '{generalInfo,region}' = '[]'
                           then 0
                       else 1 end) = 1 then 1
           else 0 end   as is_filled,
       1::int           as is_required_field
from imp.profiles p
where country = 10
union all
select country,
       p.id             as profile_id,
       p.date_created,
       p.date_submitted as submitted_datediff,
       p.is_submitted,
       7::int           as funnel_step, /*Is Ready Work Abroad*/
       case
           when data::json #> '{generalInfo, isReadyWorkAbroad}' is null or
                data::json #>> '{generalInfo,isReadyWorkAbroad}' is null then 0
           else 1 end   as is_filled,
       0::int           as is_required_field
from imp.profiles p
where country = 10
union all
select country,
       p.id             as profile_id,
       p.date_created,
       p.date_submitted as submitted_datediff,
       p.is_submitted,
       8::int           as funnel_step, /*Is Ready For Relocate*/
       case
           when data::json #> '{generalInfo,isReadyForRelocate}' is null or
                data::json #>> '{generalInfo,isReadyForRelocate}' is null then 0
           else 1 end   as is_filled,
       0::int           as is_required_field
from imp.profiles p
where country = 10
    /*GI end*/
union all
select country,
       p.id             as profile_id,
       p.date_created,
       p.date_submitted as submitted_datediff,
       p.is_submitted,
       9::int           as funnel_step, /*What job are you searching == desired Sentinels*/
       case
           when data::json -> 'desiredSentinels' is null
               or data::json ->> 'desiredSentinels' is null
               or data::json ->> 'desiredSentinels' = ''
               or data::json ->> 'desiredSentinels' = '[]'
               then 0
           else 1 end   as is_filled,
       1::int           as is_required_field
from imp.profiles p
where country = 10
union all
select country,
       p.id             as profile_id,
       p.date_created,
       p.date_submitted as submitted_datediff,
       p.is_submitted,
       10::int          as funnel_step, /*work Experience*/
       case
           when data::json -> 'workExperience' is null
               or data::json ->> 'workExperience' is null
               or data::json ->> 'workExperience' = ''
               or data::json ->> 'workExperience' = '[]'
               then 0
           else 1 end   as is_filled,
       1::int           as is_required_field
from imp.profiles p
where country = 10
    /*Additional questions start*/
union all
select country,
       p.id             as profile_id,
       p.date_created,
       p.date_submitted as submitted_datediff,
       p.is_submitted,
       11::int          as funnel_step, /*driver Licenses*/
       case
           when data::json -> 'driverLicenses' is null
               or data::json ->> 'driverLicenses' is null
               or data::json ->> 'driverLicenses' = ''
               or data::json ->> 'driverLicenses' = '[]'
               then 0
           else 1 end   as is_filled,
       0::int           as is_required_field
from imp.profiles p
where country = 10
union all
select country,
       p.id             as profile_id,
       p.date_created,
       p.date_submitted as submitted_datediff,
       p.is_submitted,
       12::int          as funnel_step, /*languages*/
       case
           when data::json -> 'languages' is null
               or data::json ->> 'languages' is null
               or data::json ->> 'languages' = ''
               or data::json ->> 'languages' = '[]'
               then 0
           else 1 end   as is_filled,
       0::int           as is_required_field
from imp.profiles p
where country = 10
    /*AQ end*/
union all
select country,
       p.id             as profile_id,
       p.date_created,
       p.date_submitted as submitted_datediff,
       p.is_submitted,
       13::int          as funnel_step, /*About me*/
       case
           when data::json -> 'aboutMe' is null
               or data::json ->> 'aboutMe' is null
               or data::json ->> 'aboutMe' = ''
               or data::json ->> 'aboutMe' = '[]'
               then 0
           else 1 end   as is_filled,
       0::int           as is_required_field
from imp.profiles p
where country = 10
union all
select country,
       p.id                                                         as profile_id,
       p.date_created,
       p.date_submitted                                             as submitted_datediff,
       p.is_submitted,
       14::int                                                      as funnel_step, /*employment Types*/
       case
           when data::json -> 'employmentTypes' is null
               or data::json ->> 'employmentTypes' is null
               or data::json ->> 'employmentTypes' = ''
               or data::json ->> 'employmentTypes' = '[]'
               then 0
           else 1 end                                               as is_filled,
       case when date_submitted < 44710 then 1::int else 0::int end as is_required_field
from imp.profiles p
where country = 10
union all
select country,
       p.id             as profile_id,
       p.date_created,
       p.date_submitted as submitted_datediff,
       p.is_submitted,
       15::int          as funnel_step, /*Is Visible For Employers*/
       case
           when data::json -> 'isVisibleForEmployers' is null
               or data::json ->> 'isVisibleForEmployers' is null
               or data::json ->> 'isVisibleForEmployers' = ''
               or data::json ->> 'isVisibleForEmployers' = '[]'
               then 0
           else 1 end   as is_filled,
       0::int           as is_required_field
from imp.profiles p
where country = 10
union all
select country,
       p.id             as profile_id,
       p.date_created,
       p.date_submitted as submitted_datediff,
       p.is_submitted,
       16::int          as funnel_step, /*Salary*/
       case
           when data::json -> 'salary' is null
               or data::json ->> 'salary' is null
               or data::json ->> 'salary' = ''
               or data::json ->> 'salary' = '[]'
               then 0
           else 1 end   as is_filled,
       0::int           as is_required_field
from imp.profiles p
where country = 10

union all
select p.country,
       p.id             as profile_id,
       p.date_created,
       p.date_submitted as submitted_datediff,
       p.is_submitted,
       17::int          as funnel_step, /*Currency*/
       case
           when data::json -> 'currency' is null
               or data::json ->> 'currency' is null
               or data::json ->> 'currency' = ''
               or data::json ->> 'currency' = '[]'
               then 0
           else 1 end   as is_filled,
       0::int           as is_required_field
from imp.profiles p
where country = 10
union all
select country,
       p.id             as profile_id,
       p.date_created,
       p.date_submitted as submitted_datediff,
       p.is_submitted,
       17::int          as funnel_step, /*Photo*/
       case
           when data::json -> 'photoId' is null
               or data::json ->> 'photoId' is null
               or data::json ->> 'photoId' = ''
               or data::json ->> 'photoId' = '[]'
               then 0
           else 1 end   as is_filled,
       0::int           as is_required_field
from imp.profiles p
where country = 10
union all
select country,
       p.id                                                    as profile_id,
       p.date_created,
       p.date_submitted                                        as submitted_datediff,
       p.is_submitted,
       18::int                                                 as funnel_step, /*Education*/
       case
           when data::json -> 'education' is null
               or data::json ->> 'education' is null
               or data::json ->> 'education' = ''
               or data::json ->> 'education' = '[]'
               then 0
           else 1 end                                          as is_filled,
       case when date_submitted < 44709 then 0::int else 1 end as is_required_field
from imp.profiles p
where country = 10
union all
select country,
       p.id             as profile_id,
       p.date_created,
       p.date_submitted as submitted_datediff,
       p.is_submitted,
       19::int          as funnel_step, /*Certificates*/
       case
           when data::json -> 'certificates' is null
               or data::json ->> 'certificates' is null
               or data::json ->> 'certificates' = ''
               or data::json ->> 'certificates' = '[]'
               then 0
           else 1 end   as is_filled,
       0::int           as is_required_field
from imp.profiles p
where country = 10
union all
select country,
       p.id             as profile_id,
       p.date_created,
       p.date_submitted as submitted_datediff,
       p.is_submitted,
       20::int          as funnel_step, /*Work experience + About me = Work experience info*/
       case
           when (case
                     when data::json -> 'workExperience' is null
                         or data::json ->> 'workExperience' is null
                         or data::json ->> 'workExperience' = ''
                         or data::json ->> 'workExperience' = '[]'
                         then 0
                     else 1 end) = 1
               or (case
                       when data::json -> 'aboutMe' is null
                           or data::json ->> 'aboutMe' is null
                           or data::json ->> 'aboutMe' = ''
                           or data::json ->> 'aboutMe' = '[]'
                           then 0
                       else 1 end) = 1 then 1
           else 0 end   as is_filled,
       1::int           as is_required_field
from imp.profiles p
where country = 10
union all
select country,
       p.id             as profile_id,
       p.date_created,
       p.date_submitted as submitted_datediff,
       p.is_submitted,
       21::int          as funnel_step, /*General info*/
       case
           when (case
                     when data::json #> '{generalInfo,lastName}' is null or
                          data::json #>> '{generalInfo,lastName}' is null or
                          data::json #>> '{generalInfo,lastName}' = '' then 0
                     else 1 end) = 1 and
                (case
                     when data::json #> '{generalInfo,firstName}' is null or
                          data::json #>> '{generalInfo,firstName}' is null or
                          data::json #>> '{generalInfo,firstName}' = '' then 0
                     else 1 end) = 1 and
                (case
                     when data::json #> '{generalInfo,gender}' is null or data::json #>> '{generalInfo,gender}' is null
                         then 0
                     else 1 end) = 1 and
                (case
                     when (case
                               when data::json #> '{generalInfo,regions}' is null or
                                    data::json #>> '{generalInfo,regions}' is null or
                                    data::json #>> '{generalInfo,regions}' = '[""]' or
                                    data::json #>> '{generalInfo,regions}' = '[]'
                                   then 0
                               else 1 end) = 1
                         or (case
                                 when data::json #> '{generalInfo,region}' is null or
                                      data::json #>> '{generalInfo,region}' is null or
                                      data::json #>> '{generalInfo,region}' = '[""]' or
                                      data::json #>> '{generalInfo,region}' = '[]'
                                     then 0
                                 else 1 end) = 1 then 1
                     else 0 end) = 1 and
                (case
                     when (
                              case
                                  when data::json #> '{generalInfo,fullBirthDate}' is null or
                                       data::json #>> '{generalInfo,fullBirthDate}' is null or
                                       data::json #>> '{generalInfo,fullBirthDate}' = '' then 0
                                  else 1 end
                              ) = 1
                         or (
                                case
                                    when data::json #> '{generalInfo,yearOfBirth}' is null or
                                         data::json #>> '{generalInfo,yearOfBirth}' is null or
                                         data::json #>> '{generalInfo,yearOfBirth}' = '' then 0
                                    else 1 end
                                ) = 1 then 1
                     else 0 end) = 1 then 1
           else 0 end   as is_filled,
       1::int           as is_required_field
from imp.profiles p
where country = 10
union all
select country,
       p.id             as profile_id,
       p.date_created,
       p.date_submitted as submitted_datediff,
       p.is_submitted,
       22::int          as funnel_step, /*Salary period*/
       case
           when data::json -> 'salaryPeriod' is null
               or data::json ->> 'salaryPeriod' is null
               or data::json ->> 'salaryPeriod' = ''
               or data::json ->> 'salaryPeriod' = '[]'
               then 0
           else 1 end   as is_filled,
       0::int           as is_required_field
from imp.profiles p
where country = 10;
