select sj.country as country_id,
       vpa.profile_id,
       sj.id_session as session_id,
       sj.id as jdp_id,
       sj.date_diff as jdp_viewed_datediff,
       sj.date as jdp_viewed_datetime,
       sj.id_account as account_id,
             (case when sj.job_id_project = -1 then 1 else 0 end) /**is dte*/ +
             (case when sj.flags & 256 = 256 then 2 else 0 end)  /*is premium*/
             + (case when sj.flags & 4 = 4 then 4 else 0 end) /*has apply*/ as jdp_flag
from imp.session_jdp sj
join profile.v_profile_account vpa
  on vpa.country_id = sj.country
 and vpa.account_id = sj.id_account
where sj.country = 1 and sj.date_diff = ${DT_NOW} - 1
union
-- последняя jdp до авторизации
select country_id, profile_id, session_id, jdp_id, jdp_viewed_datediff, jdp_viewed_datetime, account_id::int, jdp_flag
from (select sj.country                                                             as country_id,
             ps.profile_id,
             sj.id_session                                                          as session_id,
             sj.id                                                                  as jdp_id,
             sj.date_diff                                                           as jdp_viewed_datediff,
             sj.date as jdp_viewed_datetime,
             null                                                                   as account_id,
             (case when sj.job_id_project = -1 then 1 else 0 end) /**is dte*/ +
             (case when sj.flags & 256 = 256 then 2 else 0 end)  /*is premium*/
             + (case when sj.flags & 4 = 4 then 4 else 0 end) /*has apply*/ as jdp_flag,
             ps.sign_in_datetime,
             max(sj.date) over (partition by sj.country, sj.id_session)             as max_jdp_datetime,
             min(ps.sign_in_datetime) over (partition by sj.country, sj.id_session) as min_sign_in_datetime
      from imp.session_jdp sj
               join profile.profile_session ps
                    on ps.country_id = sj.country
                        and ps.session_id = sj.id_session
                        and sj.date < ps.sign_in_datetime
      where sj.country = 1
        and sj.id_account is null
        and sj.date_diff = ${DT_NOW} - 1
     ) a
where max_jdp_datetime = jdp_viewed_datetime
 and min_sign_in_datetime = sign_in_datetime
;
