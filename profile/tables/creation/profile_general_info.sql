create table profile_general_info
(
    country_id            smallint not null,
    profile_id            integer  not null,
    is_submitted          boolean,
    gender                smallint,
    region                varchar(500),
    regions               varchar(1000),
    yearofbirth           integer,
    last_name             varchar(255),
    first_name            varchar(255),
    isreadyforrelocate    boolean,
    isvisibleforemployers boolean,
    isreadyworkabroad     boolean,
    full_birth_date       date,
    primary key (country_id, profile_id)
);

alter table profile_general_info
    owner to postgres;

create index ind_profile_genera_info_region_prof
    on profile_general_info (profile_id);


alter table profile.profile_general_info add column workexperience_cnt int;
alter table profile.profile_general_info add column desiredsentinel_cnt int;
alter table profile.profile_general_info add column certificate_cnt int;
alter table profile.profile_general_info add column language_cnt int;
alter table profile.profile_general_info add column education_cnt int;

alter table profile.profile_general_info add column submitted_datediff int;
alter table profile.profile_general_info add column created_date date;
