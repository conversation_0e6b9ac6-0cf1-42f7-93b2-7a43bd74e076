create table profile.sudoku_profile
(
	initial_datediff integer,
	profile_id integer,
	create_profile_datediff integer,
	submission_datediff integer,
	is_deleted boolean,
	has_sudoku_call integer,
	sudoku_call_datetime timestamp,
	call_status_name varchar(255),
	source integer
);

alter table profile.sudoku_profile owner to postgres;

grant select on profile.sudoku_profile to readonly;

grant select on profile.sudoku_profile to writeonly_product;

grant select on profile.sudoku_profile to readonly_ds;

