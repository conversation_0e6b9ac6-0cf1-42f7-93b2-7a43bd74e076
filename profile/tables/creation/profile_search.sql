create table profile_search (
    country_id smallint not null,
    profile_id int null,
    search_id bigint null,
    search_datediff int null,
    session_id bigint null,
    account_id int null,
    search_datetime timestamp null,

constraint pk_profile_search_id
           primary key (country_id, search_id, search_datediff)
);

alter table profile_search owner to postgres;

create index ind_profile_search_p on profile_search (profile_id);
create index ind_profile_search_ses on profile_search (session_id);