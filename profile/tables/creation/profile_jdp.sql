create table profile_jdp (
    id serial,
    country_id smallint not null,
    profile_id int null,
    session_id bigint null,
    jdp_id bigint null,
    jdp_viewed_datediff int null,
    account_id int null,
    is_dte int null,

constraint pk_profile_jdp_id
           primary key (id)
);

alter table profile_jdp owner to postgres;

create index ind_profile_jdp_j on profile_jdp (jdp_id, jdp_viewed_datediff);
create index ind_profile_jdp_sea on profile_jdp (account_id);
create index ind_profile_jdp_ses on profile_jdp (session_id);
