create table profile_session (
    country_id smallint not null,
    profile_id int not null,
    session_id bigint not null,
    session_datediff int not null,
    sign_in_datetime timestamp not null,
    sign_out_datetime timestamp null,

constraint pk_table_idc_id
           primary key (country_id, profile_id, session_id, session_datediff, sign_in_datetime)
);

alter table profile_session owner to postgres;

create index ind_profile_session_p on profile_session (profile_id);
create index ind_profile_session_sd on profile_session (session_id, session_datediff);

alter table profile.profile_session add column session_cookie_label bigint;
