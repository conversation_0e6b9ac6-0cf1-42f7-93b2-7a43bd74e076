-- циклы поиска работы: старт - создание профиля
select ps.country_id,
       ps.profile_id as profile_id,
       ps.submission_datediff as seeker_cycle_first_datediff,
       1 /*profile creation*/ as seeker_cycle_start_type_id
from profile.profile_submitted_with_double ps
where ps.submission_datediff = ${DT_NOW} - 1

union
-- старт - первое обращение за последние 28 дней со стороны соискателя
select distinct
       country_id,
       profile_id,
       action_datediff as seeker_cycle_first_datediff,
       2 /*action to contact from job seeker*/ as seeker_cycle_start_type_id
from profile.job_seeker_action_to_contact patc
where
      not exists
          (select 1
           from profile.job_seeker_action_to_contact patc_lag
           where patc.country_id = patc_lag.country_id
             and patc.profile_id = patc_lag.profile_id
             and patc_lag.action_datediff between patc.action_datediff - 28 and patc.action_datediff - 1)
   and not exists
          (select 1
           from profile.profile_submitted_with_double ps
           where patc.country_id = ps.country_id
             and patc.profile_id = ps.profile_id
             and ps.submission_datediff between patc.action_datediff - 28 and patc.action_datediff - 0)
   and not exists
          (select 1
           from employer.employer_action_to_contact actc_lag
           where patc.country_id = actc_lag.country_id
             and patc.profile_id = actc_lag.profile_id
             and actc_lag.action_datediff between patc.action_datediff - 28 and patc.action_datediff - 1)
and action_datediff = ${DT_NOW} - 1
union
-- старт - первое обращение за последние 28 дней со стороны работодателя
select distinct actc.country_id,
       actc.profile_id,
       actc.action_datediff as seeker_cycle_first_datediff,
       3 /*action to contact from employer*/ as seeker_cycle_start_type_id
from employer.employer_action_to_contact actc
where actc.action_datediff = ${DT_NOW} - 1
and
      not exists
          (select 1
           from employer.employer_action_to_contact actc_lag
           where actc.country_id = actc_lag.country_id
             and actc.profile_id = actc_lag.profile_id
             and actc_lag.action_datediff between actc.action_datediff - 28 and actc.action_datediff - 1)
  and
      not exists
          (select 1
           from profile.job_seeker_action_to_contact patc_lag
           where actc.country_id = patc_lag.country_id
             and actc.profile_id = patc_lag.profile_id
             and patc_lag.action_datediff between actc.action_datediff - 28 and actc.action_datediff - 0)
   and not exists
          (select 1
           from profile.profile_submitted_with_double ps
           where actc.country_id = ps.country_id
             and actc.profile_id = ps.profile_id
             and ps.submission_datediff between actc.action_datediff - 28 and actc.action_datediff - 0)
;