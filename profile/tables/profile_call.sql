select pj.country_id                                  as country_id,
       pj.profile_id,
       pj.jdp_id                                      as jdp_id,
       sja.date_diff                                  as jdp_action_datediff,
       min(sja.date)                                  as jdp_action_datetime,
       sj.uid_job                                     as job_uid
from profile.profile_jdp pj
join imp.session_jdp sj on pj.country_id = sj.country and pj.jdp_id = sj.id and pj.jdp_viewed_datediff = sj.date_diff
join imp.session_jdp_action sja on sj.country = sja.country and sj.date_diff = sja.date_diff and sj.id = sja.id_jdp and sja.type = 56 and sja.flags &4 = 0 /*not repeated*/
where pj.country_id = 1 and sja.date_diff = ${DT_NOW} - 1
group by pj.country_id,
         pj.profile_id,
         pj.jdp_id,
         sja.date_diff,
         sj.uid_job;
