select distinct
       country_id,
       profile_id,
       employer_account_id,
       platform_user_type_id,
       action_type_id,
       action_datediff,
       action_datetime,
       feature_id,
       dense_rank() over (partition by country_id, profile_id,employer_account_id  order by order_message_id) as order_action_id
from (
         select distinct case when e.country_code = 'ua' then 1
                              when e.country_code = 'hu' then 10
                         else 0 end                                                                              as country_id,
                         id_profile                                                                              as profile_id,
                         case
                             when pm.author_type = 0 then 2 /*Employer*/
                             when pm.author_type = 1 then 1 /*Job seeker*/
                             end                                                                                 as platform_user_type_id,
                         public.fn_get_date_diff(pm.date_created)                                                       as action_datediff,
                         case
                             when pm.author_type = 0 then 3 /*Profile Base*/
                             when pm.author_type = 1 then 4 /*Chat Profile Base Answer*/
                             end                                                                                 as feature_id,
                         dense_rank()
                         over (partition by pm.sources, pm.id_profile, pm.id_employer order by pm.date_created)     as order_message_id,
                         max(pm.id_account_author)
                         over (partition by pm.sources, pm.id_profile, pm.id_employer)                           as employer_account_id,
                         case
                             when pm.author_type = 0 then 4 /*question*/
                             when pm.author_type = 1 then 3 /*question answer*/
                             end                                                                                 as action_type_id,
                         case
                             when lag(pm.author_type) over (partition by pm.sources, pm.id_profile, pm.id_employer order by pm.date_created) =
                                  pm.author_type then 1
                             else 0 end                                                        as is_same_author_type_as_previous,
                         pm.date_created as action_datetime
         from imp_employer.profile_message pm
         left join imp_employer.employer e on e.sources = pm.sources and e.id = pm.id_employer
         where pm.sources = 1
           and pm.id_profile > 0
           --and pm.date_created between current_date - 28 and current_date
     ) a
where is_same_author_type_as_previous = 0 and action_datediff = ${DT_NOW} - 1
union all
select distinct
       country_id,
       profile_id,
       coalesce(employer_account_id, 0) as employer_account_id,
       platform_user_type_id,
       action_type_id,
       action_datediff,
       action_datetime,
       feature_id,
       has_first_action_to_contact + dense_rank() over (partition by country_id, id_apply  order by order_message_id) as order_action_id
from (
         select distinct case when e.country_code = 'ua' then 1
                              when e.country_code = 'hu' then 10
                         else 0 end                                                            as country_id,
                         jap.id_profile                                                        as profile_id,
                         case
                             when pm.author_type = 0 then 2 /*Employer*/
                             when pm.author_type = 1 then 1 /*Job seeker*/
                             end                                                               as platform_user_type_id,
                         public.fn_get_date_diff(pm.date_created)                                     as action_datediff,
                         case
                             when pm.author_type = 0 and ja.flags & 1024 = 0 then 1 /*Apply Answer*/
                             when pm.author_type = 0 and ja.flags & 1024 = 1024 then 2 /*Recommended Apply*/
                             when pm.author_type = 1 and ja.flags & 1024 = 0 then 2 /*Chat Apply Answer*/
                             when pm.author_type = 1 and ja.flags & 1024 = 1024 then 3 /*Chat Recommended Apply Answer*/
                             end                                                               as feature_id,
                         dense_rank() over (partition by pm.sources, pm.id_apply order by pm.date_created)  as order_message_id,
                         case
                                   when ja.flags & 1024 = 0 then 1
                                   when ja.flags & 1024 = 1024 then 0
                                   else 0
                             end as has_first_action_to_contact,
                         max(pm.id_account_author) over (partition by pm.sources, pm.id_apply) as employer_account_id,
                         case
                             when pm.author_type = 0 and pm.type = 0 then 4 /*question*/
                             when pm.author_type = 0 and pm.type = 1 then 101 /*reject*/
                             when pm.author_type = 0 and pm.type = 2 then 102 /*invitation*/
                             when pm.author_type = 0 and pm.type is null then 4 /*question*/
                             when pm.author_type = 1 and pm.type = 0 then 3 /*question answer*/
                             when pm.author_type = 1 and pm.type = 1 then 4 /*reject answer*/
                             when pm.author_type = 1 and pm.type = 2 then 5 /*invitation answer*/
                             when pm.author_type = 1 and pm.type is null  then 3 /*question answer*/
                             end                                                               as action_type_id,
                         case
                             when lag(pm.author_type) over (partition by pm.id_apply, pm.sources order by pm.date_created) =
                                  pm.author_type then 1
                             else 0 end                                                        as is_same_author_type_as_previous,
                         pm.id_apply,
                         pm.date_created as action_datetime
         from imp_employer.job_apply_message pm
                  join imp_employer.job_apply ja
                       on ja.sources = pm.sources
                           and ja.id = pm.id_apply
                  join imp_employer.job_apply_profile jap
                       on jap.sources = ja.sources
                           and jap.id_apply = ja.id
                  left join imp_employer.job j on j.sources = ja.sources and ja.id_job = j.id
                  left join imp_employer.employer e on j.sources = e.sources and j.id_employer = e.id
         where pm.sources = 1
           and jap.id_profile > 0
           --and pm.date_created between current_date - 28 and current_date
     ) a
where is_same_author_type_as_previous = 0 and action_datediff = ${DT_NOW} - 1;
