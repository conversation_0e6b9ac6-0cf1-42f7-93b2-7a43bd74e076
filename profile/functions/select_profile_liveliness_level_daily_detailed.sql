create function profile.select_profile_liveliness_level_daily_detailed(_datediff integer) returns TABLE(country_id smallint, report_datediff integer, profile_id integer, registration_source_id integer, profile_blue_collar_type_id integer, is_created_last_7_days integer, is_session_after_creation_last_7_days integer, is_activated_last_7_days integer, is_session_after_activation_last_7_days integer, is_returned_apply_last_7_days integer)
	language plpgsql
as $$
begin
return query
    select vpca.country_id,
           _datediff - 1  as report_datediff,
           vpca.profile_id,
           coalesce((select rs.registration_source_id
               from profile.profile_registration_source rs
                where rs.country_id = vpca.country_id
                  and rs.profile_id = vpca.profile_id
               ),0) as registration_source_id,
            CASE
               WHEN pbs.blue_score >= 0::double precision AND pbs.blue_score <= 0.34::double precision THEN 1
               WHEN pbs.blue_score >= 0.35::double precision AND pbs.blue_score <= 0.64::double precision THEN 2
               WHEN pbs.blue_score >= 0.65::double precision AND pbs.blue_score <= 1::double precision THEN 3
               ELSE 0
            END AS profile_blue_collar_type_id,
           max(case when vpca.submission_datediff between _datediff - 7 and _datediff - 1 then 1 else 0 end) as is_created_last_7_days,
           max(case when s.date_diff > vpca.submission_datediff then 1 else 0 end) as is_session_after_creation_last_7_days,
           max(case when vpca.activation_datediff between _datediff - 7 and _datediff - 1 then 1 else 0 end) as is_activated_last_7_days,
           max(case when s.date_diff > vpca.activation_datediff then 1 else 0 end) as is_session_after_activation_last_7_days,
           max(case when vpca.last_active_datediff between _datediff - 7 and _datediff - 1 and last_active_datediff > activation_datediff then 1 else 0 end) as is_returned_apply_last_7_days
    from profile.v_profile_creation_activation vpca
    left join imp.profile_cookie_labels pcl
         on pcl.country = vpca.country_id
        and pcl.id_profile = vpca.profile_id
    left join imp.session s
          on s.cookie_label = pcl.cookie_labels
        and s.country = pcl.country
        and s.date_diff between _datediff - 7 and _datediff - 1
        and s.date_diff > vpca.submission_datediff
    left join profile.v_profile_blue_score pbs on pbs.country_id = vpca.country_id and pbs.profile_id = vpca.profile_id
    where creation_datediff between 44034 and _datediff - 1
    group by vpca.profile_id, vpca.country_id,
             CASE
               WHEN pbs.blue_score >= 0::double precision AND pbs.blue_score <= 0.34::double precision THEN 1
               WHEN pbs.blue_score >= 0.35::double precision AND pbs.blue_score <= 0.64::double precision THEN 2
               WHEN pbs.blue_score >= 0.65::double precision AND pbs.blue_score <= 1::double precision THEN 3
               ELSE 0
            END;

end;
$$;

alter function profile.select_profile_liveliness_level_daily_detailed(integer) owner to rlu;

