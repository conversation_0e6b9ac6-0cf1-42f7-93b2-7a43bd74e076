create or replace function public.select_specified_profile_region(integer)
    returns TABLE(country_id smallint, profile_id integer, region_id integer)
    language plpgsql
as
$$
    #variable_conflict use_column
begin
        drop table if exists temp_specified_profile_region;
        drop table if exists temp_region;
        drop table if exists temp_parce_prof;

        create temp table temp_parce_prof as
        select country,
               id,
               cast(data as jsonb) ->> 'generalInfo' as generalInfo,
               cast(data as jsonb) ->> 'desiredJobs' as desiredJobs
        from imp.profiles
        where country in (1, 10, 11) and data is not null;

        create temp table temp_region as
        select country,
               id,
               cast(generalInfo as jsonb) ->> 'region' as region,
               trim(both (replace(replace(replace(cast(generalInfo as jsonb) ->> 'regions', '"', ''), '[', ''), ']', '')) ) as regions
        from temp_parce_prof
        order by id;


        create temp table temp_specified_profile_region as
        select t.country as country_id, t.id as profile_id, s.id as region_id
        from temp_region t
        left join dimension.info_region s on t.country = s.country and lower(t.region) = lower(s.display_name)
        where s.id is not null
        order by t.id;

        delete from temp_region where country = 1 and id in (select distinct profile_id from temp_specified_profile_region where country_id = 1);
        delete from temp_region where country = 10 and id in (select distinct profile_id from temp_specified_profile_region where country_id = 10);
        delete from temp_region where country = 11 and id in (select distinct profile_id from temp_specified_profile_region where country_id = 11);

        insert into temp_specified_profile_region (country_id, profile_id, region_id)
        select t.country as country_id, t.id as profile_id, s.id as region_id
        from temp_region t
        left join dimension.info_region s on t.country = s.country and lower(t.regions) = lower(s.display_name)
        where s.id is not null
        order by t.id;

        delete from temp_region where country = 1 and id in (select distinct profile_id from temp_specified_profile_region where country_id = 1);
        delete from temp_region where country = 10 and id in (select distinct profile_id from temp_specified_profile_region where country_id = 10);
        delete from temp_region where country = 11 and id in (select distinct profile_id from temp_specified_profile_region where country_id = 11);

        insert into temp_specified_profile_region (country_id, profile_id, region_id)
        select distinct t.country as country_id, t.id as profile_id, s.id_region as region_id
        from temp_region t
        left join dimension.info_region_lang s on t.country = s.country_id and lower(t.regions) = lower(s.display_name)
        where country_id = 1 and s.id_region is not null
        order by t.id;

        return query
        select t.country_id, t.profile_id, t.region_id
        from temp_specified_profile_region t;

 end;
$$;

alter function public.select_specified_profile_region(integer) owner to rlu;
