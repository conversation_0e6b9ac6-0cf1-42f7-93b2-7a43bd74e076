create procedure traffic.insert_session_action_agg()
	language plpgsql
as $$
begin


	insert into traffic.session_action_agg(country_id, action_datediff, session_id, session_action_click_cnt)
	select sc.country            as country_id,
		   sc.date_diff             action_datediff,
		   sc.id_session         as session_id,
		   count(distinct sc.id) as session_action_click_cnt
	from imp.session_action as sc
	where sc.country in (1, 10)
	  and sc.date_diff = public.fn_get_date_diff(current_date - 1)
	group by 1, 2, 3;


end;
$$;

alter procedure traffic.insert_session_action_agg() owner to yiv;

