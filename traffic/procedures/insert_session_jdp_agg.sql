create procedure traffic.insert_session_jdp_agg()
	language plpgsql
as $$
begin

	truncate traffic.session_jdp_agg;

	insert into traffic.session_jdp_agg(country_id, jdp_view_datediff, session_id, is_dte_vacancy, jdp_view_cnt, job_view_cnt)
	select sj.country                                         as country_id,
		   sj.date_diff                                       as jdp_view_datediff,
		   sj.id_session                                      as session_id,
		   case when sj.job_id_project = -1 then 1 else 0 end as is_dte_vacancy,
		   count(distinct sj.id)                              as jdp_view_cnt,
		   count(distinct sj.uid_job)                         as job_view_cnt
	from imp.session_jdp as sj
	where sj.country in (1, 10)
		and date_diff >= 44206
	  --and date_diff = public.fn_get_date_diff(current_date - 1)
	group by 1, 2, 3, 4;


end;
$$;

alter procedure traffic.insert_session_jdp_agg() owner to postgres;

