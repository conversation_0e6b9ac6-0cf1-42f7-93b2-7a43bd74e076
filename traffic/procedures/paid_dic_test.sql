SELECT cs.country,
 cs.type AS source,
 CASE
 WHEN cs.label ~ similar_escape('cba[0-9]*_dsa%'::text, NULL::text) THEN 'cba_dsa_'::text || replace(
 replace("substring"(cs.label, '[0-9]+'::text), '_'::text, ''::text), '['::text, ''::text)
 WHEN cs.label ~ similar_escape('cba[0-9]*_pmax%'::text, NULL::text) THEN 'cba_pmax_'::text || replace(
 replace("substring"(cs.label, '[0-9]+'::text), '_'::text, ''::text), '['::text, ''::text)
 WHEN cs.type ~~ '%facebook%'::text AND cs.label = 'other_fb'::text THEN 'fb_other'::text
 WHEN cs.type ~~ '%facebook%'::text AND cs.label <> 'other_fb'::text THEN 'fb_'::text || cs.label
 WHEN cs.type = 'bing'::text THEN 'bing_'::text || cs.label
 WHEN cs.type = 'smart'::text THEN 'smart_'::text || cs.label
 WHEN cs.type = 'remarketing'::text THEN 'rem_'::text || cs.label
 WHEN cs.type = 'gdn'::text THEN 'gdn_'::text || cs.label
 ELSE cs.label
 END AS label,
 cs.is_test
FROM traffic.paid_traf_cost_2023 cs
GROUP BY cs.country, cs.type,
 (
 CASE
 WHEN cs.label ~ similar_escape('cba[0-9]*_dsa%'::text, NULL::text) THEN 'cba_dsa_'::text ||
 replace(replace(
 "substring"(cs.label, '[0-9]+'::text),
 '_'::text,
 ''::text),
 '['::text,
 ''::text)
 WHEN cs.label ~ similar_escape('cba[0-9]*_pmax%'::text, NULL::text) THEN 'cba_pmax_'::text ||
 replace(replace(
 "substring"(cs.label, '[0-9]+'::text),
 '_'::text,
 ''::text),
 '['::text,
 ''::text)
 WHEN cs.type ~~ '%facebook%'::text AND cs.label = 'other_fb'::text THEN 'fb_other'::text
 WHEN cs.type ~~ '%facebook%'::text AND cs.label <> 'other_fb'::text THEN 'fb_'::text || cs.label
 WHEN cs.type = 'bing'::text THEN 'bing_'::text || cs.label
 WHEN cs.type = 'smart'::text THEN 'smart_'::text || cs.label
 WHEN cs.type = 'remarketing'::text THEN 'rem_'::text || cs.label
 WHEN cs.type = 'gdn'::text THEN 'gdn_'::text || cs.label
 ELSE cs.label
 END), cs.is_test
