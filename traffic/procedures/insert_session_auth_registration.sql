create or replace procedure traffic.insert_session_auth_registration(_datediff integer)
    language plpgsql
as
$$
begin

        insert into traffic.session_auth_registration(country_id, action_datediff, cookie_label, traffic_source_id, traffic_source_name, traf_current_source_id,
                                                      traf_current_source_name, registration_source_id, registration_source_name, registration_method_id, session_id, session_auth_id)
        select distinct sa.country               as country_id,
                        sa.date_diff,
                        s.cookie_label,
                        s.id_traf_source as traf_source_id,
                        utc.name                 as traf_source_name,
                        s.id_current_traf_source as traf_current_source_id,
                        utc2.name                as traf_current_source_name,
                        sa.source                as registration_source_id,
                        aus.source_name          as registration_source_name,
                        sa.method                as registration_method_id,
                        sa.id_session            as session_id,
                        sa.id                    as session_auth_id
        from imp.session s
        join imp.session_auth sa on s.country = sa.country and s.date_diff = sa.date_diff and s.id = sa.id_session
        join imp.session_auth_action saa on sa.country = saa.country and sa.id = saa.id_auth and sa.date_diff = saa.date_diff and saa.flags & 5 = 5 /* success registration*/
        left join dimension.auth_source aus on aus.id = sa.source
        left join dimension.u_traffic_source utc on utc.country = s.country and utc.id = s.id_traf_source
        left join dimension.u_traffic_source utc2 on utc2.country = s.country and utc2.id = s.id_current_traf_source
        where sa.country in (1, 10, 11) and s.date_diff = _datediff;



end;
$$;

alter procedure traffic.insert_session_auth_registration(integer) owner to postgres;

