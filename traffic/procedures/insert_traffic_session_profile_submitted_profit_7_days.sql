create procedure traffic.insert_traffic_session_profile_submitted_profit_7_days()
	language plpgsql
as $$
begin


	truncate traffic.traffic_session_profile_submitted_profit_7_days;


	insert into traffic.traffic_session_profile_submitted_profit_7_days(country_id, session_datediff,
                                                             session_traffic_source_group_id, session_traffic_source_id,
                                                             session_traffic_source_name, session_cnt,
                                                             profile_submitted_cnt, profile_base_open_contact_price,
                                                             profile_base_open_contact_jcoin_cnt,
                                                             apply_open_contact_price, apply_open_contact_jcoin_cnt,
                                                             digital_recruiter_open_contact_price,
                                                             digital_recruiter_open_contact_jcoin_cnt,
                                                             call_open_contact_price, call_open_contact_jcoin_cnt)
	with traffic as (
		select s.country_id,
			   s.session_datediff,
			   s.session_traffic_source_group_id,
			   s.session_traffic_source_id,
			   s.session_traffic_source_name,
			   count(distinct session_id) as session_cnt
		from traffic.session_metric s
		where s.session_datediff >= 44407
		group by 1, 2, 3, 4, 5),

		 user_arpu as (
			 select arpu.country_id,
					arpu.submission_datediff,
					arpu.session_traffic_source_group_id,
					arpu.session_traffic_source_id,
					count(distinct arpu.profile_id)                    as profile_submitted_cnt,
					sum(arpu.profile_base_open_contact_price)          as profile_base_open_contact_price,
					sum(arpu.profile_base_open_contact_jcoin_cnt)      as profile_base_open_contact_jcoin_cnt,
					sum(arpu.apply_open_contact_price)                 as apply_open_contact_price,
					sum(arpu.apply_open_contact_jcoin_cnt)             as apply_open_contact_jcoin_cnt,
					sum(arpu.digital_recruiter_open_contact_price)     as digital_recruiter_open_contact_price,
					sum(arpu.digital_recruiter_open_contact_jcoin_cnt) as digital_recruiter_open_contact_jcoin_cnt,
					sum(arpu.call_open_contact_price)                  as call_open_contact_price,
					sum(arpu.call_open_contact_jcoin_cnt)              as call_open_contact_jcoin_cnt
			 from profile.traffic_profile_arpu_7_days arpu
			 where arpu.submission_datediff >= 44407
			 group by 1, 2, 3, 4)

	select distinct traffic.country_id,
					traffic.session_datediff,
					traffic.session_traffic_source_group_id,
					traffic.session_traffic_source_id,
					traffic.session_traffic_source_name,
					traffic.session_cnt,
					coalesce(user_arpu.profile_submitted_cnt, 0)                    as profile_submitted_cnt,
					coalesce(user_arpu.profile_base_open_contact_price, 0)          as profile_base_open_contact_price,
					coalesce(user_arpu.profile_base_open_contact_jcoin_cnt, 0)      as profile_base_open_contact_jcoin_cnt,
					coalesce(user_arpu.apply_open_contact_price, 0)                 as apply_open_contact_price,
					coalesce(user_arpu.apply_open_contact_jcoin_cnt, 0)             as apply_open_contact_jcoin_cnt,
					coalesce(user_arpu.digital_recruiter_open_contact_price, 0)     as digital_recruiter_open_contact_price,
					coalesce(user_arpu.digital_recruiter_open_contact_jcoin_cnt, 0) as digital_recruiter_open_contact_jcoin_cnt,
					coalesce(user_arpu.call_open_contact_price, 0)                  as call_open_contact_price,
					coalesce(user_arpu.call_open_contact_jcoin_cnt, 0)              as call_open_contact_jcoin_cnt
	from traffic
			 left join user_arpu
					   on traffic.country_id = user_arpu.country_id
						   and traffic.session_datediff = user_arpu.submission_datediff
						   and traffic.session_traffic_source_group_id = user_arpu.session_traffic_source_group_id
						   and traffic.session_traffic_source_id = user_arpu.session_traffic_source_id
	where traffic.country_id = 1
	  and fn_get_date_diff(current_date) - traffic.session_datediff >= 7
	  and traffic.session_datediff >= 44407;




end;
$$;

alter procedure traffic.insert_traffic_session_profile_submitted_profit_7_days() owner to yiv;

