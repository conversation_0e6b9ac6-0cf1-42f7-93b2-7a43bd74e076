create or replace procedure traffic.insert_session_metric_agg(_datediff integer)
    language plpgsql
as
$$
begin

        insert into traffic.session_profile_active_retention (country_id, session_datediff, session_id, source_id, letter_type)
        select sj.country as country_id,
                sj.date_diff as session_datediff,
                sj.id_session as session_id,
                min(case when sj.letter_type in (1,3,5,8,9,13,14,39,52, 53, 54, 56, 57, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69,71,74) then 1 /*from letter*/
                        when s.id_current_traf_source in (327686 ) then 2 /*viber_bot traffic*/
                    when s.id_current_traf_source in (198) then 3 /*telegram_bot traffic*/
                    when sj.flags & 67108864 = 67108864  then 4 /*has jdp FromCorezoidBotViber*/
                    when sj.flags & 33554432 = 33554432 /*FromCorezoidBotTelegram*/ then 5/*has jdp FromCorezoidBotTelegram*/
                end) as source_id,
                min(sj.letter_type) as letter_type
        from imp.session_jdp sj
        join imp.session s on s.country = sj.country and s.date_diff = sj.date_diff and s.id = sj.id_session
        where sj.country in (1,10,11)
                and (sj.letter_type in (1,3,5,8,9,13,14,39,52, 53, 54, 56, 57, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69,71,74) or sj.flags & 33554432 = 33554432 or sj.flags & 67108864 = 67108864 or s.id_current_traf_source in (327686,198))
                and sj.date_diff = _datediff
        group by sj.country,
                    sj.date_diff,
                    sj.id_session;




        insert into traffic.session_from_letter (country_id, session_datediff, session_id, source_id, letter_type)
        select s.country as country_id,
                s.date_diff as session_datediff,
                s.id as session_id,
                min(case when sj.letter_type not in (52, 53, 54, 57, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69) then 1 /*with letter type*/
                        when s.id_current_traf_source in (327685/*from letter traffic*/, 327692/*dcz_email*/, 327691 /*dcz_viber*/ ) then 2
                        when sa.id_session is not null then 3 /*from letter with alertview*/
                end) as source_id,
                min(coalesce(sj.letter_type,ev.letter_type)) as letter_type
        from imp.session s
        left join imp.session_jdp sj on s.country = sj.country and s.date_diff = sj.date_diff and s.id = sj.id_session and sj.letter_type is not null /*тільки листи*/
        left join imp.session_alertview sa on sa.country = s.country and sa.date_diff = s.date_diff and sa.id_session = s.id
        left join imp.email_visit ev on ev.country = sa.country and ev.date_diff = sa.date_diff and ev.id_alert = sa.sub_id_alert
        where s.country in (1,10,11)
                and (sj.letter_type not in (52, 53, 54, 57, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69) /*Letter type for Profile*/
                or  s.id_current_traf_source in (327685/*from letter traffic*/, 327692/*dcz_email*/, 327691 /*dcz_viber*/)
                or sa.id_session is not null)
                and s.date_diff = _datediff
        and s.is_bot = 0
        group by s.country,
                    s.date_diff,
                    s.id;




        insert into traffic.session_with_apply (country_id, session_datediff, session_id, apply_cnt)
        select sj.country as country_id,
               sj.date_diff as session_datediff,
               sj.id_session as session_id,
               count(distinct sa.id) as apply_cnt
        from imp.session_apply sa
        join imp.session_jdp_action sja on sja.country = sa.country and sja.date_diff = sa.date_diff and sja.id = sa.id_src_jdp_action
        join imp.session_jdp sj on sj.country = sja.country and sj.date_diff = sja.date_diff and sj.id = sja.id_jdp and sj.job_id_project = -1
        where sa.country in (1, 10, 11) and sa.date_diff = _datediff
        group by country_id,
                 sj.date_diff,
                 sj.id_session;


        insert into traffic.session_with_click_call (country_id, session_datediff, session_id, click_call_cnt)
        select ucc.country_id,
               ucc.jdp_viewed_datediff as session_datediff,
               ucc.session_id,
               count(distinct ucc.jdp_id) as click_call_cnt
        from job_seeker.user_call_click ucc
        where ucc.country_id in (1, 10, 11) and ucc.jdp_viewed_datediff = _datediff and ucc.jdp_flag & 1 = 1
        group by country_id,
                 ucc.jdp_viewed_datediff,
                 ucc.session_id;


        insert into traffic.session_with_profile_creation (country_id, session_datediff, session_id, profile_created_cnt)
        select s2.country_id,
               s2.session_datediff,
               s2.session_id,
               count(distinct s2.profile_id) as profile_created_cnt
        from ( select ps.country_id,
                      ps.id as  profile_id,
                      s.session_id,
                      s.session_datediff,
                      s.sign_in_datetime,
                      min(s.sign_in_datetime) over (partition by ps.country_id,ps.id) as min_sign_in_datetime
                from profile.profile_submitted ps
                join profile.profile_session s on s.country_id = ps.country_id and s.profile_id = ps.id and s.session_datediff = ps.submission_datediff
                where ps.submission_datediff = _datediff )s2
        where s2.min_sign_in_datetime = sign_in_datetime
        group by s2.country_id,
                 s2.session_datediff,
                 s2.session_id;


        insert into traffic.session_metric(country_id, session_id, session_datediff, session_current_traffic_source_id, session_traffic_source_id,
                                    session_current_traffic_source_name, session_traffic_source_name, is_returned, is_mobile,
                                    is_session_registration, is_session_with_profile_creation)
        select distinct s.country                                             as country_id,
                        s.id                                                  as session_id,
                        s.date_diff                                           as session_datediff,
                        s.id_current_traf_source                              as session_current_traffic_source_id,
                        s.id_traf_source                                      as session_traffic_source_id,
                        ts_current.name                                       as session_current_traffic_source_name,
                        ts.name                                               as session_traffic_source_name,
                        sign(s.flags & 2)                                     as is_returned,
                        sign(s.flags & 16)                                    as is_mobile,
                        case when ps.session_id is not null then 1 else 0 end as is_session_registration,
                        case when pc.session_id is not null then 1 else 0 end as is_session_with_profile_creation
        from imp.session s
                 left join dimension.u_traffic_source ts_current
                           on s.country = ts_current.country and ts_current.id = s.id_current_traf_source
                 left join dimension.u_traffic_source ts on s.country = ts.country and ts.id = s.id_traf_source
                 left join traffic.session_auth_registration ps
                           on ps.country_id = s.country and ps.action_datediff = s.date_diff and ps.session_id = s.id
                 left join traffic.session_with_profile_creation pc
                           on pc.country_id = s.country and pc.session_datediff = s.date_diff and pc.session_id = s.id
        where s.country in (1, 10, 11)
          and s.date_diff = _datediff
          and s.is_bot = 0
          and s.flags & 64 = 0 /* drop mobila app session */;



analyze traffic.session_metric;
analyze traffic.session_with_apply;
analyze traffic.session_with_click_call;
analyze traffic.session_with_profile_creation;
analyze traffic.session_search_agg;
analyze traffic.session_action_agg;
analyze traffic.session_jdp_agg;
analyze traffic.session_jdp_agg;



        insert into traffic.session_metric_agg (country_id, session_datediff, session_traffic_source_id,
                                        session_traffic_source_name, session_current_traffic_source_id, session_current_traffic_source_name,
                                        is_returned, is_mobile, is_session_registration,
                                        is_session_with_profile_creation, session_cnt, apply_cnt, click_call_cnt,
                                        profile_created_cnt, search_cnt, session_action_click_cnt, dte_jdp_view_cnt,
                                        agg_jdp_view_cnt)
        select sm.country_id,
               sm.session_datediff,
               sm.session_traffic_source_id,
               sm.session_traffic_source_name,
               sm.session_current_traffic_source_id,
               sm.session_current_traffic_source_name,
               sm.is_returned,
               sm.is_mobile,
               sm.is_session_registration,
               sm.is_session_with_profile_creation,
               coalesce(count(distinct sm.session_id), 0)       as session_cnt,
               coalesce(sum(apply.apply_cnt), 0)                as apply_cnt,
               coalesce(sum(call.click_call_cnt), 0)            as click_call_cnt,
               coalesce(sum(pc.profile_created_cnt), 0)         as profile_created_cnt,
               coalesce(sum(search.session_search_cnt), 0)      as search_cnt,
               coalesce(sum(click.session_action_click_cnt), 0) as session_action_click_cnt,
               coalesce(sum(jdp_dte.jdp_view_cnt), 0)           as dte_jdp_view_cnt,
               coalesce(sum(jdp_agg.jdp_view_cnt), 0)           as agg_jdp_view_cnt
        from traffic.session_metric sm
                 left join traffic.session_with_apply apply
                           on apply.country_id = sm.country_id and apply.session_datediff = sm.session_datediff and
                              apply.session_id = sm.session_id
                 left join traffic.session_with_click_call call
                           on call.country_id = sm.country_id and call.session_datediff = sm.session_datediff and
                              call.session_id = sm.session_id
                 left join traffic.session_with_profile_creation pc
                           on pc.country_id = sm.country_id and pc.session_datediff = sm.session_datediff and
                              pc.session_id = sm.session_id
                 left join traffic.session_search_agg search
                           on search.country_id = sm.country_id and search.search_datediff = sm.session_datediff
                               and search.session_id = sm.session_id
                 left join traffic.session_action_agg click
                           on click.country_id = sm.country_id and click.action_datediff = sm.session_datediff
                               and click.session_id = sm.session_id
                 left join traffic.session_jdp_agg jdp_dte
                           on jdp_dte.country_id = sm.country_id
                               and jdp_dte.jdp_view_datediff = sm.session_datediff
                               and jdp_dte.session_id = sm.session_id
                               and jdp_dte.is_dte_vacancy = 1
                 left join traffic.session_jdp_agg jdp_agg
                           on jdp_agg.country_id = sm.country_id
                               and jdp_agg.jdp_view_datediff = sm.session_datediff
                               and jdp_agg.session_id = sm.session_id
                               and jdp_agg.is_dte_vacancy = 0
        where sm.session_datediff = _datediff
        group by sm.country_id,
                 sm.session_datediff,
                 sm.session_traffic_source_id,
                 sm.session_traffic_source_name,
                 sm.session_current_traffic_source_id,
                 sm.session_current_traffic_source_name,
                 sm.is_returned,
                 sm.is_mobile,
                 sm.session_traffic_source_group_id,
                 sm.is_session_registration,
                 sm.is_session_with_profile_creation;








        insert into traffic.session_paid_search(country_id, session_datediff, session_id, search_id, search_date, keyword_name, region_id, region_original_name)
        with search_data as (
            select sts.country_id,
                   sts.session_datediff,
                   sts.session_id,
                   ss.id                                                                       as search_id,
                   ss.date                                                                     as search_date,
                   ss.q_kw                                                                     as keyword_name,
                   ss.q_id_region                                                              as region_id,
                   ss.q_txt_region                                                             as region_original_name,
                   min(ss.date) over (partition by ss.country, ss.id_session)                  as min_search_date,
                   rank() over (partition by ss.country, ss.id_session order by ss.date,ss.id) as rank
            from traffic.session_metric sts
            left join imp.session_search ss on ss.country = sts.country_id and ss.date_diff = sts.session_datediff and ss.id_session = sts.session_id
            where country_id = 1 and session_traffic_source_group_id = 2 and sts.session_datediff = _datediff
        )
        select country_id,
               session_datediff,
               session_id,
               min(search_id) as search_id,
               search_date,
               keyword_name,
               region_id,
               region_original_name
        from search_data sd
        left join dimension.info_region ir on ir.country = sd.country_id and ir.id = sd.region_id
        where search_date = min_search_date and rank = 1
        group by country_id,
                 session_datediff,
                 session_id,
                 search_date,
                 keyword_name,
                 region_id,
                 region_original_name;


    end;

$$;

alter procedure traffic.insert_session_metric_agg(integer) owner to rlu;

