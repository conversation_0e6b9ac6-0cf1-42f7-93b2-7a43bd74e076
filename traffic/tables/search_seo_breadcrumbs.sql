create or replace procedure traffic.prc_search_seo_breadcrumbs()
    language 'plpgsql'
as
$$
begin

    delete
    from traffic.search_seo_breadcrumbs
    where date >= '2024-09-01'::date;

    insert into traffic.search_seo_breadcrumbs(country, date, region, query_category, imp_cnt, clicks, position_sum,
                                               url_cnt, seo_query_cnt, req_total, req_success, bot_url_cnt, search_cnt,
                                               click_cnt, revenue_usd, click_paid_cnt, conversion_away_cnt,
                                               conversion_revenue_usd, conversion_cnt, total_seo_query)
    select country,
           date,
           region,
           query_category,
           imp_cnt,
           clicks,
           position_sum,
           url_cnt,
           seo_query_cnt,
           req_total,
           req_success,
           bot_url_cnt,
           search_cnt,
           click_cnt,
           revenue_usd,
           click_paid_cnt,
           conversion_away_cnt,
           conversion_revenue_usd,
           conversion_cnt,
           total_seo_query
    from vnov.v_search_seo_breadcrumbs_test_2;

end
$$;
