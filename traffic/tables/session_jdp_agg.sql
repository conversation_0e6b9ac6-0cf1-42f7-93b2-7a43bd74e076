create table traffic.session_jdp_agg as
select sj.country                                         as country_id,
       sj.date_diff                                       as jdp_view_datediff,
       sj.id_session                                      as session_id,
       case when sj.job_id_project = -1 then 1 else 0 end as is_dte_vacancy,
       count(distinct sj.id)                              as jdp_view_cnt,
       count(distinct sj.uid_job)                         as job_view_cnt
from imp.session_jdp as sj
where sj.country = 1
  and date_diff >= 44407
group by 1, 2, 3, 4;
