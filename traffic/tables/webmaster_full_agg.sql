SET NOCOUNT ON;


SELECT '{to_sqlcode_country_code}'  AS country_code,
       data_date                    AS date,
       SUM(impressions)             AS imp,
       SUM(clicks)                  AS clicks,
       SUM(sum_position)            AS position_sum
FROM dbo.{to_sqlcode_tbl_stat} with (nolock)
WHERE data_date BETWEEN '{to_sqlcode_reload_date_start}' AND '{to_sqlcode_reload_date_end}'
GROUP BY data_date;
