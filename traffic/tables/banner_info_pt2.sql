SET NOCOUNT ON;

SELECT b.id              AS banner_id,
       b.id_partner      AS partner_id,
       b.id_place        AS place_id,
       b.url,
       b.name            AS donor_name,
       b.activation_date,
       b.deactivation_date,
       b.priority,
       b.flags,
       b.replace_adx,
       b.click_price,
       b.email           AS manager_email,
       CAST(NULL AS bit) AS on_keywords,
       CAST(NULL AS bit) AS on_region,
       NULL              AS active,
       NULL              AS limit_max_show,
       NULL              AS limit_max_click,
       NULL              AS limit_max_show_per_day,
       NULL              AS limit_max_click_per_day,
       NULL              AS limit_max_day
FROM dbo.banner b WITH (NOLOCK)
;
