
SET NOCOUNT ON;

SELECT date,
       adset_id,
       account_id,
       inline_link_clicks,
       spend,
       impr
INTO #TempFacebookAdset
FROM PublicStatistic.dbo.facebook_adset WITH (NOLOCK)
WHERE date >= '2023-09-01';

SELECT id,
       name,
       campaign_id
INTO #TempAdSets
FROM Facebook.dbo.ad_sets WITH (NOLOCK);

SELECT id,
       name
INTO #TempCampaigns
FROM Facebook.dbo.campaigns WITH (NOLOCK);

SELECT id,
       name
INTO #TempAccounts
FROM Facebook.dbo.accounts WITH (NOLOCK);

SELECT date,
       cop_usd
INTO #TempCurrency
FROM PublicStatistic.dbo.currency WITH (NOLOCK)
WHERE date >= '2023-09-01';

SELECT id,
       type,
       name
INTO #TempLabels
FROM Marketing.legent.labels_and_channels WITH (NOLOCK);

SELECT fad.date,
       fad.adset_id,
       gr.name                                               AS ad_name,
       cam.id                                                AS campaign_id,
       cam.name                                              AS campaign_name,
       fad.account_id,
       acc.name                                              AS account_name,
       lbl.type                                              AS "source",
       lbl.name                                              AS "label",
       SUM(fad.inline_link_clicks)                           AS clicks,
       SUM(fad.spend)                                        AS cost,
       SUM(ROUND(fad.spend / CAST(cur.cop_usd AS FLOAT), 4)) AS cost_usd,
       SUM(fad.impr)                                         AS imp
FROM #TempFacebookAdset fad
         LEFT JOIN #TempAdSets gr ON fad.adset_id = gr.id
         LEFT JOIN #TempCampaigns cam ON cam.id = gr.campaign_id
         LEFT JOIN #TempAccounts acc ON acc.id = fad.account_id
         LEFT JOIN #TempCurrency cur ON cur.date = fad.date
         LEFT JOIN #TempLabels lbl ON lbl.id = Marketing.dbo.get_label_id_from_campaign(cam.name)
GROUP BY fad.date,
         fad.adset_id,
         gr.name,
         cam.id,
         cam.name,
         fad.account_id,
         acc.name,
         lbl.type,
         lbl.name;

DROP TABLE #TempFacebookAdset, #TempAdSets, #TempCampaigns, #TempAccounts, #TempCurrency, #TempLabels;
