select id,
       original_id,
       campaign_id,
       name,
       keywords,
       bid,
       counter_items,
       counter_ads,
       isNeedSync         as is_need_sync,
       sync_at,
       created_at,
       updated_at,
       isEmptyFinalUrl    as is_empty_final_url,
       isNeedUpload       as is_need_upload,
       status,
       is_status_changed,
       bid_update_at,
       sync_error_id,
       sync_keyword_at,
       sync_adgroup_at,
       sync_ex_bid,
       isNeedUploadStatus as is_need_upload_status,
       name_plus_keyword_checksum,
       isAutoBidding      as is_auto_bidding
from [Marketing].[dbo].[ad_groups] groups with (nolock)
inner join
     (Select distinct stat.ad_group_original_id
       FROM Marketing.dbo.ad_groups_cost_statistic stat with (nolock)
       Where cast(stat.date as date) >= '2023-07-01'
        ) stat on stat.ad_group_original_id = groups.original_id
;
