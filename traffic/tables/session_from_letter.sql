select s.country as country_id,
        s.date_diff as session_datediff,
        s.id as session_id,
        min(case when sj.letter_type not in (52, 53, 54, 57, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69) then 1 /*with letter type*/
                when s.id_current_traf_source in (327685/*from letter traffic*/, 327692/*dcz_email*/, 327691 /*dcz_viber*/ ) then 2
                when sa.id_session is not null then 3 /*from letter with alertview*/
        end) as source_id,
        min(coalesce(sj.letter_type,ev.letter_type)) as letter_type
from imp.session s
left join imp.session_jdp sj on s.country = sj.country and s.date_diff = sj.date_diff and s.id = sj.id_session and sj.letter_type is not null /*тільки листи*/
left join imp.session_alertview sa on sa.country = s.country and sa.date_diff = s.date_diff and sa.id_session = s.id
left join imp.email_visit ev on ev.country = sa.country and ev.date_diff = sa.date_diff and ev.id_alert = sa.sub_id_alert
where s.country in (1,10,11)
        and (sj.letter_type not in (52, 53, 54, 57, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69) /*Letter type for Profile*/
        or  s.id_current_traf_source in (327685/*from letter traffic*/, 327692/*dcz_email*/, 327691 /*dcz_viber*/)
        or sa.id_session is not null)
        and s.date_diff = _datediff
and s.is_bot = 0
group by s.country,
            s.date_diff,
            s.id;
