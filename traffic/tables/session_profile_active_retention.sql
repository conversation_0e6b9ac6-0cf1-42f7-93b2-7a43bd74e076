select sj.country as country_id,
        sj.date_diff as session_datediff,
        sj.id_session as session_id,
        min(case when sj.letter_type in (1,3,5,8,9,13,14,39,52, 53, 54, 56, 57, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69,71,74) then 1 /*from letter*/
                when s.id_current_traf_source in (327686 ) then 2 /*viber_bot traffic*/
            when s.id_current_traf_source in (198) then 3 /*telegram_bot traffic*/
            when sj.flags & 67108864 = 67108864  then 4 /*has jdp FromCorezoidBotViber*/
            when sj.flags & 33554432 = 33554432 /*FromCorezoidBotTelegram*/ then 5/*has jdp FromCorezoidBotTelegram*/
        end) as source_id,
        min(sj.letter_type) as letter_type
from imp.session_jdp sj
join imp.session s on s.country = sj.country and s.date_diff = sj.date_diff and s.id = sj.id_session
where sj.country in (1,10,11)
        and (sj.letter_type in (1,3,5,8,9,13,14,39,52, 53, 54, 56, 57, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69,71,74) or sj.flags & 33554432 = 33554432 or sj.flags & 67108864 = 67108864 or s.id_current_traf_source in (327686,198))
        and sj.date_diff = _datediff
group by sj.country,
            sj.date_diff,
            sj.id_session;
