create table traffic.session_from_letter
(
	country_id smallint not null,
	session_datediff integer not null,
	session_id bigint not null,
	source_id integer,
	letter_type integer,
	constraint pk_session_from_letter
		primary key (country_id, session_datediff, session_id)
);

alter table traffic.session_from_letter owner to rlu;

grant select on traffic.session_from_letter to readonly;

grant select on traffic.session_from_letter to writeonly_product;

