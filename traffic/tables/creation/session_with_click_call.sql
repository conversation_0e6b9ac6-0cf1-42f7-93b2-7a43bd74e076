create table traffic.session_with_click_call
(
	country_id smallint not null,
	session_datediff integer not null,
	session_id bigint not null,
	click_call_cnt integer,
	constraint pk_session_with_click_call
		primary key (country_id, session_datediff, session_id)
);

alter table traffic.session_with_click_call owner to postgres;

grant select on traffic.session_with_click_call to readonly;

grant select on traffic.session_with_click_call to writeonly_product;

