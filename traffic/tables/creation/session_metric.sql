create table traffic.session_metric
(
	country_id smallint not null,
	session_id bigint not null,
	session_datediff integer not null,
	session_traffic_source_id integer,
	session_traffic_source_name varchar(255),
	is_returned double precision,
	is_mobile double precision,
	is_session_with_profile_submitted integer,
	is_session_with_profile_creation integer,
	session_traffic_source_group_id integer,
	constraint session_metric_pkey
		primary key (country_id, session_id, session_datediff)
);

alter table traffic.session_metric owner to postgres;

grant select on traffic.session_metric to readonly;


alter table traffic.session_metric add column session_current_traffic_source_id integer;
alter table traffic.session_metric add column session_current_traffic_source_name varchar(255);

