create table traffic.webmaster_query_statistic
(
	country_id smallint not null,
	date date not null,
	url_hash bigint not null,
	query_hash bigint not null,
	url varchar,
	query varchar,
	position numeric,
	impressions numeric,
	clicks numeric,
	ctr numeric,
	constraint webmaster_query_statistic_pk
		primary key (country_id, date, url_hash, query_hash)
)
partition by RANGE (country_id);

alter table traffic.webmaster_query_statistic owner to postgres;

grant select on traffic.webmaster_query_statistic to readonly;

create table traffic.webmaster_query_statistic_pt_ua
partition of traffic.webmaster_query_statistic
(
	constraint webmaster_query_statistic_pt_ua_pkey
		primary key (country_id, date, url_hash, query_hash)
)
FOR VALUES FROM ('1') TO ('2');

alter table traffic.webmaster_query_statistic_pt_ua owner to postgres;

grant select on traffic.webmaster_query_statistic_pt_ua to readonly;

create table traffic.webmaster_query_statistic_pt_2_3
partition of traffic.webmaster_query_statistic
(
	constraint webmaster_query_statistic_pt_2_3_pkey
		primary key (country_id, date, url_hash, query_hash)
)
FOR VALUES FROM ('2') TO ('4');

alter table traffic.webmaster_query_statistic_pt_2_3 owner to postgres;

grant select on traffic.webmaster_query_statistic_pt_2_3 to readonly;

create table traffic.webmaster_query_statistic_pt_4
partition of traffic.webmaster_query_statistic
(
	constraint webmaster_query_statistic_pt_4_pkey
		primary key (country_id, date, url_hash, query_hash)
)
FOR VALUES FROM ('4') TO ('5');

alter table traffic.webmaster_query_statistic_pt_4 owner to postgres;

grant select on traffic.webmaster_query_statistic_pt_4 to readonly;

create table traffic.webmaster_query_statistic_pt_5_7
partition of traffic.webmaster_query_statistic
(
	constraint webmaster_query_statistic_pt_5_7_pkey
		primary key (country_id, date, url_hash, query_hash)
)
FOR VALUES FROM ('5') TO ('8');

alter table traffic.webmaster_query_statistic_pt_5_7 owner to postgres;

grant select on traffic.webmaster_query_statistic_pt_5_7 to readonly;

create table traffic.webmaster_query_statistic_pt_8
partition of traffic.webmaster_query_statistic
(
	constraint webmaster_query_statistic_pt_8_pkey
		primary key (country_id, date, url_hash, query_hash)
)
FOR VALUES FROM ('8') TO ('9');

alter table traffic.webmaster_query_statistic_pt_8 owner to postgres;

grant select on traffic.webmaster_query_statistic_pt_8 to readonly;

create table traffic.webmaster_query_statistic_pt_9
partition of traffic.webmaster_query_statistic
(
	constraint webmaster_query_statistic_pt_9_pkey
		primary key (country_id, date, url_hash, query_hash)
)
FOR VALUES FROM ('9') TO ('10');

alter table traffic.webmaster_query_statistic_pt_9 owner to postgres;

grant select on traffic.webmaster_query_statistic_pt_9 to readonly;

create table traffic.webmaster_query_statistic_pt_10_11
partition of traffic.webmaster_query_statistic
(
	constraint webmaster_query_statistic_pt_10_11_pkey
		primary key (country_id, date, url_hash, query_hash)
)
FOR VALUES FROM ('10') TO ('12');

alter table traffic.webmaster_query_statistic_pt_10_11 owner to postgres;

grant select on traffic.webmaster_query_statistic_pt_10_11 to readonly;

create table traffic.webmaster_query_statistic_pt_12_14
partition of traffic.webmaster_query_statistic
(
	constraint webmaster_query_statistic_pt_12_14_pkey
		primary key (country_id, date, url_hash, query_hash)
)
FOR VALUES FROM ('12') TO ('15');

alter table traffic.webmaster_query_statistic_pt_12_14 owner to postgres;

grant select on traffic.webmaster_query_statistic_pt_12_14 to readonly;

create table traffic.webmaster_query_statistic_pt_15
partition of traffic.webmaster_query_statistic
(
	constraint webmaster_query_statistic_pt_15_pkey
		primary key (country_id, date, url_hash, query_hash)
)
FOR VALUES FROM ('15') TO ('16');

alter table traffic.webmaster_query_statistic_pt_15 owner to postgres;

grant select on traffic.webmaster_query_statistic_pt_15 to readonly;

create table traffic.webmaster_query_statistic_pt_16_18
partition of traffic.webmaster_query_statistic
(
	constraint webmaster_query_statistic_pt_16_18_pkey
		primary key (country_id, date, url_hash, query_hash)
)
FOR VALUES FROM ('16') TO ('19');

alter table traffic.webmaster_query_statistic_pt_16_18 owner to postgres;

grant select on traffic.webmaster_query_statistic_pt_16_18 to readonly;

create table traffic.webmaster_query_statistic_pt_19
partition of traffic.webmaster_query_statistic
(
	constraint webmaster_query_statistic_pt_19_pkey
		primary key (country_id, date, url_hash, query_hash)
)
FOR VALUES FROM ('19') TO ('20');

alter table traffic.webmaster_query_statistic_pt_19 owner to postgres;

grant select on traffic.webmaster_query_statistic_pt_19 to readonly;

create table traffic.webmaster_query_statistic_pt_20_25
partition of traffic.webmaster_query_statistic
(
	constraint webmaster_query_statistic_pt_20_25_pkey
		primary key (country_id, date, url_hash, query_hash)
)
FOR VALUES FROM ('20') TO ('26');

alter table traffic.webmaster_query_statistic_pt_20_25 owner to postgres;

grant select on traffic.webmaster_query_statistic_pt_20_25 to readonly;

create table traffic.webmaster_query_statistic_pt_26_35
partition of traffic.webmaster_query_statistic
(
	constraint webmaster_query_statistic_pt_26_35_pkey
		primary key (country_id, date, url_hash, query_hash)
)
FOR VALUES FROM ('26') TO ('36');

alter table traffic.webmaster_query_statistic_pt_26_35 owner to postgres;

grant select on traffic.webmaster_query_statistic_pt_26_35 to readonly;

create table traffic.webmaster_query_statistic_pt_36_50
partition of traffic.webmaster_query_statistic
(
	constraint webmaster_query_statistic_pt_36_50_pkey
		primary key (country_id, date, url_hash, query_hash)
)
FOR VALUES FROM ('36') TO ('51');

alter table traffic.webmaster_query_statistic_pt_36_50 owner to postgres;

grant select on traffic.webmaster_query_statistic_pt_36_50 to readonly;

create table traffic.webmaster_query_statistic_pt_51_60
partition of traffic.webmaster_query_statistic
(
	constraint webmaster_query_statistic_pt_51_60_pkey
		primary key (country_id, date, url_hash, query_hash)
)
FOR VALUES FROM ('51') TO ('61');

alter table traffic.webmaster_query_statistic_pt_51_60 owner to postgres;

grant select on traffic.webmaster_query_statistic_pt_51_60 to readonly;

create table traffic.webmaster_query_statistic_pt_61_71
partition of traffic.webmaster_query_statistic
(
	constraint webmaster_query_statistic_pt_61_71_pkey
		primary key (country_id, date, url_hash, query_hash)
)
FOR VALUES FROM ('61') TO ('72');

alter table traffic.webmaster_query_statistic_pt_61_71 owner to postgres;

grant select on traffic.webmaster_query_statistic_pt_61_71 to readonly;

create table traffic.webmaster_query_statistic_pt_def
partition of traffic.webmaster_query_statistic
(
	constraint webmaster_query_statistic_pt_def_pkey
		primary key (country_id, date, url_hash, query_hash)
)
DEFAULT;

alter table traffic.webmaster_query_statistic_pt_def owner to postgres;

grant select on traffic.webmaster_query_statistic_pt_def to readonly;

