
create table traffic.session_paid_search_profile_arpu_7_days
(
        country_id                               smallint,
        submission_datediff                      integer,
        session_id                               bigint,
        search_id                                bigint,
        keyword_name                             varchar(4000),
        region_id                                integer,
        region_original_name                     varchar(200),
        profile_id                               integer,
        session_traffic_source_id                integer,
        profile_base_open_contact_price          numeric,
        profile_base_open_contact_jcoin_cnt      bigint,
        apply_open_contact_price                 numeric,
        apply_open_contact_jcoin_cnt             bigint,
        digital_recruiter_open_contact_price     numeric,
        digital_recruiter_open_contact_jcoin_cnt bigint,
        call_open_contact_price                  numeric,
        call_open_contact_jcoin_cnt              bigint
);

alter table traffic.session_paid_search_profile_arpu_7_days
    owner to postgres;
