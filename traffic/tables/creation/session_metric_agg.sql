create table traffic.session_metric_agg
(
	country_id smallint,
	session_datediff integer,
	session_traffic_source_id integer,
	session_traffic_source_name varchar(255),
	is_returned double precision,
	is_mobile double precision,
	session_traffic_source_group_id integer,
	is_session_with_profile_submitted integer,
	is_session_with_profile_creation integer,
	session_cnt bigint,
	apply_cnt bigint,
	click_call_cnt bigint,
	profile_created_cnt bigint,
	search_cnt numeric,
	session_action_click_cnt numeric,
	dte_jdp_view_cnt numeric,
	agg_jdp_view_cnt numeric
);

alter table traffic.session_metric_agg owner to postgres;

grant select on traffic.session_metric_agg to readonly;


alter table traffic.session_metric_agg add column session_current_traffic_source_id integer;
alter table traffic.session_metric_agg add column session_current_traffic_source_name varchar(255);

