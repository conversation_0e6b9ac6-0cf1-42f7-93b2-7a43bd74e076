create table traffic.ahrefs_statistic
(
	id integer not null
		constraint ahrefs_statistic_pkey
			primary key,
	dm varchar(256),
	date timestamp,
	organic_kw integer,
	organic_traffic integer,
	dm_rank integer,
	ahrefs_rank integer,
	rd_total integer,
	rd_gov integer,
	rd_edu integer,
	ips integer,
	subnets integer,
	linked_dm integer,
	backlinks_total integer,
	backlinks_text integer,
	backlinks_nofollow integer,
	backlinks_redirect integer,
	backlinks_image integer,
	backlinks_frame integer,
	backlinks_form integer,
	backlinks_gov integer,
	backlinks_edu integer,
	rd_dofollow integer
);

alter table traffic.ahrefs_statistic owner to postgres;

grant select on traffic.ahrefs_statistic to readonly;

