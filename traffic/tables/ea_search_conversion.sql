    CREATE TEMP TABLE temp_info_project AS
    SELECT id, name
    FROM link_dbo.info_project;
    CREATE INDEX idx_temp_info_project_id ON temp_info_project (id);

    CREATE TEMP TABLE temp_u_traffic_source AS
    SELECT id, name, is_paid, channel
    FROM link_dbo.u_traffic_source;

    CREATE TEMP TABLE temp_job_history AS
    SELECT uid, id_project
    FROM link_dbo.job_history;

    ANALYZE temp_info_project;
    ANALYZE temp_u_traffic_source;
    ANALYZE temp_job_history;


    CREATE TEMP TABLE temp_search_jobs_cnt AS
    SELECT si.date                                      AS date_diff,
           si.id_search                                 AS search_id,
           CASE WHEN jh.id_project = -1 THEN jh.uid END AS job_ea_uid,
           jh.uid                                       AS job_uid
    FROM public.session_impression si
             JOIN public.session s
                  ON s.id = si.id_session
                      AND s.date_diff = si.date
             LEFT JOIN temp_job_history jh
                       ON jh.uid = si.uid_job
    WHERE s.date_diff = _start_date
      AND s.flags & 1 != 1;

    CREATE INDEX idx_temp_search_jobs_cnt_search_id ON temp_search_jobs_cnt (search_id);
    ANALYZE temp_search_jobs_cnt;


    CREATE TEMP TABLE temp_search_click_cnt AS
    SELECT sa.date_diff,
           sa.id_project                                 AS project_id,
           ip.name                                       AS project_name,
           'aways'                                       AS metric,
           CASE
               WHEN sa.id_jdp IS NOT NULL THEN 'Away from Jdp'
               WHEN sa.letter_type IS NOT NULL
                   THEN CONCAT('Away from Letter Type ', sa.letter_type)
               WHEN sa.id_click IS NOT NULL THEN 'Away from SERP'
               WHEN sa.id_click_no_serp IS NOT NULL THEN 'No serp'
               ELSE 'Other'
           END                                           AS click_type,
           CASE
               WHEN s.flags & 16 = 16 THEN 1
               WHEN s.flags & 64 = 64 OR s.flags & 128 = 128 THEN 2
               ELSE 0
           END                                           AS is_mobile,
           uts.name                                      AS traffic_name,
           uts.is_paid                                   AS traffic_is_paid,
           COALESCE(uts.channel, 'undefined')            AS traffic_channel,
           s.ip_cc,
           COALESCE(ss.search_source, ssj.search_source) AS search_source,
           COALESCE(sc.id_search, scj.id_search)         AS search_id,
           LOWER(COALESCE(ss.q_kw, ssj.q_kw))            AS q_kw,
           COALESCE(ss.q_id_region, ssj.q_id_region)     AS q_id_region,
           sa.id                                         AS away_id,
           NULL                                          AS jdp_id,
           NULL                                          AS conversion_id,
           NULL                                          AS phone_view_id
    FROM public.session_away sa
             JOIN public.session s
                  ON sa.id_session = s.id
                      AND sa.date_diff = s.date_diff
             LEFT JOIN temp_info_project ip
                       ON sa.id_project = ip.id
             LEFT JOIN temp_u_traffic_source uts
                       ON s.id_current_traf_source = uts.id
-- serp -> away
             LEFT JOIN public.session_click sc
                       ON sc.date_diff = sa.date_diff
                           AND sc.id = sa.id_click
-- serp -> jdp -> away
             LEFT JOIN public.session_jdp sj
                       ON sj.date_diff = sa.date_diff
                           AND sj.id = sa.id_jdp
             LEFT JOIN public.session_click scj
                       ON scj.date_diff = sj.date_diff
                           AND scj.id = sj.id_click
             LEFT JOIN public.session_search ss
                       ON ss.date_diff = sc.date_diff
                           AND ss.id = sc.id_search
             LEFT JOIN public.session_search ssj
                       ON ssj.date_diff = scj.date_diff
                           AND ssj.id = scj.id_search
    WHERE sa.date_diff = _start_date
      AND s.flags & 1 != 1;


    INSERT INTO temp_search_click_cnt
    SELECT sj.date_diff,
           sj.job_id_project                                             AS project_id,
           ip.name                                                       AS project_name,
           'applies'                                                     AS metric,
           CASE
               WHEN sj.letter_type IS NOT NULL
                   THEN CONCAT('JDP from Letter Type ', sj.letter_type)
               WHEN sj.id_click IS NOT NULL THEN 'JDP from SERP'
               WHEN sj.id_click_no_serp IS NOT NULL THEN 'No serp'
               ELSE 'Other'
           END                                                           AS click_type,
           CASE
               WHEN s.flags & 16 = 16 THEN 1
               WHEN s.flags & 64 = 64 OR s.flags & 128 = 128 THEN 2
               ELSE 0
           END                                                           AS is_mobile,
           uts.name                                                      AS traffic_name,
           uts.is_paid                                                   AS traffic_is_paid,
           COALESCE(uts.channel, 'undefined')                            AS traffic_channel,
           s.ip_cc,
           ss.search_source,
           sc.id_search                                                  AS search_id,
           LOWER(ss.q_kw)                                                AS q_kw,
           ss.q_id_region                                                AS q_id_region,
           NULL                                                          AS away_id,
           sj.id                                                         AS jdp_id,
           sapp.id                                                       AS conversion_id,
           CASE WHEN sja.type IN (13, 19, 21, 34) THEN sj.id_session END AS phone_view_id
    FROM public.session_jdp sj
             JOIN public.session s
                  ON sj.date_diff = s.date_diff
                      AND sj.id_session = s.id
             LEFT JOIN public.session_jdp_action sja
                       ON sj.date_diff = sja.date_diff
                           AND sj.id = sja.id_jdp
             LEFT JOIN public.session_apply sapp
                       ON sapp.date_diff = sja.date_diff
                           AND sapp.id_src_jdp_action = sja.id
             LEFT JOIN public.session_click sc
                       ON sj.date_diff = sc.date_diff
                           AND sj.id_click = sc.id
             LEFT JOIN temp_u_traffic_source uts
                       ON s.id_current_traf_source = uts.id
             LEFT JOIN temp_info_project ip
                       ON sj.job_id_project = ip.id
             LEFT JOIN public.session_search ss
                       ON ss.date_diff = sc.date_diff
                           AND ss.id = sc.id_search
             LEFT JOIN public.session_away sa
                       ON sj.date_diff = sa.date_diff
                           AND sj.id = sa.id_jdp
    WHERE sj.date_diff = _start_date
      AND s.flags & 1 != 1
      AND sa.id IS NULL;

    CREATE INDEX idx_temp_search_click_cnt_search_id ON temp_search_click_cnt (search_id);
    ANALYZE temp_search_click_cnt;


    /*DELETE
    FROM an.rpl_ea_search_conversion
    WHERE date_diff = _start_date;


    INSERT INTO an.rpl_ea_search_conversion(country_id, date_diff, traffic_channel, q_kw, q_id_region,
                                            search_wdata_cnt, search_away_cnt, away_cnt, jdp_click_cnt,
                                            conversion_cnt, phone_view_cnt, job_ea_cnt, job_cnt)*/

    SELECT _country_id                                                           AS country_id,
           scc.date_diff,
           scc.traffic_channel,
           scc.q_kw,
           scc.q_id_region,
           COUNT(DISTINCT scc.search_id)                                         AS search_wdata_cnt,
           COUNT(DISTINCT CASE WHEN scc.metric = 'aways' THEN scc.search_id END) AS search_away_cnt,
           COUNT(DISTINCT scc.away_id)                                           AS away_cnt,
           COUNT(DISTINCT scc.jdp_id)                                            AS jdp_click_cnt,
           COUNT(DISTINCT scc.conversion_id)                                     AS conversion_cnt,
           COUNT(DISTINCT scc.phone_view_id)                                     AS phone_view_cnt,
           COUNT(DISTINCT sjc.job_ea_uid)                                        AS job_ea_cnt,
           COUNT(DISTINCT sjc.job_uid)                                           AS job_cnt
    FROM temp_search_click_cnt scc
             LEFT JOIN temp_search_jobs_cnt sjc
                       ON sjc.search_id = scc.search_id
    GROUP BY scc.date_diff,
             scc.traffic_channel,
             scc.q_kw,
             scc.q_id_region;
