-- Base: traffic.session_metric (Link: https://gitlab.jooble.com/an/dwh-sql/-/blob/master/traffic/tables/session_metric.sql)
create table traffic.session_paid_search as
with search_data as (
    select sts.country_id,
           sts.session_datediff,
           sts.session_id,
           ss.id                                                                       as search_id,
           ss.date                                                                     as search_date,
           ss.q_kw                                                                     as keyword_name,
           ss.q_id_region                                                              as region_id,
           ss.q_txt_region                                                             as region_original_name,
           min(ss.date) over (partition by ss.country, ss.id_session)                  as min_search_date,
           rank() over (partition by ss.country, ss.id_session order by ss.date,ss.id) as rank
    from traffic.session_metric sts
             left join imp.session_search ss
                       on ss.country = sts.country_id
                           and ss.date_diff = sts.session_datediff
                           and ss.id_session = sts.session_id
    where country_id = 1
      and session_traffic_source_group_id = 2 /*paid traffic group*/
      and sts.session_datediff >= 44407 /*2021-08-01 Start date of new monetization*/
)

select country_id,
       session_datediff,
       session_id,
       min(search_id) as search_id,
       search_date,
       keyword_name,
       region_id,
       region_original_name
from search_data sd
         left join dimension.info_region ir
                   on ir.country = sd.country_id
                       and ir.id = sd.region_id
where search_date = min_search_date
  and rank = 1
group by country_id,
         session_datediff,
         session_id,
         search_date,
         keyword_name,
         region_id,
         region_original_name;


-- add primary key for table
alter table traffic.session_paid_search
    add constraint session_paid_search_pk
        primary key (country_id,
                     session_datediff,
                     session_id);
