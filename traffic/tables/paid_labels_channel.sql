SELECT ad.campaign,
        lower("left"(ad.campaign::text, 2))				    AS country_cc,
        'Adwords'                                           AS data_type,
        CASE WHEN ad.campaign::text ~~* '%_dsa%'::text THEN 'DSA'::character varying
			 WHEN ad.campaign::text ~~* '%_pmax_%'::text THEN 'Max_eff'::character varying
			 WHEN ad.campaign::text ~~* '%_srch_%'::text OR ad.campaign::text ~~* '%_search_%'::text
			 THEN 'Search'::character varying ELSE 'other'::text end     AS source,
        lower(CASE WHEN position('[' in ad.campaign::text) > 0 AND position(']' in ad.campaign::text) > 0
			 THEN regexp_replace(ad.campaign::text, '.*\[(.*?)\].*', '\1') else 'other' end)	AS label,
        ''::text									                                            AS is_test,
        CASE WHEN lower(ad.campaign::text) ~~* '%_index%'::text THEN 'index'::character varying
			 WHEN lower(ad.campaign::text) ~~* '%_feeds%'::text THEN 'feed'::character varying
             WHEN lower(ad.campaign::text) ~~* '%_categories%'::text THEN 'categories'::character varying
			 WHEN lower(ad.campaign::text) ~~* '%_cc]%'::text OR lower(ad.campaign::text) ~~* '%_conv]%'::text THEN 'conversions'::character varying
             ELSE null::text END                                                                AS comment
FROM imp_statistic.adwords ad
LEFT JOIN traffic.paid_labels_channel labels ON ad.campaign = labels.campaign
WHERE ad.day >= (current_date - 7)
        AND labels.campaign is null
GROUP BY ad.campaign,
        lower("left"(ad.campaign::text, 2)),
        CASE WHEN ad.campaign::text ~~* '%_dsa%'::text THEN 'DSA'::character varying
			 WHEN ad.campaign::text ~~* '%_pmax_%'::text THEN 'Max_eff'::character varying
			 WHEN ad.campaign::text ~~* '%_srch_%'::text OR ad.campaign::text ~~* '%_search_%'::text
			 THEN 'Search'::character varying ELSE 'other'::text end,
        lower(CASE WHEN position('[' in ad.campaign::text) > 0 AND position(']' in ad.campaign::text) > 0
			 THEN regexp_replace(ad.campaign::text, '.*\[(.*?)\].*', '\1') else 'other' end),
        CASE WHEN lower(ad.campaign::text) ~~* '%_index%'::text THEN 'index'::character varying
			 WHEN lower(ad.campaign::text) ~~* '%_feeds%'::text THEN 'feed'::character varying
             WHEN lower(ad.campaign::text) ~~* '%_categories%'::text THEN 'categories'::character varying
			 WHEN lower(ad.campaign::text) ~~* '%_cc]%'::text OR lower(ad.campaign::text) ~~* '%_conv]%'::text THEN 'conversions'::character varying
             ELSE null::text END
;
