SET NOCOUNT ON;


DECLARE @date_from DATE = :to_sql_previous_month_start;
DECLARE @date_to DATE = :to_sql_previous_month_end;
DECLARE @country VARCHAR(2) = :to_sql_country_code;
DECLARE @display_coef_table BIT = 0;

EXEC('
    /* Declare tmp tables */
    DECLARE @coeff_table TABLE (
        position INT NOT NULL PRIMARY KEY,
        ctr FLOAT NOT NULL,
        coef DECIMAL(14,10) NULL
    );

    DECLARE @big_query_data_table TABLE (
        url_h64 BIGINT NOT NULL PRIMARY KEY,
        url NVARCHAR(MAX) NOT NULL,
        kw_h64 BIGINT NOT NULL,
        reg_id INT NULL,
        impressions INT NOT NULL,
        clicks INT NOT NULL,
        sum_position INT NOT NULL,
        avg_position INT NOT NULL,
        ctr DECIMAL(14,10) NOT NULL,
        INDEX IX NONCLUSTERED (kw_h64, reg_id)
    );

    /* Step1. Fill @big_query_data_table */
    INSERT INTO @big_query_data_table
    SELECT 
        u.url_h64,
        u.url,
        ISNULL(u.kw_h64, 0),
        ISNULL(u.rgn_id, -1),
        SUM(stat.impressions),
        SUM(stat.clicks),
        SUM(stat.sum_position),
        ROUND(SUM(stat.sum_position) / SUM(impressions), 0),
        CAST((SUM(stat.clicks) * 1.0) / (SUM(stat.impressions) * 1.0) AS DECIMAL(14,10))
    FROM BigQuery.dbo.' + @country + '_stat stat WITH(NOLOCK)
    INNER JOIN BigQuery.dbo.' + @country + '_url u WITH(NOLOCK) 
        ON stat.url_h64 = u.url_h64
    WHERE stat.data_date BETWEEN ''' + @date_from + ''' AND ''' + @date_to + '''
      AND u.type_id IN (1, 2, 12)
    GROUP BY u.url_h64, u.url, u.kw_h64, u.rgn_id;

    /* Step3. Fill & calc @coeff_table */
    INSERT INTO @coeff_table (position, ctr)
    SELECT avg_position, AVG(ctr)
    FROM @big_query_data_table
    GROUP BY avg_position;

    UPDATE @coeff_table
    SET coef = 1
    WHERE position = 0;

    UPDATE main
    SET main.coef = top_1.ctr / main.ctr
    FROM @coeff_table main
    INNER JOIN @coeff_table top_1 ON top_1.position = 1
    WHERE main.position BETWEEN 1 AND 10;

    UPDATE main
    SET main.coef = top_10.coef
    FROM @coeff_table main
    INNER JOIN @coeff_table top_10 ON top_10.position = 10
    WHERE main.position > 10;

    /* Step4. Display results */
    SELECT ''' + @date_from + '<-->' + @date_to + ''' AS period, 
           bq_tab.url, 
           bq_tab.kw_h64, 
           q.ss AS kw, 
           bq_tab.reg_id, 
           bq_tab.impressions, 
           bq_tab.clicks, 
           bq_tab.sum_position, 
           bq_tab.avg_position, 
           bq_tab.ctr, 
           bq_tab.impressions * bq_tab.ctr * c_tab.coef AS potential_traffic
    FROM @big_query_data_table bq_tab
    LEFT JOIN @coeff_table c_tab ON (c_tab.position = bq_tab.avg_position)
    LEFT JOIN SeoQueries.dbo.' + @country + '_seo_query q WITH(NOLOCK) 
        ON q.ss_hash64 = bq_tab.kw_h64
    ORDER BY potential_traffic DESC;

    IF (' + @display_coef_table + ' = 1)
        SELECT * FROM @coeff_table;
');
