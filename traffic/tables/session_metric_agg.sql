select sm.country_id,
       sm.session_datediff,
       sm.session_traffic_source_id,
       sm.session_traffic_source_name,
       sm.session_current_traffic_source_id,
       sm.session_current_traffic_source_name,
       sm.is_returned,
       sm.is_mobile,
       sm.is_session_registration,
       sm.is_session_with_profile_creation,
       coalesce(count(distinct sm.session_id), 0)       as session_cnt,
       coalesce(sum(apply.apply_cnt), 0)                as apply_cnt,
       coalesce(sum(call.click_call_cnt), 0)            as click_call_cnt,
       coalesce(sum(pc.profile_created_cnt), 0)         as profile_created_cnt,
       coalesce(sum(search.session_search_cnt), 0)      as search_cnt,
       coalesce(sum(click.session_action_click_cnt), 0) as session_action_click_cnt,
       coalesce(sum(jdp_dte.jdp_view_cnt), 0)           as dte_jdp_view_cnt,
       coalesce(sum(jdp_agg.jdp_view_cnt), 0)           as agg_jdp_view_cnt
from traffic.session_metric sm
         left join traffic.session_with_apply apply
                   on apply.country_id = sm.country_id and apply.session_datediff = sm.session_datediff and
                      apply.session_id = sm.session_id
         left join traffic.session_with_click_call call
                   on call.country_id = sm.country_id and call.session_datediff = sm.session_datediff and
                      call.session_id = sm.session_id
         left join traffic.session_with_profile_creation pc
                   on pc.country_id = sm.country_id and pc.session_datediff = sm.session_datediff and
                      pc.session_id = sm.session_id
         left join traffic.session_search_agg search
                   on search.country_id = sm.country_id and search.search_datediff = sm.session_datediff
                       and search.session_id = sm.session_id
         left join traffic.session_action_agg click
                   on click.country_id = sm.country_id and click.action_datediff = sm.session_datediff
                       and click.session_id = sm.session_id
         left join traffic.session_jdp_agg jdp_dte
                   on jdp_dte.country_id = sm.country_id
                       and jdp_dte.jdp_view_datediff = sm.session_datediff
                       and jdp_dte.session_id = sm.session_id
                       and jdp_dte.is_dte_vacancy = 1
         left join traffic.session_jdp_agg jdp_agg
                   on jdp_agg.country_id = sm.country_id
                       and jdp_agg.jdp_view_datediff = sm.session_datediff
                       and jdp_agg.session_id = sm.session_id
                       and jdp_agg.is_dte_vacancy = 0
where sm.session_datediff >= 44407
group by sm.country_id,
         sm.session_datediff,
         sm.session_traffic_source_id,
         sm.session_traffic_source_name,
         sm.session_current_traffic_source_id,
         sm.session_current_traffic_source_name,
         sm.is_returned,
         sm.is_mobile,
         sm.is_session_registration,
         sm.is_session_with_profile_creation;
