select Date      as date,
       Country   as country,
       Type      as type,
       Device    as device,
       Label     as label,
       sum(EA)   as ea,
       sum(EA_1) as ea_1
from (select d                             Date,
             rev.country                   Country,
             isnull(type, 'other')         Type,
             isnull(device, 'not_defined') Device,
             isnull(label, 'other')        Label,
             sum(value)                    EA,
             NULL as                       EA_1
      from PublicStatistic.dbo.adv_revenue_ea_new rev
               left join [Marketing].[legent].[labels_and_channels] ll
                         on rev.country = ll.country collate SQL_Latin1_General_CP1_CI_AS and
                            rev.src = ll.name collate SQL_Latin1_General_CP1_CI_AS
      where year(d) >= 2023
        and is_return = 0
        and src like '%cpc%' --and rev.country not like 'ua'
      group by d, rev.country, type, device, label
      having sum(value) > 0
      UNION
      select d                             Date,
             rev.country                   Country,
             isnull(type, 'other')         Type,
             isnull(device, 'not_defined') Device,
             isnull(label, 'other')        Label,
             null       as                 EA,
             sum(value) as                 EA_1
      from PublicStatistic.dbo.adv_revenue_ea_new rev
               left join [Marketing].[legent].[labels_and_channels] ll
                         on rev.country = ll.country collate SQL_Latin1_General_CP1_CI_AS and
                            rev.src = ll.name collate SQL_Latin1_General_CP1_CI_AS
      where year(d) >= 2023
        and is_return = 1
        and src like '%cpc%' --and rev.country not like 'ua'
      group by d, rev.country, type, device, label
      having sum(value) > 0) t
group by Date, Country, Type, Device, Label;
