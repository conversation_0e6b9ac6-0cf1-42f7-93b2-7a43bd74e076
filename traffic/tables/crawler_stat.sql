SET NOCOUNT ON;

SELECT '{to_sqlcode_country_code}'  AS country_code,
       cs.data_date                 AS date,
       cs.url_h64,
       sq.ss,
       sq.ss_hash64,
       r.id_region,
       r.display_name,
       cs.req_total,
       cs.req_success,
       u.url
FROM BigQuery.dbo.{to_sqlcode_tbl_url} u WITH (NOLOCK)
         INNER JOIN BigQuery.dbo.{to_sqlcode_tbl_crawler_stat} cs WITH (NOLOCK) ON cs.url_h64 = u.url_h64
         LEFT JOIN SeoQueries.dbo.{to_sqlcode_tbl_seo_query} sq WITH (NOLOCK) ON u.kw_h64 = sq.ss_hash64
         LEFT JOIN SeoQueries.dbo.{to_sqlcode_tbl_region} r WITH (NOLOCK) ON r.id_region = u.rgn_id AND r.id_lang = 0
WHERE cs.data_date BETWEEN '{to_sqlcode_reload_date_start}' AND '{to_sqlcode_reload_date_end}'
;
