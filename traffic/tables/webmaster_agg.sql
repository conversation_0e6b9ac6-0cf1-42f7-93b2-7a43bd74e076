CREATE OR REPLACE PROCEDURE traffic.prc_webmaster_agg(IN _date_start date)
    LANGUAGE plpgsql
AS
$$

BEGIN

    -- reload data in the table
    DELETE
    FROM traffic.webmaster_agg
    WHERE date >= _date_start;
    --

    INSERT INTO traffic.webmaster_agg(date, year, country, country_cc, is_jdp, impressions, clicks)
    SELECT ws.date::date       AS date,
           ic.dt_year          AS year,
           c.name_country_eng  AS country,
           LOWER(c.alpha_2)    AS country_cc,
           CASE
               WHEN SUBSTRING(ws.url::text, '%#"jdp#"%'::text, '#'::text) IS NOT NULL THEN 'JDP'::text
               ELSE
                   CASE
                       WHEN SUBSTRING(ws.url::text, '%#"jooble.org/salary/#"%'::text, '#'::text) IS NOT NULL
                           THEN 'Salary'::text
                       ELSE
                           CASE
                               WHEN SUBSTRING(ws.url::text, '%#"jooble.org/tax-calculator#"%'::text,
                                              '#'::text) IS NOT NULL THEN 'Tax Calculator'::text
                               ELSE 'Other'::text
                           END
                   END
           END                 AS is_jdp,
           SUM(ws.impressions) AS impressions,
           SUM(ws.clicks)      AS clicks
    FROM traffic.webmaster_statistic ws
             JOIN dimension.countries c
                  ON ws.country_id = c.id
             LEFT JOIN dimension.info_calendar ic
                       ON ic.dt::date = ws.date::date
    WHERE ws.date >= _date_start
    GROUP BY ws.date::date, ic.dt_year, c.name_country_eng, LOWER(c.alpha_2),
             CASE
                 WHEN SUBSTRING(ws.url::text, '%#"jdp#"%'::text, '#'::text) IS NOT NULL THEN 'JDP'::text
                 ELSE
                     CASE
                         WHEN SUBSTRING(ws.url::text, '%#"jooble.org/salary/#"%'::text, '#'::text) IS NOT NULL
                             THEN 'Salary'::text
                         ELSE
                             CASE
                                 WHEN SUBSTRING(ws.url::text, '%#"jooble.org/tax-calculator#"%'::text,
                                                '#'::text) IS NOT NULL THEN 'Tax Calculator'::text
                                 ELSE 'Other'::text
                             END
                     END
             END;
END;
$$;
