select sj.country as country_id,
        sj.date_diff as session_datediff,
        sj.id_session as session_id,
        count(distinct sa.id) as apply_cnt
from imp.session_apply sa
join imp.session_jdp_action sja on sja.country = sa.country and sja.date_diff = sa.date_diff and sja.id = sa.id_src_jdp_action
join imp.session_jdp sj on sj.country = sja.country and sj.date_diff = sja.date_diff and sj.id = sja.id_jdp and sj.job_id_project = -1
where sa.country = 1 and sa.date_diff = _datediff
group by country_id,
            sj.date_diff,
            sj.id_session;
