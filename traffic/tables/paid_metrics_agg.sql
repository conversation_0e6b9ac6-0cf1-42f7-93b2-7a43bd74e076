    /*create temp table conversion_away_connection_tmp AS
    SELECT sa.id_project,
            min(cac.date_diff) AS conversion_start
    FROM link_auction.conversion_away_connection cac
    JOIN session_away sa ON cac.date_diff = sa.date_diff AND cac.id_session_away = sa.id
    WHERE sa.date_diff = (an.fn_get_date_diff(current_date) - _day_cnt)--(an.fn_get_date_diff(current_date) - 1)
    GROUP BY sa.id_project;*/

    CREATE TEMP TABLE temp_u_traffic_source AS
    SELECT id, is_paid
    FROM link_dbo.u_traffic_source;
    CREATE INDEX temp_u_traffic_source_id_idx ON temp_u_traffic_source (id);

    CREATE TEMP TABLE temp_info_project AS
    SELECT id, name, hide_in_search
    FROM link_dbo.info_project;
    CREATE INDEX temp_info_project_id_idx ON temp_info_project (id);

    CREATE TEMP TABLE temp_campaign AS
    SELECT id, name, id_site
    FROM link_auction.campaign;
    CREATE INDEX temp_campaign_id_idx ON temp_campaign (id);

    CREATE TEMP TABLE temp_site AS
    SELECT id, id_user
    FROM link_auction.site;
    CREATE INDEX temp_site_id_idx ON temp_site (id);

    CREATE TEMP TABLE temp_user AS
    SELECT id, flags
    FROM link_auction."user";
    CREATE INDEX temp_user_id_idx ON temp_user (id);

    CREATE TEMP TABLE temp_dis_conversion_away_connection AS
    SELECT DISTINCT id_session_away
    FROM link_auction.conversion_away_connection;
    CREATE INDEX temp_dis_conversion_away_connection_id_session_away_idx ON temp_dis_conversion_away_connection (id_session_away);


    ANALYSE temp_u_traffic_source;
    ANALYSE temp_info_project;
    ANALYSE temp_campaign;
    ANALYSE temp_site;
    ANALYSE temp_user;
    ANALYSE temp_dis_conversion_away_connection;


    CREATE TEMP TABLE tmp_revenue AS
    SELECT sa.date_diff,
           s.id_traf_source,
           s.id_current_traf_source,
           s.id                                        AS id_session,
           NULL::bigint                                AS id_away,
           sa.id                                       AS id_click,
           CASE
               WHEN sa.id_jdp IS NOT NULL THEN 'Away from JDP'::text
               WHEN sa.id_click IS NOT NULL THEN 'Away from SERP'::text
               WHEN COALESCE(sa.letter_type, sj.letter_type) IS NOT NULL THEN 'Away from LT8'::text
               ELSE 'Other'::text
           END                                         AS away_type,
           CASE
               WHEN sa.letter_type IS NOT NULL THEN CONCAT('Letter Type ', sa.letter_type)
               WHEN sa.id_click IS NOT NULL THEN 'Click'::text
               WHEN sa.id_jdp IS NOT NULL THEN 'Jdp'::text
               WHEN sa.id_click_no_serp IS NOT NULL THEN 'No serp'::text
               ELSE 'Other'::text
           END                                         AS click_type,
           sa.id_project,
           ac.name                                     AS campaign_name,
           ac.id                                       AS id_campaign,
           sa.click_price * ic.value_to_usd            AS click_price_usd,
           COALESCE(sa.letter_type, sj.letter_type)    AS letter_type,
           COALESCE(sc.id_recommend, scj.id_recommend) AS id_recommend,
           COALESCE(sc.id_alertview, scj.id_alertview) AS id_alertview,
           COALESCE(sc.id_search, scj.id_search)       AS id_search,
           ext.id                                      AS id_external,
           CASE
               WHEN sa.click_price <> 0::numeric THEN 'paid'::text
               WHEN (sc.flags & 128) = 128 OR (sj.flags & 256) = 256 THEN 'premium'::text
               ELSE 'free'::text
           END                                         AS is_paid,
           SIGN((s.flags & 16)::double precision)      AS is_mobile,
           s.ip_cc,
           SIGN((s.flags & 2)::double precision)       AS is_returned,
           s.session_create_page_type,
           CASE
               WHEN uts.is_paid = 1 AND (((SELECT cl.flags
                                           FROM an.snap_campaign_log cl
                                           WHERE cl.date <= sa.date
                                             AND cl.id_campaign = ac.id
                                           ORDER BY cl.date DESC
                                           LIMIT 1)) & 32) = 32 THEN 1
               ELSE 0
           END                                         AS is_paid_overflow,
           CASE
               WHEN s.ip_cc::text = CURRENT_DATABASE() OR s.ip_cc::text = 'gb'::text AND CURRENT_DATABASE() = 'uk'::name
                   THEN 1
               ELSE 0
           END                                         AS is_local,
           iif((sa.flags & 512) <> 0, 1, 0)            AS is_duplicated,
           au.id                                       AS user_id,
           CASE
               WHEN (sa.flags & 2048) = 2048 OR (COALESCE(ss.search_source, ssj.search_source) = ANY
                                                 (ARRAY [118, 119, 134, 136, 137, 138, 139, 140, 141, 142, 143, 144]))
                   THEN 1
               WHEN (sa.flags & 4096) = 4096 OR COALESCE(ss.search_source, ssj.search_source) = 145 THEN 2
               WHEN COALESCE(ss.search_source, ssj.search_source) = ANY (ARRAY [146, 147, 148, 149, 150]) THEN 3
               WHEN COALESCE(ss.search_source, ssj.search_source) = ANY (ARRAY [151, 152, 153, 154]) THEN 4
               WHEN COALESCE(ss.search_source, ssj.search_source) = ANY (ARRAY [155, 156, 157, 158]) THEN 5
               ELSE NULL::integer
           END                                         AS add_placement,
           s.flags,
           NULL::bigint                                AS id_session_away,
           0                                           AS is_apply,
           COALESCE(jh.id_category, j.id_category)     AS id_job_category
    FROM public.session_away sa
             JOIN public.session s
                  ON sa.date_diff = s.date_diff AND sa.id_session = s.id
             JOIN an.snap_info_currency ic
                  ON ic.id = sa.id_currency
             LEFT JOIN temp_campaign ac
                       ON ac.id = sa.id_campaign
             LEFT JOIN temp_site ast
                       ON ac.id_site = ast.id
             LEFT JOIN temp_user au
                       ON au.id = ast.id_user
             LEFT JOIN temp_u_traffic_source uts
                       ON s.id_traf_source = uts.id
             LEFT JOIN public.session_click sc
                       ON sc.date_diff = sa.date_diff AND sc.id = sa.id_click
             LEFT JOIN public.session_jdp sj
                       ON sj.date_diff = sa.date_diff AND sj.id = sa.id_jdp
             LEFT JOIN public.session_click scj
                       ON scj.date_diff = sj.date_diff AND scj.id = sj.id_click
             LEFT JOIN public.session_external ext
                       ON ext.date_diff = sa.date_diff AND ext.id_away = sa.id
             LEFT JOIN public.session_search ss
                       ON ss.date_diff = sc.date_diff AND ss.id = sc.id_search
             LEFT JOIN public.session_search ssj
                       ON ssj.date_diff = scj.date_diff AND ssj.id = scj.id_search
             LEFT JOIN an.snap_job j
                       ON sa.id_job = j.id
             LEFT JOIN an.snap_job_history jh
                       ON sa.uid_job = jh.uid
    WHERE sa.date_diff = datediff
      AND (COALESCE(s.flags, 0) & 1) = 0
      AND (sa.id_campaign = 0 OR (au.flags & 2) = 0)
      AND (COALESCE(sa.flags, 0) & 2) = 0;


    INSERT INTO tmp_revenue
    SELECT sc.date_diff,
           s.id_traf_source,
           s.id_current_traf_source,
           s.id                                               AS id_session,
           NULL::bigint                                       AS id_away,
           sc.id                                              AS id_click,
           CASE
               WHEN sa.id_jdp IS NOT NULL THEN 'Away from JDP'::text
               WHEN sa.id_click IS NOT NULL THEN 'Away from SERP'::text
               WHEN COALESCE(sa.letter_type, sj.letter_type) IS NOT NULL THEN 'Away from LT8'::text
               ELSE 'Other'::text
           END                                                AS away_type,
           CASE
               WHEN COALESCE(sa.letter_type, sj.letter_type) IS NOT NULL
                   THEN CONCAT('Letter Type ', COALESCE(sa.letter_type, sj.letter_type))
               WHEN sa.id_click IS NOT NULL THEN 'Click'::text
               WHEN COALESCE(sj.id, sa.id_jdp) IS NOT NULL THEN 'Jdp'::text
               WHEN COALESCE(sa.id_click_no_serp, sj.id_click_no_serp) IS NOT NULL THEN 'No serp'::text
               ELSE 'Other'::text
           END                                                AS click_type,
           sc.id_project,
           ac.name                                            AS campaign_name,
           ac.id                                              AS id_campaign,
           sc.click_price * ic.value_to_usd                   AS click_price_usd,
           COALESCE(sa.letter_type, sj.letter_type)           AS letter_type,
           sc.id_recommend,
           sc.id_alertview,
           sc.id_search,
           ext.id                                             AS id_external,
           CASE
               WHEN sc.click_price <> 0::numeric THEN 'paid'::text
               WHEN (sc.flags & 128) = 128 OR (sj.flags & 256) = 256 THEN 'premium'::text
               ELSE 'free'::text
           END                                                AS is_paid,
           SIGN((s.flags & 16)::double precision)             AS is_mobile,
           s.ip_cc,
           SIGN((s.flags & 2)::double precision)              AS is_returned,
           s.session_create_page_type,
           CASE
               WHEN uts.is_paid = 1 AND (((SELECT cl.flags
                                           FROM an.snap_campaign_log cl
                                           WHERE cl.date <= sa.date
                                             AND cl.id_campaign = ac.id
                                           ORDER BY cl.date DESC
                                           LIMIT 1)) & 32) = 32 THEN 1
               ELSE 0
           END                                                AS is_paid_overflow,
           CASE
               WHEN s.ip_cc::text = CURRENT_DATABASE() OR s.ip_cc::text = 'gb'::text AND CURRENT_DATABASE() = 'uk'::name
                   THEN 1
               ELSE 0
           END                                                AS is_local,
           iif((sc.flags & 4096) <> 0, 1, 0)                  AS is_duplicated,
           au.id                                              AS user_id,
           CASE
               WHEN (sa.flags & 2048) = 2048 OR sj.source = 9 OR
                    (ss.search_source = ANY (ARRAY [118, 119, 134, 136, 137, 138, 139, 140, 141, 142, 143, 144]))
                   THEN 1
               WHEN (sa.flags & 4096) = 4096 OR ss.search_source = 145 THEN 2
               WHEN ss.search_source = ANY (ARRAY [146, 147, 148, 149, 150]) THEN 3
               WHEN ss.search_source = ANY (ARRAY [151, 152, 153, 154]) THEN 4
               WHEN ss.search_source = ANY (ARRAY [155, 156, 157, 158]) THEN 5
               ELSE NULL::integer
           END                                                AS add_placement,
           s.flags,
           NULL::bigint                                       AS id_session_away,
           CASE WHEN sc.job_destination = 3 THEN 1 ELSE 0 END AS is_apply,
           COALESCE(jh.id_category, j.id_category)            AS id_job_category
    FROM public.session_click sc
             JOIN public.session s
                  ON sc.date_diff = s.date_diff AND sc.id_session = s.id
             JOIN an.snap_info_currency ic
                  ON ic.id = sc.id_currency
             LEFT JOIN temp_campaign ac
                       ON ac.id = sc.id_campaign
             LEFT JOIN temp_site ast
                       ON ac.id_site = ast.id
             LEFT JOIN temp_user au
                       ON au.id = ast.id_user
             LEFT JOIN temp_u_traffic_source uts
                       ON s.id_traf_source = uts.id
             LEFT JOIN public.session_away sa
                       ON sc.date_diff = sa.date_diff AND sc.id = sa.id_click
             LEFT JOIN public.session_jdp sj
                       ON sj.date_diff = sa.date_diff AND sj.id = sa.id_jdp
             LEFT JOIN public.session_external ext
                       ON ext.date_diff = sc.date_diff AND (ext.id_away = sa.id OR ext.id_jdp = sj.id)
             LEFT JOIN public.session_search ss
                       ON ss.date_diff = sc.date_diff AND ss.id = sc.id_search
             LEFT JOIN an.snap_job j
                       ON sc.id_job = j.id
             LEFT JOIN an.snap_job_history jh
                       ON sc.uid_job = jh.uid
    WHERE sc.date_diff = datediff
      AND (COALESCE(s.flags, 0) & 1) = 0
      AND (sc.id_campaign = 0 OR (au.flags & 2) = 2)
      AND (COALESCE(sc.flags, 0) & 16) = 0;


    INSERT INTO tmp_revenue
    SELECT scns.date_diff,
           s.id_traf_source,
           s.id_current_traf_source,
           s.id                                                 AS id_session,
           NULL::bigint                                         AS id_away,
           scns.id                                              AS id_click,
           CASE
               WHEN sa.id_jdp IS NOT NULL THEN 'Away from JDP'::text
               WHEN sa.id_click IS NOT NULL THEN 'Away from SERP'::text
               WHEN sa.letter_type IS NOT NULL THEN 'Away from LT8'::text
               ELSE 'Other'::text
           END                                                  AS away_type,
           CASE
               WHEN scns.letter_type IS NOT NULL THEN CONCAT('Letter Type ', scns.letter_type)
               ELSE 'No serp'::text
           END                                                  AS click_type,
           scns.id_project,
           ac.name                                              AS campaign_name,
           ac.id                                                AS id_campaign,
           scns.click_price * ic.value_to_usd                   AS click_price_usd,
           scns.letter_type,
           scns.id_recommend,
           NULL::bigint                                         AS id_alertview,
           NULL::bigint                                         AS id_search,
           ext.id                                               AS id_external,
           CASE
               WHEN scns.click_price <> 0::numeric THEN 'paid'::text
               WHEN (scns.flags & 128) = 128 THEN 'premium'::text
               ELSE 'free'::text
           END                                                  AS is_paid,
           SIGN((s.flags & 16)::double precision)               AS is_mobile,
           s.ip_cc,
           SIGN((s.flags & 2)::double precision)                AS is_returned,
           s.session_create_page_type,
           CASE
               WHEN uts.is_paid = 1 AND (((SELECT cl.flags
                                           FROM an.snap_campaign_log cl
                                           WHERE cl.date <= sa.date
                                             AND cl.id_campaign = ac.id
                                           ORDER BY cl.date DESC
                                           LIMIT 1)) & 32) = 32 THEN 1
               ELSE 0
           END                                                  AS is_paid_overflow,
           CASE
               WHEN s.ip_cc::text = CURRENT_DATABASE() OR s.ip_cc::text = 'gb'::text AND CURRENT_DATABASE() = 'uk'::name
                   THEN 1
               ELSE 0
           END                                                  AS is_local,
           iif((scns.flags & 4096) <> 0, 1, 0)                  AS is_duplicated,
           au.id                                                AS user_id,
           NULL::integer                                        AS add_placement,
           s.flags,
           NULL::bigint                                         AS id_session_away,
           CASE WHEN scns.job_destination = 3 THEN 1 ELSE 0 END AS is_apply,
           COALESCE(jh.id_category, j.id_category)              AS id_job_category
    FROM public.session_click_no_serp scns
             JOIN public.session s
                  ON scns.date_diff = s.date_diff AND scns.id_session = s.id
             JOIN an.snap_info_currency ic
                  ON ic.id = scns.id_currency
             JOIN temp_campaign ac
                  ON ac.id = scns.id_campaign
             JOIN temp_site ast
                  ON ac.id_site = ast.id
             JOIN temp_user au
                  ON au.id = ast.id_user
             LEFT JOIN temp_u_traffic_source uts
                       ON s.id_traf_source = uts.id
             LEFT JOIN public.session_jdp sj
                       ON sj.id_click_no_serp = scns.id AND sj.date_diff = scns.date_diff
             LEFT JOIN public.session_away sa
                       ON sa.id_click_no_serp = scns.id AND sa.date_diff = scns.date_diff
             LEFT JOIN public.session_external ext
                       ON ext.id_jdp = sj.id OR ext.id_away = sa.id
             LEFT JOIN an.snap_job j
                       ON scns.id_job = j.id
             LEFT JOIN an.snap_job_history jh
                       ON scns.uid_job = jh.uid
    WHERE scns.date_diff = datediff
      AND (COALESCE(s.flags, 0) & 1) = 0
      AND (au.flags & 2) = 2
      AND (COALESCE(scns.flags, 0) & 16) = 0;


    CREATE TEMP TABLE away_revenue_union AS
    SELECT sa.date_diff,
           s.id_traf_source,
           s.id_current_traf_source,
           sa.id_session,
           sa.id                                       AS id_away,
           'aways'                                     AS metric,
           NULL                                        AS id_click,
           CASE
               WHEN sa.id_jdp IS NOT NULL THEN 'Away from JDP'::text
               WHEN sa.id_click IS NOT NULL THEN 'Away from SERP'::text
               WHEN COALESCE(sa.letter_type, sj.letter_type) IS NOT NULL THEN 'Away from LT8'::text
               ELSE 'Other'::text
           END                                         AS away_type,
           CASE
               WHEN sa.letter_type IS NOT NULL THEN CONCAT('Letter Type ', sa.letter_type)
               WHEN sa.id_click IS NOT NULL THEN 'Click'::text
               WHEN sa.id_jdp IS NOT NULL THEN 'Jdp'::text
               WHEN sa.id_click_no_serp IS NOT NULL THEN 'No serp'::text
               ELSE 'Other'::text
           END                                         AS click_type,
           sa.id_project,
           ac.name                                     AS campaign_name,
           ac.id                                       AS id_campaign,
           0::numeric                                  AS click_price_usd,
           COALESCE(sa.letter_type, sj.letter_type)    AS letter_type,
           COALESCE(sc.id_recommend, scj.id_recommend) AS id_recommend,
           COALESCE(sc.id_alertview, scj.id_alertview) AS id_alertview,
           COALESCE(sc.id_search, scj.id_search)       AS id_search,
           ext.id                                      AS id_external,
           CASE
               WHEN sa.click_price <> 0::numeric THEN 'paid'::text
               WHEN (sc.flags & 128) = 128 OR (sj.flags & 256) = 256 THEN 'premium'::text
               ELSE 'free'::text
           END                                         AS is_paid,
           SIGN((s.flags & 16)::double precision)      AS is_mobile,
           s.ip_cc,
           SIGN((s.flags & 2)::double precision)       AS is_returned,
           s.session_create_page_type,
           CASE
               WHEN uts.is_paid = 1 AND (((SELECT cl.flags
                                           FROM an.snap_campaign_log cl
                                           WHERE cl.date <= sa.date
                                             AND cl.id_campaign = ac.id
                                           ORDER BY cl.date DESC
                                           LIMIT 1)) & 32) = 32 THEN 1
               ELSE 0
           END                                         AS is_paid_overflow,
           CASE
               WHEN s.ip_cc::text = CURRENT_DATABASE() OR s.ip_cc::text = 'gb'::text AND CURRENT_DATABASE() = 'uk'::name
                   THEN 1
               ELSE 0
           END                                         AS is_local,
           iif((sa.flags & 512) <> 0, 1, 0)            AS is_duplicated,
           au.id                                       AS user_id,
           CASE
               WHEN (sa.flags & 2048) = 2048 OR (COALESCE(ss.search_source, ssj.search_source) = ANY
                                                 (ARRAY [118, 119, 134, 136, 137, 138, 139, 140, 141, 142, 143, 144]))
                   THEN 1
               WHEN (sa.flags & 4096) = 4096 OR COALESCE(ss.search_source, ssj.search_source) = 145 THEN 2
               WHEN COALESCE(ss.search_source, ssj.search_source) = ANY (ARRAY [146, 147, 148, 149, 150]) THEN 3
               WHEN COALESCE(ss.search_source, ssj.search_source) = ANY (ARRAY [151, 152, 153, 154]) THEN 4
               WHEN COALESCE(ss.search_source, ssj.search_source) = ANY (ARRAY [155, 156, 157, 158]) THEN 5
               ELSE NULL::integer
           END                                         AS add_placement,
           s.flags,
           away_con.id_session_away,
           COALESCE(jh.id_category, j.id_category)     AS id_job_category
    FROM public.session_away sa
             JOIN public.session s
                  ON sa.date_diff = s.date_diff AND sa.id_session = s.id
             LEFT JOIN temp_u_traffic_source uts
                       ON s.id_traf_source = uts.id
             LEFT JOIN temp_campaign ac
                       ON ac.id = sa.id_campaign
             LEFT JOIN temp_site ast
                       ON ac.id_site = ast.id
             LEFT JOIN temp_user au
                       ON au.id = ast.id_user
             LEFT JOIN public.session_click sc
                       ON sc.date_diff = sa.date_diff AND sc.id = sa.id_click
             LEFT JOIN public.session_jdp sj
                       ON sj.date_diff = sa.date_diff AND sj.id = sa.id_jdp
             LEFT JOIN public.session_click scj
                       ON scj.date_diff = sj.date_diff AND scj.id = sj.id_click
             LEFT JOIN public.session_external ext
                       ON ext.date_diff = sa.date_diff AND ext.id_away = sa.id
             LEFT JOIN public.session_search ss
                       ON ss.date_diff = sc.date_diff AND ss.id = sc.id_search
             LEFT JOIN public.session_search ssj
                       ON ssj.date_diff = scj.date_diff AND ssj.id = scj.id_search
             LEFT JOIN temp_dis_conversion_away_connection away_con
                       ON away_con.id_session_away = sa.id
             LEFT JOIN an.snap_job j
                       ON sa.id_job = j.id
             LEFT JOIN an.snap_job_history jh
                       ON sa.uid_job = jh.uid
    WHERE sa.date_diff = datediff
      AND (COALESCE(s.flags, 0) & 1) = 0
      AND (COALESCE(sa.flags, 0) & 2) = 0; /* test campaign away */


    INSERT INTO away_revenue_union
    SELECT date_diff,
           id_traf_source,
           id_current_traf_source,
           id_session,
           id_away,
           CASE WHEN is_apply = 0 THEN 'aways' ELSE 'applies' END AS metric,
           id_click,
           away_type,
           click_type,
           id_project,
           campaign_name,
           id_campaign,
           click_price_usd,
           letter_type,
           id_recommend,
           id_alertview,
           id_search,
           id_external,
           is_paid,
           is_mobile,
           ip_cc,
           is_returned,
           session_create_page_type,
           is_paid_overflow,
           is_local,
           is_duplicated,
           user_id,
           add_placement,
           flags,
           id_session_away,
           id_job_category
    FROM tmp_revenue;


    CREATE TEMP TABLE temp_session_jdp_action AS
    SELECT id, id_jdp, date_diff, click_price
    FROM public.session_jdp_action
    WHERE date_diff = datediff;
    CREATE INDEX idx_temp_session_jdp_action_id ON temp_session_jdp_action (id);
    CREATE INDEX idx_temp_session_jdp_action_id_jdp ON temp_session_jdp_action (id_jdp);
    ANALYZE temp_session_jdp_action;


    INSERT INTO away_revenue_union
    SELECT sj.date_diff,
           s.id_traf_source,
           s.id_current_traf_source,
           s.id                                                    AS id_session,
           sj.id                                                   AS id_jdp,
           'applies'                                               AS metric,
           NULL                                                    AS id_click,
           'JDP only'::text                                        AS away_type,
           CASE
               WHEN sj.letter_type IS NOT NULL THEN CONCAT('Letter Type ', sj.letter_type)
               WHEN sj.id_click IS NOT NULL THEN 'Click'::text
               WHEN sj.id_click_no_serp IS NOT NULL THEN 'No serp'::text
               ELSE 'Other'::text
           END                                                     AS click_type,
           sj.job_id_project                                       AS id_project,
           ac.name                                                 AS campaign_name,
           ac.id                                                   AS id_campaign,
           0                                                       AS click_price_usd,
           sj.letter_type,
           sc.id_recommend,
           sc.id_alertview,
           sc.id_search,
           ext.id                                                  AS id_external,
           CASE
               WHEN COALESCE(sja.click_price, sc.click_price, scns.click_price) <> 0::numeric THEN 'paid'::text
               WHEN (sc.flags & 128) = 128 OR (sj.flags & 256) = 256 THEN 'premium'::text
               ELSE 'free'::text
           END                                                     AS is_paid,
           SIGN((s.flags & 16)::double precision)                  AS is_mobile,
           s.ip_cc,
           SIGN((s.flags & 2)::double precision)                   AS is_returned,
           s.session_create_page_type,
           CASE
               WHEN uts.is_paid = 1 AND (((SELECT cl.flags
                                           FROM an.snap_campaign_log cl
                                           WHERE cl.date <= sa.date
                                             AND cl.id_campaign = ac.id
                                           ORDER BY cl.date DESC
                                           LIMIT 1)) & 32) = 32 THEN 1
               ELSE 0
           END                                                     AS is_paid_overflow,
           CASE
               WHEN s.ip_cc::text = CURRENT_DATABASE() OR s.ip_cc::text = 'gb'::text AND CURRENT_DATABASE() = 'uk'::name
                   THEN 1
               ELSE 0
           END                                                     AS is_local,
           iif((COALESCE(sc.flags, scns.flags) & 4096) <> 0, 1, 0) AS is_duplicated,
           ast.id_user                                             AS user_id,
           NULL::integer                                           AS add_placement,
           s.flags,
           saa.id                                                  AS id_session_away,
           COALESCE(jh.id_category, j.id_category)                 AS id_job_category
    FROM public.session_jdp sj
             JOIN public.session s
                  ON sj.date_diff = s.date_diff AND sj.id_session = s.id
             LEFT JOIN temp_u_traffic_source uts
                       ON uts.id = s.id_traf_source
             LEFT JOIN public.session_click sc
                       ON sc.date_diff = sj.date_diff AND sc.id = sj.id_click
             LEFT JOIN temp_campaign ac
                       ON ac.id = sc.id_campaign
             LEFT JOIN temp_site ast
                       ON ac.id_site = ast.id
             LEFT JOIN public.session_away sa
                       ON sj.date_diff = sa.date_diff AND sj.id = sa.id_jdp
             LEFT JOIN public.session_click_no_serp scns
                       ON sj.date_diff = scns.date_diff AND sj.id_click_no_serp = scns.id
             LEFT JOIN temp_session_jdp_action /*public.session_jdp_action*/ sja
                       ON sj.date_diff = sja.date_diff AND sj.id_ref_action = sja.id
             LEFT JOIN public.session_external ext
                       ON ext.date_diff = sj.date_diff AND (ext.id_away = sa.id OR ext.id_jdp = sj.id)
             LEFT JOIN temp_session_jdp_action /*public.session_jdp_action*/ sjaa
                       ON sj.date_diff = sjaa.date_diff AND sj.id = sjaa.id_jdp
             LEFT JOIN public.session_apply saa
                       ON saa.date_diff = sjaa.date_diff AND saa.id_src_jdp_action = sjaa.id
             LEFT JOIN an.snap_job_region jr
                       ON sj.uid_job = jr.uid
             LEFT JOIN an.snap_job j
                       ON scns.id_job = j.id
             LEFT JOIN an.snap_job_history jh
                       ON sj.uid_job = jh.uid
    WHERE sj.date_diff = datediff
      --AND sa.id is null
      AND (COALESCE(s.flags, 0) & 1) = 0
      AND (COALESCE(sc.flags, 0) & 16) = 0;


    CREATE TEMP TABLE tmp_sam AS
    SELECT tsam.id_alertview,
           MIN(tsam.id_message::text) AS id_message
    FROM public.session_alertview_message tsam
    WHERE tsam.date_diff = datediff
    GROUP BY tsam.id_alertview;
    CREATE INDEX tmp_sam_id_alertview_idx ON tmp_sam (id_alertview);
    ANALYZE tmp_sam;

    CREATE TEMP TABLE temp_session_utm AS
    SELECT date_diff, id_session, utm_content
    FROM public.session_utm
    WHERE date_diff = datediff;
    CREATE INDEX idx_temp_session_utm_id_session ON temp_session_utm (id_session);
    ANALYZE temp_session_utm;

    CREATE TEMP TABLE temp_vw_info_project_discount AS
    SELECT *
    FROM link_dbo.vw_info_project_discount;
    ANALYZE temp_vw_info_project_discount;

    ANALYZE away_revenue_union;


    CREATE TEMP TABLE temp_click_revenue_conversion_raw AS
    SELECT s1.date_diff,
           s1.id_traf_source,
           s1.id_current_traf_source,
           s1.id_session,
           s1.id_away,
           s1.metric                                            AS conversion_type,
           s1.away_type,
           s1.click_price_usd,
           s1.click_price_usd * COALESCE((SELECT d.discount
                                          FROM temp_vw_info_project_discount /*link_dbo.vw_info_project_discount*/ d
                                          WHERE d.id_project = s1.id_project
                                            AND ((DATE_PART('year'::text, d.date) -
                                                  DATE_PART('year'::text, '1900-01-01'::date)) * 12::double precision +
                                                 (DATE_PART('month'::text, d.date) -
                                                  DATE_PART('month'::text, '1900-01-01'::date))) <=
                                                (DATE_TRUNC('month'::text, CURRENT_DATE::timestamp WITH TIME ZONE)::date -
                                                 '1900-01-01'::date)::double precision
                                          ORDER BY d.date DESC
                                          LIMIT 1), 0::numeric) AS revenue_usd_discount,
           s1.user_id,
           s1.id_project,
           info_project.name                                    AS project_name,
           s1.campaign_name,
           s1.id_campaign,
           CASE
               WHEN s1.add_placement = 1 THEN 'salary page'::text
               WHEN s1.add_placement = 2 THEN 'category page'::text
               WHEN s1.add_placement = 3 THEN 'company page'::text
               WHEN s1.add_placement = 4 THEN 'skill page'::text
               WHEN s1.add_placement = 5 THEN 'job description page'::text
               WHEN (s1.flags & 64) = 64 OR (s1.flags & 128) = 128 THEN 'mobile app'::text
               WHEN info_project.hide_in_search = 1 THEN 'ad exchange'::text
               WHEN COALESCE(s1.letter_type, tes.letter_type) IS NOT NULL THEN CONCAT('letter type ',
                                                                                      COALESCE(s1.letter_type, tes.letter_type))
               WHEN s1.id_recommend IS NOT NULL THEN 'recommendations'::text
               WHEN s1.id_alertview IS NOT NULL THEN 'other letter types'::text
               WHEN s1.id_search IS NOT NULL THEN 'search'::text
               WHEN s1.id_external IS NOT NULL THEN 'external'::text
               ELSE 'other'::text
           END                                                  AS placement,
           s1.id_click,
           s1.click_type                                        AS click_category,
           s1.session_create_page_type,
           s1.ip_cc,
           s1.is_local,
           s1.is_mobile,
           s1.is_paid,
           s1.is_returned,
           s1.is_paid_overflow,
           s1.is_duplicated,
           MIN(sutm.utm_content)                                AS utm_content,
           s1.id_session_away,
           s1.id_job_category
    FROM away_revenue_union s1
             LEFT JOIN temp_info_project info_project
                       ON s1.id_project = info_project.id
             LEFT JOIN tmp_sam sam
                       ON sam.id_alertview = s1.id_alertview
             LEFT JOIN an.email_sent tes
                       ON tes.id_message::text = sam.id_message
             LEFT JOIN temp_session_utm /*public.session_utm*/ sutm
                       ON sutm.date_diff = s1.date_diff AND sutm.id_session = s1.id_session
    GROUP BY s1.date_diff, s1.id_traf_source, s1.id_current_traf_source, s1.id_session, s1.id_away, s1.metric,
             s1.away_type,
             s1.click_price_usd, s1.user_id, s1.id_project, info_project.name, s1.campaign_name,
             s1.id_campaign,
             (
                 CASE
                     WHEN s1.add_placement = 1 THEN 'salary page'::text
                     WHEN s1.add_placement = 2 THEN 'category page'::text
                     WHEN s1.add_placement = 3 THEN 'company page'::text
                     WHEN s1.add_placement = 4 THEN 'skill page'::text
                     WHEN s1.add_placement = 5 THEN 'job description page'::text
                     WHEN (s1.flags & 64) = 64 OR (s1.flags & 128) = 128 THEN 'mobile app'::text
                     WHEN info_project.hide_in_search = 1 THEN 'ad exchange'::text
                     WHEN COALESCE(s1.letter_type, tes.letter_type) IS NOT NULL THEN CONCAT('letter type ',
                                                                                            COALESCE(s1.letter_type, tes.letter_type))
                     WHEN s1.id_recommend IS NOT NULL THEN 'recommendations'::text
                     WHEN s1.id_alertview IS NOT NULL THEN 'other letter types'::text
                     WHEN s1.id_search IS NOT NULL THEN 'search'::text
                     WHEN s1.id_external IS NOT NULL THEN 'external'::text
                     ELSE 'other'::text
                 END), s1.id_click, s1.click_type, s1.session_create_page_type, s1.ip_cc, s1.is_local, s1.is_mobile,
             s1.is_paid, s1.is_returned, s1.is_paid_overflow, s1.is_duplicated, s1.id_session_away, s1.id_job_category;

--
    TRUNCATE TABLE an.rpl_paid_metrics_agg;
--

    CREATE TEMP TABLE result AS
    SELECT _country_id                                                              AS country_id,
           r.date_diff,
           r.id_traf_source,
           r.id_current_traf_source,
           r.user_id,
           r.id_project,
           r.project_name,
           r.campaign_name,
           r.id_campaign,
           r.placement,
           r.away_type,
           r.click_category,
           r.conversion_type,
           r.session_create_page_type,
           r.ip_cc,
           r.is_local,
           r.is_mobile,
           r.is_paid,
           r.is_returned,
           r.is_paid_overflow,
           r.is_duplicated,
           r.utm_content,
           r.id_job_category,
           SUM(CASE WHEN r.is_duplicated = 0 THEN r.click_price_usd ELSE 0 END)     AS revenue_usd,
           SUM(CASE WHEN r.is_duplicated = 1 THEN r.click_price_usd ELSE 0 END)     AS duplicated_revenue_usd,
           SUM(CASE
                   WHEN r.is_duplicated = 0 AND r.is_paid_overflow = 1 THEN r.click_price_usd
                   ELSE 0
               END)                                                                 AS paid_overflow_revenue_usd,
           NULL                                                                     AS conv_revenue_usd,
           COUNT(DISTINCT CASE WHEN r.is_duplicated = 0 THEN r.id_away END)         AS jdp_away_count,
           COUNT(DISTINCT CASE WHEN r.is_duplicated = 1 THEN r.id_away END)         AS duplicated_away_count,
           COUNT(DISTINCT CASE WHEN r.is_duplicated = 0 THEN r.id_click END)        AS click_count,
           COUNT(DISTINCT CASE WHEN r.is_duplicated = 1 THEN r.id_click END)        AS duplicated_click_count,
           COUNT(DISTINCT CASE
                              WHEN r.is_duplicated = 0 AND r.is_paid_overflow = 1
                                  THEN r.id_click
                          END)                                                      AS paid_overflow_click_count,
           COUNT(DISTINCT CASE WHEN r.is_duplicated = 0 THEN r.id_session_away END) AS conversion_count
    FROM temp_click_revenue_conversion_raw r
    GROUP BY r.date_diff,
             r.id_traf_source,
             r.id_current_traf_source,
             r.user_id,
             r.id_project,
             r.project_name,
             r.campaign_name,
             r.id_campaign,
             r.placement,
             r.away_type,
             r.click_category,
             r.conversion_type,
             r.session_create_page_type,
             r.ip_cc,
             r.is_local,
             r.is_mobile,
             r.is_paid,
             r.is_returned,
             r.is_paid_overflow,
             r.is_duplicated,
             r.utm_content,
             r.id_job_category;


    INSERT INTO an.rpl_paid_metrics_agg(country, action_datediff, traf_source_id, current_traf_source_id, user_id,
                                    project_id, project_name, campaign_name, campaign_id, placement, away_type,
                                    click_category, conversion_type, session_create_page_type, ip_cc, is_local,
                                    is_mobile, is_paid,
                                    is_returned, is_paid_overflow, is_duplicated, utm_content, job_category_id,
                                    revenue_usd,
                                    duplicated_revenue_usd, paid_overflow_revenue_usd, conv_revenue_usd,
                                    jdp_away_cnt, duplicated_away_cnt,
                                    click_cnt, duplicated_click_cnt, paid_overflow_click_cnt, conversion_cnt)
    SELECT country_id,
        date_diff,
        id_traf_source,
        id_current_traf_source,
        user_id,
        id_project,
        project_name,
        campaign_name,
        id_campaign,
        placement,
        away_type,
        click_category,
        conversion_type,
        session_create_page_type,
        ip_cc,
        is_local,
        is_mobile,
        is_paid,
        is_returned,
        is_paid_overflow,
        is_duplicated,
        utm_content,
        id_job_category,
        revenue_usd,
        duplicated_revenue_usd,
        paid_overflow_revenue_usd,
        conv_revenue_usd::numeric,
        jdp_away_count,
        duplicated_away_count,
        click_count,
        duplicated_click_count,
        paid_overflow_click_count,
        conversion_count
    FROM result;

