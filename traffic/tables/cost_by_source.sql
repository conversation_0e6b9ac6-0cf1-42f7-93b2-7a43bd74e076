select traffic_datediff = datediff(day,'1900-01-01', [date]),
	   country_id = case cbs.name when 'ua' then 1
								  when 'ua' then 2
								  when 'de' then 3
								  when 'uk' then 4
								  when 'fr' then 5
								  when 'ca' then 6
								  when 'us' then 7
								  when 'id' then 8
								  when 'ru' then 9
								  when 'hu' then 10
								  when 'ro' then 11
					else null
					end,
	   traffic_cost_usd = cbs.cost_usd,
	   traffic_source_id = lac.jooble_id
from [PublicStatistic].[dbo].[cost_by_source] cbs
inner join [Marketing].[legent].[labels_and_channels] lac on lac.country=cbs.name collate Cyrillic_General_CI_AS and lac.name = cbs.src collate Cyrillic_General_CI_AS;
