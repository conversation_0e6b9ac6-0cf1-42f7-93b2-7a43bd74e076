select case when camp.id is null then 0 else camp.id end as id,
       original_id,
       case when account_id is null then 0 else account_id end as account_id,
       case when camp.name is null then 'empty' else camp.name end as name,
       Marketing.dbo.get_label_id_from_campaign(camp.name) as label_id,
       label.name as label_name,
       label.type as label_type,
       case when counter_adgroups is null then 0 else counter_adgroups end as counter_adgroups,
       sync_at,
       created_at,
       updated_at,
       isNeedUpload             as is_need_upload,
       status,
       geo_target,
       tracking_url_template,
       bid_mobile_coef,
       bid_desctop_coef,
       bid_tablet_coef,
       is_criterion_loaded,
       price_language,
       price_type,
       price_qualifier,
       price_currency,
       budget,
       budget_id,
       autoupdate,
       is_google_bids_syncer,
       budget_update_at
from [dbo].[campaigns] camp
left join legent.labels_and_channels label on label.id = Marketing.dbo.get_label_id_from_campaign(camp.name);
