CREATE OR REPLACE PROCEDURE traffic.prc_search_seo_initiatives()
    LANGUAGE 'plpgsql'
AS
$$

DECLARE
    _date_start          date    = DATE_TRUNC('month', CURRENT_DATE - INTERVAL '2 months')::DATE;
    _date_start_datediff integer = _date_start - DATE '1900-01-01';

BEGIN

    DELETE
    FROM traffic.search_seo_initiatives
    WHERE date >= _date_start;


    CREATE TEMP TABLE tmp_dic_filter AS
    SELECT DISTINCT dd.country_cc,
                    countries.id               AS country_id,
                    countries.name_country_eng AS country
    FROM traffic.dic_seo_initiatives dd
             LEFT JOIN dimension.countries
                       ON dd.country_cc = LOWER(countries.alpha_2);

    CREATE TEMP TABLE tmp_search_raw AS
    SELECT tmp_dic_filter.country,
           tmp_dic_filter.country_cc,
           (DATE '1900-01-01' + search_agg.date_diff * INTERVAL '1 day')::date AS date,
           search_agg.is_local,
           search_agg.q_kw,
           search_agg.q_id_region,
           SUM(search_agg.search_cnt)                                          AS search_cnt,
           SUM(search_agg.revenue_usd)                                         AS revenue_usd,
           SUM(search_agg.click_cnt)                                           AS click_cnt,
           SUM(search_agg.click_paid_cnt)                                      AS click_paid_cnt,
           SUM(search_agg.conversion_away_cnt)                                 AS conversion_away_cnt,
           SUM(search_agg.conversion_revenue_usd)                              AS conversion_revenue_usd,
           SUM(search_agg.conversion_cnt)                                      AS conversion_cnt,
           SUM(search_agg.destination_away_click)                              AS destination_away_click,
           SUM(search_agg.destination_jdp_apply_click)                         AS destination_jdp_apply_click,
           SUM(search_agg.destination_jdp_away_click)                          AS destination_jdp_away_click,
           SUM(search_agg.click_premium_cnt)                                   AS click_premium_cnt,
           SUM(search_agg.click_free_cnt)                                      AS click_free_cnt,
           SUM(search_agg.with_paid_jobs)                                      AS with_paid_jobs,
           SUM(search_agg.with_only_free_jobs)                                 AS with_only_free_jobs,
           SUM(search_agg.without_any_clicks)                                  AS without_any_clicks
    FROM aggregation.search_agg
             LEFT JOIN dimension.u_traffic_source uts1
                       ON uts1.id = search_agg.id_current_traf_source AND uts1.country = search_agg.country_id
             JOIN tmp_dic_filter
                  ON search_agg.country_id = tmp_dic_filter.country_id
    WHERE search_agg.date_diff >= _date_start_datediff
      AND search_agg.date_diff <= (CURRENT_DATE - DATE '1900-01-01') - 4
      AND uts1.channel::text = 'Organic Search'::text
    GROUP BY tmp_dic_filter.country, tmp_dic_filter.country_cc,
             (DATE '1900-01-01' + search_agg.date_diff * INTERVAL '1 day')::date,
             search_agg.is_local, search_agg.q_kw, search_agg.q_id_region;

    CREATE TEMP TABLE tmp_search_agg AS
    SELECT dd.group_id,
           dd.group_name,
           dd.country_cc,
           dd.type,
           dd.seo_query_id,
           dd.region_id,
           dd.ss_hash64,
           dd.ss,
           ss.country,
           ss.date,
           ss.is_local,
           ss.search_cnt,
           ss.revenue_usd,
           ss.click_cnt,
           ss.click_paid_cnt,
           ss.conversion_away_cnt,
           ss.conversion_revenue_usd,
           ss.conversion_cnt,
           ss.destination_away_click,
           ss.destination_jdp_apply_click,
           ss.destination_jdp_away_click,
           ss.click_premium_cnt,
           ss.click_free_cnt,
           ss.with_paid_jobs,
           ss.with_only_free_jobs,
           ss.without_any_clicks
    FROM traffic.dic_seo_initiatives dd
             LEFT JOIN tmp_search_raw ss
                       ON dd.country_cc::text = ss.country_cc::text AND dd.ss = ss.q_kw::bpchar;

    CREATE TEMP TABLE tmp_web_bigquery_statistics AS
    SELECT tmp_dic_filter.country,
           tmp_dic_filter.country_cc,
           ws.date,
           ws.kw_h64,
           ws.rgn_id,
           SUM(ws.imp)                AS imp_cnt,
           SUM(ws.clicks)             AS clicks,
           SUM(ws.position_sum)       AS position_avg,
           COUNT(DISTINCT ws.url_h64) AS url_cnt,
           COUNT(DISTINCT ws.kw_h64)  AS seo_query_cnt,
           SUM(bot.req_total)         AS req_total,
           SUM(bot.req_success)       AS req_success
    FROM traffic.web_bigquery_statistic ws
             JOIN tmp_dic_filter
                  ON ws.country_code::text = tmp_dic_filter.country_cc::text
             LEFT JOIN traffic.crawler_stat bot
                       ON bot.date = ws.date AND ws.country_code::text = bot.country_code::text AND
                          ws.url_h64 = bot.url_h64
    WHERE ws.date >= _date_start
    GROUP BY tmp_dic_filter.country, tmp_dic_filter.country_cc, ws.date, ws.kw_h64, ws.rgn_id;

    CREATE TEMP TABLE tmp_seo_agg AS
    SELECT dd.group_id,
           dd.group_name,
           dd.country_cc,
           dd.type,
           dd.seo_query_id,
           dd.region_id,
           dd.ss_hash64,
           dd.ss,
           ws.country,
           ws.date,
           ws.imp_cnt,
           ws.clicks,
           ws.position_avg,
           ws.url_cnt,
           ws.seo_query_cnt,
           ws.req_total,
           ws.req_success
    FROM traffic.dic_seo_initiatives dd
             LEFT JOIN tmp_web_bigquery_statistics ws
                       ON dd.country_cc::text = ws.country_cc::text AND dd.ss_hash64 = ws.kw_h64;

    CREATE TEMP TABLE tmp_unions AS
    SELECT ss.group_id,
           ss.group_name,
           ss.country_cc,
           ss.type,
           ss.seo_query_id,
           ss.region_id,
           ss.ss_hash64,
           ss.ss,
           ss.country,
           ss.date,
           ss.is_local,
           ss.search_cnt,
           ss.revenue_usd,
           ss.click_cnt,
           ss.click_paid_cnt,
           ss.conversion_away_cnt,
           ss.conversion_revenue_usd,
           ss.conversion_cnt,
           ss.destination_away_click,
           ss.destination_jdp_apply_click,
           ss.destination_jdp_away_click,
           ss.click_premium_cnt,
           ss.click_free_cnt,
           ss.with_paid_jobs,
           ss.with_only_free_jobs,
           ss.without_any_clicks,
           0 AS imp_cnt,
           0 AS clicks,
           0 AS position_avg,
           0 AS url_cnt,
           0 AS seo_query_cnt,
           0 AS req_total,
           0 AS req_success
    FROM tmp_search_agg ss
    UNION ALL
    SELECT ws.group_id,
           ws.group_name,
           ws.country_cc,
           ws.type,
           ws.seo_query_id,
           ws.region_id,
           ws.ss_hash64,
           ws.ss,
           ws.country,
           ws.date,
           NULL::integer AS is_local,
           0             AS search_cnt,
           0             AS revenue_usd,
           0             AS click_cnt,
           0             AS click_paid_cnt,
           0             AS conversion_away_cnt,
           0             AS conversion_revenue_usd,
           0             AS conversion_cnt,
           0             AS destination_away_click,
           0             AS destination_jdp_apply_click,
           0             AS destination_jdp_away_click,
           0             AS click_premium_cnt,
           0             AS click_free_cnt,
           0             AS with_paid_jobs,
           0             AS with_only_free_jobs,
           0             AS without_any_clicks,
           ws.imp_cnt,
           ws.clicks,
           ws.position_avg,
           ws.url_cnt,
           ws.seo_query_cnt,
           ws.req_total,
           ws.req_success
    FROM tmp_seo_agg ws;


    INSERT INTO traffic.search_seo_initiatives(group_id, group_name, country_cc, type, seo_query_id, region_id,
                                               ss_hash64, ss, country, date, is_local, search_cnt, revenue_usd,
                                               click_cnt, click_paid_cnt, conversion_away_cnt, conversion_revenue_usd,
                                               conversion_cnt, destination_away_click, destination_jdp_apply_click,
                                               destination_jdp_away_click, click_premium_cnt, click_free_cnt,
                                               with_paid_jobs, with_only_free_jobs, without_any_clicks, imp_cnt, clicks,
                                               position_avg, url_cnt, seo_query_cnt, req_total, req_success)
    SELECT group_id,
           group_name,
           country_cc,
           type,
           seo_query_id,
           region_id,
           ss_hash64,
           ss,
           country,
           date,
           is_local,
           search_cnt,
           revenue_usd,
           click_cnt,
           click_paid_cnt,
           conversion_away_cnt,
           conversion_revenue_usd,
           conversion_cnt,
           destination_away_click,
           destination_jdp_apply_click,
           destination_jdp_away_click,
           click_premium_cnt,
           click_free_cnt,
           with_paid_jobs,
           with_only_free_jobs,
           without_any_clicks,
           imp_cnt,
           clicks,
           position_avg,
           url_cnt,
           seo_query_cnt,
           req_total,
           req_success
    FROM tmp_unions;

END
$$;
