create view traffic.v_paid_traffic_cost(date, traffic_source_name, is_phone, impressions_cnt, clicks_cnt, cost_usd, cost_cop) as
	SELECT a.day                                                                       AS date,
       substr(a.campaign::text, "position"(a.campaign::text, '_cpc'::text) + 1, 4) AS traffic_source_name,
       CASE
           WHEN a.campaign::text ~~ '%_Desktop_%'::text THEN 'Desktop'::text
           WHEN a.campaign::text ~~ '%_Mobile_%'::text THEN 'Mobile'::text
           ELSE 'not_defined'::text
           END                                                                     AS is_phone,
       sum(a.impressions)                                                          AS impressions_cnt,
       sum(a.clicks)                                                               AS clicks_cnt,
       sum(a.cost)::numeric / cs.to_usd                                            AS cost_usd,
       sum(a.cost)                                                                 AS cost_cop
FROM imp_statistic.m_adwords a
         JOIN imp_statistic.currency_source cs ON cs.currency = 'COP'::bpchar AND a.day = cs.date
WHERE a.campaign::text ~~ 'UA%'::text
  AND a.campaign::text !~~ '%Employer%'::text
  AND a.day >= '2021-08-01'::date
  AND a.day <= (CURRENT_DATE - 1)
GROUP BY a.day, (substr(a.campaign::text, "position"(a.campaign::text, '_cpc'::text) + 1, 4)),
         (
             CASE
                 WHEN a.campaign::text ~~ '%_Desktop_%'::text THEN 'Desktop'::text
                 WHEN a.campaign::text ~~ '%_Mobile_%'::text THEN 'Mobile'::text
                 ELSE 'not_defined'::text
                 END), cs.to_usd;

alter table traffic.v_paid_traffic_cost owner to postgres;

grant select, update on traffic.v_paid_traffic_cost to readonly;

