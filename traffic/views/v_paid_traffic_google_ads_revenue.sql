create view traffic.v_paid_traffic_google_ads_revenue(date, traffic_source_name, device_type, call_cnt, apply_cnt, profile_cnt, apply_revenue_usd, profile_revenue_usd, call_revenue_usd) as
	SELECT uac.date,
       COALESCE(substr(uac.name::text, "position"(uac.name::text, '_cpc'::text) + 1, 4),
                'other'::text)                                AS traffic_source_name,
       CASE
           WHEN uac.name::text ~~ '%_Desktop_%'::text THEN 'Desktop'::text
           WHEN uac.name::text ~~ '%_Mobile_%'::text THEN 'Mobile'::text
           ELSE 'not_defined'::text
           END                                                AS device_type,
       COALESCE(sum(uac.cnt_ring), 0::bigint)                 AS call_cnt,
       COALESCE(sum(uac.cnt_apply), 0::bigint)                AS apply_cnt,
       COALESCE(sum(uac.cnt_profile), 0::bigint)              AS profile_cnt,
       COALESCE(sum(uac.val_apply / cs.to_usd), 0::numeric)   AS apply_revenue_usd,
       COALESCE(sum(uac.val_profile / cs.to_usd), 0::numeric) AS profile_revenue_usd,
       COALESCE(sum(uac.val_ring / cs.to_usd), 0::numeric)    AS call_revenue_usd
FROM imp_statistic.ua_conversions uac
         JOIN imp_statistic.currency_source cs ON cs.currency = 'COP'::bpchar AND cs.date = uac.date
WHERE uac.date >= '2021-08-01'::date
  AND uac.date <= (CURRENT_DATE - 1)
GROUP BY uac.date, (COALESCE(substr(uac.name::text, "position"(uac.name::text, '_cpc'::text) + 1, 4), 'other'::text)),
         (
             CASE
                 WHEN uac.name::text ~~ '%_Desktop_%'::text THEN 'Desktop'::text
                 WHEN uac.name::text ~~ '%_Mobile_%'::text THEN 'Mobile'::text
                 ELSE 'not_defined'::text
                 END), cs.to_usd;

alter table traffic.v_paid_traffic_google_ads_revenue owner to postgres;

grant select, update on traffic.v_paid_traffic_google_ads_revenue to readonly;

