create or replace view traffic.v_seo_matrix_report(country, week_num, date_range, num, metric, value) as
WITH country_calendar AS (
    SELECT lower(countries.alpha_2::text)                                                                     AS country_cc,
           countries.id                                                                                       AS country_id,
           (date_part('year'::text, ic.dt) || '-'::text) ||
           date_part('week'::text, ic.dt)                                                                     AS week_number,
           (to_char(min(fn_get_date_from_date_diff(ic.date_diff))::timestamp with time zone, 'DD.MM.YY'::text) ||
            ' - '::text) || to_char(max(fn_get_date_from_date_diff(ic.date_diff))::timestamp with time zone,
                                    'DD.MM.YY'::text)                                                         AS date_range
    FROM dimension.countries
             CROSS JOIN dimension.info_calendar ic
    WHERE (countries.id = ANY (ARRAY [2, 3, 4, 6, 9]))
      AND ic.dt >= '2024-04-29'::date
      AND ic.dt <= (CURRENT_DATE - date_part('dow'::text, CURRENT_DATE)::integer)
    GROUP BY (lower(countries.alpha_2::text)), countries.id,
             ((date_part('year'::text, ic.dt) || '-'::text) || date_part('week'::text, ic.dt))
),
     search_raw AS (
         SELECT search_agg.country_id,
                (date_part('year'::text, fn_get_date_from_date_diff(search_agg.date_diff)) || '-'::text) ||
                date_part('week'::text, fn_get_date_from_date_diff(search_agg.date_diff)) AS week_number,
                count(DISTINCT
                      CASE
                          WHEN search_agg.revenue_usd > 0::numeric THEN concat(search_agg.q_kw, search_agg.q_id_region)
                          ELSE NULL::text
                          END)                                                            AS url_with_rev,
                sum(search_agg.search_cnt)                                                AS search_cnt,
                sum(search_agg.click_cnt)                                                 AS click_cnt,
                sum(search_agg.revenue_usd)                                               AS revenue_usd,
                sum(search_agg.click_paid_cnt)                                            AS click_paid_cnt,
                sum(search_agg.conversion_away_cnt)                                       AS conversion_away_cnt,
                sum(search_agg.conversion_revenue_usd)                                    AS conversion_revenue_usd,
                sum(search_agg.conversion_cnt)                                            AS conversion_cnt
         FROM aggregation.search_agg
                  LEFT JOIN dimension.u_traffic_source uts1
                            ON uts1.id = search_agg.id_current_traf_source AND uts1.country = search_agg.country_id
         WHERE search_agg.date_diff >= 45549
           AND (search_agg.country_id = ANY (ARRAY [2, 3, 4, 6, 9]))
           AND uts1.channel::text = 'Organic Search'::text
         GROUP BY search_agg.country_id,
                  ((date_part('year'::text, fn_get_date_from_date_diff(search_agg.date_diff)) || '-'::text) ||
                   date_part('week'::text, fn_get_date_from_date_diff(search_agg.date_diff)))
     ),
     web_bigquery_statistics AS (
         SELECT ws_1.country_code                                                                       AS country_cc,
                (date_part('year'::text, ws_1.date) || '-'::text) || date_part('week'::text, ws_1.date) AS week_number,
                sum(ws_1.imp)                                                                           AS imp_cnt,
                sum(ws_1.clicks)                                                                        AS clicks,
                sum(ws_1.position_sum)                                                                  AS position_sum,
                count(DISTINCT
                      CASE
                          WHEN ws_1.imp > 0 THEN ws_1.url_h64
                          ELSE NULL::bigint
                          END)                                                                          AS url_with_imp,
                count(DISTINCT
                      CASE
                          WHEN ws_1.clicks > 0 THEN ws_1.url_h64
                          ELSE NULL::bigint
                          END)                                                                          AS url_with_click
         FROM traffic.web_bigquery_statistic ws_1
         WHERE ws_1.date >= '2024-09-16'::date
           AND (ws_1.country_code::text = ANY (ARRAY ['us'::text, 'uk'::text, 'de'::text, 'fr'::text, 'pl'::text]))
           AND (ws_1.url::text ~~ '%jooble.org/job%'::text OR ws_1.url::text ~~ '%jooble.org/emploi%'::text OR
                ws_1.url::text ~~ '%jooble.org/stellenangebot%'::text OR ws_1.url::text ~~ '%jooble.org/praca%'::text)
         GROUP BY ws_1.country_code,
                  ((date_part('year'::text, ws_1.date) || '-'::text) || date_part('week'::text, ws_1.date))
     ),
     crawler_stat_raw AS (
         SELECT bot.country_code                                                                      AS country_cc,
                (date_part('year'::text, bot.date) || '-'::text) || date_part('week'::text, bot.date) AS week_number,
                sum(bot.req_total)                                                                    AS req_total,
                sum(bot.req_success)                                                                  AS req_success,
                count(DISTINCT bot.url_h64)                                                           AS bot_url_cnt,
                count(DISTINCT
                      CASE
                          WHEN bot.req_success > 0 THEN bot.url_h64
                          ELSE NULL::bigint
                          END)                                                                        AS bot_url_succ,
                count(DISTINCT bot.ss_hash64)                                                         AS bot_kwh_cnt
         FROM traffic.crawler_stat bot
         WHERE bot.date >= '2024-09-16'::date
           AND (bot.country_code = ANY (ARRAY ['us'::bpchar, 'uk'::bpchar, 'de'::bpchar, 'fr'::bpchar, 'pl'::bpchar]))
           AND bot.url !~~ '%jooble.org/m/job%'::text
         GROUP BY bot.country_code,
                  ((date_part('year'::text, bot.date) || '-'::text) || date_part('week'::text, bot.date))
     ),
     seo_matrix AS (
         SELECT matrix.country_cc,
                (date_part('year'::text, matrix.action_date::date) || '-'::text) ||
                date_part('week'::text, matrix.action_date::date) AS week_number,
                max(
                        CASE
                            WHEN date_part('isodow'::text, matrix.action_date::date) = 7::double precision
                                THEN matrix.seo_query
                            ELSE NULL::bigint
                            END)                                  AS seo_query,
                max(
                        CASE
                            WHEN date_part('isodow'::text, matrix.action_date::date) = 7::double precision
                                THEN matrix.region
                            ELSE NULL::bigint
                            END)                                  AS region,
                max(
                        CASE
                            WHEN date_part('isodow'::text, matrix.action_date::date) = 7::double precision
                                THEN matrix.matrix_wide
                            ELSE NULL::bigint
                            END)                                  AS matrix_wide,
                max(
                        CASE
                            WHEN date_part('isodow'::text, matrix.action_date::date) = 7::double precision
                                THEN matrix.actual_matrix
                            ELSE NULL::bigint
                            END)                                  AS actual_matrix,
                max(
                        CASE
                            WHEN date_part('isodow'::text, matrix.action_date::date) = 7::double precision
                                THEN matrix.actual_paid_matrix
                            ELSE NULL::bigint
                            END)                                  AS actual_paid_matrix
         FROM traffic.seo_matrix_stat matrix
         WHERE matrix.country_cc = ANY (ARRAY ['uk'::text, 'us'::text, 'de'::text, 'pl'::text, 'fr'::text])
         GROUP BY matrix.country_cc,
                  ((date_part('year'::text, matrix.action_date::date) || '-'::text) ||
                   date_part('week'::text, matrix.action_date::date))
     ),
     seo_matrix_history AS (
         SELECT hh.country_code                                                                 AS country_cc,
                (date_part('year'::text, ic.dt) || '-'::text) || date_part('week'::text, ic.dt) AS week_number,
                hh.date,
                hh.seo_query,
                hh.region,
                hh.wide_matrix,
                hh.actual_matrix,
                hh.actual_paid_matrix,
                hh.revenue,
                hh.url_with_rev,
                hh.url_with_click,
                hh.url_with_impr,
                hh.imp,
                hh.click
         FROM traffic.seo_matrix_stat_history hh
                  LEFT JOIN dimension.info_calendar ic ON ic.date_diff = hh.date_diff
     ),
     data_agg AS (
         SELECT cc_1.country_cc,
                cc_1.week_number,
                cc_1.date_range,
                COALESCE(search_raw.url_with_rev, hh.url_with_rev::bigint)         AS url_with_rev,
                COALESCE(search_raw.revenue_usd, hh.revenue::numeric)              AS revenue_usd,
                COALESCE(ws_1.imp_cnt, hh.imp::bigint)                             AS imp_cnt,
                COALESCE(ws_1.clicks, hh.click::bigint)                            AS clicks,
                COALESCE(ws_1.url_with_imp, hh.url_with_impr::bigint)              AS url_with_imp,
                COALESCE(ws_1.url_with_click, hh.url_with_click::bigint)           AS url_with_click,
                bot.req_total,
                bot.req_success,
                bot.bot_url_cnt,
                bot.bot_url_succ,
                bot.bot_kwh_cnt,
                COALESCE(matrix.seo_query, hh.seo_query::bigint)                   AS seo_query,
                COALESCE(matrix.region, hh.region::bigint)                         AS region,
                COALESCE(matrix.matrix_wide, hh.wide_matrix::bigint)               AS matrix_wide,
                COALESCE(matrix.actual_matrix, hh.actual_matrix::bigint)           AS actual_matrix,
                COALESCE(matrix.actual_paid_matrix, hh.actual_paid_matrix::bigint) AS actual_paid_matrix
         FROM country_calendar cc_1
                  LEFT JOIN search_raw
                            ON search_raw.country_id = cc_1.country_id AND search_raw.week_number = cc_1.week_number
                  LEFT JOIN web_bigquery_statistics ws_1
                            ON ws_1.country_cc::text = cc_1.country_cc AND ws_1.week_number = cc_1.week_number
                  LEFT JOIN crawler_stat_raw bot
                            ON bot.country_cc::text = cc_1.country_cc AND bot.week_number = cc_1.week_number
                  LEFT JOIN seo_matrix matrix
                            ON matrix.country_cc = cc_1.country_cc AND matrix.week_number = cc_1.week_number
                  LEFT JOIN seo_matrix_history hh
                            ON hh.country_cc = cc_1.country_cc AND hh.week_number = cc_1.week_number
     ),
     unions AS (
         SELECT ss.country_cc,
                ss.week_number,
                ss.date_range,
                3                   AS num,
                'Revenue usd'::text AS metric,
                ss.revenue_usd      AS value
         FROM data_agg ss
         UNION ALL
         SELECT ss.country_cc,
                ss.week_number,
                ss.date_range,
                13                       AS num,
                'URL with_revenue'::text AS metric,
                ss.url_with_rev          AS value
         FROM data_agg ss
         UNION ALL
         SELECT ws_1.country_cc,
                ws_1.week_number,
                ws_1.date_range,
                1                        AS num,
                'Impression (GSC)'::text AS metric,
                ws_1.imp_cnt             AS value
         FROM data_agg ws_1
         UNION ALL
         SELECT ws_1.country_cc,
                ws_1.week_number,
                ws_1.date_range,
                2                    AS num,
                'Clicks (GSC)'::text AS metric,
                ws_1.clicks          AS value
         FROM data_agg ws_1
         UNION ALL
         SELECT ws_1.country_cc,
                ws_1.week_number,
                ws_1.date_range,
                11                    AS num,
                'URL with_impr'::text AS metric,
                ws_1.url_with_imp     AS value
         FROM data_agg ws_1
         UNION ALL
         SELECT ws_1.country_cc,
                ws_1.week_number,
                ws_1.date_range,
                12                     AS num,
                'URL with_click'::text AS metric,
                ws_1.url_with_click    AS value
         FROM data_agg ws_1
         UNION ALL
         SELECT bot.country_cc,
                bot.week_number,
                bot.date_range,
                15                AS num,
                'Req total'::text AS metric,
                bot.req_total     AS value
         FROM data_agg bot
         UNION ALL
         SELECT bot.country_cc,
                bot.week_number,
                bot.date_range,
                16                  AS num,
                'Req success'::text AS metric,
                bot.req_success     AS value
         FROM data_agg bot
         UNION ALL
         SELECT bot.country_cc,
                bot.week_number,
                bot.date_range,
                17                    AS num,
                'Bot url total'::text AS metric,
                bot.bot_url_cnt       AS value
         FROM data_agg bot
         UNION ALL
         SELECT bot.country_cc,
                bot.week_number,
                bot.date_range,
                18                      AS num,
                'Bot url success'::text AS metric,
                bot.bot_url_succ        AS value
         FROM data_agg bot
         UNION ALL
         SELECT matrix.country_cc,
                matrix.week_number,
                matrix.date_range,
                5                 AS num,
                'SEO Query'::text AS metric,
                matrix.seo_query  AS value
         FROM data_agg matrix
         UNION ALL
         SELECT matrix.country_cc,
                matrix.week_number,
                matrix.date_range,
                6              AS num,
                'Region'::text AS metric,
                matrix.region  AS value
         FROM data_agg matrix
         UNION ALL
         SELECT matrix.country_cc,
                matrix.week_number,
                matrix.date_range,
                7                       AS num,
                'Wide matrix URL'::text AS metric,
                matrix.matrix_wide      AS value
         FROM data_agg matrix
         UNION ALL
         SELECT matrix.country_cc,
                matrix.week_number,
                matrix.date_range,
                8                         AS num,
                'Actual matrix URL'::text AS metric,
                matrix.actual_matrix      AS value
         FROM data_agg matrix
         UNION ALL
         SELECT matrix.country_cc,
                matrix.week_number,
                matrix.date_range,
                9                              AS num,
                'Actual paid matrix URL'::text AS metric,
                matrix.actual_paid_matrix      AS value
         FROM data_agg matrix
         UNION ALL
         SELECT cc_1.country_cc,
                cc_1.week_number,
                cc_1.date_range,
                4             AS num,
                ' '::text     AS metric,
                NULL::numeric AS value
         FROM country_calendar cc_1
         UNION ALL
         SELECT cc_1.country_cc,
                cc_1.week_number,
                cc_1.date_range,
                14            AS num,
                ' '::text     AS metric,
                NULL::numeric AS value
         FROM country_calendar cc_1
     )
SELECT cc.name_country_eng AS country,
       ws.week_number      AS week_num,
       ws.date_range,
       ws.num,
       ws.metric,
       sum(ws.value)       AS value
FROM unions ws
         LEFT JOIN dimension.countries cc ON ws.country_cc = lower(cc.alpha_2::text)
GROUP BY cc.name_country_eng, ws.week_number, ws.date_range, ws.metric, ws.num
UNION ALL
SELECT tt.country,
       tt.week_num,
       tt.date_range,
       tt.num,
       tt.metric,
       tt.value
FROM traffic.seo_matrix_stat_report tt
WHERE tt.num = ANY (ARRAY [1, 2, 3, 4, 11, 12, 13, 14, 15, 16, 17, 18]);

alter table traffic.v_seo_matrix_report
    owner to vnov;

grant select on traffic.v_seo_matrix_report to readonly;

grant select on traffic.v_seo_matrix_report to math;

grant select on traffic.v_seo_matrix_report to write_ono;

grant select on traffic.v_seo_matrix_report to writeonly_pyscripts;

grant select on traffic.v_seo_matrix_report to readonly_aggregation;

