create view traffic.v_session_paid_search_profile_submission as
select sps.country_id,
       sps.session_datediff,
       sps.keyword_name,
       sps.region_id,
       sps.region_original_name,
       count(distinct sps.session_id)  as session_cnt,
       count(distinct arpu.profile_id) as profile_submitted_cnt
from traffic.session_paid_search sps
         left join traffic.session_paid_search_profile_arpu_7_days arpu
                   on sps.country_id = arpu.country_id
                       and sps.session_datediff = arpu.submission_datediff
                       and sps.session_id = arpu.session_id
group by sps.country_id,
         sps.session_datediff,
         sps.keyword_name,
         sps.region_id,
         sps.region_original_name;
