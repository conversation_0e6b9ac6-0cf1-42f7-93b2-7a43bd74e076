create or replace view traffic.v_webmaster_statistic
            (alpha_2, name_country_eng, date, is_jdp, impression_cnt, clicks_cnt, position_avg, ctr_avg, url_cnt) as
SELECT c.alpha_2,
       c.name_country_eng,
       ws.date,
       CASE
           WHEN "substring"(ws.url::text, '%#"jdp#"%'::text, '#'::text) IS NOT NULL THEN 'JDP'::text
           ELSE
               CASE
                   WHEN "substring"(ws.url::text, '%#"jooble.org/salary/#"%'::text, '#'::text) IS NOT NULL THEN 'Salary'::text
                   ELSE
                       CASE
                           WHEN "substring"(ws.url::text, '%#"jooble.org/tax-calculator#"%'::text,
                                            '#'::text) IS NOT NULL THEN 'Tax Calculator'::text
                           ELSE 'Other'::text
                           END
                   END
           END                                                   AS is_jdp,
       sum(ws.impressions)                                       AS impression_cnt,
       sum(ws.clicks)                                            AS clicks_cnt,
       sum(ws."position" * ws.impressions) / sum(ws.impressions) AS position_avg,
       sum(ws.clicks) / sum(ws.impressions)                      AS ctr_avg,
       count(ws.url)                                             AS url_cnt
FROM traffic.webmaster_statistic ws
         JOIN dimension.countries c ON ws.country_id = c.id
WHERE ws.date >= '2023-01-01'
GROUP BY c.alpha_2, c.name_country_eng, ws.date,
         (
             CASE
                 WHEN "substring"(ws.url::text, '%#"jdp#"%'::text, '#'::text) IS NOT NULL THEN 'JDP'::text
                 ELSE
                     CASE
                         WHEN "substring"(ws.url::text, '%#"jooble.org/salary/#"%'::text, '#'::text) IS NOT NULL
                             THEN 'Salary'::text
                         ELSE
                             CASE
                                 WHEN "substring"(ws.url::text, '%#"jooble.org/tax-calculator#"%'::text,
                                                  '#'::text) IS NOT NULL THEN 'Tax Calculator'::text
                                 ELSE 'Other'::text
                                 END
                         END
                 END);

alter table traffic.v_webmaster_statistic
    owner to postgres;

grant select on traffic.v_webmaster_statistic to readonly;

grant select on traffic.v_webmaster_statistic to writeonly_product;

grant select on traffic.v_webmaster_statistic to writeonly_pyscripts;

