with has_view as (
    select distinct id_search
        , id_account
        , id_profile
        , sources
    from imp_employer.profile_search_action
    where action=0
        and sources=1
)
select s.id_account as account_id
    , id_session as session_id
    , trigger
    , query_hash
    , cast(s.date as date) as search_date
    , s.id   as search_id
    , case when has_view.id_search is null then 0 else 1 end as has_view_in_search
    , count(has_view.id_profile) as profile_view_cnt
from imp_employer.profile_search s
    left join has_view on s.sources = has_view.sources and has_view.id_search = s.id
where cast(s.date as date) = current_date - 1
group by s.id_account
    , id_session
    , trigger
    , query_hash
    , cast(s.date as date)
    , s.id
    , case when has_view.id_search is null then 0 else 1 end
;
