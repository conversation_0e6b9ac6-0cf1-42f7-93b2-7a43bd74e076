with viewed_profile as (
    select distinct id_profile,
           id_search,
           id_account,
           cast(date as date) as date_view,
           action,
           sources
    from imp_employer.profile_search_action
    where action=0),
itc_action as (
    select distinct id_profile,
           id_search,
           id_account,
           cast(date as date) as date_action,
           action as intention_to_contact_type,
           sources
    from imp_employer.profile_search_action
    where action in (1, 2, 3, 4, 6))

select distinct eacc.country_code
    , eacc.employer_id
    , vp.id_account as account_id
    , vp.id_profile as profile_id
    , 1 as is_view
    , date_view
    , coalesce(date_action, '2999-12-31') as date_interact
    , case when itc.id_profile is null then 0 else 1 end as is_intention_to_contact
    , case when itc.id_profile is not null then intention_to_contact_type else 0 end as intention_to_contact_type
from viewed_profile vp
join retention_ea.stg_employer_related_account eacc on  eacc.sources=vp.sources and eacc.account_id=vp.id_account
left join itc_action itc on vp.sources=itc.sources  and vp.id_profile=itc.id_profile and vp.id_account = itc.id_account
;
