select id_account as account_id,
       id as search_id,
       coalesce((select string_agg(employment_Types, ', ')
       from (
                    select id,
                           regexp_split_to_table(replace(replace(cast(additional_filters_json as jsonb) ->> 'employmentTypes', '[', ''), ']', ''), ', ') as employment_Types
                    from imp_employer.profile_search
                    where id = ps.id
                    order by employment_Types ) as d ), '-1') as employment_Types,

       coalesce(cast(cast(cast(additional_filters_json as jsonb) ->> 'age' as jsonb) ->> 'AgeTo' as int), -1) as Age_To,
       coalesce(cast(cast(cast(additional_filters_json as jsonb) ->> 'age' as jsonb) ->> 'AgeFrom' as int), -1) as Age_From,
       coalesce(cast(cast(additional_filters_json as jsonb) ->> 'gender' as smallint), -1) as gender,
       coalesce(cast(cast(cast(additional_filters_json as jsonb) ->> 'salary' as jsonb) ->> 'SalaryFrom' as float), -1) as Salary_From,
       coalesce(cast(cast(cast(additional_filters_json as jsonb) ->> 'salary' as jsonb) ->> 'SalaryTo' as float), -1) as Salary_To,
       coalesce(cast(cast(additional_filters_json as jsonb) ->> 'salary' as jsonb) ->> 'CurrencyId', '-1') as Currency_Id,
       coalesce(cast(cast(cast(additional_filters_json as jsonb) ->> 'salary' as jsonb) ->> 'Rate' as int), -1) as Rate,
       sources
from imp_employer.profile_search ps
where additional_filters_json <> '{}' and date_diff = public.fn_get_date_diff(current_date) - 1;
