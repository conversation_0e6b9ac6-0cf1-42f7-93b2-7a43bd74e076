with ea_bp as (
    -- Все работодатели, которые совершали поиск в базе в разбивке по дню
    select distinct id_employer
        , id_account
        , cast(date_search as date) as date_search
        , sources
    from nsh.ea_profile_search
),
sess as (
    select employer_id
         , account_id
         , session_date
        , sources
    from retention_ea.ea_session_history
    where sources = 1
      -- сессии только EA
      and is_joobler = 0
      -- все сессии после запуска БП
      AND session_date>= current_date - 1
      -- уменьшаем выборку - пока только в Украине БП
      and country_code = 'UA'
)
select distinct sess.*
    , acc.account_type
    , case when ea_bp.id_account is null then 0 else 1 end as is_active_in_profile_base
from sess
    inner join retention_ea.stg_employer_related_account acc on acc.sources=sess.sources
        and acc.employer_id=sess.employer_id
    left join ea_bp on sess.employer_id=ea_bp.id_employer and sess.sources=ea_bp.sources
        and sess.session_date=ea_bp.date_search -- поиск в день активности
;