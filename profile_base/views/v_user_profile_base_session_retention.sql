create view profile_base.v_user_profile_base_session_retention(country_id, database_source_id, employer_id, account_type_id, employer_account_creation_datediff, has_usage_profile_base_in_7_days_after_registration, is_retained_in_8_14_days, session_in_8_14_days_cnt) as
	WITH employer_registration AS (
    SELECT DISTINCT 1                                                                AS country_id,
                    r.sources                                                        AS database_source_id,
                    r.account_type                                                   AS account_type_id,
                    fn_get_date_diff(r.ea_date_created::timestamp without time zone) AS employer_account_creation_datediff,
                    r.employer_id
    FROM retention_ea.stg_employer_related_account r
    WHERE r.country_code::text = 'UA'::text
      AND (r.moderation_status = ANY (ARRAY [1, 2]))
      AND r.ea_date_created >= '2020-11-01'::date
),
     employer_account AS (
         SELECT DISTINCT 1                                                                AS country_id,
                         r.sources                                                        AS database_source_id,
                         fn_get_date_diff(r.ea_date_created::timestamp without time zone) AS employer_account_creation_datediff,
                         r.employer_id,
                         r.account_id
         FROM retention_ea.stg_employer_related_account r
         WHERE r.country_code::text = 'UA'::text
           AND (r.moderation_status = ANY (ARRAY [1, 2]))
           AND r.ea_date_created >= '2020-11-01'::date
     ),
     employer_profile_base_use AS (
         SELECT pitc.database_source_id,
                1                               AS country_id,
                ea.employer_id,
                min(pitc.action_datediff)       AS usage_first_datediff,
                count(DISTINCT pitc.profile_id) AS usage_cnt
         FROM employer.profile_intention_to_contact pitc
                  JOIN employer_account ea ON ea.account_id = pitc.employer_account_id
         WHERE pitc.action_type_id = ANY (ARRAY [1, 2, 3, 4, 6])
         GROUP BY pitc.database_source_id, ea.employer_id
     ),
     employer_session AS (
         SELECT 1                                                             AS country_id,
                s.sources                                                     AS database_source_id,
                s.employer_id,
                fn_get_date_diff(s.session_date::timestamp without time zone) AS session_datediff,
                sum(s.cnt_sess_by_day)                                        AS session_cnt
         FROM retention_ea.ea_session_history s
         WHERE s.is_joobler = 0
           AND s.country_code::text = 'UA'::text
           AND (s.moderation_status_ea = ANY (ARRAY [1, 2]))
           AND s.session_date >= '2020-11-01'::date
         GROUP BY s.sources, s.employer_id, (fn_get_date_diff(s.session_date::timestamp without time zone))
     )
SELECT er.country_id,
       er.database_source_id,
       er.employer_id,
       er.account_type_id,
       er.employer_account_creation_datediff,
       CASE
           WHEN epbu.usage_first_datediff <= (er.employer_account_creation_datediff + 7) THEN 1
           ELSE 0
           END          AS has_usage_profile_base_in_7_days_after_registration,
       max(
               CASE
                   WHEN es.session_datediff >= (er.employer_account_creation_datediff + 8) AND
                        es.session_datediff <= (er.employer_account_creation_datediff + 14) THEN 1
                   ELSE 0
                   END) AS is_retained_in_8_14_days,
       sum(
               CASE
                   WHEN es.session_datediff >= (er.employer_account_creation_datediff + 8) AND
                        es.session_datediff <= (er.employer_account_creation_datediff + 14) THEN es.session_cnt
                   ELSE 0::numeric
                   END) AS session_in_8_14_days_cnt
FROM employer_registration er
         LEFT JOIN employer_profile_base_use epbu
                   ON er.database_source_id = epbu.database_source_id AND er.country_id = epbu.country_id AND
                      er.employer_id = epbu.employer_id
         LEFT JOIN employer_session es
                   ON er.database_source_id = es.database_source_id AND er.country_id = es.country_id AND
                      er.employer_id = es.employer_id
GROUP BY er.country_id, er.database_source_id, er.employer_id, er.account_type_id,
         er.employer_account_creation_datediff,
         (
             CASE
                 WHEN epbu.usage_first_datediff <= (er.employer_account_creation_datediff + 7) THEN 1
                 ELSE 0
                 END);

alter table profile_base.v_user_profile_base_session_retention owner to postgres;

grant select on profile_base.v_user_profile_base_session_retention to npo;

grant select on profile_base.v_user_profile_base_session_retention to readonly;

grant select on profile_base.v_user_profile_base_session_retention to writeonly_product;

grant select on profile_base.v_user_profile_base_session_retention to readonly_ds;

grant select on profile_base.v_user_profile_base_session_retention to "pavlo.kvasnii";

