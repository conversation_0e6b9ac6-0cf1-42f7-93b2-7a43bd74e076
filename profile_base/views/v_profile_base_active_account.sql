create view profile_base.v_profile_base_active_account(month_date, active_account_cnt, active_account_with_search_cnt, search_cnt, profile_open_cnt, profile_intention_to_contact_cnt, active_account_with_search_prc, search_cnt_per_account_with_search, profile_open_per_search, itc_per_profile_open) as
	WITH profile_base_monthly_data AS (
    SELECT date(date_trunc('month'::text, s.date_started))                               AS month_date,
           count(DISTINCT s.id_account)                                                  AS active_account_cnt,
           count(DISTINCT ps.id_account)                                                 AS active_account_with_search_cnt,
           count(DISTINCT (ps.id::character varying::text || '_'::text) || ps.date_diff) AS search_cnt,
           count(DISTINCT
                 CASE
                     WHEN psa.action = 0 THEN (psa.id_profile::character varying::text || '_'::text) ||
                                              psa.id_account::character varying::text
                     ELSE NULL::text
                     END)                                                                AS profile_open_cnt,
           count(DISTINCT
                 CASE
                     WHEN psa.action = ANY (ARRAY [1, 2, 3, 6]) THEN
                             (psa.id_profile::character varying::text || '_'::text) || psa.id_account::character varying::text
                     ELSE NULL::text
                     END)                                                                AS profile_intention_to_contact_cnt
    FROM imp_employer.employer_account_session s
             JOIN imp_employer.employer_account ea ON s.sources = ea.sources AND s.id_account = ea.id_account
             JOIN imp_employer.employer e
                  ON e.sources = s.sources AND e.id = ea.id_employer AND e.country_code::text = 'ua'::text
             LEFT JOIN profile_base.active_employer_weekly aew ON aew.employer_account_id = s.id_account AND
                                                                  aew.week_start_datediff =
                                                                  fn_get_date_diff(date_trunc('week'::text,
                                                                                              fn_get_timestamp_from_date_diff(s.date_diff)))
             LEFT JOIN imp_employer.profile_search ps
                       ON ps.sources = s.sources AND ps.id_account = s.id_account AND ps.id_session = s.id AND
                          ps.date_diff = s.date_diff
             LEFT JOIN imp_employer.profile_search_action psa
                       ON psa.sources = ps.sources AND psa.id_account = ps.id_account AND psa.id_search = ps.id AND
                          psa.date_diff = ps.date_diff
    WHERE s.sources = 1
      AND s.using_secret_key = false
      AND s.date_started >= '2021-01-01 00:00:00'::timestamp without time zone
      AND s.date_started <= (CURRENT_DATE - 1)
      AND (aew.employer_account_id IS NULL OR
           CASE
               WHEN aew.active_vacancy_cnt = 0::numeric OR (aew.employer_action_cnt / aew.active_vacancy_cnt) >= 150::numeric
                   THEN 1
               ELSE 0
               END = 0)
    GROUP BY (date_trunc('month'::text, s.date_started))
    ORDER BY (date_trunc('month'::text, s.date_started))
)
SELECT profile_base_monthly_data.month_date,
       profile_base_monthly_data.active_account_cnt,
       profile_base_monthly_data.active_account_with_search_cnt,
       profile_base_monthly_data.search_cnt,
       profile_base_monthly_data.profile_open_cnt,
       profile_base_monthly_data.profile_intention_to_contact_cnt,
       round(profile_base_monthly_data.active_account_with_search_cnt::numeric /
             profile_base_monthly_data.active_account_cnt::numeric, 8)                                               AS active_account_with_search_prc,
       round(profile_base_monthly_data.search_cnt::numeric /
             profile_base_monthly_data.active_account_with_search_cnt::numeric,
             8)                                                                                                      AS search_cnt_per_account_with_search,
       round(profile_base_monthly_data.profile_open_cnt::numeric / profile_base_monthly_data.search_cnt::numeric,
             8)                                                                                                      AS profile_open_per_search,
       round(profile_base_monthly_data.profile_intention_to_contact_cnt::numeric /
             profile_base_monthly_data.profile_open_cnt::numeric,
             8)                                                                                                      AS itc_per_profile_open
FROM profile_base_monthly_data;

alter table profile_base.v_profile_base_active_account owner to kpav;

grant select on profile_base.v_profile_base_active_account to readonly;

grant select on profile_base.v_profile_base_active_account to writeonly_product;

grant select on profile_base.v_profile_base_active_account to readonly_ds;

grant select on profile_base.v_profile_base_active_account to "pavlo.kvasnii";

