create procedure profile_base.insert_profile_search_parse_pivot()
	language plpgsql
as $$
begin


	truncate profile_base.profile_search_parse_pivot;


	insert into profile_base.profile_search_parse_pivot(search_id, sources, category_name, filter_value)
	with temp_profile_search as (
	select id_account as account_id,
		   id as search_id,
		   (select string_agg(employment_Types, ', ')
		   from (
						select id,
							   regexp_split_to_table(replace(replace(cast(additional_filters_json as jsonb) ->> 'employmentTypes', '[', ''), ']', ''), ', ') as employment_Types
						from imp_employer.profile_search
						where id = ps.id
						order by employment_Types ) as d ) as employment_Types,

		   cast(cast(cast(additional_filters_json as jsonb) ->> 'age' as jsonb) ->> 'AgeTo' as int) as Age_To,
		   cast(cast(cast(additional_filters_json as jsonb) ->> 'age' as jsonb) ->> 'AgeFrom' as int) as Age_From,
		   cast(cast(additional_filters_json as jsonb) ->> 'gender' as smallint) as gender,
		   cast(cast(cast(additional_filters_json as jsonb) ->> 'salary' as jsonb) ->> 'SalaryFrom' as float) as Salary_From,
		   cast(cast(cast(additional_filters_json as jsonb) ->> 'salary' as jsonb) ->> 'SalaryTo' as float) as Salary_To,
		   cast(cast(additional_filters_json as jsonb) ->> 'salary' as jsonb) ->> 'CurrencyId' as Currency_Id,
		   cast(cast(cast(additional_filters_json as jsonb) ->> 'salary' as jsonb) ->> 'Rate' as int) as Rate,
		   sources
	from imp_employer.profile_search ps
	where additional_filters_json <> '{}')
	select search_id, sources, f.category_name, f.filter_value
	from (
		  select search_id,
				 sources,
				 unnest(
						 array ['employment_types', 'age_to', 'age_from', 'gender', 'salary_from', 'salary_to', 'currency_id', 'rate'])                                       as category_name,
				 unnest(
						 array [employment_types, age_to::varchar, age_from::varchar, gender::varchar, salary_from::varchar, salary_to::varchar, currency_id, rate::varchar]) as filter_value
		  from temp_profile_search
		  order by search_id
		  ) as f
	where f.filter_value is not null;


end;
$$;

alter procedure profile_base.insert_profile_search_parse_pivot() owner to postgres;

