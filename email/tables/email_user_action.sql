select es.date_diff as sent_datediff,
        es.country,
        es.id_message,
        count(distinct case when sja.type in (13,19,21,34) then sja.id_jdp end) as call,
        count(distinct case when sja.type in (30,2,15) and sja.flags&1 = 1 then sja.id_jdp end) as apply,
        3*0.27*0.5*count(distinct case when sjd.job_id_project = -1 and sign(sjd.flags & 256) = 1 and sja.type in (13, 19, 21, 34) then sjd.id end) + 0.27*0.5*count(distinct case when sjd.job_id_project = -1 and sign(sjd.flags & 256) = 0 and sja.type in (13, 19, 21, 34) then sjd.id end) + 3*count(distinct case when sjd.job_id_project = -1 and sign(sjd.flags & 256) = 1 and sja.type in (30, 2, 15)  and sja.flags & 1 = 1 then sjd.id end) + count(distinct case when sjd.job_id_project = -1 and sign(sjd.flags & 256) = 0 and sja.type in (30, 2, 15)  and sja.flags & 1 = 1 then sjd.id end) as bp,
        count(distinct case when sja.type in (1,29,13,19,21,34)  then sjd.id end) as ith_jdp
from imp.email_sent es
inner join imp.session_alertview_message sam on sam.id_message = es.id_message and sam.country = es.country
inner join imp.session_jdp sjd on sjd.id_session = sam.id_session and sjd.country = sam.country
inner join imp.session_jdp_action sja on sja.id_jdp = sjd.id and sja.country = sjd.country and sja.type in (1,2,13,15,19,21,29,30,34)
where es.country <= 11 and es.date_diff between ${DT_NOW} - 2 and ${DT_NOW} - 1
group by es.date_diff,
         es.country,
         es.id_message
union all
select es.date_diff as sent_datediff,
       es.country as country_id,
       es.id_message as message_id,
       count(distinct case when sja.type in (13,19,21,34) then sja.id_jdp end) as call_cnt,
       count(distinct case when sja.type in (30,2,15) and sja.flags&1 = 1 then sja.id_jdp end) as apply_cnt,
       3*0.27*0.5*count(distinct case when sjd.job_id_project = -1 and sign(sjd.flags & 256) = 1 and sja.type in (13, 19, 21, 34) then sjd.id end) + 0.27*0.5*count(distinct case when sjd.job_id_project = -1 and sign(sjd.flags & 256) = 0 and sja.type in (13, 19, 21, 34) then sjd.id end) + 3*count(distinct case when sjd.job_id_project = -1 and sign(sjd.flags & 256) = 1 and sja.type in (30, 2, 15)  and sja.flags & 1 = 1 then sjd.id end) + count(distinct case when sjd.job_id_project = -1 and sign(sjd.flags & 256) = 0 and sja.type in (30, 2, 15)  and sja.flags & 1 = 1 then sjd.id end) as bp,
       count(distinct case when sja.type in (1,29,13,19,21,34)  then sjd.id end) as jdp_ith
from imp.email_sent es
inner join imp.session_click_message scm on scm.id_message = es.id_message and scm.country = es.country
inner join imp.session_jdp sjd on sjd.id_session = scm.id_session and sjd.country = scm.country
inner join imp.session_jdp_action sja on sja.id_jdp = sjd.id and sja.country = sjd.country and sja.type in (1,2,13,15,19,21,29,30,34)
where es.country <= 11 and es.letter_type in (12,15) and es.date_diff between ${DT_NOW} - 2 and ${DT_NOW} - 1
group by es.date_diff,
         es.country,
         es.id_message;
