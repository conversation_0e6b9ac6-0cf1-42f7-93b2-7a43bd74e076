-- auto-generated definition
create table email_agg_daily
(
    country_id           smallint not null,
    action_date          date     not null,
    alertview_cnt        integer,
    click_cnt            integer,
    jdp_cnt              integer,
    jdp_away_cnt         integer,
    jdp_away_revenue     numeric(18, 4),
    serp_away_cnt        integer,
    serp_away_revenue    numeric(18, 4),
    lt8_away             integer,
    lt8_revenue          numeric(18, 4),
    lt8_jdp              integer,
    lt8_jdp_away         integer,
    lt8_jdp_away_revenue numeric(18, 4),
    lt1_sent             integer,
    lt8_sent             integer,
    constraint pk_dashboard_id
        primary key (country_id, action_date)
);

alter table email_agg_daily
    owner to postgres;

grant select on email_agg_daily to readonly;

grant select on email_agg_daily to writeonly_product;

