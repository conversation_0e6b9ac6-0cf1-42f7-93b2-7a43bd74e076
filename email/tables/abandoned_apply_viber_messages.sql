create temp table temp_abandoned_apply_viber_messages_session_test as
        select sj.country,
               sj.id as id_jdp,
               sj.date_diff,
               sj.id_account,
               sj.id_session,
               sj.uid_job,
               max(case when sja.type in (13, 19, 21, 34) then 1 else 0 end) as is_call_click,
               max(case when (sja.type=2 and sj.flags & 1=1) then 1 else 0 end) as  is_apply
        from imp.session_jdp sj
        left join imp.session_jdp_action sja on sj.country = sja.country and sj.date_diff = sja.date_diff and sj.id = sja.id_jdp
        where sj.flags & 8 = 8
            and sj.flags & 128 = 128
            and sj.country = 1
            and sja.country = 1
            and sj.job_id_project = -1
            and sj.date_diff >= 44173
            and sja.date_diff >= 44173
        group by sj.country, sj.id, sj.date_diff, sj.id_account, sj.id_session, sj.uid_job;

        truncate table email.abandoned_apply_viber_messages;

        with abandoned_apply_messages as (
        select vms.country,
               vms.date_diff,
               cast(vms.date as date) as date_sent_message,
               vms.id_account,
               vms.status,
               esaa.uid_job
        from imp.viber_message_sent vms
        join imp.email_sent_abandoned_apply esaa on vms.id_message=esaa.id_message and vms.id_account=esaa.id_account and vms.country=esaa.country
        where vms.country = 1 and message_type = 1)

        select aam.country,
               aam.date_diff,
               aam.date_sent_message,
               aam.id_account,
               aam.status,
               aam.uid_job,
               aba_ses.id_session,
               aba_ses.id_jdp,
               max(case when aba_ses.uid_job is not null then 1 else 0 end) as has_link_on_jdp,
               max(case when aba_ses.uid_job is not null and is_apply = 1 then 1 else 0 end) as has_apply_on_the_same_jdp,
               max(case when aba_ses.uid_job is not null and is_apply = 0 and is_call_click = 1 then 1
                        else 0 end) as has_call_on_the_same_jdp
        from abandoned_apply_messages aam
        left join temp_abandoned_apply_viber_messages_session_test aba_ses on aam.country = aba_ses.country and aam.id_account = aba_ses.id_account and aam.uid_job = aba_ses.uid_job
        group by aam.country, aam.date_diff, aam.date_sent_message, aam.id_account, aam.status, aam.status, aam.uid_job, aba_ses.id_session, aba_ses.id_jdp;
