select distinct sent_message_datediff,
        sent_message_date,
        count(profile_id) over (partition by sent_message_datediff, country_id) as message_sent_daily_cnt,
        sum(case when status in (3,4,5) then 1 else 0 end) over (partition by sent_message_datediff, country_id) as message_delivered_daily_cnt,
        sum(has_link_visit) over (partition by sent_message_datediff, country_id) as has_link_visit_cnt,
        sum(has_submit_profile) over (partition by sent_message_datediff, country_id) as has_submit_profile_cnt
from email.abandoned_profile_viber_messages;
