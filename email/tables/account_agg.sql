SET NOCOUNT ON;
        
select format(account.date_add, 'yyyy-MM-dd')                                         AS account_date,
       account.send_interval                                                          AS account_send_interval,
       account_info.id_unsub_type                                                     AS account_unsub_type,
       datediff(day, format(account.date_add, 'yyyy-MM-dd'), account_info.unsub_date) as account_unsub_date_period,
       u_traffic_source.channel                                                       as account_channel,
       u_traffic_source.name                                                          AS account_source_name,
       u_traffic_source.is_paid                                                       AS account_source_is_paid,
       account.source,
       count(distinct account.id)                                                     AS account_cnt,
       sum(account_revenue.total_revenue)                                             as account_revenue,
       count(distinct case
                          when account_contact.verify_date is not null
                              then account_contact.id_account end)                    as varified_account_cnt
from dbo.account with (nolock)
         left join dbo.account_info with (nolock) on account.id = account_info.id_account
         left join dbo.account_contact with (nolock) on account.id = account_contact.id_account
         left join dbo.u_traffic_source with (nolock) on account_info.id_traf_src = u_traffic_source.id
         left join dbo.account_revenue with (nolock) on account.id = account_revenue.id_account
group by format(account.date_add, 'yyyy-MM-dd'),
         account.send_interval,
         account_info.id_unsub_type,
         datediff(day, format(account.date_add, 'yyyy-MM-dd'), account_info.unsub_date),
         u_traffic_source.channel,
         u_traffic_source.name,
         u_traffic_source.is_paid,
         account.source
;
