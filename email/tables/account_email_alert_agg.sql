declare
              @country_id int = ${country_id};

select @country_id as country_id,
       cast(email_alert.date_add as date) as alert_date,
       case when email_alert.id_type_unsub is null and   account_info.id_unsub_type is null then 1 else 0 end as is_active_alert,
       email_alert.id_alert_type as id_alert_type,
       email_alert.id_type_unsub as id_type_unsub,
       account.send_interval as account_send_interval,
       count(email_alert.id) as alert_cnt
from dbo.email_alert with(nolock)
 join dbo.account  with(nolock) on  email_alert.id_account = account.id
 join dbo.account_info  with(nolock) on account.id = account_info.id_account
group by cast(email_alert.date_add as date),
         case when email_alert.id_type_unsub is null and account_info.id_unsub_type is null then 1 else 0 end,
         email_alert.id_alert_type ,
         email_alert.id_type_unsub,
         account.send_interval;