declare @date_diff_start int = datediff(day, 0, dbo.ex_getdate() - 1),
		@date_diff_end int = datediff(day, 0, dbo.ex_getdate() - 1),
		@country varchar(2)
		
set @country = substring(DB_NAME(), 5, 2)

begin try

select @country as country,
	   cast(a.session_day as datetime) as session_day,
	   a.returned_user,
	   a.session_count,
	   a.serp_clicks_away_mob_premium_count,
	   a.serp_clicks_away_desktop_premium_count,
	   a.serp_clicks_jdp_mob_dte_premium_count,
	   a.serp_clicks_jdp_desktop_dte_premium_count,
	   a.serp_clicks_jdp_mob_notdte_premium_count,
	   a.serp_clicks_jdp_desktop_notdte_premium_count,
	   a.serp_clicks_away_mob_notpremium_count,
	   a.serp_clicks_away_desktop_notpremium_count,
	   a.serp_clicks_jdp_mob_dte_notpremium_count,
	   a.serp_clicks_jdp_desktop_dte_notpremium_count,
	   a.serp_clicks_jdp_mob_notdte_notpremium_count,
	   a.serp_clicks_jdp_desktop_notdte_notpremium_count,
	   a.jdp_mob_serp_dte_premium_count,
	   a.jdp_desktop_serp_dte_premium_count,
	   a.jdp_mob_notserp_dte_premium_count,
	   a.jdp_desktop_notserp_dte_premium_count,
	   a.jdp_mob_serp_dte_notpremium_count,
	   a.jdp_desktop_serp_dte_notpremium_count,
	   a.jdp_mob_notserp_dte_notpremium_count,
	   a.jdp_desktop_notserp_dte_notpremium_count,
	   a.jdp_mob_serp_notdte_count,
	   a.jdp_desktop_serp_notdte_count,
	   a.jdp_mob_notserp_notdte_count,
	   a.jdp_desktop_notserp_notdte_count,
	   a.itc_mob_serp_dte_premium,
	   a.itc_desktop_serp_dte_premium,
	   a.itc_mob_notserp_dte_premium,
	   a.itc_desktop_notserp_dte_premium,
	   a.itc_mob_serp_dte_notpremium,
	   a.itc_desktop_serp_dte_notpremium,
	   a.itc_mob_notserp_dte_notpremium,
	   a.itc_desktop_notserp_dte_notpremium,
	   a.itc_mob_serp_notdte,
	   a.itc_desktop_serp_notdte,
	   a.itc_mob_notserp_notdte,
	   a.itc_desktop_notserp_notdte,
	   a.calls_mob_serp_dte_premium,
	   a.calls_desktop_serp_dte_premium,
	   a.calls_mob_notserp_dte_premium,
	   a.calls_desktop_notserp_dte_premium,
	   a.calls_mob_serp_dte_notpremium,
	   a.calls_desktop_serp_dte_notpremium,
	   a.calls_mob_notserp_dte_notpremium,
	   a.calls_desktop_notserp_dte_notpremium,
	   a.calls_mob_serp_notdte,
	   a.calls_desktop_serp_notdte,
	   a.calls_mob_notserp_notdte,
	   a.calls_desktop_notserp_notdte,
	   a.apply_submit_mob_serp_dte_premium,
	   a.apply_submit_desktop_serp_dte_premium,
	   a.apply_submit_mob_notserp_dte_premium,
	   a.apply_submit_desktop_notserp_dte_premium,
	   a.apply_submit_mob_serp_dte_notpremium,
	   a.apply_submit_desktop_serp_dte_notpremium,
	   a.apply_submit_mob_notserp_dte_notpremium,
	   a.apply_submit_desktop_notserp_dte_notpremium,
	   a.apply_submit_mob_serp_notdte,
	   a.apply_submit_desktop_serp_notdte,
	   a.apply_submit_mob_notserp_notdte,
	   a.apply_submit_desktop_notserp_notdte,
	   a.click_away_mob_serp_dte_premium,
	   a.click_away_desktop_serp_dte_premium,
	   a.click_away_mob_notserp_dte_premium,
	   a.click_away_desktop_notserp_dte_premium,
	   a.click_away_mob_serp_dte_notpremium,
	   a.click_away_desktop_serp_dte_notpremium,
	   a.click_away_mob_notserp_dte_notpremium,
	   a.click_away_desktop_notserp_dte_notpremium,
	   a.click_away_mob_serp_notdte,
	   a.click_away_desktop_serp_notdte,
	   a.click_away_mob_notserp_notdte,
	   a.click_away_desktop_notserp_notdte,
	   a.click_apply_mob_serp_dte_premium,
	   a.click_apply_desktop_serp_dte_premium,
	   a.click_apply_mob_notserp_dte_premium,
	   a.click_apply_desktop_notserp_dte_premium,
	   a.click_apply_mob_serp_dte_notpremium,
	   a.click_apply_desktop_serp_dte_notpremium,
	   a.click_apply_mob_notserp_dte_notpremium,
	   a.click_apply_desktop_notserp_dte_notpremium,
	   a.click_apply_mob_serp_notdte,
	   a.click_apply_desktop_serp_notdte,
	   a.click_apply_mob_notserp_notdte,
	   a.click_apply_desktop_notserp_notdte,
	   count(a.user_id) as users
from ( select s.cookie_label as [user_id],
			  cast(s.start_date as date) as session_day,
			  min(sign(s.flags & 2)) as returned_user,
			  count(distinct s.id) as session_count,
			  count(distinct case when sc.job_destination = 1 and sign(sc.flags & 8) = 1 and sign(sc.flags & 128) = 1 then sc.id else null end) as serp_clicks_away_mob_premium_count,
			  count(distinct case when sc.job_destination = 1 and sign(sc.flags & 8) = 0 and sign(sc.flags & 128) = 1 then sc.id else null end) as serp_clicks_away_desktop_premium_count,
			  count(distinct case when sc.job_destination in (2,3,4) and sign(sc.flags & 8) = 1 and sc.id_project = -1 and sign(sc.flags & 128) = 1 then sc.id else null end) as serp_clicks_jdp_mob_dte_premium_count,
			  count(distinct case when sc.job_destination in (2,3,4) and sign(sc.flags & 8) = 0 and sc.id_project = -1 and sign(sc.flags & 128) = 1 then sc.id else null end) as serp_clicks_jdp_desktop_dte_premium_count,
			  count(distinct case when sc.job_destination in (2,3,4) and sign(sc.flags & 8) = 1 and sc.id_project <> -1 and sign(sc.flags & 128) = 1 then sc.id else null end) as serp_clicks_jdp_mob_notdte_premium_count,
			  count(distinct case when sc.job_destination in (2,3,4) and sign(sc.flags & 8) = 0 and sc.id_project <> -1 and sign(sc.flags & 128) = 1 then sc.id else null end) as serp_clicks_jdp_desktop_notdte_premium_count,
			  count(distinct case when sc.job_destination = 1 and sign(sc.flags & 8) = 1 and sign(sc.flags & 128) = 0 then sc.id else null end) as serp_clicks_away_mob_notpremium_count,
			  count(distinct case when sc.job_destination = 1 and sign(sc.flags & 8) = 0 and sign(sc.flags & 128) = 0 then sc.id else null end) as serp_clicks_away_desktop_notpremium_count,
			  count(distinct case when sc.job_destination in (2,3,4) and sign(sc.flags & 8) = 1 and sc.id_project = -1 and sign(sc.flags & 128) = 0 then sc.id else null end) as serp_clicks_jdp_mob_dte_notpremium_count,
			  count(distinct case when sc.job_destination in (2,3,4) and sign(sc.flags & 8) = 0 and sc.id_project = -1 and sign(sc.flags & 128) = 0 then sc.id else null end) as serp_clicks_jdp_desktop_dte_notpremium_count,
			  count(distinct case when sc.job_destination in (2,3,4) and sign(sc.flags & 8) = 1 and sc.id_project <> -1 and sign(sc.flags & 128) = 0 then sc.id else null end) as serp_clicks_jdp_mob_notdte_notpremium_count,
			  count(distinct case when sc.job_destination in (2,3,4) and sign(sc.flags & 8) = 0 and sc.id_project <> -1 and sign(sc.flags & 128) = 0 then sc.id else null end) as serp_clicks_jdp_desktop_notdte_notpremium_count,
			  count(distinct case when sjd.flags & 1 = 1 and sc.id = sjd.id_click and sc.uid_job = sjd.uid_job and sc.id_project = -1 and sign(sjd.flags & 256) = 1 then sjd.id else null end) as jdp_mob_serp_dte_premium_count,
			  count(distinct case when sjd.flags & 1 = 0 and sc.id = sjd.id_click and sc.uid_job = sjd.uid_job and sc.id_project = -1 and sign(sjd.flags & 256) = 1 then sjd.id else null end) as jdp_desktop_serp_dte_premium_count,
			  count(distinct case when sjd.flags & 1 = 1 and (sc.id <> sjd.id_click or sc.uid_job <> sjd.uid_job) and sc.id_project = -1 and sign(sjd.flags & 256) = 1 then sjd.id else null end) as jdp_mob_notserp_dte_premium_count,
			  count(distinct case when sjd.flags & 1 = 0 and (sc.id <> sjd.id_click or sc.uid_job <> sjd.uid_job) and sc.id_project = -1 and sign(sjd.flags & 256) = 1 then sjd.id else null end) as jdp_desktop_notserp_dte_premium_count,
			  count(distinct case when sjd.flags & 1 = 1 and sc.id = sjd.id_click and sc.uid_job = sjd.uid_job and sc.id_project = -1 and sign(sjd.flags & 256) = 0 then sjd.id else null end) as jdp_mob_serp_dte_notpremium_count,
			  count(distinct case when sjd.flags & 1 = 0 and sc.id = sjd.id_click and sc.uid_job = sjd.uid_job and sc.id_project = -1 and sign(sjd.flags & 256) = 0 then sjd.id else null end) as jdp_desktop_serp_dte_notpremium_count,
			  count(distinct case when sjd.flags & 1 = 1 and (sc.id <> sjd.id_click or sc.uid_job <> sjd.uid_job) and sc.id_project = -1 and sign(sjd.flags & 256) = 0 then sjd.id else null end) as jdp_mob_notserp_dte_notpremium_count,
			  count(distinct case when sjd.flags & 1 = 0 and (sc.id <> sjd.id_click or sc.uid_job <> sjd.uid_job) and sc.id_project = -1 and sign(sjd.flags & 256) = 0 then sjd.id else null end) as jdp_desktop_notserp_dte_notpremium_count,
			  count(distinct case when sjd.flags & 1 = 1 and sc.id = sjd.id_click and sc.uid_job = sjd.uid_job and sc.id_project <> -1 then sjd.id else null end) as jdp_mob_serp_notdte_count,
			  count(distinct case when sjd.flags & 1 = 0 and sc.id = sjd.id_click and sc.uid_job = sjd.uid_job and sc.id_project <> -1 then sjd.id else null end) as jdp_desktop_serp_notdte_count,
			  count(distinct case when sjd.flags & 1 = 1 and (sc.id <> sjd.id_click or sc.uid_job <> sjd.uid_job) and sc.id_project <> -1 then sjd.id else null end) as jdp_mob_notserp_notdte_count,
			  count(distinct case when sjd.flags & 1 = 0 and (sc.id <> sjd.id_click or sc.uid_job <> sjd.uid_job) and sc.id_project <> -1 then sjd.id else null end) as jdp_desktop_notserp_notdte_count,
			  count(distinct case when sja.type in (1, 29, 13, 19, 21, 34) and sjd.flags & 1 = 1 and sc.id = sjd.id_click and sc.uid_job = sjd.uid_job and sc.id_project = -1 and sign(sjd.flags & 256) = 1 then sjd.id end) as itc_mob_serp_dte_premium,
			  count(distinct case when sja.type in (1, 29, 13, 19, 21, 34) and sjd.flags & 1 = 0 and sc.id = sjd.id_click and sc.uid_job = sjd.uid_job and sc.id_project = -1 and sign(sjd.flags & 256) = 1 then sjd.id end) as itc_desktop_serp_dte_premium,
			  count(distinct case when sja.type in (1, 29, 13, 19, 21, 34) and sjd.flags & 1 = 1 and (sc.id <> sjd.id_click or sc.uid_job <> sjd.uid_job) and sc.id_project = -1 and sign(sjd.flags & 256) = 1 then sjd.id end) as itc_mob_notserp_dte_premium,
			  count(distinct case when sja.type in (1, 29, 13, 19, 21, 34) and sjd.flags & 1 = 0 and (sc.id <> sjd.id_click or sc.uid_job <> sjd.uid_job) and sc.id_project = -1 and sign(sjd.flags & 256) = 1 then sjd.id end) as itc_desktop_notserp_dte_premium,
			  count(distinct case when sja.type in (1, 29, 13, 19, 21, 34) and sjd.flags & 1 = 1 and sc.id = sjd.id_click and sc.uid_job = sjd.uid_job and sc.id_project = -1 and sign(sjd.flags & 256) = 0 then sjd.id end) as itc_mob_serp_dte_notpremium,
			  count(distinct case when sja.type in (1, 29, 13, 19, 21, 34) and sjd.flags & 1 = 0 and sc.id = sjd.id_click and sc.uid_job = sjd.uid_job and sc.id_project = -1 and sign(sjd.flags & 256) = 0 then sjd.id end) as itc_desktop_serp_dte_notpremium,
			  count(distinct case when sja.type in (1, 29, 13, 19, 21, 34) and sjd.flags & 1 = 1 and (sc.id <> sjd.id_click or sc.uid_job <> sjd.uid_job) and sc.id_project = -1 and sign(sjd.flags & 256) = 0 then sjd.id end) as itc_mob_notserp_dte_notpremium,
			  count(distinct case when sja.type in (1, 29, 13, 19, 21, 34) and sjd.flags & 1 = 0 and (sc.id <> sjd.id_click or sc.uid_job <> sjd.uid_job) and sc.id_project = -1 and sign(sjd.flags & 256) = 0 then sjd.id end) as itc_desktop_notserp_dte_notpremium,
			  count(distinct case when sja.type in (1, 29, 13, 19, 21, 34) and sjd.flags & 1 = 1 and sc.id = sjd.id_click and sc.uid_job = sjd.uid_job and sc.id_project <> -1 then sjd.id end) as itc_mob_serp_notdte,
			  count(distinct case when sja.type in (1, 29, 13, 19, 21, 34) and sjd.flags & 1 = 0 and sc.id = sjd.id_click and sc.uid_job = sjd.uid_job and sc.id_project <> -1 then sjd.id end) as itc_desktop_serp_notdte,
			  count(distinct case when sja.type in (1, 29, 13, 19, 21, 34) and sjd.flags & 1 = 1 and (sc.id <> sjd.id_click or sc.uid_job <> sjd.uid_job) and sc.id_project <> -1 then sjd.id end) as itc_mob_notserp_notdte,
			  count(distinct case when sja.type in (1, 29, 13, 19, 21, 34) and sjd.flags & 1 = 0 and (sc.id <> sjd.id_click or sc.uid_job <> sjd.uid_job) and sc.id_project <> -1 then sjd.id end) as itc_desktop_notserp_notdte,
			  count(distinct case when sja.type in (13, 19, 21, 34) and sjd.flags & 1 = 1 and sc.id = sjd.id_click and sc.uid_job = sjd.uid_job and sc.id_project = -1 and sign(sjd.flags & 256) = 1 then sjd.id end) as calls_mob_serp_dte_premium,
			  count(distinct case when sja.type in (13, 19, 21, 34) and sjd.flags & 1 = 0 and sc.id = sjd.id_click and sc.uid_job = sjd.uid_job and sc.id_project = -1 and sign(sjd.flags & 256) = 1 then sjd.id end) as calls_desktop_serp_dte_premium,
			  count(distinct case when sja.type in (13, 19, 21, 34) and sjd.flags & 1 = 1 and (sc.id <> sjd.id_click or sc.uid_job <> sjd.uid_job) and sc.id_project = -1 and sign(sjd.flags & 256) = 1 then sjd.id end) as calls_mob_notserp_dte_premium,
			  count(distinct case when sja.type in (13, 19, 21, 34) and sjd.flags & 1 = 0 and (sc.id <> sjd.id_click or sc.uid_job <> sjd.uid_job) and sc.id_project = -1 and sign(sjd.flags & 256) = 1 then sjd.id end) as calls_desktop_notserp_dte_premium,
			  count(distinct case when sja.type in (13, 19, 21, 34) and sjd.flags & 1 = 1 and sc.id = sjd.id_click and sc.uid_job = sjd.uid_job and sc.id_project = -1 and sign(sjd.flags & 256) = 0 then sjd.id end) as calls_mob_serp_dte_notpremium,
			  count(distinct case when sja.type in (13, 19, 21, 34) and sjd.flags & 1 = 0 and sc.id = sjd.id_click and sc.uid_job = sjd.uid_job and sc.id_project = -1 and sign(sjd.flags & 256) = 0 then sjd.id end) as calls_desktop_serp_dte_notpremium,
			  count(distinct case when sja.type in (13, 19, 21, 34) and sjd.flags & 1 = 1 and (sc.id <> sjd.id_click or sc.uid_job <> sjd.uid_job) and sc.id_project = -1 and sign(sjd.flags & 256) = 0 then sjd.id end) as calls_mob_notserp_dte_notpremium,
			  count(distinct case when sja.type in (13, 19, 21, 34) and sjd.flags & 1 = 0 and (sc.id <> sjd.id_click or sc.uid_job <> sjd.uid_job) and sc.id_project = -1 and sign(sjd.flags & 256) = 0 then sjd.id end) as calls_desktop_notserp_dte_notpremium,
			  count(distinct case when sja.type in (13, 19, 21, 34) and sjd.flags & 1 = 1 and sc.id = sjd.id_click and sc.uid_job = sjd.uid_job and sc.id_project <> -1 then sjd.id end) as calls_mob_serp_notdte,
			  count(distinct case when sja.type in (13, 19, 21, 34) and sjd.flags & 1 = 0 and sc.id = sjd.id_click and sc.uid_job = sjd.uid_job and sc.id_project <> -1 then sjd.id end) as calls_desktop_serp_notdte,
			  count(distinct case when sja.type in (13, 19, 21, 34) and sjd.flags & 1 = 1 and (sc.id <> sjd.id_click or sc.uid_job <> sjd.uid_job) and sc.id_project <> -1 then sjd.id end) as calls_mob_notserp_notdte,
			  count(distinct case when sja.type in (13, 19, 21, 34) and sjd.flags & 1 = 0 and (sc.id <> sjd.id_click or sc.uid_job <> sjd.uid_job) and sc.id_project <> -1 then sjd.id end) as calls_desktop_notserp_notdte,
			  count(distinct case when sja.type in (30, 2, 15) and sjd.flags & 1 = 1 and sc.id = sjd.id_click and sc.uid_job = sjd.uid_job and sc.id_project = -1 and sign(sjd.flags & 256) = 1 then sjd.id end) as apply_submit_mob_serp_dte_premium,
			  count(distinct case when sja.type in (30, 2, 15) and sjd.flags & 1 = 0 and sc.id = sjd.id_click and sc.uid_job = sjd.uid_job and sc.id_project = -1 and sign(sjd.flags & 256) = 1 then sjd.id end) as apply_submit_desktop_serp_dte_premium,
			  count(distinct case when sja.type in (30, 2, 15) and sjd.flags & 1 = 1 and (sc.id <> sjd.id_click or sc.uid_job <> sjd.uid_job) and sc.id_project = -1 and sign(sjd.flags & 256) = 1 then sjd.id end) as apply_submit_mob_notserp_dte_premium,
			  count(distinct case when sja.type in (30, 2, 15) and sjd.flags & 1 = 0 and (sc.id <> sjd.id_click or sc.uid_job <> sjd.uid_job) and sc.id_project = -1 and sign(sjd.flags & 256) = 1 then sjd.id end) as apply_submit_desktop_notserp_dte_premium,
			  count(distinct case when sja.type in (30, 2, 15) and sjd.flags & 1 = 1 and sc.id = sjd.id_click and sc.uid_job = sjd.uid_job and sc.id_project = -1 and sign(sjd.flags & 256) = 0 then sjd.id end) as apply_submit_mob_serp_dte_notpremium,
			  count(distinct case when sja.type in (30, 2, 15) and sjd.flags & 1 = 0 and sc.id = sjd.id_click and sc.uid_job = sjd.uid_job and sc.id_project = -1 and sign(sjd.flags & 256) = 0 then sjd.id end) as apply_submit_desktop_serp_dte_notpremium,
			  count(distinct case when sja.type in (30, 2, 15) and sjd.flags & 1 = 1 and (sc.id <> sjd.id_click or sc.uid_job <> sjd.uid_job) and sc.id_project = -1 and sign(sjd.flags & 256) = 0 then sjd.id end) as apply_submit_mob_notserp_dte_notpremium,
			  count(distinct case when sja.type in (30, 2, 15) and sjd.flags & 1 = 0 and (sc.id <> sjd.id_click or sc.uid_job <> sjd.uid_job) and sc.id_project = -1 and sign(sjd.flags & 256) = 0 then sjd.id end) as apply_submit_desktop_notserp_dte_notpremium,
			  count(distinct case when sja.type in (30, 2, 15) and sjd.flags & 1 = 1 and sc.id = sjd.id_click and sc.uid_job = sjd.uid_job and sc.id_project <> -1 then sjd.id end) as apply_submit_mob_serp_notdte,
			  count(distinct case when sja.type in (30, 2, 15) and sjd.flags & 1 = 0 and sc.id = sjd.id_click and sc.uid_job = sjd.uid_job and sc.id_project <> -1 then sjd.id end) as apply_submit_desktop_serp_notdte,
			  count(distinct case when sja.type in (30, 2, 15) and sjd.flags & 1 = 1 and (sc.id <> sjd.id_click or sc.uid_job <> sjd.uid_job) and sc.id_project <> -1 then sjd.id end) as apply_submit_mob_notserp_notdte,
			  count(distinct case when sja.type in (30, 2, 15) and sjd.flags & 1 = 0 and (sc.id <> sjd.id_click or sc.uid_job <> sjd.uid_job) and sc.id_project <> -1 then sjd.id end) as apply_submit_desktop_notserp_notdte,
			  count(distinct case when sja.type = 1 and sja.flags & 2 = 0 and sjd.flags & 1 = 1 and sc.id = sjd.id_click and sc.uid_job = sjd.uid_job and sc.id_project = -1 and sign(sjd.flags & 256) = 1 then sjd.id end) as click_away_mob_serp_dte_premium,
			  count(distinct case when sja.type = 1 and sja.flags & 2 = 0 and sjd.flags & 1 = 0 and sc.id = sjd.id_click and sc.uid_job = sjd.uid_job and sc.id_project = -1 and sign(sjd.flags & 256) = 1 then sjd.id end) as click_away_desktop_serp_dte_premium,
			  count(distinct case when sja.type = 1 and sja.flags & 2 = 0 and sjd.flags & 1 = 1 and (sc.id <> sjd.id_click or sc.uid_job <> sjd.uid_job) and sc.id_project = -1 and sign(sjd.flags & 256) = 1 then sjd.id end) as click_away_mob_notserp_dte_premium,
			  count(distinct case when sja.type = 1 and sja.flags & 2 = 0 and sjd.flags & 1 = 0 and (sc.id <> sjd.id_click or sc.uid_job <> sjd.uid_job) and sc.id_project = -1 and sign(sjd.flags & 256) = 1 then sjd.id end) as click_away_desktop_notserp_dte_premium,
			  count(distinct case when sja.type = 1 and sja.flags & 2 = 0 and sjd.flags & 1 = 1 and sc.id = sjd.id_click and sc.uid_job = sjd.uid_job and sc.id_project = -1 and sign(sjd.flags & 256) = 0 then sjd.id end) as click_away_mob_serp_dte_notpremium,
			  count(distinct case when sja.type = 1 and sja.flags & 2 = 0 and sjd.flags & 1 = 0 and sc.id = sjd.id_click and sc.uid_job = sjd.uid_job and sc.id_project = -1 and sign(sjd.flags & 256) = 0 then sjd.id end) as click_away_desktop_serp_dte_notpremium,
			  count(distinct case when sja.type = 1 and sja.flags & 2 = 0 and sjd.flags & 1 = 1 and (sc.id <> sjd.id_click or sc.uid_job <> sjd.uid_job) and sc.id_project = -1 and sign(sjd.flags & 256) = 0 then sjd.id end) as click_away_mob_notserp_dte_notpremium,
			  count(distinct case when sja.type = 1 and sja.flags & 2 = 0 and sjd.flags & 1 = 0 and (sc.id <> sjd.id_click or sc.uid_job <> sjd.uid_job) and sc.id_project = -1 and sign(sjd.flags & 256) = 0 then sjd.id end) as click_away_desktop_notserp_dte_notpremium,
			  count(distinct case when sja.type = 1 and sja.flags & 2 = 0 and sjd.flags & 1 = 1 and sc.id = sjd.id_click and sc.uid_job = sjd.uid_job and sc.id_project <> -1 then sjd.id end) as click_away_mob_serp_notdte,
			  count(distinct case when sja.type = 1 and sja.flags & 2 = 0 and sjd.flags & 1 = 0 and sc.id = sjd.id_click and sc.uid_job = sjd.uid_job and sc.id_project <> -1 then sjd.id end) as click_away_desktop_serp_notdte,
			  count(distinct case when sja.type = 1 and sja.flags & 2 = 0 and sjd.flags & 1 = 1 and (sc.id <> sjd.id_click or sc.uid_job <> sjd.uid_job) and sc.id_project <> -1 then sjd.id end) as click_away_mob_notserp_notdte,
			  count(distinct case when sja.type = 1 and sja.flags & 2 = 0 and sjd.flags & 1 = 0 and (sc.id <> sjd.id_click or sc.uid_job <> sjd.uid_job) and sc.id_project <> -1 then sjd.id end) as click_away_desktop_notserp_notdte,
			  count(distinct case when sja.type = 1 and sja.flags & 2 = 2 and sjd.flags & 1 = 1 and sc.id = sjd.id_click and sc.uid_job = sjd.uid_job and sc.id_project = -1 and sign(sjd.flags & 256) = 1 then sjd.id end) as click_apply_mob_serp_dte_premium,
			  count(distinct case when sja.type = 1 and sja.flags & 2 = 2 and sjd.flags & 1 = 0 and sc.id = sjd.id_click and sc.uid_job = sjd.uid_job and sc.id_project = -1 and sign(sjd.flags & 256) = 1 then sjd.id end) as click_apply_desktop_serp_dte_premium,
			  count(distinct case when sja.type = 1 and sja.flags & 2 = 2 and sjd.flags & 1 = 1 and (sc.id <> sjd.id_click or sc.uid_job <> sjd.uid_job) and sc.id_project = -1 and sign(sjd.flags & 256) = 1 then sjd.id end) as click_apply_mob_notserp_dte_premium,
			  count(distinct case when sja.type = 1 and sja.flags & 2 = 2 and sjd.flags & 1 = 0 and (sc.id <> sjd.id_click or sc.uid_job <> sjd.uid_job) and sc.id_project = -1 and sign(sjd.flags & 256) = 1 then sjd.id end) as click_apply_desktop_notserp_dte_premium,
			  count(distinct case when sja.type = 1 and sja.flags & 2 = 2 and sjd.flags & 1 = 1 and sc.id = sjd.id_click and sc.uid_job = sjd.uid_job and sc.id_project = -1 and sign(sjd.flags & 256) = 0 then sjd.id end) as click_apply_mob_serp_dte_notpremium,
			  count(distinct case when sja.type = 1 and sja.flags & 2 = 2 and sjd.flags & 1 = 0 and sc.id = sjd.id_click and sc.uid_job = sjd.uid_job and sc.id_project = -1 and sign(sjd.flags & 256) = 0 then sjd.id end) as click_apply_desktop_serp_dte_notpremium,
			  count(distinct case when sja.type = 1 and sja.flags & 2 = 2 and sjd.flags & 1 = 1 and (sc.id <> sjd.id_click or sc.uid_job <> sjd.uid_job) and sc.id_project = -1 and sign(sjd.flags & 256) = 0 then sjd.id end) as click_apply_mob_notserp_dte_notpremium,
			  count(distinct case when sja.type = 1 and sja.flags & 2 = 2 and sjd.flags & 1 = 0 and (sc.id <> sjd.id_click or sc.uid_job <> sjd.uid_job) and sc.id_project = -1 and sign(sjd.flags & 256) = 0 then sjd.id end) as click_apply_desktop_notserp_dte_notpremium,
			  count(distinct case when sja.type = 1 and sja.flags & 2 = 2 and sjd.flags & 1 = 1 and sc.id = sjd.id_click and sc.uid_job = sjd.uid_job and sc.id_project <> -1 then sjd.id end) as click_apply_mob_serp_notdte,
			  count(distinct case when sja.type = 1 and sja.flags & 2 = 2 and sjd.flags & 1 = 0 and sc.id = sjd.id_click and sc.uid_job = sjd.uid_job and sc.id_project <> -1 then sjd.id end) as click_apply_desktop_serp_notdte,
			  count(distinct case when sja.type = 1 and sja.flags & 2 = 2 and sjd.flags & 1 = 1 and (sc.id <> sjd.id_click or sc.uid_job <> sjd.uid_job) and sc.id_project <> -1 then sjd.id end) as click_apply_mob_notserp_notdte,
			  count(distinct case when sja.type = 1 and sja.flags & 2 = 2 and sjd.flags & 1 = 0 and (sc.id <> sjd.id_click or sc.uid_job <> sjd.uid_job) and sc.id_project <> -1 then sjd.id end) as click_apply_desktop_notserp_notdte
	   from session s with (nolock)
	   left join session_click sc with (nolock) on sc.id_session = s.id and sc.date_diff = s.date_diff
	   left join session_jdp sjd with (nolock) on sc.id_session = sjd.id_session and sc.date_diff = sjd.date_diff
	   left join session_jdp_action sja with (nolock) on sjd.date_diff = sja.date_diff and sjd.id = sja.id_jdp and sja.type in (1, 29, 30, 2, 15, 13, 19, 21, 34)
	   where s.date_diff between @date_diff_start and @date_diff_end and s.flags & 1 = 0 and s.flags & 4 = 0
			 and not exists (select 1
							 from u_traffic_source traffic with(nolock)
							 where traffic.id = s.id_traf_source and (traffic.name like 'LP_am%' or traffic.name like 'LP_val%'))
	   group by s.cookie_label, cast(s.start_date as date) ) a
group by a.session_day, a.returned_user, a.session_count, a.serp_clicks_away_mob_premium_count, a.serp_clicks_away_desktop_premium_count, a.serp_clicks_jdp_mob_dte_premium_count,
		 a.serp_clicks_jdp_desktop_dte_premium_count, a.serp_clicks_jdp_mob_notdte_premium_count, a.serp_clicks_jdp_desktop_notdte_premium_count, a.serp_clicks_away_mob_notpremium_count,
		 a.serp_clicks_away_desktop_notpremium_count, a.serp_clicks_jdp_mob_dte_notpremium_count, a.serp_clicks_jdp_desktop_dte_notpremium_count, a.serp_clicks_jdp_mob_notdte_notpremium_count,
		 a.serp_clicks_jdp_desktop_notdte_notpremium_count, a.jdp_mob_serp_dte_premium_count, a.jdp_desktop_serp_dte_premium_count, a.jdp_mob_notserp_dte_premium_count,
		 a.jdp_desktop_notserp_dte_premium_count, a.jdp_mob_serp_dte_notpremium_count, a.jdp_desktop_serp_dte_notpremium_count, a.jdp_mob_notserp_dte_notpremium_count,
		 a.jdp_desktop_notserp_dte_notpremium_count, a.jdp_mob_serp_notdte_count, a.jdp_desktop_serp_notdte_count, a.jdp_mob_notserp_notdte_count, a.jdp_desktop_notserp_notdte_count,
		 a.itc_mob_serp_dte_premium, a.itc_desktop_serp_dte_premium, a.itc_mob_notserp_dte_premium, a.itc_desktop_notserp_dte_premium, a.itc_mob_serp_dte_notpremium,
		 a.itc_desktop_serp_dte_notpremium, a.itc_mob_notserp_dte_notpremium, a.itc_desktop_notserp_dte_notpremium, a.itc_mob_serp_notdte, a.itc_desktop_serp_notdte,
		 a.itc_mob_notserp_notdte, a.itc_desktop_notserp_notdte, a.calls_mob_serp_dte_premium, a.calls_desktop_serp_dte_premium, a.calls_mob_notserp_dte_premium,
		 a.calls_desktop_notserp_dte_premium, a.calls_mob_serp_dte_notpremium, a.calls_desktop_serp_dte_notpremium, a.calls_mob_notserp_dte_notpremium,
		 a.calls_desktop_notserp_dte_notpremium, a.calls_mob_serp_notdte, a.calls_desktop_serp_notdte, a.calls_mob_notserp_notdte, a.calls_desktop_notserp_notdte,
		 a.apply_submit_mob_serp_dte_premium, a.apply_submit_desktop_serp_dte_premium, a.apply_submit_mob_notserp_dte_premium, a.apply_submit_desktop_notserp_dte_premium,
		 a.apply_submit_mob_serp_dte_notpremium, a.apply_submit_desktop_serp_dte_notpremium, a.apply_submit_mob_notserp_dte_notpremium, a.apply_submit_desktop_notserp_dte_notpremium,
		 a.apply_submit_mob_serp_notdte, a.apply_submit_desktop_serp_notdte, a.apply_submit_mob_notserp_notdte, a.apply_submit_desktop_notserp_notdte, a.click_away_mob_serp_dte_premium,
		 a.click_away_desktop_serp_dte_premium, a.click_away_mob_notserp_dte_premium, a.click_away_desktop_notserp_dte_premium, a.click_away_mob_serp_dte_notpremium,
		 a.click_away_desktop_serp_dte_notpremium, a.click_away_mob_notserp_dte_notpremium, a.click_away_desktop_notserp_dte_notpremium, a.click_away_mob_serp_notdte,
		 a.click_away_desktop_serp_notdte, a.click_away_mob_notserp_notdte, a.click_away_desktop_notserp_notdte, a.click_apply_mob_serp_dte_premium, a.click_apply_desktop_serp_dte_premium,
		 a.click_apply_mob_notserp_dte_premium, a.click_apply_desktop_notserp_dte_premium, a.click_apply_mob_serp_dte_notpremium, a.click_apply_desktop_serp_dte_notpremium,
		 a.click_apply_mob_notserp_dte_notpremium, a.click_apply_desktop_notserp_dte_notpremium, a.click_apply_mob_serp_notdte, a.click_apply_desktop_serp_notdte,
		 a.click_apply_mob_notserp_notdte, a.click_apply_desktop_notserp_notdte
				
end try

begin catch

	select @country as countryError, ERROR_NUMBER() as error_num, ERROR_MESSAGE() as error_mess
	
end catch 
