with temp_ as (
select s.country,
       cast(s.start_date as date) as dt,
       sjd.flags & 1 as device,
       sign(s.flags & 2)  as is_returned,
       sign(sjd.flags & 1024) as no_cv_vacancy,
       sign(sjd.flags & 256) as is_premium,
       case when sjd.job_id_project = -1 then 1 else 0 end as is_ea,
       s.id as id_session,
       sjd.id as id_jdp,
       s.id_traf_source,
       s.cookie_label,
       coalesce(sjd.id_account,0) as id_account,
       case when sjd.flags & 4 = 4 then 1 when sjd.flags & 8 = 8 then 2 else 0 end as jdp_response_type_id,
       count(distinct sc.id) as serp_clicks,
       count(distinct case when sc.flags & 128 = 128 then sc.id end ) as serp_clicks_premium,
       count(distinct case when (sja.type in (1) and sja.flags & 2 = 2)  or sja.type in (29) then sjd.id end) as apply_clicks,
       count(distinct case when sja.type in (30, 2, 15)  and sja.flags & 1 = 1 then sjd.id end) as applies,
       count(distinct case when sja.type in (13, 19, 21, 34) then sjd.id end) as call_clicks,
       count(distinct case when sja.type in (1) and sja.flags & 2 = 0 then sjd.id end) as aways
from imp.session s
join imp.session_jdp sjd on sjd.country = s.country and sjd.id_session = s.id and sjd.date_diff = s.date_diff
left join imp.session_click sc on sc.country = sjd.country and sc.date_diff = sjd.date_diff and sc.id = sjd.id_click and sc.uid_job = sjd.uid_job and sc.flags & 4 = 0 and sc.flags & 1 = 0
left join imp.session_jdp_action sja on sjd.country = sja.country and sjd.date_diff = sja.date_diff and sjd.id = sja.id_jdp and sja.type in (1, 29, 30, 2, 15, 13, 19, 21, 34)
where s.country in (1,7,10,11,14,16,17,20,21,22,23,24,25,26,27,28,29) and s.date_diff = ${R_DT_NOW} - 1 and s.flags & 1 = 0 and s.flags & 4 = 0
group by s.country,
         sjd.flags  & 1,
         sign(s.flags & 2),
         cast(s.start_date as date),
         sign(sjd.flags & 1024),
         s.id,
         sjd.id,
         s.cookie_label,
         coalesce(sjd.id_account,0),
         s.start_date,
         case when sjd.flags & 4 = 4 then 1 when sjd.flags & 8 = 8 then 2 else 0 end,
         sign(sjd.flags & 256),
         case when sjd.job_id_project = -1 then 1 else 0 end,
         s.id_traf_source)

select a.country,
	   a.dt,
	   a.device,
	   a.id_traf_source,
	   a.is_returned,
	   a.no_cv_vacancy,
	   a.is_premium,
	   a.is_ea,
	   a.jdp_response_type_id,
	   count(distinct a.id_session) as sessions,
	   count(distinct a.cookie_label) as clients,
	   sum(serp_clicks_premium) as serp_clicks_premium,
	   sum(serp_clicks) as serp_clicks,
	   count(distinct id_jdp) as views,
	   sum(aways) as aways,
	   sum(call_clicks) as call_clicks,
	   sum(apply_clicks) as apply_clicks,
	   sum(applies) as applies,
	   count(distinct case when call_clicks = 1 or apply_clicks = 1 or aways = 1 then id_jdp end) as responds
from temp_ a
group by a.country,
			a.dt,
			a.device,
			a.is_returned,
			a.no_cv_vacancy,
			a.is_premium,
			a.is_ea,
			a.jdp_response_type_id,
			a.id_traf_source;