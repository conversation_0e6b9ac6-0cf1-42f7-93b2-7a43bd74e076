select dim_c.alpha_2 as country,
       sj.date_diff,
       sj.id                                                          as id_jdp,
       count(distinct case when saa.type in (4) then saa.id end)           as upload_cv_click,
       count(distinct case when saa.type in (28) then saa.id end)          as upload_cv_success,
       count(distinct case when saa.type in (29) then saa.id end)              as upload_cv_error
from imp.session_apply_action saa
join imp.session_jdp sj on sj.id = saa.id_jdp and sj.date_diff = saa.date_diff and sj.country = saa.country
join imp.session s on s.date_diff = sj.date_diff and s.id = sj.id_session and s.country = sj.country
left join dimension.countries dim_c on saa.country = dim_c.id
where saa.type in (4, 28, 29)
  and saa.date >= (current_date - interval '2 day')::timestamp
  and s.flags & 1 = 0
  and s.flags & 4 = 0
and saa.country in (12, 11, 10, 9, 8, 7, 1)
group by dim_c.alpha_2,
         sj.date_diff,
         sj.id;
