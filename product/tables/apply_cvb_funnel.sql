select dim_c.alpha_2 as country,
       sj.date_diff,
       sj.id                                                                    as id_jdp,
       max(saa.date)                                                            as date_click_create,
       max(case when cba.step = 1 then 1 else 0 end)                                   as view_1_step,
       max(case when cba.step = 1 and cba.type = 4 /*NextButton*/ then 1  else 0 end)   as click_next_from_1_step,
       max(case when cba.step = 1 and cba.type = 5 /*PreviousStep*/ then 1  else 0 end) as click_back_from_1_step,
       max(case when cba.step = 2 then 1 else 0  end)                                   as view_2_step,
       max(case when cba.step = 2 and cba.type = 4 /*NextButton*/ then 1  else 0 end)   as click_next_from_2_step,
       max(case when cba.step = 2 and cba.type = 5 /*PreviousStep*/ then 1 else 0  end) as click_back_from_2_step,
       max(case when cba.step = 3 then 1  else 0 end)                                   as view_3_step,
       max(case when cba.step = 3 and cba.type = 5 /*PreviousStep*/ then 1 else 0  end) as click_back_from_3_step,
       max(case when cba.step = 3 and cba.type = 1 then 1 else 0  end)                  as cv_created,
       max(case when cba.step = 3 and cba.type = 1 then cba.date end)           as date_created
from imp.session_apply_action saa
         join imp.session_jdp sj
              on sj.id = saa.id_jdp
                  and sj.date_diff = saa.date_diff
                  and sj.country = saa.country
         join imp.session s
              on s.date_diff = sj.date_diff
                  and s.id = sj.id_session
                  and s.country = sj.country
         left join imp.cv_build_action cba
                   on cba.date_diff = saa.date_diff
                       and cba.id_jdp = saa.id_jdp
                       and cba.country = saa.country
         left join dimension.countries dim_c on dim_c.id = saa.country
where saa.type = 5
  and saa.date >= (current_date - interval '2 day')::timestamp
  and s.flags & 1 = 0
  and s.flags & 4 = 0
and saa.country in (12, 11, 10, 9, 8, 7, 1)
group by dim_c.alpha_2, sj.date_diff,
         sj.id;
