create table product.cv_parsed_2_0
(
	country varchar(2) default ''::character varying not null,
	id bigint default '0'::numeric not null,
	id_session bigint,
	id_jdp bigint,
	date_created timestamp with time zone,
	has_photo smallint,
	city varchar(800),
	name varchar(500),
	phone varchar(50),
	email varchar(500),
	expirience_title_1 varchar(500),
	expirience_name_1 varchar(500),
	expirience_period_id_1 smallint,
	expirience_responsibilities_1 text,
	expirience_title_2 varchar(500),
	expirience_name_2 varchar(500),
	expirience_period_id_2 smallint,
	expirience_responsibilities_2 text,
	expirience_title_3 varchar(500),
	expirience_name_3 varchar(500),
	expirience_period_id_3 smallint,
	expirience_responsibilities_3 text,
	education_degree_id_1 varchar(500),
	education_name_1 varchar(500),
	education_occupation_1 varchar(500),
	education_grad_year_1 smallint,
	education_degree_id_2 varchar(500),
	education_name_2 varchar(500),
	education_occupation_2 varchar(500),
	education_grad_year_2 smallint,
	education_degree_id_3 varchar(500),
	education_name_3 varchar(500),
	education_occupation_3 varchar(500),
	education_grad_year_3 smallint,
	skills_value_1 text,
	hasexperience boolean,
	position boolean,
	yearofbirth smallint,
	relocation_type_id smallint,
	salary_amount integer,
	salary_currency_id smallint,
	salary_period_id smallint,
	employmenttypes_id_1 smallint,
	employmenttypes_id_2 smallint,
	employmenttypes_id_3 smallint,
	employmenttypes_id_4 smallint,
	cvb_version varchar(10) default '2.0'::character varying,
	constraint idx_90144_primary
		primary key (country, id)
);

alter table product.cv_parsed_2_0 owner to postgres;

grant select on product.cv_parsed_2_0 to npo;

grant select on product.cv_parsed_2_0 to readonly;

grant delete, insert, select, update on product.cv_parsed_2_0 to writeonly_product;

grant select on product.cv_parsed_2_0 to ksha;

