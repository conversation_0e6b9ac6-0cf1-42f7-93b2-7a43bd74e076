-- auto-generated definition
create table technology_keyword
(
    technology_id            integer            not null,
    technology_name          varchar(30)        not null,
    keyword_name             varchar(30)        not null,
    is_active_by_title       smallint default 0 not null,
    is_active_by_description smallint default 0,
    tag_type_id              smallint default 1,
    constraint technology_keyword_pkey
        primary key (technology_name, keyword_name)
)
    tablespace data3;

alter table technology_keyword
    owner to dap;

grant select on technology_keyword to readonly;

