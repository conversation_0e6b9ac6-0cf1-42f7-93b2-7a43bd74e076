-- get jobs description for jobs with known technologies
 create table aggregation_partner.job_it
 (country_id smallint not null,
  job_id bigint not null,
  job_similar_group_id bigint not null,
  job_created_datetime timestamp not null,
  job_title varchar(2048) not null,
  company_name varchar(2048) null,
  telephone_list varchar(2048) null,
  email_list varchar(2048) null,
  job_url varchar(2048) not null,
  project_id int,
  project_source_type_id smallint
 )
 ;

alter table aggregation_partner.job_it
	add constraint job_it_pk
		primary key (country_id, job_id);

