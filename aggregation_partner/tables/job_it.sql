declare @country int = (select id from [dwh].postgres.dimension.countries c where c.alpha_2 = substring(db_name(), 5, 2)) ,
        @date_begin date = '2022-05-07', -- yesterday
        @date_end date = '2022-05-08' -- today
      ;


with job_it_with_similar (country_id, job_id, job_similar_group_id, job_created_datetime, job_title, company_name, telephones, emails,
                          job_url, project_id, project_source_type_id, job_in_similar_group_rank)
        as  (
        select jt.country_id,
               jt.job_id,
               j.id_similar_group as job_similar_group_id,
               jt.job_created_datetime,
               j.title                     as job_title,
               j.company_name              as company_name,
               j.telephones                as telephones,
               j.emails                    as emails,
               jr.url   				   as job_url,
               ip.id as project_id,
               ip.source_type as project_source_type_id,
               rank() over (partition by id_similar_group
                   order by
                       case when len(j.telephones) > 5 then 1 else 0 end desc, -- sorted by phone existence
                       case when len(j.emails) > 5 then 1 else 0 end desc, -- sorted by email existence
                       case when ip.source_type = 2 /*from company site*/ then 1 else 0 end desc, -- sorted by source from company site
                       case when ip.source_type = 1 /*job board*/ then 1 else 0 end desc, -- sorted by source from job board
                       case when len(jr.url) > 5 then 1 else 0 end desc, -- sorted by not empty urls
                       date_created, -- sorted by oldest sources
                       jr.uid -- sorted by random feature

                   )                                          as job_in_similar_group_rank
        from [dwh].postgres.aggregation_partner.job_technology jt
                 join job j with (nolock)
                      on j.id = jt.job_id
                 join job_region jr with (nolock)
                      on jr.id_job = j.id
                 join info_project ip with (nolock)
                           on j.id_project = ip.id
       where ip.source_type <> 3 /*Recruitment agency*/
        and jt.job_created_datetime between @date_begin and @date_end
        and jt.country_id = @country
    )
select distinct country_id,
       job_id,
       job_similar_group_id,
       job_created_datetime,
       job_title,
       company_name,
       telephones,
       emails,
       job_url,
       project_id,
       project_source_type_id
from job_it_with_similar
where job_in_similar_group_rank = 1 ;
