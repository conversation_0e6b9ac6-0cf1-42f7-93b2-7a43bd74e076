declare @country int = (select id from [dwh].postgres.dimension.countries c where c.alpha_2 = substring(db_name(), 5, 2)) ,
        @date_begin date = '2022-09-05', -- yesterday
        @date_end date = '2022-09-06' -- today
      ;

         -- technology by sentinel
         select distinct @country       as country_id,
                         js.id_job      as job_id,
                         ts.technology_id,
                         dt.technology_name,
                         j.date_created as job_created_datetime
         from job_sentinel js with (nolock)
                  join job j with (nolock)
                       on j.id = js.id_job and
                          j.date_created between @date_begin and @date_end
             join job_cluster jc with (nolock)
               on jc.id_job = j.id and
                  jc.id_cluster = 167 /*new collars*/
                  join [dwh].postgres.aggregation_partner.technology_sentinel ts
                       on ts.job_sentinel_id = js.id_sentinel
                  join [dwh].postgres.aggregation_partner.technology_keyword dt on ts.technology_id = dt.technology_id
         where js.id_sentinel in (select distinct job_sentinel_id from [dwh].postgres.aggregation_partner.technology_sentinel)
           and j.emails is not null /*only with emails*/

         union

         -- technology by title
         select distinct @country         as country_id,
                         j.id             as job_id,
                         dt.technology_id as technology_id,
                         dt.technology_name,
                         j.date_created   as job_created_datetime
         from job j with (nolock)
                  join [dwh].postgres.aggregation_partner.technology_keyword dt
                       on  dt.is_active_by_title = 1 /*only active keyword and technologies*/ and
                           dt.tag_type_id = 1 /*only technologies*/
         where lower(title) like '%' + dt.keyword_name + '%' collate SQL_Latin1_General_CP1_CI_AS
           and j.date_created between @date_begin and @date_end
           and j.emails is not null /*only with emails*/

union

        -- technology by description
        select distinct
               @country as country_id,
               j.id as job_id,
               dt.technology_id as technology_id,
               dt.technology_name,
               j.date_created as job_created_datetime
        from job j with (nolock)

                 join job_text jt with (nolock)
                      on jt.hash64 = j.text_hash64
                  join [dwh].postgres.aggregation_partner.technology_keyword dt
                        on dt.is_active_by_description = 1 /*only active keyword and technologies*/ and
                           dt.tag_type_id = 1 /*only technologies*/
        where lower(jt.text) like '%'+dt.keyword_name+'%' collate SQL_Latin1_General_CP1_CI_AS and
               j.date_created between @date_begin and @date_end and
               j.emails is not null /*only with emails*/
union



         -- job profession by title
         select distinct @country         as country_id,
                         j.id             as job_id,
                         dt.technology_id as technology_id,
                         dt.technology_name,
                         j.date_created   as job_created_datetime
         from job j with (nolock)
         join job_text jt with (nolock)
          on jt.hash64 = j.text_hash64
         join [dwh].postgres.aggregation_partner.technology_keyword dt
           on  dt.is_active_by_title = 1  and
               dt.tag_type_id = 2 /*only titles*/
         where lower(title) like '%' + dt.keyword_name + '%' collate SQL_Latin1_General_CP1_CI_AS
           and j.date_created between @date_begin and @date_end
           and j.emails is not null /*only with emails*/
           and exists (select 1
                        from [dwh].postgres.aggregation_partner.industry_keyword ik
                        where lower(jt.text) like '%' + ik.keyword_name + '%' collate SQL_Latin1_General_CP1_CI_AS
                          and ik.is_active = 1 and
                                ik.industry_id = 1 /*only for IT*/
             )

