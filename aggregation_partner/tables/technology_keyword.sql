INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (1, 'sql', 'sql', 0, 0, 1);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (6, 'javascript_react', 'react', 0, 0, 1);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (7, 'unit', ' unit', 0, 0, 1);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (8, 'linux', 'linux', 0, 0, 1);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (11, 'oracle', 'oracle', 0, 0, 1);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (12, 'salesforce', 'salesforce', 0, 0, 1);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (13, 'rail', 'rail', 0, 0, 1);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (14, 'tableau', 'tableau', 0, 0, 1);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (15, 'xml', 'xml', 0, 0, 1);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (16, 'unix', ' unix', 0, 0, 1);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (18, 'ssis', 'ssis', 0, 0, 1);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (19, 'wordpress', 'wordpress', 0, 0, 1);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (20, 'vba', 'vba', 0, 0, 1);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (21, 'oracle_database', 'oracle', 0, 0, 1);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (29, 'c-level', 'chief operating officer', 1, 1, 2);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (28, 'it_recruiter', 'technical recruiter', 1, 1, 2);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (28, 'it_recruiter', 'technical recruit', 1, 1, 2);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (29, 'c-level', 'coo ', 1, 0, 2);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (9, 'c++', 'c++', 1, 1, 1);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (17, 'kotlin', 'kotlin', 1, 1, 1);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (4, 'c#', 'c#', 1, 1, 1);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (5, 'java', 'java,', 1, 1, 1);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (5, 'java', 'java ', 1, 1, 1);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (3, 'python', 'python', 1, 1, 1);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (22, 'ruby', 'ruby', 1, 1, 1);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (26, 'javascript_vue', ' vue', 1, 1, 1);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (10, 'php', 'php', 1, 1, 1);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (5, 'java', 'java.', 1, 1, 1);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (23, '.net', ' .net', 1, 1, 1);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (25, 'javascript_angular', 'angular', 1, 1, 1);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (2, 'javascript', 'javascript', 1, 1, 1);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (24, 'unity', ' unity', 1, 1, 1);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (29, 'c-level', 'cpo', 1, 0, 2);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (29, 'c-level', 'delivery director', 1, 1, 2);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (29, 'c-level', 'ceo', 1, 0, 2);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (27, 'developer', 'ingeniera de software', 1, 1, 2);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (27, 'developer', 'fejlesztő', 1, 1, 2);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (27, 'developer', 'full-stack', 1, 1, 2);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (29, 'c-level', 'head of talent acquisition ', 1, 1, 2);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (28, 'it_recruiter', 'talent acquisition', 1, 1, 2);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (29, 'c-level', 'chief people officer', 1, 0, 2);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (27, 'developer', ' it', 0, 0, 2);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (29, 'c-level', 'chief marketing officer', 1, 1, 2);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (29, 'c-level', 'product development director', 1, 1, 2);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (27, 'developer', 'entwickler', 1, 1, 2);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (29, 'c-level', 'head of recruitment', 1, 0, 2);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (29, 'c-level', 'chief operations officer', 1, 1, 2);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (27, 'developer', 'programmer', 1, 1, 2);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (27, 'developer', 'dezvoltator', 1, 1, 2);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (29, 'c-level', 'cfo', 1, 0, 2);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (27, 'developer', 'developer', 1, 1, 2);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (29, 'c-level', 'cmo', 1, 0, 2);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (27, 'developer', 'разработчик', 1, 1, 2);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (29, 'c-level', 'vp of delivery', 1, 1, 2);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (29, 'c-level', 'talent acquisition director', 1, 1, 2);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (27, 'developer', 'udvikler', 1, 1, 2);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (27, 'developer', 'full stack', 1, 1, 2);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (29, 'c-level', 'vp of product', 1, 1, 2);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (27, 'developer', 'programación', 1, 1, 2);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (27, 'developer', 'розробник', 1, 1, 2);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (27, 'developer', 'back-end', 1, 1, 2);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (27, 'developer', 'ontwikkelaar', 1, 1, 2);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (29, 'c-level', 'hrd', 1, 0, 2);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (29, 'c-level', 'chief financial officer', 1, 1, 2);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (27, 'developer', 'backend', 1, 1, 2);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (27, 'developer', 'ingenieria de software', 1, 1, 2);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (27, 'developer', 'vývojář', 1, 1, 2);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (29, 'c-level', 'human resources director', 1, 0, 2);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (27, 'developer', 'frontend', 1, 1, 2);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (27, 'developer', 'arquitecto de software', 1, 1, 2);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (27, 'developer', 'programador', 1, 1, 2);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (27, 'developer', 'desenvolvedor', 1, 1, 2);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (27, 'developer', 'back end', 1, 1, 2);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (29, 'c-level', 'chief executive officer', 1, 1, 2);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (27, 'developer', 'front-end', 1, 1, 2);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (29, 'c-level', 'vp of human resources', 1, 1, 2);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (29, 'c-level', 'vp of sales', 1, 1, 2);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (28, 'it_recruiter', 'it recruit', 1, 1, 2);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (29, 'c-level', 'vp of engineering', 1, 1, 2);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (27, 'developer', 'front end', 1, 1, 2);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (27, 'developer', 'développeur', 1, 1, 2);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (27, 'developer', 'kehittäjä', 1, 1, 2);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (27, 'developer', 'deweloper', 1, 1, 2);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (27, 'developer', 'pemaju', 1, 1, 2);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (29, 'c-level', 'coo.', 1, 0, 2);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (29, 'c-level', 'coo)', 1, 0, 2);
INSERT INTO aggregation_partner.technology_keyword (technology_id, technology_name, keyword_name, is_active_by_title, is_active_by_description, tag_type_id) VALUES (29, 'c-level', 'coo,', 1, 0, 2);