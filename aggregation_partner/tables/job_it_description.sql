declare @country int = (select id from [dwh].postgres.dimension.countries c where c.alpha_2 = substring(db_name(), 5, 2)) ,
        @date_begin date = '2022-05-07', -- yesterday
        @date_end date = '2022-05-08' -- today
      ;

select @country as country_id,
        ji.job_id,
        jt.text as job_description,
        j.date_created

 from job_text jt with (nolock)
 join job j with (nolock)
   on j.text_hash64 = jt.hash64
 join [dwh].postgres.aggregation_partner.job_it ji
   on ji.country_id = @country and
      ji.job_id = j.id and
      ji.job_created_datetime between @date_begin and @date_end
 ;
