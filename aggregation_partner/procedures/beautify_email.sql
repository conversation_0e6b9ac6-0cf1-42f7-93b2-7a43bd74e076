create function aggregation_partner.beautify_email(email_str character varying) returns character varying
	language plpgsql
as $$
declare
    last_dot_position int := length(email_str) - position('.' in reverse(email_str)) + 1;
    fixed_email       varchar;
    domain_matches    int;
begin

    select count(*)
    into domain_matches
    from dimension.email_top_level_domain etld
    where etld.domain = substr(email_str, last_dot_position, length(email_str));

    if domain_matches > 0 then
        return email_str;
    end if;

    -- find domain to level
    create temp table temp_domain_data as
    select email_str                                               as email,
           substr(email_str, last_dot_position, length(email_str)) as domain_top_level,
           domain_matches                                          as domain_match_cnt;

-- cases without domain match
    create temp table temp_all_domain_matches as
    select dd.*,
           etld2.domain,
           length(etld2.domain)                                as domain_matched_length,
           max(length(etld2.domain)) over (partition by email) as match_with_longest_domain
    from temp_domain_data dd
             left join dimension.email_top_level_domain etld2
                       on 1 = 1
    where domain_match_cnt = 0
      and dd.domain_top_level like '%' || etld2.domain || '%';


-- cases with more than 1 domain match (choose longest)
    create temp table temp_fixed_email as
    select distinct email,
                    substr(email, 1, last_dot_position - 1) || domain as beatufied_email,
                    domain_top_level,
                    domain
    from temp_all_domain_matches
    where match_with_longest_domain = domain_matched_length;

    select beatufied_email
    into fixed_email
    from temp_fixed_email;

    drop table if exists temp_domain_data;
    drop table if exists temp_all_domain_matches;
    drop table if exists temp_fixed_email;

    return fixed_email;

end;
$$;

alter function aggregation_partner.beautify_email(varchar) owner to mb;

