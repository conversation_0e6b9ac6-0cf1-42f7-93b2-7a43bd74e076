create or replace view aggregation_partner.v_job_it as
select ji.country_id,
       c.alpha_2 as country,
       ji.job_id,
       ji.job_created_datetime,
       job_title,
       ji.company_name,
       --email_list,
       job_url,
       jid.job_description,
       ii.industry_it_name,
       aggregation_partner.beautify_email(lower(case
                                  when position(';' in ji.email_list) = 0 then ji.email_list
                                  else substr(ji.email_list, 1, position(';' in ji.email_list) - 1)
    end)) as email
from aggregation_partner.job_it ji
         join dimension.countries c
              on c.id = ji.country_id
         left join dimension.company_industry ci
                   on ci.company_name = ji.company_name
         left join dimension.industry_it ii
                   on ii.industry_it_id = ci.industry_id
         left join aggregation_partner.job_it_description jid
                on jid.country_id = ji.country_id and
                   jid.job_id = ji.job_id
where ji.job_created_datetime > (CURRENT_DATE - 21)
  /*conditions are filtered Recruiting agencies*/
  and lower(ji.company_name) not like '%recruit%' and
      lower(ji.company_name) not like '%confidential%' and
      lower(ji.company_name) not like '%consult%' and
      lower(ji.company_name) not like '%resourc%' and
      (ci.industry_id <> 11 or ci.industry_id is null) /*Recruiting agencies*/ and
      email_list is not NULL
;

grant select on aggregation_partner.v_job_it to readonly;
