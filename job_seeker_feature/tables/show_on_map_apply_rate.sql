select afc.country_id,
       afc.jdp_viewed_datediff,
       case when sja.id is not null then 1 else 0 end as has_show_on_map_click,
       count(distinct afc.jdp_id) as apply_click_cnt,
       count(distinct case when afc.is_success = 1 then afc.jdp_id end) as apply_cnt
from apply.application_form_conversion afc
left join imp.session_jdp_action sja on sja.country = afc.country_id and sja.date_diff = afc.jdp_viewed_datediff and sja.id_jdp = afc.jdp_id
    and sja.type in ( 35, 46,  48)
where afc.country_id = 1
  and afc.jdp_viewed_datediff = ${DT_NOW} - 1
group by afc.country_id,
         afc.jdp_viewed_datediff,
         case when sja.id is not null then 1 else 0 end;