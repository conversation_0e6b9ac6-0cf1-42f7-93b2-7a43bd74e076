create or replace procedure balance_ea.insert_employer_balance_state_monthly()
	language sql
as $$
insert into balance_ea.employer_balance_state_monthly(country_code, country_name, employer_id, balance_type, currency_id,
                                                              balance_main_currency, balance_bonus_currency, balance_main_usd,
                                                              balance_bonus_usd, sources, time_key)
        with bal_ea as (
            select id_employer
                , type
                , vat
                , premium_price
                , id_currency
                , balance as curr_balance  -- балансы для дальнейших целей сегментации
                , balance_bonus as curr_balance_bonus
                , sources
            from imp_employer.employer_balance
        ),
        ea as (
            select id as id_employer
                , upper(country_code) as country_code
                , id_counterparty
                , sources
                , id_cdp
            from imp_employer.employer
            ),
        usd_price as (
            select upper(country_domain) as country_domain
                , premium_price as premium_price_usd
                , id_counterparty
                , sources
            from imp_employer.info_country_premium
            where id_currency=22
        ),
        all_d as (
            select ea.id_employer
                , bal_ea.id_currency
                , ea.country_code
                , ea.id_cdp
                , bal_ea.curr_balance as balance_main_currency
                , bal_ea.curr_balance_bonus as balance_bonus_currency
                , cp.premium_price
                , bal_ea.vat
                , bal_ea.premium_price as pp_price_vat
                , coalesce(usd_price.premium_price_usd, -1) as premium_price_usd
                , ea.sources
                , type
            from bal_ea
                    inner join ea on bal_ea.id_employer = ea.id_employer and bal_ea.sources=ea.sources
                    inner join imp_employer.info_currency curr on curr.id = bal_ea.id_currency
                    inner join imp_employer.info_country_premium cp on curr.id = cp.id_currency
                                                                        and cp.id_counterparty = ea.id_counterparty
                                                                        and ea.country_code = upper(cp.country_domain)
                     left join usd_price on ea.country_code = usd_price.country_domain and ea.sources=usd_price.sources
        )
        select country_code,
               coalesce(dim_c.name_country_eng, '-99') as name_country,
               id_employer,
               type as balance_type,
               id_currency,
               balance_main_currency,
               balance_bonus_currency,
               round((premium_price_usd / premium_price ) * (balance_main_currency/(1 + vat/100)), 2) as balance_main_usd,
               round((premium_price_usd / premium_price ) * (balance_bonus_currency/(1 + vat/100)), 2) as balance_bonus_usd,
               sources,
               current_date as time_key
        from all_d
            left join dimension.countries dim_c on all_d.country_code = upper(dim_c.alpha_2);

$$;

alter procedure balance_ea.insert_employer_balance_state_monthly() owner to rlu;

