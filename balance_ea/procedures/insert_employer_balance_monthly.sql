create or replace procedure balance_ea.insert_employer_balance_monthly()
	language sql
as $$
truncate table balance_ea.employer_balance_monthly;

            insert into balance_ea.employer_balance_monthly(report_month, employer_id, country_code, country_name, company_name, industry,
                                                        balance_type, current_balance, current_balance_bonus, current_balance_usd,
                                                        current_balance_bonus_usd, change_type, currency_name, value_currency,
                                                        bonus_spent_currency, vat, value_usd_vat, value_usd, sum_bonuses_spent_usd_vat,
                                                        sum_bonuses_spent_usd)
            with bal_hist as (
            select id_employer,
                 -- сумма пополн-возврата средств на бонусном счету
                   case
                       when change_type in (5, 8) then '5,8'
                       when change_type in (1, 6, 3) then '1,6,3'
                       when change_type in (0, 2) then '0,2'
                       else '-99' end as change_type,
                   sum(value) as value,
                   sum(bonuses_spent) as bonuses_spent,
                   cast(date_created as date) as date_created,
                   sources
            from imp_employer.employer_balance_history
            where cast(date_created as date) >= '2019-01-01'
                and change_type not in (4, 7) -- 7 для постейд списания. Фактически их пока пару штук.
            group by id_employer,
                 -- сумма пополн-возврата средств на бонусном счету
                     case
                       when change_type in (5, 8) then '5,8'
                       when change_type in (1, 6, 3) then '1,6,3'
                       when change_type in (0, 2) then '0,2'
                       else '-99' end,
                     cast(date_created as date),
                     sources
        ),
         bal_calc as (
             select distinct id_employer,
                    change_type,
                    sum(value) over (partition by sources, id_employer, change_type, to_char(date_created::date, 'YYYY-MM')) as sum_value,
                    sum(bonuses_spent) over (partition by sources, id_employer, change_type, to_char(date_created::date, 'YYYY-MM')) as sum_bonuses_spent,
                    to_char(date_created::date, 'YYYY-MM-01') as report_month,
                    sources
             from bal_hist
        ),
        bal_ea as (
            select id_employer,
                   type,
                   premium_price,
                   id_currency,
                   vat,
                   balance as curr_balance,
                   balance_bonus as curr_balance_bonus,
                   sources
            from imp_employer.employer_balance
        ),
        ea as (
            select id as id_employer,
                   upper(country_code) as country_code,
                   id_counterparty,
                   id_cdp,
                   sources
            from imp_employer.employer),
        usd_price as (
            select upper(country_domain) as country_domain,
                   premium_price as premium_price_usd,
                   id_counterparty,
                   sources
            from imp_employer.info_country_premium
            where id_currency = 22),
        all_d as (
            select distinct ea.id_employer,
                   ea.sources,
                   ea.id_cdp,
                   ea.country_code,
                   bal_ea.id_currency,
                   curr.short_name as currency_name,
                   cp.premium_price,
                   bal_ea.type,
                   bal_ea.vat,
                   bal_calc.change_type,
                   bal_calc.sum_value,
                   bal_calc.sum_bonuses_spent,
                   bal_calc.report_month,
                   bal_ea.curr_balance,
                   bal_ea.curr_balance_bonus,
                   bal_ea.premium_price as pp_price_vat,
                   coalesce(usd_price.premium_price_usd, -1) as premium_price_usd
            from bal_calc
            inner join ea on bal_calc.id_employer = ea.id_employer and bal_calc.sources=ea.sources
            inner join bal_ea on bal_calc.id_employer = bal_ea.id_employer and bal_calc.sources=bal_ea.sources
            inner join imp_employer.info_currency curr on curr.id = bal_ea.id_currency
            inner join imp_employer.info_country_premium cp on curr.id = cp.id_currency and cp.id_counterparty = ea.id_counterparty and ea.country_code = upper(cp.country_domain)
            left join usd_price on ea.country_code = usd_price.country_domain and ea.sources=usd_price.sources
        )
        select report_month,
               id_employer,
               country_code,
               coalesce(dim_c.name_country_eng, '-99') as country_name,
               coalesce(cdp.company_name, '-99') as company_name,
               coalesce(cdp.industry, '-99') as industry,
               all_d.type,
               curr_balance,
               curr_balance_bonus,
               round((premium_price_usd / premium_price ) * (curr_balance/(1 + vat/100)), 2) as curr_balance_usd,
               round((premium_price_usd / premium_price ) * (curr_balance_bonus/(1 + vat/100)), 2) as curr_balance_bonus_usd,
               change_type,
               currency_name,
               sum_value as value_currency,
               sum_bonuses_spent as bonuses_spent_currency,
               vat,
               round((premium_price_usd / premium_price ) * sum_value, 2) as value_usd_vat,
               round((premium_price_usd / premium_price ) * (sum_value/(1 + vat/100)), 2) as value_usd, -- [VALUE] / (1 + [vat]/100)
               round((premium_price_usd / premium_price ) * sum_bonuses_spent, 2) as sum_bonuses_spent_usd_vat,
               round((premium_price_usd / premium_price ) * (sum_bonuses_spent/(1 + vat/100)), 2) as sum_bonuses_spent_usd -- [VALUE] / (1 + [vat]/100)
        from all_d
        left join imp_employer.employer_cdp cdp on all_d.id_cdp = cdp.id and all_d.sources = cdp.sources
        left join dimension.countries dim_c on all_d.country_code = upper(dim_c.alpha_2);


$$;

alter procedure balance_ea.insert_employer_balance_monthly() owner to rlu;

