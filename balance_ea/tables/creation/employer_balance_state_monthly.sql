create table balance_ea.employer_balance_state_monthly
(
	id serial
		constraint pk_employer_balance_state_monthly_id
			primary key,
	country_code varchar(2) not null,
	country_name varchar(25),
	employer_id integer not null,
	balance_type smallint,
	currency_id smallint,
	balance_main_currency numeric(19,5),
	balance_bonus_currency numeric(19,5),
	balance_main_usd numeric(19,5),
	balance_bonus_usd numeric(19,5),
	sources smallint not null,
	time_key date not null
);

alter table balance_ea.employer_balance_state_monthly owner to postgres;

grant select on sequence balance_ea.employer_balance_state_monthly_id_seq to npo;

create index ind_employer_balance_state_monthly_dd
	on balance_ea.employer_balance_state_monthly (time_key);

grant select on balance_ea.employer_balance_state_monthly to npo;

grant select on balance_ea.employer_balance_state_monthly to readonly;

