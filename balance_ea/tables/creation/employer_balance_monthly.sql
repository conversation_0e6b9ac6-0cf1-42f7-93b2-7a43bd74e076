create table balance_ea.employer_balance_monthly
(
	report_month varchar(15),
	employer_id integer,
	country_code varchar(2),
	country_name varchar(50),
	company_name varchar(2048),
	industry varchar(1024),
	balance_type smallint,
	current_balance numeric(19,5),
	current_balance_bonus numeric(19,5),
	current_balance_usd numeric(19,5),
	current_balance_bonus_usd numeric(19,5),
	change_type varchar(100),
	currency_name varchar(255),
	value_currency numeric(19,5),
	bonus_spent_currency numeric(19,5),
	vat numeric(19,5),
	value_usd_vat numeric(19,5),
	value_usd numeric(19,5),
	sum_bonuses_spent_usd_vat numeric(19,5),
	sum_bonuses_spent_usd numeric(19,5)
);

alter table balance_ea.employer_balance_monthly owner to postgres;

grant select on balance_ea.employer_balance_monthly to readonly;

grant select on balance_ea.employer_balance_monthly to writeonly_product;

